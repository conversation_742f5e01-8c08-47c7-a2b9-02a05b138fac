package com.megvii.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.megvii.controller.api.PageLogRequestVO;
import com.megvii.controller.api.PagingResponse;
import com.megvii.controller.api.UserListVO;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.OperateLogDO;
import com.megvii.entity.opdb.contract.UserChangeContractBacklog;
import com.megvii.mapper.filter.PageLogRequestFilter;
import com.megvii.service.OperateLogService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 操作日志，前端控制器
 *
 * <AUTHOR>
 * @date 2020/04/17
 */
@RestController
@RequestMapping("/api/log")
public class OperateLogController {

    @Autowired
    private OperateLogService operateLogService;

    @PostMapping(value = "/page/list")
    public PagingResponse pageLog(@Valid @RequestBody PageLogRequestVO request){
        PagingResponse response = new PagingResponse();
        PageLogRequestFilter filter =new PageLogRequestFilter();
        BeanUtils.copyProperties(request,filter);
        PageHelper.startPage(request.getCurrent(),request.getSize());
        List<OperateLogDO> operateLogDOS = operateLogService.pageLogs(filter);
        PageInfo<OperateLogDO> pageInfo = new PageInfo<>(operateLogDOS);
        response.setData(operateLogDOS);
        response.setPageNumber(pageInfo.getPageNum());
        response.setTotalPage(pageInfo.getPages());
        response.setTotalCount(pageInfo.getTotal());
        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        return response;
    }

}

