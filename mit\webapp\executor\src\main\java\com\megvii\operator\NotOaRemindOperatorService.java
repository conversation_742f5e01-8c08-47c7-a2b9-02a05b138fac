package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.RemindService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/2/25 14:30
 */
@Service
public class NotOaRemindOperatorService extends BaseOperatorService {

    @Autowired
    private NotOaRemindOperatorService notOaRemindOperatorService;

    @Autowired
    private RemindService remindService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {

        return notOaRemindOperatorService.operateOnce(paraMap, params);

    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        String result = remindService.sendUserNotOaRemind();
        return new OperateResult(1, result);

    }


}
