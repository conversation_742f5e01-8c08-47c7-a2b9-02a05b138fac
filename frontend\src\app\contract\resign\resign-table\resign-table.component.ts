import { Component, OnInit, Input } from '@angular/core'
import { ResignService } from '../resign.service'
import { Router } from '@angular/router'
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal'
import { ContractService } from '../../contract.service'
import { MessageService, SendRequest } from '../../../core/services'
import { Observable } from 'rxjs'
import { SaveKey } from '../../../core/services/saveTabKey.service'
@Component({
  selector: 'app-resign-table',
  templateUrl: './resign-table.component.html',
  styleUrls: ['./resign-table.component.less']
})
export class ResignTableComponent implements OnInit {
  @Input() type = ''
  timer: any
  window = window as any
  isSearchExpand = false
  page = 1
  pageSize = 10
  totalCount = 0
  total = 0
  entryDateRange = []
  mapOfExpandData: { [key: string]: boolean } = {}
  // listOfTeamOption: Array<{ label: string; value: string }> = [{ label: '全部', value: '' }] // team候选项
  listOfData = []
  // 是否显示扫描上传弹框
  isUploadVisible = false
  // 是否打开去盖章弹框
  isStampVisible = false
  confirmModal: NzModalRef
  leaveFileList = []
  // 记录userId
  userId = ''
  // 下载文件连接
  downLoadUrl
  stampData = {
    stampName: '',
    stampId: '',
    stampDate: '',
    contractId: '',
    company: ''
  }
  staffid = null
  id = null
  username = null
  team = null
  // 是否有补偿
  compensation = 0
  // 是否有竞业合同
  competition = 0
  // 股份
  shares = 0
  stockManage = 0
  // 有无特定公司
  specialCompany = 0
  dismissionId
  userContractFile

  // 压缩包文件列表
  fileList = []
  loading = false
  isAdding = false
  pullLoading = false
  signLoading = false
  nowUserName = ''
  isOkDisable = true
  uploadLoading = false
  deleteSnapLoading = false
  warnInfo = ''
  constructor(
    private resignService: ResignService,
    private router: Router,
    private modal: NzModalService,
    private contractService: ContractService,
    private messageService: MessageService,
    private saveKey: SaveKey
  ) {}
  ngOnInit(): void {
    this.getDimissionUserLists()
  }
  onExpand() {
    this.isSearchExpand = !this.isSearchExpand
  }
  nzFilterOption = () => true
  onSearch() {
    this.page = 1
    this.getDimissionUserLists()
  }
  // searchTeam(value: string) {
  //   clearTimeout(this.timer)
  //   this.timer = setTimeout(() => {
  //     this.getTeamList(value)
  //   }, 500)
  // }
  onReset() {
    this.staffid = null
    this.id = null
    this.username = null
    this.team = null
    this.entryDateRange = []
    this.page = 1
    this.getDimissionUserLists()
  }
  // getTeamList(value: string) {
  //   this.contractService.getTeamList({ teamName: encodeURIComponent(value) }).subscribe((res) => {
  //     const { code, msg, data } = res
  //     if (code === 0) {
  //       this.listOfTeamOption = [{ name: '全部', code: '' }, ...data]
  //     } else {
  //       this.messageService.error(msg)
  //     }
  //   })
  // }
  pageSizeChange(event) {
    this.pageSize = event
    this.getDimissionUserLists()
  }
  pageChange() {
    this.mapOfExpandData = {}
    this.getDimissionUserLists()
  }
  // 获取离职合同列表
  getDimissionUserLists() {
    let status = ''
    console.log('typeeeee', this.type)
    if (this.type === 'noHandle') {
      status = '0'
    } else if (this.type === 'noSign') {
      status = '1'
    } else if (this.type === 'noStamp') {
      status = '2'
    } else if (this.type === 'noEmail') {
      status = '3'
    }
    let params = {
      pageNumber: this.page,
      pageSize: this.pageSize,
      flowStatus: status,
      userId: this.id || null,
      workNumber: this.staffid || null,
      spell: this.username || null,
      topTeam: this.team || null,
      dismissionDateFrom: this.entryDateRange[0]
        ? this.entryDateRange[0].getFullYear() +
          '-' +
          (this.entryDateRange[0].getMonth() + 1) +
          '-' +
          this.entryDateRange[0].getDate()
        : null,
      dismissionDateTo: this.entryDateRange[1]
        ? this.entryDateRange[1].getFullYear() +
          '-' +
          (this.entryDateRange[1].getMonth() + 1) +
          '-' +
          this.entryDateRange[1].getDate()
        : null
    }
    this.resignService.getuserDimissionList(params).subscribe((res) => {
      const { code, data, totalCount } = res
      if (code === 0) {
        this.totalCount = totalCount
        this.total = totalCount || 0
        this.listOfData = data
      }
      console.log('listdataaa', this.listOfData)
    })
  }
  userExpandChange(event, user) {
    if (event) {
      Object.keys(this.mapOfExpandData).forEach(
        (key) => (this.mapOfExpandData[key] = key == user['id'] && event)
      )
      this.searchSingleUserContract(user)
    }
  }
  // 查询员工离职合同
  searchSingleUserContract(user) {
    let params = {
      userId: user.account || '',
      dismissionId: user.dismissionId || ''
    }
    this.resignService.searchUserContract(params).subscribe((res) => {
      const { code, data, msg } = res
      if (code === 0) {
        user.contractList = data
      } else {
        this.messageService.error(msg)
      }
    })
  }
  // 跳转人员详情列表
  onUpdate(userid, dismissionid) {
    this.router.navigate([this.router.url + '/user'], {
      queryParams: { userId: userid, dismissionId: dismissionid }
    })
  }
  // 关闭扫描上传弹框
  showUpload(data) {
    // ;[
    // {
    //   key: 'terminationAgreement',
    //   fileListKey: 'terminFileList'
    // },
    // {
    //   key: 'resignationCertificate',
    //   fileListKey: 'resignFileList'
    // },
    // {
    //   key: 'salaryStatement',
    //   fileListKey: 'salaryFileList'
    // },
    // {
    //   key: 'competitionNotice',
    //   fileListKey: 'noticeFileList'
    // },
    // {
    //   key: 'economicAgreement',
    //   fileListKey: 'economicFileList'
    // }
    //   {
    //     key: 'userContractFile',
    //     fileListKey: 'fileList'
    //   }
    // ].forEach((item: { key: string; fileListKey: string }) => {
    //   if (data[item.key]) {
    //     this[item.key] = data[item.key]
    //     this[item.fileListKey] = [
    //       {
    //         name: data[item.key],
    //         // url: `/api/userDimission/contractFile/file?name=${data[item.key]}&userId=${
    //         //   data['account']
    //         // }`,
    //         url: `/api/contract/contractFile/upload?userId=${data['account']}&fileName=${
    //           data[item.key]
    //         }`,
    //         status: 'done'
    //       }
    //     ]
    //   } else {
    //     this[item.key] = null
    //     this[item.fileListKey] = []
    //   }
    // })

    this.userId = data['account']
    this.dismissionId = data['dismissionId']
    this.isUploadVisible = true
    this.nowUserName = data['userName']
    this.warnInfo = ''
    this.uploadLoading = false
    console.log('cccccdata', data)
  }
  handleCancel() {
    this.resetUpload()
    this.isUploadVisible = false
  }
  resetUpload() {
    this.fileList = []
  }
  handleOk() {
    this.signLoading = true
    this.dismissionId = this.dismissionId
    let params = {
      dismissionId: this.dismissionId,
      userContractFile: this.userContractFile
    }
    if (this.fileList.length !== 0) {
      this.resignService.submitFile(params).subscribe((res) => {
        this.signLoading = false
        const { code, msg } = res
        if (code === 0) {
          this.messageService.success('提交成功')
          this.isUploadVisible = false
          this.resetUpload()
          this.getDimissionUserLists()
        } else {
          this.messageService.error(msg)
        }
      })
    } else {
      this.messageService.error('请上传必上传文件')
    }
  }
  // 撤回弹框
  returnWarn(id, data) {
    this.confirmModal = this.modal.confirm({
      nzTitle: '确认要撤回这条数据吗?',
      nzContent: '温馨提示：撤回后数据将会返回到“待办理”状态',
      nzOnOk: () => {
        this.returnContractId(id, data)
      }
    })
  }
  // 调用撤回
  returnContractId(id, data) {
    let params = {
      contractId: id
    }
    this.resignService.returnContract(params).subscribe((res) => {
      const { code, msg } = res
      if (code === 0) {
        this.messageService.success('撤回成功！')
        this.searchSingleUserContract(data)
        this.getDimissionUserLists()
        this.saveKey.setMap('selectKey', 0)
      } else {
        this.messageService.error(msg)
      }
    })
  }
  // 删除草稿
  deleteSnapshotIn(disid, data) {
    let params = {
      dismissionId: disid
    }
    this.modal.confirm({
      nzTitle: '<i>确定要删除这条草稿吗?</i>',
      // nzContent: '<b>Some descriptions</b>',
      nzOkLoading: this.deleteSnapLoading,
      nzOnOk: () => {
        this.resignService.deleteSnapshot(params).subscribe((res) => {
          this.deleteSnapLoading = true
          const { code, msg } = res
          if (code === 0) {
            this.deleteSnapLoading = false
            this.messageService.success('删除成功！')
            this.searchSingleUserContract(data)
            this.getDimissionUserLists()
            this.mapOfExpandData = {}
          } else {
            this.messageService.error('删除失败！')
          }
        })
      }
    })
  }
  // 去盖章
  goToStamp(data, id, com) {
    this.stampData.stampName = data.userName
    this.stampData.stampId = data.account
    this.stampData.stampDate = data.dismissionDate
    this.stampData.contractId = id
    this.stampData.company = com
    this.isStampVisible = true
    this.compensation = data.compensation
    this.competition = data.competition
    this.shares = data.shares
    this.specialCompany = data.specialCompany
    this.stockManage = data.stockManage
  }
  handleStampCancel() {
    this.isStampVisible = false
  }
  handleStampOk() {
    this.isAdding = true
    let params = {
      contractId: this.stampData.contractId,
      company: this.stampData.company
    }

    this.resignService.toStampContract(params).subscribe((res) => {
      this.isAdding = false
      const { code, msg } = res
      if (code === 0) {
        this.messageService.success('盖章成功！')
        this.isStampVisible = false
        this.getDimissionUserLists()
        this.saveKey.setMap('selectKey', 3)
      } else {
        this.messageService.error(msg)
      }
    })
  }
  // 归档
  returnDoc(id, status) {
    if (status === '已完成') {
      this.resignService.returnDocument(id).subscribe((res) => {
        const { code, msg } = res
        if (code === 0) {
          this.messageService.success('归档成功！')
          this.getDimissionUserLists()
        } else {
          this.messageService.error(msg)
        }
      })
    }
  }
  // 处理上传文件
  // handleEconomicRemove = () => {
  //   this.economicAgreement = ''
  //   this.economicFileList = []
  // }
  // getCurrentDate() {
  //   const now = new Date()
  //   return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now
  //     .getDate()
  //     .toString()
  //     .padStart(2, '0')}`
  // }
  // updateFileName(
  //   type:
  //     | 'terminationAgreement'
  //     | 'resignationCertificate'
  //     | 'salaryStatement'
  //     | 'competitionNotice'
  //     | 'economicAgreement',
  //   fileName: string
  // ) {
  //   let name = ''
  //   let ext = fileName.slice(fileName.lastIndexOf('.'))
  //   switch (type) {
  //     case 'terminationAgreement':
  //       name = '解除劳动关系协议书'
  //       break
  //     case 'resignationCertificate':
  //       name = '离职证明'
  //       break
  //     case 'salaryStatement':
  //       name = '薪酬结算单'
  //       break
  //     case 'competitionNotice':
  //       name = '履行竞业限制义务通知书'
  //       break
  //     case 'economicAgreement':
  //       name = '经济受益权保留协议'
  //       break
  //     default:
  //       break
  //   }

  //   return `${this.userId}_${name}_${this.getCurrentDate()}${ext}`
  // }

  // beforeUpload(file) {
  //   let flag = true
  //   const isType = file.type === 'application/pdf'
  //   if (!isType) {
  //     this.messageService.error('只能上传压缩包类型文件！')
  //     flag = false
  //   }
  //   return flag
  // }

  // handlePreview(
  //   type:
  //     | 'terminFileList'
  //     | 'resignFileList'
  //     | 'salaryFileList'
  //     | 'noticeFileList'
  //     | 'economicFileList'
  // ) {
  //   return () => {
  //     if (this[type] && this[type].length > 0) {
  //       const win = window.open(this[type][0].url, '_blank')
  //       // win.onload = () => {
  //       //   setTimeout(() => {
  //       //     win.document.title = this[type][0].name
  //       //   }, 200)
  //       // }
  //     }
  //   }
  // }
  // 文件上传
  handleListRemove = (file: File) => {
    this.fileList = []
    this.isOkDisable = true
    return true
  }
  // uploadSingleFile(list, name) {
  //   return new Observable((observe) => {
  //     const formData = new FormData()
  //     const data = {
  //       name: name,
  //       userId: this.userId
  //     }
  //     list.forEach((file: any) => {
  //       formData.append('file', file)
  //     })

  //     this.resignService.uploadFile(data, formData).subscribe((res) => {
  //       const { code, msg } = res
  //       if (code === 0) {
  //         this.messageService.success('上传成功')
  //         observe.next()
  //       } else {
  //         this.messageService.error(msg)
  //         observe.error()
  //       }
  //     })
  //   })
  // }
  beforeSignUpload = (file: File) => {
    let flag = true
    let isType = false
    if (
      file.type === 'application/x-zip-compressed' ||
      file.type === 'application/zip' ||
      file.type === 'application/pdf' ||
      file.name.split('.')[1] === 'rar'
    ) {
      isType = true
    } else {
      isType = false
    }
    if (!isType) {
      this.messageService.error('只能上传规定类型文件！')
      flag = false
    }
    let maxSize = 1024 * 1024 * 20
    let size = file.size
    if (size > maxSize) {
      this.messageService.error('文件大小不能超过20M！')
      flag = false
    }
    if (flag) {
      this.uploadLoading = true
      const fileName = '离职合同-' + this.nowUserName

      const formData = new FormData()
      const data = {
        name: fileName,
        id: this.userId
      }
      formData.append('file', file)
      this.resignService.uploadFile(data, formData).subscribe((res) => {
        this.fileList = [
          {
            name: fileName,
            url: URL.createObjectURL(file),
            status: 'done'
          }
        ]
        this.uploadLoading = false
        const { code, msg, data } = res
        if (code === 0) {
          this.userContractFile = data
          this.isOkDisable = false
          this.messageService.success('上传成功')
        } else {
          this.messageService.error(msg)
        }
      })
    }
    return false
  }
  handlePreview() {
    return false
  }
  // 查看合同
  onReviewContract(contract) {
    console.log('contract', contract)

    this.contractService
      .getReviewContractUrl({ contractId: contract['contractId'] })
      .subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          if (data) {
            this.window.open(data)
          }
        } else {
          this.messageService.error(msg)
        }
      })
  }
  // 发送邮件
  sendEmail(data) {
    this.loading = true
    this.resignService.sendEmailForUser(data.dismissionId).subscribe((res) => {
      this.loading = false
      const { code, msg } = res
      if (code === 0) {
        this.messageService.success('发送成功!')
        this.getDimissionUserLists()
      } else {
        this.messageService.error(msg)
      }
    })
  }
  handlePull() {
    this.pullLoading = true
    this.resignService.handlePullDission().subscribe((res) => {
      this.pullLoading = false
      const { code, msg } = res
      if (code === 0) {
        this.messageService.success('拉取成功!')
        this.getDimissionUserLists()
      } else {
        this.messageService.error(msg)
      }
    })
  }
}
