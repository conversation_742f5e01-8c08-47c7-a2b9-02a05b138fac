import { Component, OnInit, Input } from '@angular/core'
import contractStatus from '../../../core/values/contract-status'
import { SendRequest, MessageService, CreatData } from '../../../core/services'
import { NzModalService } from 'ng-zorro-antd/modal'
import { Router } from '@angular/router'
import { employeeCurrentStatus } from '../../../core/values/employee-current-status'
import { ContractService } from '../../contract.service'
import { NzMessageService } from 'ng-zorro-antd/message'
import { MapService } from '../../../core/services/map.service'
import { ParamStoreService } from '../../../core/services/paramStore.service'
import { FormBuilder, FormGroup, Validators } from '@angular/forms'
import { environment } from 'src/environments/environment'
import { ResignService } from '../../resign/resign.service'
import { formatDate } from 'src/utils/formatDate'
/**
 * 新签合同：/new
 *  本周入职：thisWeek
 *  上周入职：lastWeek
 *  已入职未签署：notSign
 *
 * 续签合同：/extend
 *  待续签：extend
 *  带作废: voiding
 *
 * 合同转签：/endorse
 *  合同主体变更：endorse
 *
 * 变更合同：/change
 *  工作地变更：change
 */

@Component({
  selector: 'app-contract-table',
  templateUrl: './contract-table.component.html',
  styleUrls: ['./contract-table.component.less']
})
export class ContractTableComponent implements OnInit {
  @Input() type = ''
  window = window as any
  timer: any

  staffid = ''
  id = ''
  username = ''
  team = ''
  entryLocation = ''
  entryDateRange = []
  entryType = ''
  status = ''
  tempContractStatus = ''
  userList = []
  loading = false
  page = 1
  pageSize = 10
  total = 0
  mapOfExpandData: { [key: string]: boolean } = {}
  isSearchExpand = false
  contractStatus = contractStatus
  listOfTeamOption: Array<{ label: string; value: string }> = [{ label: '全部', value: '' }] // team候选项
  listOfLocationOption: Array<{ label: string; value: string }> = [] // 入职地点候选项
  entryTypeList = []
  employeeCurrentStatusList = employeeCurrentStatus
  contractStatusList = []

  // 线下签署
  companyOfOption: Array<{ value: string; text: string }> = []
  // 工作地点选项
  workBaseOfOption: Array<{ value: string; text: string }> = []
  signOfflineVisible = false
  showDown = false
  signOfflineUser: any = {}
  signOfflineForm!: FormGroup
  // 存储返回值延迟对话框消失
  ref: {}

  // 上传
  uploadFileAction = ''
  uploadFileList = [] // 上传文件列表
  userContractFile = '' // 上传地址保存
  userContractFileError = false // 上传地址保存错误

  invalidLoading = false
  isUploadVisible = false
  // 扫描上传文件列表
  signFileList = []
  userId
  uploadId
  signLoading = false
  titleMessage = ''
  uploadName = ''
  signUserContractFile = ''
  nowUserName = ''
  isOkDisable = true
  isSignDisable = false
  uploadLoading = false
  // signUsername = ''
  contractType = ''
  manageUploadFileName = ''
  warnInfo = ''
  agreeLoading = false
  isAgreeVisible = false

  editAgreeVisible = false
  editAgreeLoading = false
  uploadAgreeLoading = false
  editAgreeForm = {
    agreeName: '',
    agreeNumber: '',
    contractNumber: '',
    agreeType: '',
    agreeTheme: '',
    agreeCompany: '',
    startDate: null,
    endDate: null,
    id: '',
    userId: ''
  }
  addAgreeFormData = {
    addSpell: '',
    workNumber: '',
    type: '',
    company: '',
    startDate: null,
    endDate: null,
    userContractFile: null,
    userId: '',
    contractNumber: '',
    id: '',
    action: ''
  }
  themeValue = ''
  agreeFileList = []
  addCompany = ''
  agreeArr = ['离职相关文件', '证明材料', '违纪通知书']
  agreeUploadLoading = false
  agreeTitle = ''
  addAgreeTheme = ''
  companyOptions = []
  leaveDateRange = []
  disabledStartDate = (startValue: Date): boolean => {
    if (!startValue || !this.editAgreeForm.endDate) {
      return false
    }
    return startValue.getTime() > new Date(this.editAgreeForm.endDate).getTime()
  }

  disabledEndDate = (endValue: Date): boolean => {
    if (!endValue || !this.editAgreeForm.startDate) {
      return false
    }
    return endValue.getTime() <= new Date(this.editAgreeForm.startDate).getTime()
  }
  editDisabledStartDate = (startValue: Date): boolean => {
    if (!startValue || !this.addAgreeFormData.endDate) {
      return false
    }
    return startValue.getTime() > new Date(this.addAgreeFormData.endDate).getTime()
  }
  editDisabledEndDate = (endValue: Date): boolean => {
    if (!endValue || !this.addAgreeFormData.startDate) {
      return false
    }
    return endValue.getTime() <= new Date(this.addAgreeFormData.startDate).getTime()
  }
  constructor(
    private modalService: NzModalService,
    private router: Router,
    private sendRequest: SendRequest,
    private messageService: MessageService,
    private contractService: ContractService,
    private creatData: CreatData,
    private message: NzMessageService,
    private mapService: MapService,
    private paramStoreService: ParamStoreService,
    private fb: FormBuilder,
    private resignService: ResignService
  ) {}

  ngOnInit(): void {
    this.getParams()
    this.getEntryTypeList()
    this.searchUser()
    this.getWorkBaseList()
    this.getContractStatusList()
    this.getEntryCompanyList()
    this.getEntryWorkBaseList()
    this.initSignOfflineData()
  }

  // 获取参数
  getParams() {
    const params = this.paramStoreService.getMap(this.type)
    if (Object.keys(params).length > 0) {
      this.entryDateRange = params['entryDateRange']
      this.leaveDateRange = params['leaveDateRange']
      this.staffid = params['staffid']
      this.id = params['id']
      this.username = params['username']
      this.team = params['team']
      this.entryLocation = params['entryLocation']
      this.entryType = params['entryType']
      this.status = params['status']
      this.tempContractStatus = params['tempContractStatus']
      this.page = params['page']
      this.pageSize = params['pageSize']
    }
  }

  // 保存参数
  saveParams() {
    const params = {
      entryDateRange: this.entryDateRange,
      leaveDateRange: this.leaveDateRange,
      staffid: this.staffid,
      id: this.id,
      username: this.username,
      team: this.team,
      entryLocation: this.entryLocation,
      entryType: this.entryType,
      status: this.status,
      tempContractStatus: this.tempContractStatus,
      page: this.page,
      pageSize: this.pageSize
    }
    this.paramStoreService.saveMap(this.type, params)
  }

  nzFilterOption = () => true

  onSearch() {
    this.page = 1
    this.searchUser()
    this.mapOfExpandData = {}
  }

  searchUser() {
    console.log('this.type', this.type)

    let requestBody = {
      startExpectDate: !!this.entryDateRange[0] && formatDate(this.entryDateRange[0]),
      endExpectDate: !!this.entryDateRange[1] && formatDate(this.entryDateRange[1]),
      dismissionDateFrom:
        !!this.leaveDateRange[0] && formatDate(this.leaveDateRange[0]).split(' ')[0],
      dismissionDateTo:
        !!this.leaveDateRange[1] && formatDate(this.leaveDateRange[1]).split(' ')[0],
      workNumber: this.staffid,
      userId: this.id,
      spell: this.username,
      teamId: this.team,
      workBase: this.entryLocation,
      employType: this.entryType,
      entryFlow: this.status,
      contractStatus: this.tempContractStatus,
      pageNumber: this.page,
      pageSize: this.pageSize,
      status: ''
    }
    if (this.type === 'thisWeek') {
      const dateArr = this.getWeekStartEnd() // 获取这周起止日期
      requestBody = {
        ...requestBody,
        startExpectDate: dateArr[0],
        endExpectDate: dateArr[1],
        status: '0'
      }
      requestBody['contractStatusCode'] = '0' // 0表示除了已完成的所有状态
      requestBody['employTypeCode'] = '0' // 不包含劳务派遣
    }
    if (this.type === 'lastWeek') {
      const dateArr = this.getWeekStartEnd(7) // 获取上周周起止日期
      requestBody = {
        ...requestBody,
        startExpectDate: dateArr[0],
        endExpectDate: dateArr[1],
        status: '0'
      }
      requestBody['contractStatusCode'] = '0' // 0表示除了已完成的所有状态
      requestBody['employTypeCode'] = '0' // 不包含劳务派遣
    }
    if (this.type === 'active') {
      requestBody = {
        ...requestBody,
        status: '0'
      }
    }
    if (this.type === 'quit') {
      requestBody = {
        ...requestBody,
        status: '1'
      }
    }
    if (this.type === 'notSign') {
      requestBody = {
        ...requestBody,
        entryFlow: '在职',
        status: '0'
      }
      requestBody['contractStatusCode'] = '0' // 0表示除了已完成的所有状态
      requestBody['employTypeCode'] = '0' // 不包含劳务派遣
    }
    if (this.type === 'extend') {
      requestBody = {
        ...requestBody,
        status: '0'
      }
    }
    if (this.type === 'endorse') {
      requestBody = {
        ...requestBody,
        status: '0'
      }
    }
    if (this.type === 'change') {
      requestBody = {
        ...requestBody,
        status: '0'
      }
    }
    if (this.type === 'voiding') {
      requestBody = {
        ...requestBody,
        status: '0'
      }
    }
    this.loading = true
    if (this.type === 'extend') {
      this.contractService
        .getExtendUserList(this.creatData.creatData(requestBody))
        .subscribe((res) => {
          const { code, msg, data, totalCount } = res
          this.loading = false
          if (code === 0) {
            this.saveParams()
            this.total = totalCount || 0
            this.userList = data || []
          } else {
            this.messageService.error(msg)
          }
        })
    } else if (this.type === 'endorse') {
      this.contractService
        .getEndorseUserList(this.creatData.creatData(requestBody))
        .subscribe((res) => {
          const { code, msg, data, totalCount } = res
          this.loading = false
          /* console.log(1); */
          if (code === 0) {
            this.saveParams()
            this.total = totalCount || 0
            this.userList = data || []
          } else {
            this.messageService.error(msg)
          }
        })
    } else if (this.type === 'change') {
      this.contractService
        .getChangeUserList(this.creatData.creatData(requestBody))
        .subscribe((res) => {
          const { code, msg, data, totalCount } = res
          this.loading = false
          /* console.log(2); */
          if (code === 0) {
            this.saveParams()
            this.total = totalCount || 0
            this.userList = data || []
          } else {
            this.messageService.error(msg)
          }
        })
    } else if (this.type === 'voiding') {
      this.contractService
        .getVoidUserList(this.creatData.creatData(requestBody))
        .subscribe((res) => {
          const { code, msg, data, totalCount } = res
          this.loading = false
          if (code === 0) {
            this.saveParams()
            this.total = totalCount || 0
            this.userList = data || []
          } else {
            this.messageService.error(msg)
          }
        })
    } else {
      this.contractService.getUserList(this.creatData.creatData(requestBody)).subscribe((res) => {
        const { code, msg, data, totalCount } = res
        this.loading = false
        if (code === 0) {
          this.saveParams()
          this.total = totalCount || 0
          this.userList = data || []
        } else {
          this.messageService.error(msg)
        }
      })
    }
  }

  getWeekStartEnd(days = 0) {
    const date = new Date()
    date.setDate(date.getDate() - date.getDay() + 1 - days)
    const begin = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()

    date.setDate(date.getDate() + 7 - 1)
    const end = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
    return [begin, end]
  }

  onReset() {
    this.staffid = ''
    this.id = ''
    this.username = ''
    this.team = ''
    this.entryLocation = ''
    this.entryDateRange = []
    this.leaveDateRange = []
    this.entryType = ''
    this.status = ''
    this.tempContractStatus = ''
    this.page = 1
    this.searchUser()
  }

  pageSizeChange(event) {
    this.pageSize = event
    this.searchUser()
  }

  pageChange() {
    this.mapOfExpandData = {}
    this.searchUser()
  }

  onUpdate(id) {
    this.router.navigate([this.router.url + '/user'], {
      queryParams: { id: id }
    }) // 跳转管理员
  }

  onExpand() {
    this.isSearchExpand = !this.isSearchExpand
  }

  onReviewContract(contract) {
    this.contractService
      .getReviewContractUrl({ contractId: contract['contractId'] })
      .subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          if (data) {
            this.window.open(data)
          }
        } else {
          this.messageService.error(msg)
        }
      })
  }

  // 签署合同
  onSign(contract, user) {
    this.modalService.confirm({
      nzTitle: '确定要签署合同吗?',
      nzContent: '<b style="color: red;">点击确认后，合同将发送给员工</b>',
      nzOkText: '确认',
      nzOnOk: () => {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            this.signContract(contract, user, () => {
              resolve()
            })
          }, 1000)
        })
      },

      /* this.signContract(contract, user) */
      nzCancelText: '取消',
      nzOnCancel: () => console.log('Cancel')
    })
  }

  signContract(contract, user, fun) {
    const id = this.message.loading('提交签署中', { nzDuration: 1 }).messageId
    if (this.router.url.indexOf('endorse') > -1 || this.router.url.indexOf('change') > -1) {
      console.log(1)
      this.contractService.signContractChange({ id: contract['id'] }).subscribe((res) => {
        const { code, msg, data } = res
        this.message.remove(id)
        if (code === 0) {
          this.messageService.success('已成功发起签署')
          fun()
          this.getContractList(user)
          user['contractStatus'] = '签署中'
        } else {
          this.messageService.error(data)
          fun()
        }
      })
    } else if (this.router.url.indexOf('new') > -1) {
      this.contractService.signContractNew({ id: contract['id'] }).subscribe((res) => {
        const { code, msg, data } = res
        this.message.remove(id)
        if (code === 0) {
          this.messageService.success('已成功发起签署')
          fun()
          this.getContractList(user)
          user['contractStatus'] = '签署中'
        } else {
          this.messageService.error(msg)
          fun()
        }
      })
    } else {
      console.log(2)
      this.contractService.signContract({ id: contract['id'] }).subscribe((res) => {
        const { code, msg, data } = res
        this.message.remove(id)
        if (code === 0) {
          this.messageService.success('已成功发起签署')
          fun()
          this.getContractList(user)
          user['contractStatus'] = '签署中'
        } else {
          this.messageService.error(data)
          fun()
        }
      })
    }
  }

  // 作废合同
  onInvalid(contract, user) {
    this.modalService.confirm({
      nzTitle: '确定要作废合同吗?',
      nzContent: '<b>作废合同后，需要重新生成合同，确认作废吗？</b>',
      nzOkText: '作废',
      nzOkType: 'danger',
      nzOnOk: () => {
        this.invalidContract(contract, user)
      },
      nzCancelText: '取消',
      nzOkLoading: this.invalidLoading,
      nzOnCancel: () => {
        console.log('Cancel')
      }
    })
  }

  invalidContract(contract, user) {
    this.invalidLoading = true
    this.contractService.cancelContract({ contractId: contract['contractId'] }).subscribe((res) => {
      this.invalidLoading = false
      const { code, msg, data } = res
      if (code === 0) {
        this.messageService.success('作废成功')
        this.getContractList(user)
        user['contractStatus'] = '作废中'
        this.afterVoiding(contract, user)
      } else {
        this.messageService.error(msg)
      }
    })
  }

  // 作废合同完成后的回调
  afterVoiding(contract, user) {
    this.contractService.voidingContract({ userId: contract['userId'] }).subscribe((res) => {
      const { code, msg } = res
      if (code === 0) {
      } else {
        this.messageService.error(msg)
      }
    })
  }

  // 撤销合同
  onBackOut(contract, user) {
    this.modalService.confirm({
      nzTitle: '确定要撤销合同吗?',
      nzContent: '<b style="color: red;">撤销合同后，需要重新生成合同，确认撤销吗？</b>',
      nzOkText: '撤销',
      nzOkType: 'danger',
      nzOnOk: () => {
        return new Promise((resolve, reject) => {
          setTimeout(() => {
            this.backOutContract(contract, user, () => {
              resolve()
            })
          }, 1000)
        })
      },
      nzCancelText: '取消',
      nzOnCancel: () => console.log('Cancel')
    })
  }

  backOutContract(contract, user, fun) {
    this.contractService.recallContract({ contractId: contract['contractId'] }).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.messageService.success('撤销成功')
        fun()
        this.getContractList(user)
        user['contractStatus'] = '已撤回'
      } else {
        this.messageService.error(msg)
        fun()
      }
    })
  }

  getContractList(user) {
    if (this.type === 'active' || this.type === 'quit') {
      this.contractService
        .getContractlistWithSnapshot({ userId: user['userId'] })
        .subscribe((res) => {
          const { code, msg, data } = res
          if (code === 0) {
            user.contractList = data
          } else {
            this.messageService.error(msg)
          }
        })
    } else {
      this.contractService
        .getOtherContractWithSnapshot({ userId: user['userId'] })
        .subscribe((res) => {
          const { code, msg, data } = res
          if (code === 0) {
            user.contractList = data
          } else {
            this.messageService.error(msg)
          }
        })
    }
  }

  userExpandChange(event, user) {
    if (event) {
      Object.keys(this.mapOfExpandData).forEach(
        (key) => (this.mapOfExpandData[key] = key == user['id'] && event)
      )
      this.getContractList(user)
    }
  }

  searchTeam(value: string) {
    clearTimeout(this.timer)
    this.timer = setTimeout(() => {
      this.getTeamList(value)
    }, 500)
  }

  getTeamList(value: string) {
    this.contractService.getTeamList({ teamName: encodeURIComponent(value) }).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.listOfTeamOption = [{ name: '全部', code: '' }, ...data]
      } else {
        this.messageService.error(msg)
      }
    })
  }

  getWorkBaseList() {
    this.mapService.getMap('workBase').then(
      (data) => (this.listOfLocationOption = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  // 获取入职类型码表
  getEntryTypeList() {
    this.mapService.getMap('employType').then(
      (data) => (this.entryTypeList = data as any),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  // 获取全部合同状态
  getContractStatusList() {
    this.contractService.getContractStatusList().subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.contractStatusList = data || []
      } else {
        this.messageService.error(msg)
      }
    })
  }

  // 获取全部公司信息
  getEntryCompanyList() {
    this.mapService.getMap('company').then(
      (data) => {
        this.companyOfOption = (data as any) || []
        ;(data as any).forEach((item) => {
          if (!this.companyOptions.includes(item.name)) {
            this.companyOptions.push(item.name)
          }
        })
      },
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  // 获取全部公司信息
  getEntryWorkBaseList() {
    this.mapService.getMap('location').then(
      (data) => (this.workBaseOfOption = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  // 删除快照
  onDelDraft(contract, user) {
    this.modalService.confirm({
      nzTitle: '确定要删除草稿吗?',
      nzContent: '<b style="color: red;">草稿删除后，需要重新生成草稿，确认删除吗？</b>',
      nzOkText: '删除',
      nzOkType: 'danger',
      nzOnOk: () => this.delDraft(contract, user),
      nzCancelText: '取消',
      nzOnCancel: () => console.log('Cancel')
    })
  }

  // 删除草稿
  delDraft(contract, user) {
    this.contractService.deleteDraft({ userId: user['userId'] }).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.messageService.success('删除草稿成功')
        this.getContractList(user)
        user['contractStatus'] = data
      } else {
        this.messageService.error(msg)
      }
    })
  }

  // 线下签署
  checkSignOffline(data) {
    if(data['employType'] === '退休返聘' || data['employType'] === '劳务派遣') {
      return true
    } else {
      return (
        // 新签
        (this.type === 'notSign' && ['无合同', '已退回', '已撤回', '已作废'].includes(data['contractStatus'])) ||
        // 续签
        (this.type === 'extend' && data['contractStatus'] !== '签署中') ||
        // 转签
        (this.type === 'endorse' && data['contractStatus'] !== '签署中') ||
        // 变更
        (this.type === 'change' && data['contractStatus'] !== '签署中'))
    }
  }

  initSignOfflineData() {
    this.signOfflineForm = this.fb.group({
      contractCount: [null, [Validators.required]],
      contractDate: [[], [Validators.required]],
      company: [null, [Validators.required]]
    })
    if (this.type == 'endorse' || this.type == 'change') {
      this.signOfflineForm.addControl('newContract', this.fb.control(null))
      this.signOfflineForm.addControl('newWorkCity', this.fb.control(null, [Validators.required]))
    }
  }

  getSignStatus() {
    let signStatus = ''

    if (this.router.url.indexOf('new') > -1) {
      signStatus = '新签'
    }
    if (this.router.url.indexOf('extend') > -1) {
      signStatus = '续签'
    }
    if (this.router.url.indexOf('mgt') > -1) {
      signStatus = '变更'
    }
    if (this.router.url.indexOf('endorse') > -1) {
      signStatus = '转签'
    }
    if (this.router.url.indexOf('change') > -1) {
      signStatus = '变更'
    }

    return signStatus
  }

  onSignOffline2(user) {
    this.uploadFileAction =
      environment.urlPrefix +
      '/api/contract/contractFile/upload?userId=' +
      user.userId +
      `&fileName=${this.getSignStatus()}协议-${user.spell}`
  }

  findCompanyName(code) {
    let company = this.companyOfOption.find((item) => (item as any).code == code) as any
    if (company) return company.name
    else return ''
  }

  onSignOffline(user) {
    this.uploadFileAction =
      environment.urlPrefix +
      '/api/contract/contractFile/upload?userId=' +
      user.userId +
      `&fileName=${this.getSignStatus()}协议-${user.spell}`
    this.uploadFileList = []
    this.userContractFile = ''
    this.userContractFileError = false
    this.warnInfo = ''
    const callback = () => {
      this.signOfflineVisible = true
      this.signOfflineUser = user
      this.signOfflineForm.reset()
      for (const key in this.signOfflineForm.controls) {
        this.signOfflineForm.controls[key].markAsPristine()
        this.signOfflineForm.controls[key].updateValueAndValidity()
      }
    }

    // 合同转签 & 合同变更 需要获取数据
    if (this.type == 'endorse' || this.type == 'change') {
      this.contractService
        .getContractBacklog({
          userId: user.userId
        })
        .subscribe((res) => {
          const { code, msg, data } = res
          if (code === 0) {
            callback()
            this.signOfflineForm.patchValue({
              contractCount: data.contractCount,
              contractDate: [new Date(data.newConBeginDate), new Date(data.newConEndDate)],
              newContract: data.newContract,
              newWorkCity: data.newWorkCity,
              company: this.findCompanyName(data.newContract)
            })
          } else {
            this.messageService.error(msg)
          }
        })
    } else {
      this.contractService
        .getContractInfo({
          userId: user.userId
        })
        .subscribe((res) => {
          const { code, msg, data } = res
          if (code === 0) {
            callback()
            this.signOfflineForm.patchValue({
              contractCount: data.contractCount,
              contractDate: [new Date(data.conBeginDate), new Date(data.conEndDate)],
              company: this.findCompanyName(data.contract)
            })
          } else {
            this.messageService.error(msg)
          }
        })
    }
    this.isSignDisable = this.uploadFileList.length === 0 ? true : false
    this.nowUserName = user.spell
    console.log('userrrr', user)
  }

  handleSignOfflineOk() {
    for (const i in this.signOfflineForm.controls) {
      this.signOfflineForm.controls[i].markAsDirty()
      this.signOfflineForm.controls[i].updateValueAndValidity()
    }

    if ((this.type == 'endorse' || this.type == 'change') && !this.userContractFile) {
      this.userContractFileError = true
      return
    }

    if (!this.signOfflineForm.valid) {
      return
    }

    let signStatus = `合同${this.getSignStatus()}`

    let userContractFile = this.userContractFile
    const postData: any = {
      userId: this.signOfflineUser.userId,
      contractCount: this.signOfflineForm.value.contractCount,
      startDate: this.formattedDate(this.signOfflineForm.value.contractDate[0]),
      endDate: this.formattedDate(this.signOfflineForm.value.contractDate[1]),
      company: this.signOfflineForm.value.company,
      signStatus,
      userContractFile
    }
    if (this.type == 'endorse' || this.type == 'change') {
      postData.newContract = this.signOfflineForm.value.newContract
      postData.newWorkCity = this.signOfflineForm.value.newWorkCity
      postData.company = this.findCompanyName(this.signOfflineForm.value.newContract)

      this.contractService.signOfflineChange(postData).subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          this.messageService.success('已发起线下签署')
          this.signOfflineVisible = false
          this.searchUser()
        } else {
          this.messageService.error(msg)
        }
      })
    } else {
      console.log(2)
      this.contractService.signOffline(postData).subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          this.messageService.success('已发起线下签署')
          this.signOfflineVisible = false
          this.searchUser()
        } else {
          this.messageService.error(msg)
        }
      })
    }
  }

  handleSignOfflineCancel() {
    this.signOfflineVisible = false
  }

  formattedDate(date) {
    if (date) {
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date
        .getDate()
        .toString()
        .padStart(2, '0')}`
    }
    return ''
  }
  beforeUpload = (file: File) => {
    let flag = true
    let isType = false
    if (
      file.type === 'application/x-zip-compressed' ||
      file.type === 'application/zip' ||
      file.type === 'application/pdf' ||
      file.name.split('.')[1] === 'rar'
    ) {
      isType = true
    } else {
      isType = false
    }
    if (!isType) {
      this.messageService.error('只能上传规定类型文件！')
      flag = false
    }
    let maxSize = 1024 * 1024 * 20
    let size = file.size
    if (size > maxSize) {
      this.messageService.error('文件大小不能超过20M！')
      flag = false
    }
    return flag
  }
  // 上传
  handleUploadChange({ file, fileList, event }) {
    console.log('fileeee', file)
    console.log('follelist', fileList)

    const status = file.status
    this.userContractFileError = false
    this.userContractFile = ''
    let nowType = ''
    if (this.type === 'notSign') {
      nowType = '新签合同'
    } else if (this.type === 'extend') {
      nowType = '续签合同'
    } else if (this.type === 'endorse') {
      nowType = '转签合同'
    } else if (this.type === 'change') {
      nowType = '变更合同'
    }
    this.uploadFileList = [...fileList].slice(-1)
    this.uploadFileList[0].name = nowType + '-' + this.nowUserName

    if (status === 'done') {
      const { data, code, msg } = file.response
      if (code === 0) {
        this.userContractFile = data
        this.uploadFileList = [...fileList].slice(-1)
        this.uploadFileList[0].name = nowType + '-' + this.nowUserName
        this.isSignDisable = false
        this.messageService.success(`${this.uploadFileList[0].name} 上传成功`)
      } else {
        this.uploadFileList = []
        this.messageService.error(`${this.uploadFileList[0].name} 上传失败` + msg)
      }
    } else if (status === 'error') {
      this.uploadFileList = []
      this.messageService.error(`${this.uploadFileList[0].name} 上传时报错`)
    }
  }

  handleUploadRemove = (file: File) => {
    this.uploadFileList = []
    this.userContractFile = ''
    this.userContractFileError = false
    this.isSignDisable = true
    return true
  }

  //
  ondownfile(data) {
    let downLoadUrl =
      environment.urlPrefix +
      '/api/contract/contractFile/file?userId=' +
      data.userId +
      '&path=' +
      data.userContractFile
    window.open(downLoadUrl)
    console.log(downLoadUrl)
  }

  // 扫描上传文件部分
  handleListRemove = (file: File) => {
    this.signFileList = []
    this.isOkDisable = true
    return true
  }
  signUpload(contract) {
    this.isUploadVisible = true
    this.titleMessage = ''
    this.userId = contract.userId
    this.uploadId = contract.id
    this.signFileList = []
    this.nowUserName = contract.spell
    this.contractType = contract.signStatus
    this.warnInfo = ''
    this.uploadLoading = false
  }
  handleSignCancel() {
    this.signFileList = []
    this.isUploadVisible = false
  }
  handleSignOk() {
    this.signLoading = true
    this.signLoading = true
    let params = {
      id: this.uploadId,
      // name: this.uploadName
      name: this.signUserContractFile
    }
    if (this.signFileList.length !== 0) {
      this.resignService
        .updateManageFile(params, { userContractFile: this.signUserContractFile })
        .subscribe((res) => {
          this.signLoading = false
          const { code, msg } = res
          if (code === 0) {
            this.messageService.success('提交成功')
            this.isUploadVisible = false
            this.searchUser()
            this.mapOfExpandData = {}
          } else {
            this.messageService.error(msg)
          }
        })
    } else {
      this.messageService.error('请上传必上传文件')
    }
  }
  beforeSignUpload = (file: File) => {
    console.log('fillll', file)
    let flag = true
    let isType = false
    if (
      file.type === 'application/x-zip-compressed' ||
      file.type === 'application/zip' ||
      file.type === 'application/pdf' ||
      file.name.split('.')[1] === 'rar'
    ) {
      isType = true
    } else {
      isType = false
    }
    if (!isType) {
      this.messageService.error('只能上传规定类型文件！')
      flag = false
    }
    let maxSize = 1024 * 1024 * 20
    let size = file.size
    if (size > maxSize) {
      this.messageService.error('文件大小不能超过20M！')
      flag = false
    }
    if (flag) {
      this.uploadLoading = true
      const fileName = this.contractType + '-' + this.nowUserName
      this.uploadName = fileName
      const formData = new FormData()
      const data = {
        name: fileName,
        id: this.userId
      }
      formData.append('file', file)
      this.resignService.uploadFile(data, formData).subscribe((res) => {
        this.uploadLoading = false
        this.signFileList = [
          {
            name: fileName,
            url: URL.createObjectURL(file),
            status: 'done'
          }
        ]
        const { code, msg, data } = res
        if (code === 0) {
          this.signUserContractFile = data
          this.messageService.success('上传成功')
          this.isOkDisable = false
        } else {
          this.messageService.error(msg)
        }
      })
    }
    return false
  }
  editUpload(contract, data) {
    this.isOkDisable = false
    this.isUploadVisible = true
    this.titleMessage = '温馨提示：请注意重新上传的文件会覆盖之前文件哦~'
    this.uploadId = contract.id
    this.userId = contract.userId
    this.nowUserName = contract.spell
    this.contractType = contract.signStatus
    this.warnInfo = ''
    ;[
      {
        key: 'userContractFile',
        fileListKey: 'signFileList'
      }
    ].forEach((item: { key: string; fileListKey: string }) => {
      if (contract[item.key]) {
        this[item.key] = contract[item.key]
        this[item.fileListKey] = [
          {
            name:
              contract.signStatus +
              '-' +
              contract.spell +
              '.' +
              contract.userContractFile.split('.')[1],
            url: `/api/contract/contractFile/upload?userId=${contract['userId']}&fileName=${
              contract[item.key]
            }`,
            status: 'done'
          }
        ]
      } else {
        this[item.key] = null
        this[item.fileListKey] = []
      }
    })
    console.log('dattaaa', contract)
  }
  handlePreview() {
    return false
  }
  // 合同管理页面导出功能
  downloadOut() {
    let requestBody = {
      startExpectDate: !!this.entryDateRange[0] && formatDate(this.entryDateRange[0]),
      endExpectDate: !!this.entryDateRange[1] && formatDate(this.entryDateRange[1]),
      dismissionDateFrom:
        !!this.leaveDateRange[0] && formatDate(this.leaveDateRange[0]).split(' ')[0],
      dismissionDateTo:
        !!this.leaveDateRange[1] && formatDate(this.leaveDateRange[1]).split(' ')[0],
      workNumber: this.staffid,
      userId: this.id,
      spell: this.username,
      teamId: this.team,
      workBase: this.entryLocation,
      employType: this.entryType,
      entryFlow: this.status,
      contractStatus: this.tempContractStatus,
      pageSize: this.pageSize,
      status: '0'
    }
    // console.log('jjjjj', JSON.stringify(requestBody))

    // this.contractService.uploadOutput().subscribe((res) => {
    //   console.log('resss', res)
    // })
    let str = JSON.stringify(this.creatData.creatData(requestBody))
    let downLoadUrl = environment.urlPrefix + '/api/user/exportContract?json=' + encodeURI(str)
    window.open(downLoadUrl)
    console.log(downLoadUrl)
  }
  //新增协议
  addAgree(contract, data, action) {
    this.isAgreeVisible = true
    this.addAgreeFormData.addSpell = data.spell
    this.addAgreeFormData.workNumber = data.workNumber
    this.addAgreeFormData.type = '自定义协议'
    this.addAgreeFormData.userId = data.userId

    this.addAgreeFormData.action = action
    this.agreeLoading = false
    this.agreeArr = ['离职相关文件', '证明材料', '违纪通知书']
    this.themeValue = ''
    if (action === 'add') {
      this.agreeTitle = '新增协议'
      this.addAgreeFormData['userContractFile'] = ''
      this.agreeFileList = []
      this.addCompany = ''
      this.addAgreeFormData.id = data.id
    } else {
      this.agreeTitle = '编辑协议'
      // this.addAgreeFormData.company = contract.company
      this.addAgreeFormData.id = contract.id
      this.addAgreeFormData.startDate = contract.startDate
      this.addAgreeFormData.endDate = contract.endDate
      this.addAgreeFormData.contractNumber = contract.contractNumber
      if (contract.contractTitle && !this.agreeArr.includes(contract.contractTitle)) {
        this.agreeArr.push(contract.contractTitle)
      }
      if (contract.company && !this.companyOptions.includes(contract.company)) {
        this.companyOptions.push(contract.company)
      }
      this.addCompany = contract.company
      this.addAgreeTheme = contract.contractTitle
      if (contract['userContractFile']) {
        this.addAgreeFormData['userContractFile'] = contract['userContractFile']
        this.agreeFileList = [
          {
            name:
              contract.signStatus +
              '-' +
              contract.spell +
              '.' +
              contract.userContractFile.split('.')[1],
            url: `/api/contract/contractFile/upload?userId=${contract['userId']}&fileName=${contract['userContractFile']}`,
            status: 'done'
          }
        ]
      } else {
        this.addAgreeFormData['userContractFile'] = ''
        this.agreeFileList = []
      }
    }
  }
  handleAgreeCancel(form) {
    this.isAgreeVisible = false
    form.onReset()
    console.log('addd', this.addCompany)
  }
  handleAgreeOk(form) {
    this.agreeLoading = true
    let param = {
      subject: this.addAgreeTheme,
      company: this.addCompany,
      startDate: this.addAgreeFormData.startDate
        ? formatDate(new Date(this.addAgreeFormData.startDate)).split(' ')[0]
        : '',
      endDate: this.addAgreeFormData.endDate
        ? formatDate(new Date(this.addAgreeFormData.endDate)).split(' ')[0]
        : '',
      userContractFile: this.addAgreeFormData.userContractFile
    }
    let result
    if (this.addAgreeFormData.action === 'add') {
      result = this.contractService.submitAddAgree({
        ...param,
        userId: this.addAgreeFormData.userId,
        spell: this.addAgreeFormData.addSpell,
        workNumber: this.addAgreeFormData.workNumber,
        signStatus: this.addAgreeFormData.type
      })
    } else {
      result = this.contractService.submitEditSelfAgree({
        ...param,
        id: this.addAgreeFormData.id
      })
    }
    result.subscribe((res) => {
      this.agreeLoading = false
      const { code, msg } = res
      if (code === 0) {
        this.messageService.success('操作成功!')
        this.searchUser()
        this.mapOfExpandData = {}
        this.isAgreeVisible = false
        form.onReset()
      } else {
        this.messageService.error(msg)
      }
    })
  }
  // 编辑协议
  editAgreement(contract, data) {
    this.editAgreeVisible = true
    this.editAgreeLoading = false

    this.editAgreeForm = {
      agreeName: data.spell,
      agreeNumber: data.workNumber,
      contractNumber: contract.contractNumber,
      agreeType: contract.signStatus,
      agreeTheme: contract.contractTitle,
      agreeCompany: contract.company,
      startDate: contract.startDate,
      endDate: contract.endDate,
      id: contract.id,
      userId: contract.userId
    }
  }
  handleEditAgreeCancel() {
    this.editAgreeVisible = false
  }
  handleEditAgreeOk() {
    this.editAgreeLoading = true
    this.contractService
      .submitEditAgree({
        id: this.editAgreeForm.id,
        startDate: this.editAgreeForm.startDate
          ? formatDate(new Date(this.editAgreeForm.startDate)).split(' ')[0]
          : '',
        endDate: this.editAgreeForm.endDate
          ? formatDate(new Date(this.editAgreeForm.endDate)).split(' ')[0]
          : ''
      })
      .subscribe((res) => {
        this.editAgreeLoading = false
        const { code, msg, data } = res
        if (code === 0) {
          this.messageService.success('编辑成功！')
          this.getContractList(this.editAgreeForm)
          this.searchUser()
          this.mapOfExpandData = {}
          this.editAgreeVisible = false
        } else {
          this.messageService.error(msg)
        }
      })
  }
  addItem() {
    if (this.themeValue.trim() && !this.agreeArr.includes(this.themeValue)) {
      this.agreeArr.push(this.themeValue)
    }
  }
  handleAgreeListRemove = (file: File) => {
    this.agreeFileList = []
    this.addAgreeFormData.userContractFile = ''
    this.contractService
      .deleteAgree({
        id: this.addAgreeFormData.id,
        path: file.name.split('.')[0]
      })
      .subscribe((res) => {
        const { code, msg } = res
        if (code === 0) {
          this.messageService.success('删除成功！')
        } else {
          this.messageService.error(msg)
        }
      })
    return true
  }
  beforeAgreeUpload = (file: File) => {
    let flag = this.beforeUpload(file)
    if (flag) {
      this.agreeUploadLoading = true
      const fileName = '自定义系协议-' + this.addAgreeFormData.addSpell

      const formData = new FormData()
      const data = {
        name: fileName,
        id: this.addAgreeFormData.userId
      }
      formData.append('file', file)
      this.contractService.uploadAgreeFile(data, formData).subscribe((res) => {
        this.agreeFileList = [
          {
            name: fileName,
            url: URL.createObjectURL(file),
            status: 'done'
          }
        ]
        this.agreeUploadLoading = false
        const { code, msg, data } = res
        if (code === 0) {
          this.addAgreeFormData.userContractFile = data
          this.messageService.success('上传成功')
        } else {
          this.messageService.error(msg)
        }
      })
    }
    return false
  }
  previewAgree = (file: File) => {
    this.ondownfile({
      userId: this.addAgreeFormData.userId,
      userContractFile: this.addAgreeFormData.userContractFile
    })
    console.log(this.addAgreeFormData.userId)
    return false
  }
  selectChange(e) {
    this.addAgreeTheme = e
  }
  selectCompanyChange(e) {
    this.addCompany = e
  }
}
