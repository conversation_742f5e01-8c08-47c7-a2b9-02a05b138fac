package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.AliMailClient;
import com.megvii.client.Office365Client;
import com.megvii.common.FileUtil;
import com.megvii.entity.opdb.ImageFile;
import com.megvii.entity.opdb.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@PropertySource(value = "classpath:office365.properties", encoding = "UTF-8")
@PropertySource(value = "classpath:mail.properties", encoding = "UTF-8")
public class Office365Service {
	Logger logger = LoggerFactory.getLogger(Office365Service.class);

	private final static String MAIL_SUFFIX_MEGVII = "@megvii.com";
	private final static String MAIL_SUFFIX_MEGVII_INC = "@megvii-inc.com";


	@Value("${OFFICE_LICENSE}")
	private String officeLicense;

	@Value("${SYNC_OFFICE_GROUP_VISIBILITY_MAIL}")
	private String mailAddress;

	@Autowired
	private Office365Client office365Client;

	@Autowired
	private LdapService ldapService;

	@Autowired
	private GroupService groupService;

	@Autowired
	private MailSendService mailSendService;

	@Autowired
	private EntryService entryService;

	@Autowired
	AliMailClient aliMailClient;

	@Autowired
	AliGroupService aliGroupService;

	/**
	 * 单独添加1个组的方法
	 *
	 * @param userId
	 * @param group
	 * @return
	 */
	public String addUser2Group(String userId, String group) {
		try {
			JSONObject userInfo = office365Client.getUserOfficeInfo(userId + MAIL_SUFFIX_MEGVII);
			if (userInfo == null) {
				String message = "office365查询不到" + userId + "信息";
				logger.info(message);
				return message;
			}
			String userGraphId = userInfo.getString("id");

			Map<String, JSONObject> officeGroupMap = groupService.getAllOfficeMap();
			JSONObject data = officeGroupMap.get(group);

			office365Client.addUserGroup(userGraphId, data.getString("id"));

			return userId + "添加组" + group + "成功</br>";
		} catch (Exception e) {
			logger.error(userId + "添加组" + group + "异常");
			return userId + "添加组" + group + "失败</br>";
		}
	}


	/**
	 * 解除授权
	 * 禁用邮箱账号
	 * 先解除授权再禁用 否则404
	 *
	 * @param userId
	 * @return
	 */
	public String disableAccount(String userId) {
		StringBuilder msg = new StringBuilder();

		try {
			List<String> licenses = office365Client.getLicense(userId + MAIL_SUFFIX_MEGVII);
			if (licenses.size() == 0) {
				msg.append(userId + "未查到邮箱授权");
				return msg.toString();
			}

			for (String licens : licenses) {
				msg.append(office365Client.delAssign(userId + MAIL_SUFFIX_MEGVII, licens) ? "邮箱解除授权成功" : "邮箱解除授权失败");
			}

			msg.append(office365Client.modifyAccountEnabled(userId + MAIL_SUFFIX_MEGVII, false) ? userId + "修改邮箱状态成功--" : userId + "修改邮箱状态失败--");

		} catch (Exception e) {
			logger.error(userId + "邮箱授权解除异常******" + e);
		}
		return msg.toString();
	}


	/**
	 * 用户离职删除office365的所有组 (保留ldap逻辑，替换原office365逻辑)
	 * ldap office365
	 *
	 * @param userId
	 * @return
	 */
	public String disableInternalAccounts(String userId) {
		StringBuilder msg = new StringBuilder();
		msg.append(userId);

//		String userGraphId;
//		try {
//			JSONObject userInfo = office365Client.getUserOfficeInfo(userId + MAIL_SUFFIX_MEGVII);
//			userGraphId = userInfo.getString("id");
//		} catch (Exception e) {
//			msg.append("删除组获取用户异常:" + e.getMessage());
//			logger.error(msg.toString());
//			return msg.toString();
//		}
		JSONArray groups = aliMailClient.getMailGroupsByMember(userId + MAIL_SUFFIX_MEGVII);
		if (groups == null) {
			msg.append("getMailGroupsByMember获取用户组异常");
			logger.error(msg.toString());
			return msg.toString();
		}
		//遍历groups
		for (int i = 0; i < groups.size(); i++) {
			String group = groups.getString(i);
			String groupName = group.substring(0, group.indexOf("@"));
			try {
				ldapService.deleteGroupUser(userId, groupName);
				msg.append("删除ldap组:" + groupName + "--");
			} catch (Exception e) {
				msg.append("删除组" + groupName + "异常:" + e.getMessage());
				continue;
			}
		}

//		Map<String, JSONObject> groups = office365Client.getUserGroups(userId + MAIL_SUFFIX_MEGVII);
//		for (Map.Entry<String, JSONObject> entry : groups.entrySet()) {
//			try {
//				ldapService.deleteGroupUser(userId, entry.getKey());
//				msg.append("删除ldap组:" + entry.getKey() + "--");
//				if (groupService.selectAllSecureGroup().indexOf(entry.getKey()) == -1) {
//					JSONObject data = entry.getValue();
//					if (office365Client.deleteUserGroup(userGraphId, data.getString("id"))) {
//						msg.append("删除office组:" + entry.getKey() + "【成功】");
//					} else {
//						msg.append("删除office组:" + entry.getKey() + "【失败】");
//					}
//				}
//			} catch (Exception e) {
//				msg.append("删除组" + entry.getKey() + "异常:" + e.getMessage());
//				continue;
//			}
//		}

		return msg.toString();
	}


	/**
	 * 同步office365组的visibility属性 统一刷为private（已废弃）
	 *
	 * @return
	 */
	public String syncOfficeVisibility() {
		StringBuilder msg = new StringBuilder();

		JSONArray groups;
		try {
			groups = groupService.getAllOfficeArray();
		} catch (UnsupportedEncodingException e) {
			msg.append("获取office365组异常");
			logger.error(msg.toString());
			return msg.toString();
		}

		List<String> skipGroups = groupService.getSkipGroups();
		for (Object o : groups) {
			JSONObject group = (JSONObject) o;

			String name = group.getString("mail").split("@")[0];

			if (skipGroups.contains(name)) {
				continue;
			}

			String visibility = group.getString("visibility");
			if (group.getJSONArray("groupTypes").size() == 0 || (visibility != null && visibility.equals("Private"))) {
				continue;
			}

			msg.append(office365Client.updateGroupVisibility(name, group.getString("id"), "Private") + "</br>");
		}

		if (msg.toString().equals("")) {
			msg.append("没有需要修改属性的组");
		} else {
			mailSendService.sendMail("office365修改visibility提醒", msg.toString(), mailAddress);
		}

		return msg.toString();
	}


	/**
	 * 删除人员邮件组 ldap组 ga关系 (替换阿里mail)
	 *
	 * @param userId
	 * @param group
	 * @return
	 * @throws Exception
	 */
	public boolean removeUserGroup(String userId, List<String> group) throws Exception {
		if (userId.indexOf("t-") != -1) {
			return true;
		}

//		JSONObject userInfo = office365Client.getUserOfficeInfo(userId + MAIL_SUFFIX_MEGVII);
//		String userGraphId = userInfo.getString("id");

		Map<String, JSONObject> officeGroupMap = aliGroupService.getAllOfficeMap();
		for (String s : group) {
			JSONObject data = officeGroupMap.get(s);
			if (data == null) {
				continue;
			}
//			try {
//				office365Client.deleteUserGroup(userGraphId, data.getString("id"));
//			} catch (Exception e) {
//				logger.error(userId + "删除office365组:" + s + "错误");
//			}
			List<String> members = new ArrayList<>();
			members.add(userId + MAIL_SUFFIX_MEGVII);
			if (!aliMailClient.removeMailGroupMembers(s+MAIL_SUFFIX_MEGVII, members)){
				logger.info(userId + "删除阿里mail组:" + s + "错误");
			}
			ldapService.deleteGroupUser(userId, s);
		}
		return true;
	}

	/**
	 * 添加用户邮件组(替换阿里mail)
	 * 1.阿里mail
	 * 2.ldap
	 * 3.nsq
	 *
	 * @param user
	 * @return
	 */
	public String addUserGroup(User user) {
		try {
			List<String> groupList = groupService.getGroupList(user);
			groupList.removeAll(groupService.selectAllSecureGroup());

//			JSONObject userInfo = office365Client.getUserOfficeInfo(user.getUserId() + MAIL_SUFFIX_MEGVII);

			if (!aliMailClient.getAccountsInfo(user.getUserId() + MAIL_SUFFIX_MEGVII)) {
				String message = "阿里mail查询不到" + user.getUserId() + "信息";
				logger.info(message);
				return message;
			}
//			String userGraphId = userInfo.getString("id");

			Map<String, JSONObject> officeGroupMap = aliGroupService.getAllOfficeMap();

			for (String s : groupList) {
				try {
					JSONObject data = officeGroupMap.get(s);
					if (data == null) {
						continue;
					}

//					office365Client.addUserGroup(userGraphId, data.getString("id"));
					List<String> members = new ArrayList<>();
					members.add(user.getUserId() + MAIL_SUFFIX_MEGVII);
					aliMailClient.addMailGroupMembers(data.getString("address"), members);
					String userDn = ldapService.getRealUserDn(user.getUserId());

					ldapService.addMemberToGroupByUserDn(s, userDn);
//                    nsqProduceClient.nsqGroupAdd(user.getUserId(), s);
				} catch (Exception e) {
					logger.info(user.getUserId() + "添加组:" + s + "失败", e);
				}
			}
			return user.getUserId() + "添加阿里mail-group成功";
//		} catch (TimeoutException e) {
//			logger.error("nsq生产超时:", e);
//			return user.getUserId() + "存在失败:nsq生产超时";
//		} catch (NSQException e) {
//			logger.error("nsq生产错误:", e);
//			return user.getUserId() + "存在失败:nsq生产错误";
		} catch (Exception e) {
			logger.error("从office365获取group错误:", e);
			return user.getUserId() + "存在失败:从office365获取group错误";
		}
	}

	/**
	 * 添加用户邮件组(替换阿里mail)
	 * 1.阿里mail
	 * 2.ldap
	 * 3.nsq
	 *
	 * @param user
	 * @return
	 */
	public String addUserGroupWithSecureGroup(User user) {
		try {
			List<String> groupList = groupService.getGroupList(user);
//			groupList.removeAll(groupService.selectAllSecureGroup());

//			JSONObject userInfo = office365Client.getUserOfficeInfo(user.getUserId() + MAIL_SUFFIX_MEGVII);

			if (!aliMailClient.getAccountsInfo(user.getUserId() + MAIL_SUFFIX_MEGVII)) {
				String message = "阿里mail查询不到" + user.getUserId() + "信息";
				logger.info(message);
				return message;
			}
//			String userGraphId = userInfo.getString("id");

			Map<String, JSONObject> officeGroupMap = aliGroupService.getAllOfficeMap();

			for (String s : groupList) {
				try {
					JSONObject data = officeGroupMap.get(s);
					if (data == null) {
						continue;
					}

//					office365Client.addUserGroup(userGraphId, data.getString("id"));
					List<String> members = new ArrayList<>();
					members.add(user.getUserId() + MAIL_SUFFIX_MEGVII);
					aliMailClient.addMailGroupMembers(data.getString("address"), members);
					String userDn = ldapService.getRealUserDn(user.getUserId());

					ldapService.addMemberToGroupByUserDn(s, userDn);
//                    nsqProduceClient.nsqGroupAdd(user.getUserId(), s);
				} catch (Exception e) {
					logger.info(user.getUserId() + "添加组:" + s + "失败", e);
				}
			}
			return user.getUserId() + "添加阿里mail-group成功";
//		} catch (TimeoutException e) {
//			logger.error("nsq生产超时:", e);
//			return user.getUserId() + "存在失败:nsq生产超时";
//		} catch (NSQException e) {
//			logger.error("nsq生产错误:", e);
//			return user.getUserId() + "存在失败:nsq生产错误";
		} catch (Exception e) {
			logger.error("从office365获取group错误:", e);
			return user.getUserId() + "存在失败:从office365获取group错误";
		}
	}

	/**
	 * 修改location 后缀（已废弃）
	 * 修改状态
	 * 激活
	 *
	 * @param user
	 * @return
	 */
	public String addEmailAcount(User user) {
		String email = null;
		try {
			email = user.getUserId() + MAIL_SUFFIX_MEGVII_INC;
			String result = checkAssign(email);
			if (result != null) {
				return result;
			}
		} catch (Exception e) {
			try {
				email = user.getUserId() + MAIL_SUFFIX_MEGVII;
				String result = checkAssign(email);
				if (result != null) {
					return result;
				}
			} catch (HttpClientErrorException e1) {
				if (e1.getRawStatusCode() == 404) {
					logger.info(user.getUserId() + "没有office365账号", e1);
					return user.getUserId() + "没有office365账号";
				} else {
					logger.error(user.getUserId() + e1.getStatusCode() + e1.getStatusText());
					return user.getUserId() + e1.getStatusCode() + e1.getStatusText();
				}
			}
		}

		modifyLocation(user, email);
		email = user.getUserId() + MAIL_SUFFIX_MEGVII;

		office365Client.modifyAccountEnabled(email, true);
		boolean result = office365Client.assignAccount(email);

		if (result) {
			return user.getUserId() + "激活office365成功";
		} else {
			return user.getUserId() + "激活office365失败";
		}

	}

	/**
	 * 上传头像（已废弃）
	 * 1.查询office365用户
	 * 2.检查头像是否存在
	 * 3.查询阿里云头像信息
	 * 4.更新头像
	 *
	 * @param user
	 * @return
	 */
	public String updateUserProfile(User user) {
		JSONObject data = null;
		try {
			data = office365Client.getUserOfficeInfo(user.getUserId() + MAIL_SUFFIX_MEGVII);
		} catch (Exception e) {
			logger.error(user.getUserId() + "获取office365信息错误" + e.getMessage());
			return user.getUserId() + "获取office365信息错误";
		}

		if (data == null) {
			return user.getUserId() + "没有查询到office365用户信息";
		}

		boolean isExist = office365Client.isExistPhotoByUserId(user.getUserId(), data.get("id").toString());
		if (isExist) {
			return user.getUserId() + "已有头像或添加错误";
		}

		String photoCert = entryService.getPhotoCert(user.getUserId());

		if (photoCert == null) {
			return user.getUserId() + "查不到阿里云证件照信息";
		}

		ImageFile photoCertFile = FileUtil.getByteFromBase64(photoCert, user.getUserId());
		if (photoCertFile == null) {
			return user.getUserId() + "证件照转换错误";
		}
		String result = office365Client.updateUserProfile(user.getUserId(), data.get("id").toString(), photoCertFile.getFileBytes());

		return result;
	}

	/**
	 * 检查是否授权，邮箱不存在抛出异常（已废弃）
	 *
	 * @param email
	 * @return
	 * @throws Exception
	 */
	private String checkAssign(String email) throws HttpClientErrorException {
		String str = "?$select=licenseAssignmentStates";
		JSONObject jsonObject = office365Client.getUserOfficeInfoBySelect(email, str);
		if (jsonObject.getJSONArray("licenseAssignmentStates").size() == 0) {
			return null;
		}
		JSONObject value = (JSONObject) jsonObject.getJSONArray("licenseAssignmentStates").get(0);
		String skuId = value.getString("skuId");
		if (skuId.equals(officeLicense)) {
			return email + "已经激活office365";
		} else {
			return null;
		}
	}


	/**
	 * 根据入职地点判断location 目前没有在国外入职的部门 邮箱授权使用 CN（已废弃）
	 *
	 * @param user
	 * @param email
	 */
	private void modifyLocation(User user, String email) {
		String newEmail = user.getUserId() + MAIL_SUFFIX_MEGVII;
//        boolean base = (user.getEmployBase() != null && user.getEmployBase().equals("西雅图"))
//                || (user.getTeam() != null && user.getTeam().indexOf("西雅图")!=-1);
//
//        String location;
//        if (base) {
//            location = "US";
//        } else {
//            location = "CN";
//        }
		office365Client.modifyLocation(email, "CN", newEmail);
	}


}
