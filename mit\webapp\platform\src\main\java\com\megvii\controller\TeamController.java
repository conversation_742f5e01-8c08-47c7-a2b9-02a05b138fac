package com.megvii.controller;

import com.megvii.controller.api.TeamVO;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.Team;
import com.megvii.service.TeamService;
import java.net.URLDecoder;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/4/16 16:10
 */
@RestController
@RequestMapping("/api/team")
public class TeamController {

	@Autowired
	private TeamService	teamService;

	@GetMapping("/search")
	@ResponseBody
	public ResponseDataEntity getTeamByName(@RequestParam("teamName")String name) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			List<Team> teams = teamService.getTeamsByName(URLDecoder.decode(name, "utf-8"));
			response.setData(TeamVO.toVOs(teams));
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("failure");
		}
		return response;
	}




}
