package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service
public class DhrProveContractBacklogOperatorService extends BaseOperatorService{
    @Autowired
    DhrProveContractBacklogOperatorService dhrProveContractBacklogOperatorService;

    @Autowired
    DhrService dhrService;


    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        return dhrProveContractBacklogOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        String msg = dhrService.getProveContractBacklogFromDhr();
        return new OperateResult(1,msg);
    }
}
