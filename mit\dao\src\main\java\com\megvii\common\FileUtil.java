package com.megvii.common;

import com.megvii.entity.opdb.ImageFile;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2019/8/27 14:34
 */
public class FileUtil {

    /**
     * 将base64字符串解析为字节数组
     * @param s
     * @return
     */
    public static ImageFile getByteFromBase64(String s, String userId){
        if (s==null||"".equals(s)){
            return null;
        }
        String fimleName=userId+"."+s.substring(s.indexOf("/")+1,s.indexOf(";"));
        s=s.replaceFirst("data:image/.*?;base64,","");
        ImageFile imageFile=new ImageFile();
        imageFile.setFileBytes(Base64.getDecoder().decode(s));
        imageFile.setFileName(fimleName);
        return imageFile;

    }

    /**
     * 保存文件到指定路径
     * @param inputStream
     * @param path
     * @param fileName
     * @return
     */
    public static boolean saveFile(FileInputStream inputStream, String path, String fileName) {

        OutputStream os = null;
        try {
            byte[] bs = new byte[1024];
            int len;

            File tempFile = new File(path);
            if (!tempFile.exists()) {
                tempFile.mkdirs();
            }
            os = new FileOutputStream(tempFile.getPath() + File.separator + fileName);
            // 开始读取
            while ((len = inputStream.read(bs)) != -1) {
                os.write(bs, 0, len);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            // 完毕，关闭所有链接
            try {
                os.close();
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    /**
     * 删除文件
     * @param pathname
     * @return
     */
    public static boolean deleteFile(String pathname) {
        boolean result = false;
        File file = new File(pathname);
        if (file.exists()) {
            file.delete();
            result = true;
        }
        return result;
    }

    /**
     * 从url下载文件保存到指定路径
     * @param urlStr         url
     * @param fileName       下载到本地的文件名
     * @param savePath		 下载路径
     * @throws IOException    抛出异常
     */
    public static String  downLoadByUrl(String urlStr,String fileName,String savePath) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection)url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(5*1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        //得到输入流
        InputStream inputStream = conn.getInputStream();
        //获取自己数组
        byte[] getData = readInputStream(inputStream);
        //文件保存位置
        File saveDir = new File(savePath);
        if(!saveDir.exists()){
            saveDir.mkdir();
        }
        File file = new File(saveDir+File.separator+fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(getData);
        if(fos!=null){
            fos.close();
        }
        if(inputStream!=null){
            inputStream.close();
        }
        System.out.println("info:"+url+" download success");

        return file.getPath();

    }
    /**
     * 从输入流中获取字节数组
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static  byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }




}
