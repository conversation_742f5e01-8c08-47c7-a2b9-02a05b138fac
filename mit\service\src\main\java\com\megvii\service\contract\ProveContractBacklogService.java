package com.megvii.service.contract;

import com.megvii.entity.opdb.contract.*;
import com.megvii.mapper.UserProveContactBacklogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties", encoding = "UTF-8")
public class ProveContractBacklogService {
    private Logger logger = LoggerFactory.getLogger(ProveContractBacklogService.class);

    @Autowired
    private UserProveContactBacklogMapper userProveContactBacklogMapper;

    /**
     * 创建新证明类合同
     * @param UserProveContractBacklog
     * @return
     */
    public boolean addProveContractBacklog(UserProveContractBacklog UserProveContractBacklog) {
        if (userProveContactBacklogMapper.insertProveLog(UserProveContractBacklog) > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据dhrid更新证明类合同状态
     * @param UserProveContractBacklog
     * @return
     */
    public boolean updateProveContractBacklogByDhrId(UserProveContractBacklog UserProveContractBacklog) {
        if (userProveContactBacklogMapper.updateUserProveContractBacklog(UserProveContractBacklog) > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 拉取全部未完成的证明类合同
     * @return
     */
    public List<UserProveContractBacklog> getProveContractBacklogByStatus(String BacklogStatus) {
        return  userProveContactBacklogMapper.selectProveLogByStatus(BacklogStatus);
    }

/**
     * 根据contractId拉取证明类合同
     * @param contractId
     * @return
     */
    public UserProveContractBacklog getProveContractBacklogByContractId(String contractId) {
        return userProveContactBacklogMapper.selectProveLogByContractId(contractId);
    }

/**
     * 根据dhrid拉取证明类合同
     * @param dhrId
     * @return
     */
    public UserProveContractBacklog getProveContractBacklogByDhrId(String dhrId) {
        return userProveContactBacklogMapper.selectProveLogByDhrId(dhrId);
    }





}
