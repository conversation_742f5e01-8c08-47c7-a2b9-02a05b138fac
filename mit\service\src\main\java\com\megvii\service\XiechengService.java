package com.megvii.service;

import com.megvii.client.XiechengRestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/8/28 18:03
 */
@Service
@PropertySource(value = "classpath:xiecheng.${spring.profiles.active}.properties",encoding = "UTF-8")
public class XiechengService {

    private Logger logger= LoggerFactory.getLogger(XiechengService.class);

    @Value("${XIECHENG_APPKEY}")
    private String appKey;

    @Value("${XIECHENG_SUB_ACCOUNT_NAME}")
    private String subAccountName;

    @Value("${XIECHENG_CORPORATION_ID}")
    private String corporationId;

    @Autowired
    private XiechengRestClient client;

    @Autowired
    private TeamService teamService;

//    /**
//     * 同步携程人员信息
//     * @param user
//     * @param isValid 是否在职 入职任务传true 离职任务传false
//     * @return
//     */
//    public String syncXiechengInfo(User user, boolean isValid) {
//        if (user.getCreateTag() !=null && (user.getCreateTag() == 1 || user.getCreateTag() == 3)){
//            return user.getUserId()+"create_tag为"+user.getCreateTag()+",不更新携程账号信息。";
//        }
//
//        if (user.getCompany() == null || !user.getCompany().equals("3071")) {
//            return user.getUserId() + "公司不是北京旷视科技,不添加携程账号。";
//        }
//
//        String token = null;
//        try {
//            token = client.getXichengToken();
//        } catch (Exception e) {
//            return user.getUserId() + e.getMessage();
//        }
//
//        AuthenticationListRequst requst = null;
//        try {
//            requst = buildAuthenticationListRequst(token, user, isValid);
//        } catch (Exception e) {
//            return e.getMessage();
//        }
//
//        return client.multipleEmployeeSync(user.getUserId(), requst);
//    }
//
//
//    /**
//     * 创建携程request
//     * valid 在职A 离职I
//     * rank  固定传T3
//     * dept1 部门
//     * subAccountName 子账号
//     * @param ticket 携程token
//     * @param user   user信息
//     * @return
//     */
//    private AuthenticationListRequst buildAuthenticationListRequst(String ticket, User user, boolean isValid) throws Exception {
//        AuthenticationListRequst authenticationListRequst=new AuthenticationListRequst();
//
//        authenticationListRequst.setTicket(ticket);
//        authenticationListRequst.setCorporationID(corporationId);
//
//        Authentication authencationEntity=new Authentication();
//
//        authencationEntity.setEmployeeID(user.getUserId());
//        authencationEntity.setName(user.getSpell());
//        if (isValid) {
//            authencationEntity.setValid("A");
//        } else {
//            authencationEntity.setValid("I");
//        }
//        authencationEntity.setRank("T3");
//
//        if (user.getTeamId()!=null){
//            Team team=teamService.getTeamById(user.getTeamId());
//            if (team != null && team.getSfCode() != null){
//                authencationEntity.setDept1(team.getSfCode());
//            } else {
//                logger.error(user.getUserId() + "teamId查不到部门或部门SF_Code为null");
//                throw new Exception(user.getUserId() + "teamId查不到部门或部门SF_Code为null");
//            }
//        } else {
//            logger.error(user.getUserId() + "teamId为null");
//            throw new Exception(user.getUserId() + "teamId为null");
//        }
//
//        authencationEntity.setSubAccountName(subAccountName);
//        authencationEntity.setUseTRFlag(31);
//
//        List<AuthenticationInfoList> authenticationInfoLists=new ArrayList<>();
//
//        AuthenticationInfoList authenticationInfoList=new AuthenticationInfoList();
//        authenticationInfoList.setAuthentication(authencationEntity);
//        authenticationInfoList.setSequence("0");
//        authenticationInfoLists.add(authenticationInfoList);
//        authenticationListRequst.setAppkey(appKey);
//        authenticationListRequst.setAuthenticationInfoList(authenticationInfoLists);
//
//        return authenticationListRequst;
//    }

}
