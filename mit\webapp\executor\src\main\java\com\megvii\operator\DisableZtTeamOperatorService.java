package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.service.SyncZTDataService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/25 14:31
 */
@Service
public class DisableZtTeamOperatorService extends BaseOperatorService {

    @Autowired
    private SyncZTDataService syncZTDataService;

    @Autowired
    private DisableZtTeamOperatorService disableZtTeamOperatorService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return disableZtTeamOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = syncZTDataService.disableTeam();
        return new OperateResult(1,msg);
    }

}
