package com.megvii.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.megvii.common.DateTimeUtil;
import com.megvii.service.MMISMegviiConfigService;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.net.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
@PropertySource(value = "classpath:sf.${spring.profiles.active}.properties",encoding = "UTF-8")
public class SfRestClient {

    Logger logger=LoggerFactory.getLogger(SfRestClient.class);

    @Value("${SF_ADMIN}")
    private String sfAdmin;

    @Value("${SF_PWD}")
    private String sfPsw;

    @Value("${SF_USER_URL}")
    private String sfUserUrl;

    @Value("${SF_UPSERT_URL}")
    private String sfUpsertUrl;

    @Value("${SF_EMP_URL}")
    private String sfEmpUrl;

    @Value("${SF_SUBTEAM_URL}")
    private String sfSubTeamUrl;

    @Value("${SF_TEAM_URL}")
    private String sfTeamUrl;

    @Value("${SF_GROUP_URL}")
    private String sfGroupUrl;

    @Value("${SF_BU_URL}")
    private String sfBuUrl;

    @Value("${SF_SECTION_URL}")
    private String sfSectionUrl;

    @Value("${SF_RELATIONSHIPS_URL}")
    private String sfRelationshipsUrl;

    @Value("${SF_LOCATION_URL}")
    private String sfLocationUrl;

    @Value("${SF_DISMISS_URL}")
    private String sfDismissUrl;

    @Value("#{${SF_EMP_SOURCE}}")
    private Map<String,String> empSourceMap;

    @Value("#{${SF_DUTY}}")
    private Map<String,String> dutyMap;

    @Value("#{${SF_EMP_TYPE}}")
    private Map<String,String> empTypeMap;

    @Value("#{${SF_REFER_TITLE}}")
    private Map<String,String> referTitleMap;

    @Value("#{${SF_PERSONAL_INFO}}")
    private Map<String,String>  personalInfoMap;

    @Value("#{${SF_CONTACT_RELATION}}")
    private Map<String,String> contactRelationMap;

    @Value("#{${SF_BUSINESS_TYPE}}")
    private Map<String,String> businessTypeMap;

    @Value("#{${SF_EDUCATION_TYPE}}")
    private Map<String,String> educationTypeMap;

    @Value("${SF_BUDDY_TYPE}")
    private String SF_BUDDY_TYPE;

    @Value("${SF_HRG_TYPE}")
    private String SF_HRG_TYPE;

    @Value("${SF_SITE_HRG_TYPE}")
    private String SF_SITE_HRG_TYPE;

    @Value("${SF_LEADER_TYPE}")
    private String SF_LEADER_TYPE;

    @Value("${SF_EMAIL_TYPE}")
    private String SF_EMAIL_TYPE;

    @Value("${SF_PHONE_TYPE}")
    private String SF_PHONE_TYPE;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private MMISMegviiConfigService mmisMegviiConfigService;

    /**
     * 修改SF邮箱信息
     * @param workNumber
     * @param userId
     * @return
     */
    public boolean addEmailInfo(String workNumber, String userId) {
        HttpHeaders headers = getAuthHeader();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject metadata = new JSONObject();
        metadata.put("uri", "PerEmail");

        JSONObject data = new JSONObject();
        data.put("__metadata", metadata);
        data.put("personIdExternal", workNumber);
        data.put("emailAddress", userId + "@megvii.com");
        data.put("emailType", SF_EMAIL_TYPE);
        data.put("isPrimary", true);

        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(), headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);

        JSONObject result = responseEntity.getBody();
        if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200) {
            return true;
        } else {
            logger.error(userId + "添加邮箱失败" + result.getJSONArray("d").getJSONObject(0).get("message"));
            return false;
        }
    }

    /**
     * 抓取SF EmpJob
     * @return
     */
    public JSONArray getSfEmp() {
        String url = sfEmpUrl + "&$select=userId,jobEntryDate,probationPeriodEndDate,employeeType,managerId,businessUnit,division,department,customString16,customString17,lastModifiedDateTime,lastModifiedBy,customString14,position,customString15,customString8,customString20";

        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(url,restTemplate.exchange
                (url, HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray list = result.getJSONObject("d").getJSONArray("results");
        String nextUrl = result.getJSONObject("d").get("__next").toString();

        while (nextUrl != null) {
            JSONObject newResult = RequestCorvertUtil.getJSONObjectFromStr(nextUrl,restTemplate.exchange
                    (nextUrl, HttpMethod.GET, requestEntity, String.class).getBody());

            list.addAll(newResult.getJSONObject("d").getJSONArray("results"));
            if (newResult.getJSONObject("d").get("__next") != null) {
                nextUrl = newResult.getJSONObject("d").get("__next").toString();
            } else {
                nextUrl = null;
            }
        }
        return list;
    }

    /**
     * 全量抓取人物关系数据
     * @return
     */
    public JSONArray getSfRelationships() {
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(sfRelationshipsUrl,restTemplate.exchange
                (sfRelationshipsUrl, HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray list = result.getJSONObject("d").getJSONArray("results");
        String nextUrl = result.getJSONObject("d").get("__next").toString();
        while (nextUrl != null) {
            JSONObject newResult = RequestCorvertUtil.getJSONObjectFromStr(nextUrl, restTemplate.exchange
                    (nextUrl, HttpMethod.GET, requestEntity, String.class).getBody());

            list.addAll(newResult.getJSONObject("d").getJSONArray("results"));
            if (newResult.getJSONObject("d").get("__next") != null) {
                nextUrl = newResult.getJSONObject("d").get("__next").toString();
            } else {
                nextUrl = null;
            }
        }
        return list;
    }


    /**
     * 从SF抓取全量人员数据
     * 一次最多抓1000条
     * @return
     */
    public JSONArray getUsers() {
        String url = sfUserUrl + "&$select=userId,username,defaultFullName,cellPhone,location,email,dateOfBirth";

        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(url,restTemplate.exchange
                (url, HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray list = result.getJSONObject("d").getJSONArray("results");
        String nextUrl = result.getJSONObject("d").get("__next").toString();
        while (nextUrl != null) {
            JSONObject newResult = RequestCorvertUtil.getJSONObjectFromStr(nextUrl,restTemplate.exchange
                    (nextUrl, HttpMethod.GET, requestEntity, String.class).getBody());

            list.addAll(newResult.getJSONObject("d").getJSONArray("results"));
            if (newResult.getJSONObject("d").get("__next") != null) {
                nextUrl = newResult.getJSONObject("d").get("__next").toString();
            } else {
                nextUrl = null;
            }
        }
        return list;
    }

    /**
     * 获取SF_sections
     * @return
     */
    public JSONArray getSections() {
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(sfSectionUrl,restTemplate.exchange
                (sfSectionUrl+"&$select=mdfSystemStatus,externalCode,externalName,cust_SubTeamForOA", HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray datas = (JSONArray) result.getJSONObject("d").get("results");

        return datas;
    }

    /**
     * 获取SF_subTeams
     * @return
     */
    public JSONArray getSubTeams() {
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(sfSubTeamUrl,restTemplate.exchange
                (sfSubTeamUrl+"&$select=status,externalCode,name_localized,cust_TeamForOA", HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray datas = (JSONArray) result.getJSONObject("d").get("results");

        return datas;
    }

    /**
     * 获取SF_teams
     * @return
     */
    public JSONArray getTeams() {
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(sfTeamUrl,restTemplate.exchange
                (sfTeamUrl+"&$select=status,externalCode,name_localized,cust_BUForOA", HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray datas = (JSONArray) result.getJSONObject("d").get("results");

        return datas;
    }

    /**
     * 获取SF_bus
     * @return
     */
    public JSONArray getBus() {
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(sfBuUrl,restTemplate.exchange
                (sfBuUrl+"&$select=mdfSystemStatus,externalCode,externalName,cust_GroupForOA", HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray datas = (JSONArray) result.getJSONObject("d").get("results");

        return datas;
    }


    /**
     * 获取SF_groups 不做判断 与业务解耦 返回groups
     * @return
     */
    public JSONArray getGroups() {
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result = RequestCorvertUtil.getJSONObjectFromStr(sfGroupUrl,restTemplate.exchange
                (sfGroupUrl+"&$select=status,externalCode,name_localized", HttpMethod.GET, requestEntity, String.class).getBody());

        JSONArray datas = (JSONArray) result.getJSONObject("d").get("results");

        return datas;
    }

    /**
     *
     *查询人员是否已存在
     * @param userId  工号
     * @return
     */
    public boolean isExistUser(String userId){
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        String filterStr="&$filter=userId eq '"+userId+"'";
        JSONObject result=RequestCorvertUtil.getJSONObjectFromStr(sfUserUrl,restTemplate.exchange(sfUserUrl+"&$select=userId"+filterStr, HttpMethod.GET, requestEntity, String.class).getBody());
        JSONArray resultArray=result.getJSONObject("d").getJSONArray("results");
        if (resultArray.size()>0)
            return true;
        else
            return false;
    }

    /**
     * sf中新增User
     * @param workNumber  工号
     * @param userId      登录名
     * @param spell       中文名
     * @param employBase  base地
     * @return
     */
    public boolean createUser(String workNumber,String userId,String spell,String employBase,String mentor,String hrbp){
        try {
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            JSONObject data=new JSONObject();
            JSONObject metaJson2=new JSONObject();
            JSONObject metaJson3=new JSONObject();
            JSONObject metaJson4=new JSONObject();
            JSONObject metaJsondata1=new JSONObject();
            JSONObject metaJsondata2=new JSONObject();
            JSONObject metaJsondata3=new JSONObject();
            JSONObject metaJsondata4=new JSONObject();
            metaJsondata1.put("uri","User('"+workNumber+"')");
            metaJsondata2.put("uri","https://apisalesdemo4.successfactors.com:443/odata/v2/User('NO_HR')");
            metaJsondata2.put("type","SFOData.User");
            metaJson2.put("__metadata",metaJsondata2);
            metaJsondata3.put("uri","https://apisalesdemo4.successfactors.com:443/odata/v2/User('NO_MANAGER')");
            metaJsondata3.put("type","SFOData.User");
            metaJson3.put("__metadata",metaJsondata3);
            metaJsondata4.put("uri","User('1')");
            metaJson4.put("__metadata",metaJsondata4);
            data.put("__metadata",metaJsondata1);
            data.put("empId",workNumber);
            data.put("username",userId);
            data.put("status","t");
            data.put("timeZone","Asia/Shanghai");
            data.put("defaultLocale","zh_CN");
            data.put("firstName",spell);
            data.put("lastName",workNumber);
            data.put("password","e0d1e27909b");
            data.put("email",userId+"@megvii.com");
            data.put("hr",metaJson2);
            data.put("manager",metaJson3);
            data.put("secondManager",metaJson4);
            if (employBase!=null&&!"".equals(employBase))
                data.put("location",getLocationStr(employBase));
            if (mentor!=null&&!"".equals(mentor)){
                JSONObject metaJson5=new JSONObject();
                JSONObject metaJsondata5=new JSONObject();
                metaJsondata5.put("uri","User('"+mentor+"')");
                metaJson5.put("__metadata",metaJson5);
                data.put("manager",metaJson5);
            }
            if (hrbp!=null&&!"".equals(hrbp)){
                JSONObject metaJson6=new JSONObject();
                JSONObject metaJsondata6=new JSONObject();
                metaJsondata6.put("uri","User('"+hrbp+"')");
                metaJson6.put("__metadata",metaJson6);
                data.put("hr",metaJson6);
            }

            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<String> responseEntity =restTemplate.postForEntity(sfUserUrl,requestEntity, String.class);
            if (responseEntity.getStatusCodeValue()==201)
                return true;
            else{
                logger.error(userId+"创建sf用户失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createUser失败 "+userId,e);
            return false;
        }

    }

    /**
     *
     * 创建人员信息
     * @param workNumber  工号
     * @param source   招聘来源
     * @param expectDate   入职日期
     * @param regularDate  试用期截止日期
     * @param bankName     开户行
     * @param bankAccount   银行账号
     * @param bank          银行名称
     * @return
     */
    public boolean createEmpemployment(String workNumber,String source, LocalDateTime expectDate,LocalDateTime regularDate,String bankName,String bankAccount,String bank){
        try {
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","EmpEmployment");
            data.put("__metadata",metaJsondata);
            data.put("userId",workNumber);
            data.put("personIdExternal",workNumber);
            data.put("customString9","01");
            String sourceCode=empSourceMap.get(source);
            if (sourceCode!=null&&sourceCode!=null){
                data.put("customString3",sourceCode);
            }
            data.put("startDate","/Date("+expectDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
            if (regularDate!=null){
                data.put("customDate1","/Date("+regularDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
            }
            if (bank!=null && bankName!=null && bankAccount!=null){
                data.put("customString10",bank+bankName);
                data.put("customString11",bankAccount);
            }

            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();
            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"人员信息添加失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createEmpemployment",e);
            return false;
        }

    }

    /**
     *
     * 添加岗位信息
     * @param workNumber  工号
     * @param expectDate   入职日期
     * @param regularDate  试用期截止日期
     * @param position     position
     * @param mentor        上级工号
     * @param employBase    base地
     * @param company
     * @param employType     员工类型
     * @param duty          职责
     * @param title         title
     * @param referTitle     referTitle
     * @param workBase       工作地点
     * @param deptMap       部门信息
     * @return
     */
    public boolean createEmpJob(String workNumber,LocalDateTime expectDate,LocalDateTime regularDate,String position,String mentor,String employBase,
                                String company,String employType,String duty,String title,String referTitle,String workBase,Map<String,String> deptMap){
        try{
            String expectSfDate="/Date("+expectDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/";
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","EmpJob");
            data.put("__metadata",metaJsondata);
            data.put("userId",workNumber);
            data.put("company","1");
            data.put("eventReason","H1");
            data.put("startDate",expectSfDate);
            data.put("timezone","Asia/Shanghai");
            if (position!=null)
                data.put("position",position);
            data.put("managerId",mentor);
            data.put("workscheduleCode","WS01");
            data.put("timeTypeProfileCode","TP02");
            data.put("timeRecordingVariant","Duration");
            data.put("holidayCalendarCode","China_Holiday_Calendar");
            data.put("fte",1);
            data.put("jobEntryDate",expectSfDate);
            data.put("companyEntryDate",expectSfDate);
            if (regularDate!=null)
                data.put("probationPeriodEndDate","/Date("+regularDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
            data.put("location",getLocationCode(employBase));
            if (employType!=null)
                data.put("employeeType",empTypeMap.get(employType));
//            data.put("customString14",company);
            data.put("customString14", "3067");
            String dutyCode=dutyMap.get(duty);
            if (dutyCode!=null)
                data.put("customString7",dutyCode);
            if (title!=null)
                data.put("customString15",title);
            String referTitleCode=referTitleMap.get(referTitle);
            if (referTitleCode!=null)
                data.put("customString20",referTitleCode);
            String workBaseCode=getWorkCodeBase(workBase);
            if (workBaseCode!=null)
                data.put("customString8",workBaseCode);
            data.putAll(deptMap);
            data.put("customDate1",expectSfDate);
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();

            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加岗位信息失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createEmpJob",e);
            return false;
        }


    }

    /**
     *
     * 新增人员关系数据
     * @param workNumber  工号
     * @param hrbp  hrbp工号
     * @param buddy buddy工号
     * @param siteHrg   siteHrg工号
     * @param groupLeader   groupLeader工号
     * @param expectDate    expectDate工号
     * @return
     */
    public boolean creareEmpJobRelationships(String workNumber,String hrbp,String buddy,String siteHrg,String groupLeader,LocalDateTime expectDate){

        try{
            boolean flag=true;
            String expectSfDate="/Date("+expectDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/";

            if (hrbp!=null)
                creareEmpJobRelationship(workNumber,SF_HRG_TYPE,hrbp,expectSfDate);

            if (buddy!=null)
                creareEmpJobRelationship(workNumber,SF_BUDDY_TYPE,buddy,expectSfDate);


            if (siteHrg!=null)
                creareEmpJobRelationship(workNumber,SF_SITE_HRG_TYPE,siteHrg,expectSfDate);


            if (groupLeader!=null)
                creareEmpJobRelationship(workNumber,SF_LEADER_TYPE,groupLeader,expectSfDate);

            return flag;
        }catch (Exception e){
            logger.error("creareEmpJobRelationships",e);
            return false;
        }

    }

    /**
     *
     * 添加 EmpJobRelationships
     * @param workNumber
     * @param relationshipType
     * @param relatedUserId
     * @param startDateStr
     * @return
     */
    private boolean creareEmpJobRelationship(String workNumber,String relationshipType,String relatedUserId,String startDateStr){
        try {
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","EmpJobRelationships");
            data.put("__metadata",metaJsondata);
            data.put("userId",workNumber);
            data.put("relUserId",relatedUserId);
            data.put("relationshipType",relationshipType);
            data.put("startDate",startDateStr);
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();

            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加Relationships失败");
                return false;
            }
        }catch (Exception e){
            logger.error(workNumber+"添加失败"+relationshipType,e);
            return false;
        }

    }


    /**
     *
     * 创建email
     * @param workNumber  工号
     * @param userId       登录名
     * @return
     */
    public boolean createSfMail(String workNumber,String userId){
        try{
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","PerEmail");
            data.put("__metadata",metaJsondata);
            data.put("personIdExternal",workNumber);
            data.put("emailAddress",userId+"@megvii.com");
            data.put("emailType",SF_EMAIL_TYPE);
            data.put("isPrimary",true);
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();
            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加email失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createSfMail",e);
            return false;
        }

    }

    /**
     *
     * 添加手机号
     * @param workNumber  工号
     * @param phone     手机号
     * @return
     */
    public boolean createSfPhone(String workNumber,String phone){
        try{
            if (phone==null||"".equals(phone))
                return true;
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","PerPhone");
            data.put("__metadata",metaJsondata);
            data.put("personIdExternal",workNumber);
            data.put("phoneNumber",phone);
            data.put("phoneType",SF_PHONE_TYPE);
            data.put("isPrimary",true);
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();
            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加phone失败");
                return false;
            }

        }catch (Exception e){
            logger.error("createSfPhone",e);
            return false;
        }

    }


    /**
     *
     * 创建个人信息
     * @param workNumber
     * @param expectDate
     * @param spell
     * @param gender
     * @param householdPlace
     * @param presentAddress
     * @param maritalStatus
     * @param reproductiveStatus
     * @param disabilityStatus
     * @param householdType
     * @return
     */
    public boolean createSfPersonalInfo(String workNumber,LocalDateTime expectDate,String spell,String gender,String householdPlace,
                                        String presentAddress,String maritalStatus,String reproductiveStatus,String disabilityStatus,
                                        String householdType){
        try{
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","PerPersonal");
            data.put("__metadata",metaJsondata);
            data.put("personIdExternal",workNumber);
            data.put("startDate","/Date("+expectDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
            data.put("lastName",workNumber);
            data.put("firstName",spell);
            data.put("gender",gender);
            data.put("nationality","CHN");
            data.put("customString4",householdPlace);
            data.put("customString5",presentAddress);
            data.put("maritalStatus",personalInfoMap.get(maritalStatus));
            data.put("customString1",reproductiveStatus);
            data.put("challengeStatus","1".equals(disabilityStatus)?true:false);
            data.put("customString3",personalInfoMap.get(householdType));
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();
            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加SfPersonalInfo失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createSfPersonalInfo",e);
            return false;
        }

    }


    /**
     *
     * 身份证信息
     * @param workNumber  工号
     * @param idNumber     身份证号
     * @return
     */
    public boolean createPerNationalId(String workNumber,String idNumber){
        try{
            if (idNumber==null||"".equals(idNumber))
                return true;
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","PerNationalId");
            data.put("__metadata",metaJsondata);
            data.put("personIdExternal",workNumber);
            data.put("cardType","ResidentIdCard");
            data.put("country","CHN");
            data.put("nationalId",idNumber);
            data.put("isPrimary",true);
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();
            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加PerNationalId失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createPerNationalId",e);
            return false;
        }

    }

    /**
     * 添加出生信息
     * @param workNumber 工号
     * @param birthday    出生日期
     * @return
     */
    public boolean createSfPerPerson(String workNumber,LocalDateTime birthday){
        try {
            if (birthday==null)
                return true;
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","PerPerson");
            data.put("__metadata",metaJsondata);
            data.put("personIdExternal",workNumber);
            data.put("dateOfBirth","/Date("+birthday.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
            data.put("countryOfBirth","CHN");
            LocalDateTime now=LocalDateTime.now();
            Period diff=Period.between(birthday.toLocalDate(),now.toLocalDate());
            int a=diff.getYears();
            data.put("customDouble1",a);
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();
            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加PerPerson失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createSfPerPerson",e);
            return false;
        }

    }

    /**
     *
     * 添加联系人
     * @param workNumber  工号
     * @param contactRelation   关系
     * @param contactName   姓名
     * @param contactPhone  电话
     * @return
     */
    public boolean createPerEmergencyContacts(String workNumber,String contactRelation,String contactName,String contactPhone){
        try {
            if (contactRelation==null||"".equals(contactRelation))
                return true;
            JSONObject data=new JSONObject();
            JSONObject metaJsondata=new JSONObject();
            metaJsondata.put("uri","PerEmergencyContacts");
            data.put("__metadata",metaJsondata);
            data.put("personIdExternal",workNumber);
            data.put("name",contactName);
            data.put("relationship",contactRelationMap.get(contactRelation));
            data.put("phone",contactPhone);
            data.put("primaryFlag","Y");
            HttpHeaders headers=getAuthHeader();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
            JSONObject result=responseEntity.getBody();
            if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){
                return true;
            }else {
                logger.error(workNumber+"添加PerEmergencyContacts失败");
                return false;
            }
        }catch (Exception e){
            logger.error("createPerEmergencyContacts",e);
            return false;
        }
    }

    /**
     *
     * 添加荣誉信息
     * @param workNumber  工号
     * @param honors  荣誉List
     * @return
     */
    public boolean createBackgroundAwards(String workNumber, JSONArray honors){
        try{
            if (honors==null||honors.size()==0)
                return true;
            for (int i=0;i<honors.size();i++){
                JSONObject honor=honors.getJSONObject(i);
                if (honor.getString("honor_name")==null||"".equals(honor.getString("honor_name")))
                    continue;
                JSONObject data=new JSONObject();
                JSONObject metaJsondata=new JSONObject();
                metaJsondata.put("uri","Background_Awards");
                data.put("__metadata",metaJsondata);
                data.put("userId",workNumber);
                data.put("description",honor.get("honor_desc"));
                data.put("institution",honor.get("honor_college"));
                data.put("name",honor.get("honor_name"));
                LocalDateTime honorTime= DateTimeUtil.string2DateYMD(honor.getString("honor_time"));
                data.put("issueDate","/Date("+honorTime.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
                HttpHeaders headers=getAuthHeader();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
                ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
                JSONObject result=responseEntity.getBody();
                if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){

                }else {
                    logger.error(workNumber+"添加Background_Awards失败");
                }
            }
            return true;
        }catch (Exception e){
            logger.error("createBackgroundAwards",e);
            return false;
        }

    }

    /**
     *
     * 添加工作信息
     * businessType不能是空格 阿里云数据存在问题 抛出到sentry
     * @param workNumber 工号
     * @param employedList  雇佣信息List
     * @return
     */
    public boolean createBackgroundOutsideWorkExperience(String workNumber,JSONArray employedList){
        try {
            if (employedList==null||employedList.size()==0)
                return true;
            for (int i=0;i<employedList.size();i++){
                JSONObject employed=employedList.getJSONObject(i);
                if (employed.get("employed_company_name")==null||"".equals(employed.get("employed_company_name")))
                    continue;
                JSONObject data=new JSONObject();
                JSONObject metaJsondata=new JSONObject();
                metaJsondata.put("uri","Background_OutsideWorkExperience");
                data.put("__metadata",metaJsondata);
                data.put("userId",workNumber);
                data.put("employer",employed.get("employed_company_name"));
                String employedDate=employed.getString("employed_date");
                String[] date=employedDate.split(" - ");
                LocalDateTime startDate=DateTimeUtil.string2DateYMD(date[0]);
                LocalDateTime endDate=DateTimeUtil.string2DateYMD(date[1]);
                data.put("startDate","/Date("+startDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
                data.put("endDate","/Date("+endDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
                data.put("startTitle",employed.get("employed_post"));
                data.put("businessType",businessTypeMap.get(employed.get("employed_business_type")));
                HttpHeaders headers=getAuthHeader();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
                ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
                JSONObject result=responseEntity.getBody();
                if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){

                }else {
                    logger.error(workNumber+"添加Background_OutsideWorkExperience失败");
                }
            }
            return true;
        }catch (Exception e){
            logger.error("createBackgroundOutsideWorkExperience",e);
            return false;
        }
    }

    /**
     *
     * 添加教育背景
     * 开始时间结束时间会进行验证，不正确会抛出异常
     * @param workNumber  工号
     * @param educationList  教育List
     * @return
     */
    public boolean createBackgroundEducation(String workNumber,JSONArray educationList){
        try{
            if (educationList==null||educationList.size()==0)
                return true;
            for (int i=0;i<educationList.size();i++){
                JSONObject education=educationList.getJSONObject(i);
                if (education.get("education_school")==null|| "".equals(education.get("education_school")))
                    continue;
                JSONObject data=new JSONObject();
                JSONObject metaJsondata=new JSONObject();
                metaJsondata.put("uri","Background_Education");
                data.put("__metadata",metaJsondata);
                data.put("userId",workNumber);
                String[] dates=education.getString("education_date").split(" - ");
                LocalDateTime startDate=DateTimeUtil.string2DateYMD(dates[0]);
                LocalDateTime endDate=DateTimeUtil.string2DateYMD(dates[1]);
                data.put("startDate","/Date("+startDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
                data.put("endDate","/Date("+endDate.toInstant(ZoneOffset.of("+0")).toEpochMilli()+")/");
                data.put("school",education.get("education_school"));
                data.put("degree",educationTypeMap.get(education.get("education_degree")));
                String major = educationTypeMap.get(education.get("education_major"));
                data.put("major", major!=null?major:"804");
                HttpHeaders headers=getAuthHeader();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(data, SerializerFeature.WriteMapNullValue),headers);
                ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(sfUpsertUrl, requestEntity, JSONObject.class);
                JSONObject result=responseEntity.getBody();
                if (result.getJSONArray("d").getJSONObject(0).getIntValue("httpCode")==200){

                }else {
                    logger.error(workNumber+"添加Background_Education失败");
                }
            }
            return true;
        }catch (Exception e){
            logger.error("createBackgroundEducation",e);
            return false;
        }
    }
    /**
     * 转换为sf的base地格式
     * 先从redis拿，如果拿不到再从sf接口中取base地的code
     *
     * @param employBase  base地
     * @return
     */
    public String getLocationStr(String employBase){
        Map<String,String> locationMap=redisTemplate.opsForHash().entries("mitexecutor_sf_location");
        if (locationMap.size()==0){
            locationMap=getLocationMap();
            redisTemplate.opsForHash().putAll("mitexecutor_sf_location",locationMap);
            redisTemplate.expire("mitexecutor_sf_location", 120, TimeUnit.SECONDS);
        }
        return employBase+" ("+locationMap.get(employBase)+")";
    }

    public String getLocationCode(String employBase){
        Map<String,String> locationMap=redisTemplate.opsForHash().entries("mitexecutor_sf_location");
        if (locationMap.size()==0){
            locationMap=getLocationMap();
            redisTemplate.opsForHash().putAll("mitexecutor_sf_location",locationMap);
            redisTemplate.expire("mitexecutor_sf_location", 120, TimeUnit.SECONDS);
        }
        return locationMap.get(employBase);
    }

    /**
     * 从sf获取location字典
     * @return
     */
    private Map<String,String> getLocationMap(){
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, getAuthHeader());
        JSONObject result=RequestCorvertUtil.getJSONObjectFromStr(sfLocationUrl,restTemplate.exchange(sfLocationUrl, HttpMethod.GET, requestEntity, String.class).getBody());
        JSONArray resultArray=result.getJSONObject("d").getJSONArray("results");
        Map<String,String> locationMap=new HashMap<>();
        for (int i=0;i<resultArray.size();i++){
            if(resultArray.getJSONObject(i).getString("status")!=null && "A".equals(resultArray.getJSONObject(i).getString("status")))
                if(resultArray.getJSONObject(i).getString("name")==null)
                    continue;
            locationMap.put(resultArray.getJSONObject(i).getString("name"),resultArray.getJSONObject(i).getString("externalCode"));
        }
        return locationMap;
    }

    /**
     *
     * @param workBase  工作地点
     * @return
     */
    public String getWorkCodeBase(String workBase){
        Map<String,Object> workBaseMap=redisTemplate.opsForHash().entries("mitexecutor_sf_workBase");
        if (workBaseMap.size()==0){
            workBaseMap=mmisMegviiConfigService.getWorkSiteMap();
            redisTemplate.opsForHash().putAll("mitexecutor_sf_workBase",workBaseMap);
            redisTemplate.expire("mitexecutor_sf_workBase", 120, TimeUnit.SECONDS);
        }
        return (String) workBaseMap.get(workBase);
    }

    /**
     * 登录验证用的header
     * @return
     */
    private HttpHeaders getAuthHeader(){
        HttpHeaders headers=new HttpHeaders();
        String auth = sfAdmin + ":" + sfPsw;
        byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(Charset.forName("US-ASCII")));
        headers.set("Authorization", "Basic " + new String( encodedAuth ) );
        return headers;
    }
}
