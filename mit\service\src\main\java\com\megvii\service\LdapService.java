package com.megvii.service;

import cn.hutool.core.lang.Pair;
import com.megvii.common.CommonResult;
import com.megvii.common.PasswordGenerator;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.*;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.ldap.NameNotFoundException;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.DirContextOperations;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@PropertySource(value = "classpath:group.properties",encoding = "UTF-8")
public class LdapService {
    Logger logger= LoggerFactory.getLogger(LdapService.class);

    @Value("${spring.ldap.base}")
    private String base;

    @Value("${spring.ldap.ou}")
    private String ou;

    @Value("${spring.ldap.domain}")
    private String ldapDomain;

    @Value("#{'${specialGroup}'.split(',')}")
    private List<String> specialGroup=new ArrayList<>();

    @Value("#{${mailGroupLocation}}")
    private Map<String,String> mailGroupLocation=new HashMap<>();

    @Autowired
    RedisTemplate redisTemplate;

    @Autowired
    private LdapTemplate ldapTemplate;

    @Autowired
    UserService userService;

    @Autowired
    TeamService teamService;

    @Autowired
    private GroupService groupService;


    private static final Map<String, String> CLUSTER_MAP = new HashMap<>();
    private static final Map<String, String> MC_BG_MAP = new HashMap<>();

    private static final Map<String, String> ZK_BG_MAP = new HashMap<>();
    private static final Map<String, String> CONSUMPTION_BG_MAP = new HashMap<>();
    private static final Map<String,String> SUPPLY_BG_MAP = new HashMap<>();
    private static final Map<String,String> TECHNOLOGY_BG_MAP = new HashMap<>();
    private static final Map<String,Map<String,String>> CLUSTER_CHILD_MAP = new HashMap<>();
    private static final Map<String,String> PACE_MAP = new HashMap<>();
    static {
        CLUSTER_MAP.put("CL01", "消费物联网");
        CLUSTER_MAP.put("CL02", "智能空间业务群");
        CLUSTER_MAP.put("CL03", "供应链物联网");
//		clusterMap.put("CL05", false);
        CLUSTER_MAP.put("CL06", "技术体系");
        CLUSTER_MAP.put("CL07", "CEO线");
//        clusterMap.put("CL08", true);
        CLUSTER_MAP.put("CL09", "总裁线");
        CLUSTER_MAP.put("CL10", "CFO线");
        CLUSTER_MAP.put("CL12", "CHO线");
        CLUSTER_MAP.put("G50", "MC");
        CLUSTER_MAP.put("G53", "智能驾驶中心");
        CLUSTER_MAP.put("其他", "其他");

        CLUSTER_CHILD_MAP.put("CL01", CONSUMPTION_BG_MAP);
        CLUSTER_CHILD_MAP.put("CL03", SUPPLY_BG_MAP);
        CLUSTER_CHILD_MAP.put("CL06", TECHNOLOGY_BG_MAP);
        CLUSTER_CHILD_MAP.put("G50", MC_BG_MAP);
        CLUSTER_CHILD_MAP.put("G53", ZK_BG_MAP);
        CLUSTER_CHILD_MAP.put("CL02", PACE_MAP);

        PACE_MAP.put("G28","企业业务事业部");
        PACE_MAP.put("G52","智能空间业务管理部");
        PACE_MAP.put("BD475","算法研发部");
        PACE_MAP.put("BD473","硬件产品研发部");
        PACE_MAP.put("BD474","软件产品研发部");
        PACE_MAP.put("G04","政府业务事业部");
        PACE_MAP.put("BD455","产品经营部");


        CONSUMPTION_BG_MAP.put("G23", "移动业务事业部");
        CONSUMPTION_BG_MAP.put("G03", "云服务事业部");
        CONSUMPTION_BG_MAP.put("其他", "消费物联网其他");

        SUPPLY_BG_MAP.put("G15", "物流业务事业部");
        SUPPLY_BG_MAP.put("其他", "供应链物联网其他");

        TECHNOLOGY_BG_MAP.put("G05","研究院");
        TECHNOLOGY_BG_MAP.put("G51","工程中心");
        TECHNOLOGY_BG_MAP.put("其他","技术体系其他");

        MC_BG_MAP.put("BD412", "商务");
        MC_BG_MAP.put("BD413", "运营");
        MC_BG_MAP.put("BD414", "ASA");
        MC_BG_MAP.put("BD416", "研发");
        MC_BG_MAP.put("BD418", "产品市场&战略合作");
        MC_BG_MAP.put("BD419", "职能");
        MC_BG_MAP.put("BD426", "交付中心");
        MC_BG_MAP.put("BD427", "产品");
        MC_BG_MAP.put("BD428", "数据平台");
        MC_BG_MAP.put("BD429", "平台");
        MC_BG_MAP.put("BD487", "产品及项目");
        MC_BG_MAP.put("其他", "MC其他");

        ZK_BG_MAP.put("BD488","产品管理");
        ZK_BG_MAP.put("BD489","大感知");
        ZK_BG_MAP.put("BD490","规划控制");
        ZK_BG_MAP.put("BD491","系统&平台");
        ZK_BG_MAP.put("BD492","系统工程");
        ZK_BG_MAP.put("BD493","测试");
        ZK_BG_MAP.put("BD494","持续集成");
        ZK_BG_MAP.put("BD495","项目办公室");
        ZK_BG_MAP.put("其他", "智能驾驶中心其他");
    }

    /**
     * 禁用正式员工ldap账号 修改userAccountControl 为 514
     * @param userId
     * @return
     */
    public String disableLdap(String userId) {

        try {
            User user = userService.getUserByUserId(userId);
            String dn = getUserDN(user);
            if (dn == null){
                logger.error(userId + " ldap账号不存在:");
                return userId + "禁用ldap账号错误";
            }
            ldapTemplate.modifyAttributes(dn, new ModificationItem[]{new ModificationItem(DirContext.REPLACE_ATTRIBUTE, new BasicAttribute("userAccountControl", "514"))});
        } catch (Exception e) {
            logger.error(userId + "禁用ldap账号错误:" + e.getMessage());
            return userId + "禁用ldap账号错误";
        }
        return userId + "禁用ldap账号成功";
    }


    /**
     * 禁用ldap账号 修改userAccountControl 为 514
     * @param userId
     * @return
     */
    public String disableTempLdap(String userId) {

        try {
            ldapTemplate.modifyAttributes("cn=" + userId + ",ou=TempStaff,ou=Company" , new ModificationItem[]{new ModificationItem(DirContext.REPLACE_ATTRIBUTE, new BasicAttribute("userAccountControl", "514"))});
        } catch (Exception e) {
            logger.error(userId + "禁用ldap账号错误:" + e.getMessage());
            return userId + "禁用ldap账号错误";
        }

        return userId + "禁用ldap账号成功";
    }

    /**
     * 将员工从ldap监控组dimissioning中移除
     * @param userId
     * @return
     */
    public String removeUserFromDimissioning(String userId){
        StringBuilder msg = new StringBuilder();
        String dimissionGroupName = "dimissioning";
        try {
            List<String> userList = getGroupMembers(dimissionGroupName);
            if(userList.contains(userId)){
                msg.append(deleteGroupUser(userId, dimissionGroupName));
            }else{
                return msg.append(userId + "不在监控组中，跳过；").toString();
            }
        } catch (Exception e) {
            logger.error(userId + "移除监控组错误:" + e.getMessage());
            return msg.append(userId + "移除监控组错误").toString();
        }

        return msg.append(userId + "移除监控组成功").toString();
    }



    /**
     * 修改ldap密码
     * @param userId
     * @param pwd
     * @return
     */
    public String updatePassword(String userId, String pwd) {
        try {
            User user = userService.getUserByUserId(userId);
            String dn = getUserDN(user);
            ModificationItem[] mods = new ModificationItem[1];
            if (dn == null) {
                logger.error(userId + "dn为null");
                return userId + "修改ldap密码异常";
            }
            mods[0] = new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
                    new BasicAttribute("unicodePwd", ("\"" + pwd + "\"").getBytes("UTF-16LE")));

            ldapTemplate.modifyAttributes(dn, mods);
            return userId + "修改ldap密码成功";
        } catch (Exception e) {
            logger.error(userId + "修改ldap密码异常");
            return userId + "修改ldap密码异常";
        }
    }


    /**
     * 查询user displayName
     * @param userId
     * @return
     * @throws Exception
     */
    public String getUserDisplayName(String userId) throws Exception {
        String filter = "(sAMAccountName="+userId+")";
        List<String> list= ldapTemplate.search(ou, filter, new AttributesMapper() {
            @Override
            public Object mapFromAttributes(Attributes attributes) throws NamingException {

                Attribute a = attributes.get("displayName");
                return a==null?null:a.get();
            }
        });
        if (list.size()>0){
            return list.get(0);
        }else {
            throw new Exception("user '"+ userId +"' 不存在");
        }
    }


    /**
     * 从ldap group内删除用户
     * @param userId
     * @param group
     */
    public String deleteGroupUser(String userId, String group) {
        try {
            User user = userService.getUserByUserId(userId);
            if (user.getStatus()!=null || !StringUtils.isEmpty(user.getStatus())){
                return group + "成员" + userId + "已离职不做处理";
            }
            String userDn = getUserDN(user);
            String groupDn = getGroupDn(group);

            DirContextOperations ctxGroup = ldapTemplate.lookupContext(groupDn.toLowerCase().replaceFirst(","+base.toLowerCase(),""));
            Object[] a = ctxGroup.getObjectAttributes("member");
            for (Object o : a) {
                String temp = o.toString();
                String tempName = temp.substring(temp.indexOf("CN=") + 3, temp.indexOf(",")).toLowerCase();
                if (tempName.equals(userDn)) {
                    ctxGroup.removeAttributeValue("member", temp);
                    break;
                }
            }

            ldapTemplate.modifyAttributes(ctxGroup);
            return group + "删除" + userId + "<span style=\"color:#3eda67;\">【成功】</span>";
        } catch (Exception e) {
            logger.error(group + "删除" + userId + "<span style=\"color:#dc143c;\">【异常】</span>" + e);
            return group + "删除" + userId + "<span style=\"color:#dc143c;\">【异常】</span>";
        }

    }



    /**
     * 修改 ldap users department属性
     * @param userId
     * @param info
     * @param attribute
     */
    public void modifyUser(String userId,String info, String attribute) {
        try {
            logger.info("修改ldap用户" + userId + "的属性" + info + "为" + attribute);
            User user = userService.getUserByUserId(userId);
            String userDn = getUserDN(user);
            if (userDn == null) {
                logger.error(userId + "dn为null");
                return;
            }
            ldapTemplate.modifyAttributes(userDn, new ModificationItem[]{new ModificationItem(DirContext.REPLACE_ATTRIBUTE, new BasicAttribute(info, attribute))});
        } catch (Exception e) {
            logger.error(userId + "修改ldap属性错误:" + e.getMessage());
        }
    }


    /**
     * 修改ldap属性
     * 通过mailGroup查找 info为属性名 值为name
     * @param mailGroup
     * @param info
     * @param name
     */
    public void modifyGroup(String mailGroup, String info, String name) {
        try {
            List<Map> list = getByMailGroup(mailGroup);
            Map map = list.get(0);

            String groupDn = map.get("groupDn")!=null?map.get("groupDn").toString():null;
            String fixGroupDn = groupDn.toLowerCase().replaceFirst(","+base.toLowerCase(),"");

            ldapTemplate.modifyAttributes(fixGroupDn , new ModificationItem[]{new ModificationItem(DirContext.REPLACE_ATTRIBUTE, new BasicAttribute(info, name))});
        } catch (Exception e) {
            logger.error(name + "修改ldap属性错误:" + e.getMessage());
        }
    }

    /**
     * 通过mailGroup查询ldap属性
     * @param mailGroup
     * @return
     */
    public List<Map> getByMailGroup(String mailGroup) {
        String filter = "(sAMAccountName=" + mailGroup + ")";
        List<Map> list= ldapTemplate.search("ou=Groups,ou=Company", filter, new AttributesMapper() {
            @Override
            public Object mapFromAttributes(Attributes attributes) throws NamingException {
                Map map = new HashMap();

                Attribute groupDn = attributes.get("distinguishedname");
                if (groupDn != null) map.put("groupDn", (String)groupDn.get());

                Attribute displayname = attributes.get("displayname");
                if (displayname != null) map.put("displayname", (String)displayname.get());


                Attribute description = attributes.get("description");
                if (description != null) map.put("description", (String)description.get());

                return map;
            }
        });

        return list;
    }

    /**
     *
     * 同步人员账号至ladp并给人员添加ldap组
     * @param user
     * @return
     */
    public String synUser2Ldap(User user){
        String msg="";
        //分配密码
        if (user.getOriPassword()==null || "".equals(user.getOriPassword())){
            user.setOriPassword(PasswordGenerator.productPassword());
            userService.saveUserPassword(user);
        }
        if (isExist(user.getUserId())){
            user.setLdapFlag(2);
            return "ldap中已存在此用户"+user.getUserId();
        }

        CommonResult result1 = addUser(user);
        msg += result1.getMsg();
        if (result1.getStatus() == 1) {
            user.setLdapFlag(1);
            CommonResult result2 = addUserGroups(user, (String) result1.getMap().get("userDn"));
            msg += result2.getMsg();
        }
        return msg;
    }

    /**
     * 判断Ldap中是否已存在该人员
     * @param userId
     * @return
     */
    public boolean isExist(String userId) {
        User user = userService.getUserByUserId(userId);
        String userDn = getUserDN(user); // cn=zhangliwang,ou=CEO线,ou=Users,ou=Company

        if (userDn == null) {
            logger.error("[LDAP] userId={} 对应的 DN 为空", userId);
            return false;
        }

        // 提取 baseDn，使用较高层级避免查不到
        String baseDn = ou;
        String filter = "(sAMAccountName=" + userId + ")";

        try {
            logger.info("[LDAP] 查询用户是否存在：userId={}, baseDn={}, filter={}", userId, baseDn, filter);

            List<String> result = ldapTemplate.search(baseDn, filter, (AttributesMapper<String>) attrs -> {
                Attribute attr = attrs.get("sAMAccountName");
                return attr == null ? null : (String) attr.get();
            });

            logger.info("[LDAP] 查询用户结果：userId={}, 命中数量={}", userId, result.size());
            return !result.isEmpty();

        } catch (NameNotFoundException e) {
            logger.info("[LDAP] baseDn 不存在：{}", baseDn);
            return false;
        } catch (Exception e) {
            logger.error("[LDAP] 查询用户异常：userId={}, message={}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 向处理用户属性并添加至ldap
     * @param user
     * @return
     */
    private CommonResult addUser(User user){
        String userDN = getUserDN(user);
        if (userDN==null){
            return new CommonResult(0,"获取用户DN失败");
        }
        logger.info("添加用户"+user.getUserId()+"到ldap");
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append(userDN);
        //stringBuilder.append(base);

        Attributes attributes=new BasicAttributes() ;
        BasicAttribute objectclass=new BasicAttribute("objectclass");
        objectclass.add("top");
        objectclass.add("person");
        objectclass.add("organizationalPerson");
        objectclass.add("user");

        attributes.put(objectclass);
        attributes.put("cn",user.getUserId());
        attributes.put("sAMAccountname",user.getUserId());
        try{
            attributes.put("unicodePwd",("\""+user.getOriPassword()+"\"").getBytes("UTF-16LE"));
        }catch (Exception e){
            logger.error("ldap密码编码错误",e);
        }

        attributes.put("sn",user.getSpell().substring(0,1));

        String displayName = getDisplayName(user);
        String givenName = displayName.substring(1);
//        //若登录名以数字结尾
//        Pattern pattern = Pattern.compile("\\d+$");
//        Matcher matcher = pattern.matcher(user.getUserId());
//        if (matcher.find()&&user.getTeam()!=null&&!"".equals(user.getTeam())){
//            String[] depts=user.getTeam().split("-");
//            if (depts.length>0){
//                displayName+="-"+depts[depts.length-1];
//                givenName+="-"+depts[depts.length-1];
//            }
//
//        }

        attributes.put("displayName",displayName);
        attributes.put("userPrincipalName",user.getUserId()+ldapDomain);
        attributes.put("userAccountControl","512");

        /**
         * 从redis中获取uid
         * 假如redis中是90
         * uid取得结果为91  redis中变为91  实际90是应该使用的uid
         * 使用-1后的uid 保证redis中的uid是可用的
         */
        long uid = redisTemplate.opsForValue().increment("dirac_staff_create_uid_PROD",1);
        uid  = uid - 1;
        logger.info(user.getUserId() + "添加ldap的uid是" + uid);

        attributes.put("uidNumber",uid+"");
        attributes.put("distinguishedName",stringBuilder.toString()+","+base);
        attributes.put("mail",user.getUserId()+"@megvii.com");
        if (user.getTeam()!=null)
            attributes.put("department",user.getTeam());
        attributes.put("givenName",givenName);
        attributes.put("loginShell","/bin/bash");
        attributes.put("telephoneNumber",user.getCell());
        try{
            ldapTemplate.bind(stringBuilder.toString(),null,attributes);
            CommonResult result=new CommonResult(1,"ldap用户"+user.getUserId()+"创建成功");
            Map map=new HashMap();
            map.put("userDn",stringBuilder.toString());
            result.setMap(map);
            return  result;
        }catch (Exception e){
            logger.error("新增失败"+user.getUserId(),e);
            return new CommonResult(0,"ldap用户"+user.getUserId()+"创建失败("+e.getMessage()+")");
        }

    }

    private CommonResult addUserGroups(User user,String userDn){
        CommonResult commonResult = new CommonResult(1,"添加Group成功");
        if (user.getCreateTag() != null && (user.getCreateTag() == 4||user.getCreateTag() == 9)) {
            commonResult.setMsg("标注人员不添加group");
            return commonResult;
        }

        try {
            List<String> groupList = groupService.getGroupList(user);
            //新建账号时，添加的group需在officeSecureGroups中
            groupList.retainAll(groupService.selectAllSecureGroup());
            for (String s : groupList) {
                addMemberToGroup(s, userDn);
            }
        } catch (Throwable e) {
            commonResult.setStatus(2);
            commonResult.setMsg(",添加Group存在失败(详情查看执行器日志)");
            logger.error("group添加失败userId:" + user.getUserId() , e);
        }
        return commonResult;

    }

    /**
     * 给group添加成员 新方法  dn为直接取得  不拼接
     * @param group
     * @param userDn
     * @return
     * @throws Exception
     */
    public boolean addMemberToGroupByUserDn(String group,String userDn) throws Exception {
        String groupDn = getGroupDn(group);
        DirContextOperations ctxGroup = ldapTemplate.lookupContext(groupDn.toLowerCase().replaceFirst(","+base.toLowerCase(),""));
        ctxGroup.addAttributeValue("member", userDn);
        ldapTemplate.modifyAttributes(ctxGroup);
        return true;
    }


    /**
     * 给group添加成员
     * @param group  group名称
     * @param userDn
     * @return
     * @throws Throwable
     */
    public boolean addMemberToGroup(String group,String userDn) throws Exception{
        String groupDn = getGroupDn(group);
        DirContextOperations ctxGroup = ldapTemplate.lookupContext(groupDn.toLowerCase().replaceFirst(","+base.toLowerCase(),""));
        ctxGroup.addAttributeValue("member", userDn+","+base);
        ldapTemplate.modifyAttributes(ctxGroup);
        return true;
    }

    /**获取group的DN值
     * @param group
     * @return
     * @throws Throwable
     */
    public String getGroupDn(String group) throws Exception{
        String filter = "(sAMAccountName="+group+")";
        List<String> list= ldapTemplate.search("ou=Groups,ou=Company", filter, new AttributesMapper() {
            @Override
            public Object mapFromAttributes(Attributes attributes) throws NamingException {
                User user = new User();

                javax.naming.directory.Attribute a = attributes.get("distinguishedname");
                if (a != null) user.setUserId((String)a.get());
                return a==null?null:a.get();
            }
        });
        if (list.size()>0){
            return list.get(0);
        }else {
            throw new Exception("group '"+group+"' 不存在");
        }
    }

    /**
     * 获取user的dn
     * @param userId
     * @return
     * @throws Exception
     */
    public String getRealUserDn(String userId) throws Exception{
        String filter = "(sAMAccountName="+userId+")";
        List<String> list= ldapTemplate.search("ou=company", filter, new AttributesMapper() {
            @Override
            public Object mapFromAttributes(Attributes attributes) throws NamingException {

                javax.naming.directory.Attribute a = attributes.get("distinguishedName");
                return a==null?null:a.get();
            }
        });
        if (list.size()>0){
            return list.get(0);
        }else {
            throw new Exception("user '"+ userId +"' 不存在");
        }
    }



    /**
     * 返回的不是user dn
     * 只是user_id  还需要拼
     * @param userId
     * @return
     * @throws Exception
     */
    public String getUserDn(String userId) throws Exception{
        String filter = "(sAMAccountName="+userId+")";
        List<String> list= ldapTemplate.search(ou, filter, new AttributesMapper() {
            @Override
            public Object mapFromAttributes(Attributes attributes) throws NamingException {

                javax.naming.directory.Attribute a = attributes.get("sAMAccountName");
                return a==null?null:a.get();
            }
        });
        if (list.size()>0){
            return list.get(0);
        }else {
            throw new Exception("user '"+ userId +"' 不存在");
        }
    }

    /**
     * 获取ldap Company Groups 路径下的全部组
     * @return
     */
    public List<String> getAllGroup() {
       return ldapTemplate.list("ou=Groups,ou=Company");
    }

    /**
     * 获取ldap 指定组的人员
     * @return
     */
    public List<String> getGroupMembers(String group) {
        String filter = "(sAMAccountName=" + group + ")";
        List<Object> list= ldapTemplate.search("ou=Groups,ou=Company", filter, new AttributesMapper() {
            @Override
            public Object mapFromAttributes(Attributes attributes) throws NamingException {
                List<String> result = new ArrayList<>();

                Attribute a = attributes.get("member");

                if (a != null) {
                    NamingEnumeration namingEnumeration = a.getAll();
                    while (namingEnumeration.hasMore()){
                        String og = (String) namingEnumeration.next();
                        result.add(og.substring(og.indexOf("CN=") + 3, og.indexOf(",")).toLowerCase());
                    }

                    return result;
                }

               return result;
            }
        });
        if (list.size()>0){
            return (List<String>) list.get(0);
        }else {
            return null;
        }
    }


    /**
     * 获取ldap 指定组的人员
     * @return
     */
    public List<String> getSecureGroupMembers(String group) {
        String filter = "(sAMAccountName=" + group + ")";
        List<Object> list= ldapTemplate.search("ou=Org-Groups,ou=Groups,ou=Company", filter, new AttributesMapper() {
            @Override
            public Object mapFromAttributes(Attributes attributes) throws NamingException {
                List<String> result = new ArrayList<>();

                Attribute a = attributes.get("member");

                if (a != null) {
                    NamingEnumeration namingEnumeration = a.getAll();
                    while (namingEnumeration.hasMore()){
                        String og = (String) namingEnumeration.next();
                        result.add(og.substring(og.indexOf("CN=") + 3, og.indexOf(",")).toLowerCase());
                    }

                    return result;
                }

               return result;
            }
        });
        if (list.size()>0){
            return (List<String>) list.get(0);
        }else {
            return null;
        }
    }



    /**
     * 创建ldap组
     * @param groupName     必须
     * @param displayName
     * @param description
     * @return
     */
    public String createLdapGroup(String groupName, String displayName, String description) {
        if (groupName == null || groupName.equals("")) {
            return "groupName is null";
        }

        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append("cn=" + groupName +",");
        stringBuilder.append("ou=Groups,ou=Company");


        // 判断组是否存在
        try {
            boolean exists = ldapTemplate.lookupContext(stringBuilder.toString()) != null;
            if (exists) {
                return "ldap创建组: " + groupName + "【失败】组已存在";
            }
        } catch (NameNotFoundException e) {
            // 不存在，继续创建
        } catch (Exception e) {
            logger.error("查询组是否存在失败: {}", e.getMessage(), e);
            return "ldap创建组: " + groupName + "【失败】查询出错";
        }


        Attributes attributes=new BasicAttributes() ;
        BasicAttribute objectclass=new BasicAttribute("objectclass");
        objectclass.add("top");
        objectclass.add("group");

        attributes.put(objectclass);
        attributes.put("groupType", "-**********");
        attributes.put("cn", groupName);
        attributes.put("name", groupName);
        attributes.put("sAMAccountName", groupName);

        if (displayName != null) {
            attributes.put("displayName", displayName);
        }
        if (description != null) {
            attributes.put("description", description);
        }

        attributes.put("mail", groupName + "@megvii.com");

        try {
            ldapTemplate.bind(stringBuilder.toString(),null,attributes);
            return "ldap创建组:" + groupName + "【成功】";
        } catch (Exception e) {
            logger.error("ldap创建组:" + groupName + "【失败】:" + e.getMessage());
            return "ldap创建组:" + groupName + "【失败】";
        }

    }


    /**
     * 钉钉 AD 显示名称规则
     * 名称 + 数字 + （实习） + - + 部门简称
     * @param user
     * @return
     */
    public String getDisplayName(User user) {
        String spell = user.getSpell();

        Pattern pattern = Pattern.compile("\\d+$");
        Matcher matcher = pattern.matcher(user.getUserId());

        //带数字
        if (matcher.find()){
            String count = matcher.group();
            spell += count;
            if (user.getEmployType().indexOf("实习")!=-1) {
                spell += "(实习)";
            }
            Team team = teamService.getTeamById(user.getTeamId());
            spell += "-" + team.getShortName();
        } else {
            if (user.getEmployType().indexOf("实习")!=-1) {
                spell += "(实习)";
            }
        }

        return spell;
    }


    private String getUserDN(User user){
        Team team =  teamService.getTeamByCode(user.getTeamCode());
        if(team == null){
            logger.error(user.getUserId() + "归属team 为 null");
            return null;
        }
        if(team.getParentCode() == null){
            logger.error(user.getUserId() + "父级teamCode 为 null");
            return null;
        }
        Pair<Team, Team> pair = getTopAndSubClusterTeamInternal(team,null);
        if (pair == null){//顶级部门，如果cluster无法映射直接返回
            return "cn="+user.getUserId()+",ou="+CLUSTER_MAP.get("其他")+","+ou;
        }
        String clusterADName = CLUSTER_MAP.get(pair.getKey().getSfCode());
        if (CLUSTER_CHILD_MAP.containsKey(pair.getKey().getSfCode())){
            Map<String, String> clusterChildMap = CLUSTER_CHILD_MAP.get(pair.getKey().getSfCode());
            if (clusterChildMap.containsKey(pair.getValue().getSfCode())){
                return "cn="+user.getUserId()+",ou="+clusterChildMap.get(pair.getValue().getSfCode())+",ou="+clusterADName+","+ou;
            }else {
                return "cn="+user.getUserId()+",ou="+clusterChildMap.get("其他")+",ou="+clusterADName+","+ou;
            }
        }
        return "cn="+user.getUserId()+",ou="+clusterADName+","+ou;
    }

    //查找最上级CLUSTER部门以及调用栈的倒数第二个部门
    private Pair<Team, Team> getTopAndSubClusterTeamInternal(Team team, Team child) {
        if (team == null) {
            return null;
        }

        if (CLUSTER_MAP.containsKey(team.getSfCode())) {
            return Pair.of(team, child);
        }

        // 防御：parentCode 等于自己、断链情况
        if (team.getParentCode() == null || team.getParentCode().equals(team.getSfCode())) {
            return null;
        }

        Team parentTeam = teamService.getTeamByCode(team.getParentCode());

        return getTopAndSubClusterTeamInternal(parentTeam, team);
    }


}
