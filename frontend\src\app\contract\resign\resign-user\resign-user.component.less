:host {
  .save-create {
    text-align: right;
    padding-right: 20px;
  }

  .create-contract {
    border-top: 1px solid #d9d9d9;
    padding-top: 20px;
  }

  .create-book {
    display: inline-block;
    width: 150px;
    margin-left: 40px;
    margin-bottom: 20px;
    border-bottom: solid 2px blue;
  }
  .create-list {
    margin-left: 40px;
    margin-bottom: 20px;
    background-color: RGB(242, 242, 242);
  }

  .stampUl {
    margin-top: 20px;
    background-color: rgb(242, 242, 242);
    width: 90%;
    margin-left: 50px;
    margin-bottom: 20px;
    li:before {
      content: '';
      display: inline-block;
      width: 7px;
      height: 7px;
      background-color: blue;
      border-radius: 50%;
      margin-right: 5px;
    }
    li {
      height: 40px;
      line-height: 20px;
      padding: 10px;
      border: 1px solid rgb(217, 217, 217);
    }
  }

  .info-header {
    display: flex;
    align-items: center;
    position: relative;
    .info-open {
      position: absolute;
      right: 100px;
    }
  }
  .info-box {
    display: none;
    .info-font {
      display: inline-block;
      width: 147px;
      text-align: right;
      height: 50px;
      font-size: 14px;
    }
    .info-font1 {
      display: inline-block;
      width: 80%;
      text-align: left;
      height: 50px;
      font-size: 14px;
    }
  }
}
