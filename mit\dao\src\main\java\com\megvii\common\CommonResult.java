package com.megvii.common;

import java.util.Map;

/**
 * 用来封装方法返回结果
 */
public class CommonResult {
    private int status=1;  //1成功 0失败 2 存在失败
    private String msg;
    private Map map;

    public CommonResult() {
    }

    public CommonResult(int status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Map getMap() {
        return map;
    }

    public void setMap(Map map) {
        this.map = map;
    }
}
