import { NgModule } from '@angular/core'
import { SharedModule } from '../shared.module'
import { LocalStorage, SendRequest } from '../core/services'
import { TopmenuRoutingModule } from './topmenu-routing.module'
import { TopmenuComponent } from './topmenu.component'

@NgModule({
  imports: [SharedModule, TopmenuRoutingModule],
  providers: [LocalStorage, SendRequest],
  declarations: [TopmenuComponent]
})
export class TopmenuModule {}
