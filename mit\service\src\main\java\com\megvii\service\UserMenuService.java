package com.megvii.service;

import com.megvii.entity.opdb.*;
import com.megvii.mapper.MenuMapper;
import com.megvii.mapper.RoleMapper;
import com.megvii.mapper.RoleMenuMapper;
import com.megvii.mapper.UserRoleMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/1 18:01
 */
@Service
public class UserMenuService {

	@Autowired
	private UserService userService;

	@Autowired
	private MenuMapper menuMapper;

	@Autowired
	private RoleMapper roleMapper;

	@Autowired
	private UserRoleMapper userRoleMapper;

	@Autowired
	private RoleMenuMapper roleMenuMapper;

	/**
	 * 查询全部role
	 * @return
	 */
	public List<Role> getAllRole() {
		return roleMapper.selectAllRole();
	}


	/**
	 * 查询全部有角色的user
	 * @return
	 */
	public List<User> getAllUser() {
		List<User> users = new ArrayList<>();
		for (String s : userRoleMapper.selectAllUser()) {
			users.add(userService.getUserByUserId(s));
		}
		return users;
	}

	/**
	 * 查询user role list
	 *
	 * @return
	 */
	public List<UserRole> getAllUserRole() {
		return userRoleMapper.selectUserRole();
	}


	/**
	 * 添加用户角色
	 *
	 * @param userId
	 * @param roleCodes
	 * @return
	 */
	public boolean addUserRole(String userId, List<String> roleCodes) {
		User user = userService.getUserByUserId(userId);

		List<UserRole> userRoles = userRoleMapper.selectUserRoleByUserId(userId);
		if (userRoles != null || userRoles.size() > 0) {
			for (UserRole userRole : userRoles) {
				userRoleMapper.removeUserRole(userRole.getId());
			}
		}

		User createUser = (User) SecurityUtils.getSubject().getPrincipal();
		for (String roleCode : roleCodes) {
			Role role = roleMapper.selectRoleByCode(roleCode);
			UserRole userRole = new UserRole();
			userRole.setRoleId(role.getId());
			userRole.setRoleCode(role.getRoleCode());
			userRole.setRoleName(role.getRoleName());
			userRole.setUserId(user.getUserId());
			userRole.setUserName(user.getSpell());
			userRole.setUsableStatus("1");
			userRole.setCreateUser(createUser.getUserId());
			userRole.setUpdateUser(createUser.getUserId());
			userRoleMapper.insertUserRole(userRole);
		}

		return true;
	}


	public boolean addRoleMenu(String roleCode, String menuCode) {
		Role role = roleMapper.selectRoleByCode(roleCode);
		Menu menu = menuMapper.selectMenuByCode(menuCode);

		RoleMenu roleMenu = new RoleMenu();
		roleMenu.setRoleId(role.getId());
		roleMenu.setRoleCode(role.getRoleCode());
		roleMenu.setRoleName(role.getRoleName());
		roleMenu.setMenuId(menu.getId());
		roleMenu.setMenuCode(menu.getMenuCode());
		roleMenu.setMenuName(menu.getMenuName());
		roleMenu.setUsableStatus("1");
		roleMenu.setCreateUser("jiangyuhong");
		roleMenu.setUpdateUser("jiangyuhong");

		if (roleMenuMapper.insertRoleMenu(roleMenu) > 0) {
			return true;
		}
		return false;
	}

	/**
	 * 获取user的menu列表
	 *
	 * @param userId
	 * @return
	 */
	public List<Menu> getMenuListByUserId(String userId) {
		List<UserRole> userRole = userRoleMapper.selectUserRoleByUserId(userId);
		if (userRole == null) {
			return null;
		}
		List<Menu> menus = menuMapper.selectMenuByUserId(userId);
		return menus.stream().distinct().collect(Collectors.toList());
	}


	public List<UserRole> getRoleByUserId(String userId) {
		return userRoleMapper.selectUserRoleByUserId(userId);
	}


}
