package com.megvii.entity.opdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("megin_user_dimission_flow")
public class MeginUserDimissionFlowPO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long meginUserId;

    /**
     * DHR流程id唯一标识
     */
    private String uid;

    /**
     * oa流程id
     */
    private String oaId;

    /**
     * 内部账号
     */
    private String userId;

    /**
     * 工号
     */
    private String workNumber;

    /**
     * 姓名
     */
    private String spell;

    /**
     * 离职日期
     */
    private Date dimissionDate;

    /**
     * 离职交接人
     */
    private String handover;

    /**
     * 离职地点
     */
    private String leaWorkcity;

    private Date updateTime;

    private Date createTime;

    /**
     * 0:在途 1:离职  2:取消
     */
    private String flowStatus;

    /**
     * 离职类型
     */
    private String leaveType;

    /**
     * 在职时长
     */
    private Integer employLength;

    /**
     * 离职访谈id
     */
    private Integer interviewId;

}
