<nz-breadcrumb>
  <nz-breadcrumb-item> 合同管理 </nz-breadcrumb-item>
  <ng-container *ngIf="backurl.indexOf('new') > -1">
    <nz-breadcrumb-item>
      <a [routerLink]="[backurl]">新签合同</a>
    </nz-breadcrumb-item>
  </ng-container>
  <ng-container *ngIf="backurl.indexOf('extend') > -1">
    <nz-breadcrumb-item>
      <a [routerLink]="[backurl]">续签合同</a>
    </nz-breadcrumb-item>
  </ng-container>
  <ng-container *ngIf="backurl.indexOf('endorse') > -1">
    <nz-breadcrumb-item>
      <a [routerLink]="[backurl]">合同转签</a>
    </nz-breadcrumb-item>
  </ng-container>
  <ng-container *ngIf="backurl.indexOf('change') > -1">
    <nz-breadcrumb-item>
      <a [routerLink]="[backurl]">合同变更</a>
    </nz-breadcrumb-item>
  </ng-container>
  <ng-container *ngIf="backurl.indexOf('mgt') > -1">
    <nz-breadcrumb-item>
      <a [routerLink]="[backurl]">合同信息管理</a>
    </nz-breadcrumb-item>
  </ng-container>
  <ng-container *ngIf="backurl.indexOf('resign') > -1">
    <nz-breadcrumb-item>
      <a [routerLink]="[backurl]">离职合同</a>
    </nz-breadcrumb-item>
  </ng-container>
  <nz-breadcrumb-item> 人员详情 </nz-breadcrumb-item>
</nz-breadcrumb>
<div class="content-block content-item">
  <app-title [title]="'人员详情' + (username ? '(' + username + ')' : '')"></app-title>
  <nz-tabset
    nzSize="large"
    (nzSelectChange)="typeSelChange($event)"
    [nzSelectedIndex]="nzSelectedIndex"
  >
    <nz-tab nzTitle="基本信息">
      <nz-spin [nzSpinning]="isBasicLoading" [nzDelay]="500">
        <form nz-form class="top-20" #basicInfoForm="ngForm">
          <h3 class="left-20"><span class="title-divider"></span>人员基本信息</h3>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">工号</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="工号必填">
                <input nz-input disabled [(ngModel)]="staffId" name="staffId" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">部门</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="部门必填">
                <input nz-input disabled [(ngModel)]="dept" name="dept" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <!-- <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">ID</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="Input is required">
                <input nz-input disabled [(ngModel)]="id" name="id" required />
              </nz-form-control>
            </nz-form-item> -->
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">ID</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="Input is required">
                <input nz-input disabled [(ngModel)]="userId" name="userId" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">上级主管</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="上级领导为必填">
                <input nz-input disabled [(ngModel)]="leader" name="leader" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">真实姓名</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="Input is required">
                <input disabled nz-input [(ngModel)]="username" name="username" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">HRG</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="HRG为必填">
                <input nz-input disabled [(ngModel)]="HRG" name="HRG" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">手机号</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="Input is required">
                <input disabled nz-input [(ngModel)]="tele" name="tele" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">工作地点</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="工作地点必填">
                <nz-select
                  required
                  name="officeLocation"
                  [(ngModel)]="officeLocation"
                  nzPlaceHolder="请选择工作地点"
                >
                  <nz-option
                    *ngFor="let item of workLocationOfOption"
                    [nzValue]="item['name']"
                    [nzLabel]="item['name']"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">入职类型</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="入职类型必填">
                <input nz-input disabled [(ngModel)]="type" name="type" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">入职地点</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="请选择入职地点">
                <input
                  nz-input
                  disabled
                  [(ngModel)]="entryLocation"
                  name="entryLocation"
                  required
                />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">岗位名称</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="岗位名称为必填">
                <nz-select
                  required
                  nzShowSearch
                  nzAllowClear
                  name="station"
                  [(ngModel)]="station"
                  nzPlaceHolder="请选择岗位"
                >
                  <nz-option
                    *ngFor="let item of positionList"
                    [nzValue]="item['value']"
                    [nzLabel]="item['label']"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">现居住地址</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="现居住地址为必填">
                <input nz-input [(ngModel)]="apartment" name="apartment" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">签约公司</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="签约公司为必填">
                <nz-select
                  required
                  name="company"
                  [(ngModel)]="company"
                  nzPlaceHolder="请选择签约公司"
                >
                  <nz-option
                    *ngFor="let item of companyOfOption"
                    [nzValue]="item['code']"
                    [nzLabel]="item['name']"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">证件编号</nz-form-label>
              <nz-form-control
                [nzValidateStatus]="basicInfoForm.controls['idCard']"
                nzHasFeedback
                [nzSpan]="18"
                nzErrorTip="证件信息必填"
              >
                <nz-input-group nzCompact>
                  <nz-select
                    [(ngModel)]="idType"
                    style="width: 40%"
                    name="idType"
                    required
                    nzPlaceHolder="请选择证件类型"
                  >
                    <nz-option
                      *ngFor="let item of idTypeList"
                      [nzLabel]="item['name']"
                      [nzValue]="item['name']"
                    ></nz-option>
                  </nz-select>
                  <input
                    type="text"
                    required
                    nz-input
                    name="idCard"
                    [(ngModel)]="idCard"
                    style="width: 60%"
                  />
                </nz-input-group>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">入职日期</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="入职日期为必填">
                <nz-date-picker [(ngModel)]="entryDate" name="entryDate" required></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">备注</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="最多填写200个字">
                <input nz-input [(ngModel)]="remark" name="remark" maxlength="200" />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div class="save-create">
            <button
              nz-button
              class="right-20"
              (click)="onSave()"
              [disabled]="!basicInfoForm.form.valid"
            >
              保存
            </button>
            <button
              type="submit"
              nz-button
              nzType="primary"
              [disabled]="
                !basicInfoForm.form.valid ||
                curContractStatus == '签署中' ||
                curContractStatus == '作废中'
              "
              [nzLoading]="saving"
              (click)="onSaveAndCreate()"
            >
              保存并生成合同
            </button>
          </div>
        </form>
      </nz-spin>

      <nz-spin [nzSpinning]="isSnapShotLoading">
        <!-- 待签署合同详情 -->
        <form nz-form class="top-20 create-contract" #newContractForm="ngForm" *ngIf="isShowCreate">
          <h3 class="left-20"><span class="title-divider"></span>待签署合同详情</h3>
          <h4 class="left-30">新签合同</h4>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">合同编号</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <input
                  nz-input
                  disabled
                  [(ngModel)]="contractSnapshot['contractNumber']"
                  name="contractCode"
                />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">合同属性</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="合同属性为必填">
                <nz-select
                  required
                  name="contractProperty"
                  (ngModelChange)="contractAttributeChange($event)"
                  [(ngModel)]="contractSnapshot['contractAttribute']"
                >
                  <nz-option nzValue="固定期" nzLabel="固定期"></nz-option>
                  <nz-option nzValue="无固定期" nzLabel="无固定期"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div *ngIf="!showEndorse" nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">签订次数</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="签约次数为必填">
                <input
                  nz-input
                  required
                  [(ngModel)]="contractSnapshot['contractCount']"
                  name="contractTimer"
                />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">签约公司</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="签约公司为必填">
                <nz-select
                  required
                  name="contractCompany"
                  [(ngModel)]="contractSnapshot['company']"
                  nzPlaceHolder="请选择签约公司"
                >
                  <nz-option
                    *ngFor="let item of companyOfOption"
                    [nzValue]="item['name']"
                    [nzLabel]="item['name']"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item *ngIf="showEndorse" nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">签订次数</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="签约次数为必填">
                <input
                  nz-input
                  required
                  [(ngModel)]="contractSnapshot['contractCount']"
                  name="contractTimer"
                />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item *ngIf="!showEndorse" nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">合同状态</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <input
                  nz-input
                  disabled
                  name="contractStatus"
                  [(ngModel)]="contractSnapshot['contractStatus']"
                />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">合同类型</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="合同类型为必填">
                <nz-select
                  name="contractType"
                  [(ngModel)]="contractSnapshot['contractType']"
                  (ngModelChange)="contractTypeChange($event)"
                  required
                >
                  <nz-option nzValue="劳动合同" nzLabel="劳动合同"></nz-option>
                  <nz-option nzValue="实习协议" nzLabel="实习协议"></nz-option>
                  <!--<nz-option-->
                  <!--nzValue="退休返聘合同"-->
                  <!--nzLabel="退休返聘合同"-->
                  <!--&gt;</nz-option>-->
                  <!--<nz-option nzValue="劳务协议" nzLabel="劳务协议"></nz-option>-->
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div *ngIf="!showEndorse" nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">合同开始日期</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="合同开始日期为必填">
                <nz-date-picker
                  [(ngModel)]="contractSnapshot['startDate']"
                  (ngModelChange)="startDateChange($event)"
                  name="startDate"
                  required
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label
                [nzRequired]="
                  contractSnapshot['contractAttribute'] == '固定期' &&
                  contractSnapshot['contractType'] == '劳动合同'
                "
                [nzSpan]="6"
                >合同结束日期
              </nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="合同结束日期必填">
                <nz-date-picker
                  [disabled]="contractSnapshot['contractAttribute'] == '无固定期'"
                  [nzDisabledDate]="disabledEndDate"
                  [(ngModel)]="contractSnapshot['endDate']"
                  name="endDate"
                  [required]="
                    contractSnapshot['contractAttribute'] == '固定期' &&
                    contractSnapshot['contractType'] == '劳动合同'
                  "
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">试用期开始日期</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <nz-date-picker
                  [(ngModel)]="contractSnapshot['probationStartDate']"
                  name="probationStartDate"
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">试用期结束日期</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <nz-date-picker
                  [(ngModel)]="contractSnapshot['probationEndDate']"
                  [nzDisabledDate]="disabledProbationEndDate"
                  name="probationEndDate"
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>
          <!-- 转签合同添加字段 -->
          <div *ngIf="showEndorse">
            <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label [nzSpan]="6">旧合同开始日期</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18">
                  <nz-date-picker
                    [(ngModel)]="contractSnapshot['conBeginDate']"
                    name="conBeginDate"
                  ></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label [nzSpan]="6">旧合同结束日期</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18">
                  <nz-date-picker
                    [(ngModel)]="contractSnapshot['conEndDate']"
                    [nzDisabledDate]="disabledProbationEndDate"
                    name="conEndDate"
                  ></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label nzRequired [nzSpan]="6">新合同开始日期</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18">
                  <nz-date-picker
                    [(ngModel)]="contractSnapshot['newConBeginDate']"
                    name="newConBeginDate"
                  ></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label [nzSpan]="6">新合同结束日期</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18">
                  <nz-date-picker
                    [(ngModel)]="contractSnapshot['newConEndDate']"
                    [nzDisabledDate]="disabledProbationEndDate"
                    name="newConEndDate"
                  ></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label [nzSpan]="6">旧签署公司</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="新签署公司为必填">
                  <nz-select
                    required
                    name="contract"
                    [(ngModel)]="contractSnapshot['contract']"
                    nzPlaceHolder="请选择新签署公司"
                  >
                    <nz-option
                      *ngFor="let item of companyOfOption"
                      [nzValue]="item['code']"
                      [nzLabel]="item['name']"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label nzRequired [nzSpan]="6">新签署公司</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="新签署公司为必填">
                  <nz-select
                    required
                    name="newContract"
                    [(ngModel)]="contractSnapshot['newContract']"
                    nzPlaceHolder="请选择新签署公司"
                  >
                    <nz-option
                      *ngFor="let item of companyOfOption"
                      [nzValue]="item['code']"
                      [nzLabel]="item['name']"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label [nzSpan]="6">旧工作地点</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="旧工作地点必填">
                  <nz-select
                    name="workCity"
                    [(ngModel)]="contractSnapshot['workCity']"
                    nzPlaceHolder="请选择工作地点"
                  >
                    <nz-option
                      *ngFor="let item of workLocationOfOption"
                      [nzValue]="item['code']"
                      [nzLabel]="item['name']"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label nzRequired [nzSpan]="6">新工作地点</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="新工作地点必填">
                  <nz-select
                    required
                    name="newWorkCity"
                    [(ngModel)]="contractSnapshot['newWorkCity']"
                    nzPlaceHolder="请选择工作地点"
                  >
                    <nz-option
                      *ngFor="let item of workLocationOfOption"
                      [nzValue]="item['code']"
                      [nzLabel]="item['name']"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <!-- 变更合同添加字段 -->
          <div *ngIf="showChange">
            <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label [nzSpan]="6">旧工作地点</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="旧工作地点必填">
                  <nz-select
                    name="workCity"
                    [(ngModel)]="contractSnapshot['workCity']"
                    nzPlaceHolder="请选择工作地点"
                  >
                    <nz-option
                      *ngFor="let item of workLocationOfOption"
                      [nzValue]="item['code']"
                      [nzLabel]="item['name']"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
              <nz-form-item nz-col [nzSpan]="11">
                <nz-form-label nzRequired [nzSpan]="6">新工作地点</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="新工作地点必填">
                  <nz-select
                    required
                    name="newWorkCity"
                    [(ngModel)]="contractSnapshot['newWorkCity']"
                    nzPlaceHolder="请选择工作地点"
                  >
                    <nz-option
                      *ngFor="let item of workLocationOfOption"
                      [nzValue]="item['code']"
                      [nzLabel]="item['name']"
                    ></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">生效日期</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <nz-date-picker
                  disabled
                  [(ngModel)]="contractSnapshot['effectDate']"
                  name="effectDate"
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">备注</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="最多输入200字">
                <input
                  nz-input
                  [(ngModel)]="contractSnapshot['memo']"
                  name="contractRemark"
                  maxlength="200"
                />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div *ngIf="showEndorse" nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">合同状态</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <input
                  nz-input
                  disabled
                  name="contractStatus"
                  [(ngModel)]="contractSnapshot['contractStatus']"
                />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11"> </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">附件</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <nz-upload
                  [nzAction]="uploadFileAction"
                  [nzAccept]="'.pdf'"
                  [nzShowUploadList]="true"
                  [nzFileList]="fileList"
                  [nzRemove]="handleRemove"
                  [nzBeforeUpload]="beforeUpload"
                  (nzChange)="handleUploadChange($event)"
                  [nzWithCredentials]="true"
                >
                  <button nz-button nzType="link">上传</button>
                </nz-upload>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11"></nz-form-item>
          </div>

          <!-- 待签署入职材料 -->
          <form nz-form class="top-20 create-contract" #newContractForm1="ngForm" *ngIf="isShowStaff">
            <h3 class="left-20"><span class="title-divider"></span>待签署入职材料</h3>
            <!-- 实习生才展示实习协议和在读声明-->
            <div *ngIf="type.indexOf('实习生') !== -1">
              <h4 class="left-30">实习协议</h4>
              <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center" class="staff-info">
                <nz-form-item nz-col [nzSpan]="11">
                  <nz-form-label [nzSpan]="6">实习津贴</nz-form-label>
                  <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="实习津贴必填">
                    <input disabled nz-input [(ngModel)]="contractSnapshot['internshipAllowance']" name="money" required />
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item nz-col [nzSpan]="11">
                </nz-form-item>
              </div>
              <h4 class="left-30">在读声明</h4>
              <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center" class="staff-info">
                <nz-form-item nz-col [nzSpan]="11">
                  <nz-form-label [nzSpan]="6">在读学校</nz-form-label>
                  <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="在读学校必填">
                    <input nz-input [(ngModel)]="contractSnapshot['school']" name="school" />
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item nz-col [nzSpan]="11">
                  <nz-form-label [nzSpan]="6">入学时间</nz-form-label>
                  <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="入学时间必填">
                    <nz-date-picker
                      [(ngModel)]="contractSnapshot['admissionTime']"
                      name="admissionTime"
                  ></nz-date-picker>
                  </nz-form-control>
                </nz-form-item>
                 <nz-form-item nz-col [nzSpan]="11">
                  <nz-form-label [nzSpan]="6">毕业时间</nz-form-label>
                  <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="毕业时间必填">
                    <nz-date-picker
                      [(ngModel)]="contractSnapshot['graduationTime']"
                      name="graduationTime"
                  ></nz-date-picker>
                  </nz-form-control>
                </nz-form-item>
                <nz-form-item nz-col [nzSpan]="11">
                  <nz-form-label [nzSpan]="6">学号</nz-form-label>
                  <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="学号必填">
                    <input nz-input [(ngModel)]="contractSnapshot['studentNumber']" name="studentNumber" />
                  </nz-form-control>
                </nz-form-item>
              </div>
            </div>
            <h4 class="left-30">信息收集表</h4>
            <div class="staff-link staff-info">
              <button *ngIf="status === '等待入职' || status === '在职' ; else tip" nz-button nzType="link" (click)="linkToDetail()">查看详情</button>
              <ng-template #tip><div style="color: #f9a53c">尚未完成信息收集，暂无数据，请完成信息收集流程后，发起签署。</div></ng-template>
            </div>
            <h4 class="left-30">员工录用通知</h4>
            <div class="staff-link staff-info">
              <button *ngIf="contractSnapshot['offerPath'] !== null; else offerTip" nz-button nzType="link" (click)="onReviewOffer()">员工录用通知</button>
              <ng-template #offerTip><div style="color: #f9a53c">暂无员工录用通知，请确认员工录用通知已生成或已上传后，再发起签署。</div></ng-template>
            </div>
          </form>

          <nz-form-item>
            <div class="save-create">
              <button
                type="submit"
                nz-button
                class="right-20"
                [disabled]="!newContractForm.form.valid"
                (click)="onSubmit(true)"
              >
                保存
              </button>
              <button
                nz-button
                nzType="primary"
                (click)="onSign()"
                [disabled]="!newContractForm.form.valid"
                [nzLoading]="signing"
              >
                签署
              </button>
            </div>
          </nz-form-item>
        </form>
      </nz-spin>
    </nz-tab>
    <nz-tab nzTitle="合同历史">
      <ng-container *ngIf="contractListLoading; else contractListLoaded">
        <div class="loading-data">
          <nz-spin nzSimple></nz-spin>
          <span class="top-20">加载中</span>
        </div>
      </ng-container>
      <ng-template #contractListLoaded>
        <app-no-project *ngIf="contractList.length === 0"></app-no-project>
        <ng-container *ngIf="contractList.length !== 0">
          <div *ngFor="let contract of contractList" class="contract-detail-container">
            <div class="contract-row">
              <span>合同状态：</span>
              <span>{{ contract['contractStatus'] }}</span>
              <span>签署人：</span>
              <span>{{ contract['spell'] }}</span>
            </div>
            <div class="contract-row">
              <span>合同编号：</span>
              <span>{{ contract['contractNumber'] }}</span>
              <span>合同属性：</span>
              <span>{{ contract['contractAttribute'] }}</span>
            </div>
            <div class="contract-row">
              <span>签订次数：</span>
              <span>{{ contract['contractCount'] }}</span>
              <span>公司主体：</span>
              <span>{{ contract['company'] }}</span>
            </div>
            <div class="contract-row">
              <span>合同状态：</span>
              <span>{{ contract['contractStatus'] }}</span>
              <span>合同类型：</span>
              <span>{{ contract['contractType'] }}</span>
            </div>
            <div class="contract-row">
              <span>开始日期：</span>
              <span>{{ contract['startDate'] }}</span>
              <span>结束日期：</span>
              <span>{{ contract['endDate'] }}</span>
            </div>
            <div class="contract-row">
              <span>试用开始日期：</span>
              <span>{{ contract['probationStartDate'] }}</span>
              <span>试用结束日期：</span>
              <span>{{ contract['probationEndDate'] }}</span>
            </div>
            <div class="contract-row">
              <span>生效日期：</span>
              <span>{{ contract['effectDate'] }}</span>
              <span>备注：</span>
              <span
                ><app-short-info [info]="contract['memo']" [maxLength]="15"></app-short-info
              ></span>
            </div>
            <div class="title">
              <button
                nzType="primary"
                [disabled]="
                  contract['contractStatus'] == '草稿' || contract['contractStatus'] == '已导入'
                "
                nz-button
                (click)="onReviewContract(contract)"
              >
                查看合同
              </button>
            </div>
          </div>
        </ng-container>
      </ng-template>
    </nz-tab>
    <nz-tab nzTitle="操作历史">
      <div class="top-20 left-20">
        <ng-container *ngIf="oprtListLoading; else oprtListLoaded">
          <div class="loading-data">
            <nz-spin nzSimple></nz-spin>
            <span class="top-20">加载中</span>
          </div>
        </ng-container>
        <ng-template #oprtListLoaded>
          <app-no-project *ngIf="oprtList.length === 0"></app-no-project>
          <nz-timeline *ngIf="oprtList.length !== 0">
            <nz-timeline-item *ngFor="let item of oprtList">
              <div class="oprt-item">
                <div>
                  <span>{{ item['updateTime'] || '' }}</span>
                </div>
                <div class="top-10">
                  {{
                    item['updatorName'] +
                      item['changeType'] +
                      '了' +
                      username +
                      '(' +
                      item['userId'] +
                      ')' +
                      '的合同' +
                      '(合同编号：' +
                      item['contractNumber'] +
                      ')'
                  }}
                </div>
              </div>
            </nz-timeline-item>
          </nz-timeline>
        </ng-template>
      </div>
    </nz-tab>
  </nz-tabset>
</div>
