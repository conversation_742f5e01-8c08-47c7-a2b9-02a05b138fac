package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.Map;

@Component
@PropertySource(value = "classpath:brainsec.${spring.profiles.active}.properties",encoding = "UTF-8")
public class BrainRestClientExpel {

    private Logger logger= LoggerFactory.getLogger(BrainRestClientExpel.class);

    @Value("${BRAIN_URL}")
    private String brainUrl;

    @Value("${BRAIN_ACCESSKEY}")
    private String accessKey;

    @Value("${BRAIN_SECRETKEY}")
    private String secretKey;

    @Autowired
    private RestTemplate restTemplate;


    /*@Override
    public boolean disableUser(Map<String, String> map) throws Exception {
        return expelBrainSec(map.get("userId"));
    }*/

    /**
     * 离职资源清理
     * @param userId
     * @return
     * @throws Exception
     */
    public String expelBrainSec(String userId) {

        HttpHeaders headers = new HttpHeaders();
        headers.set("Cookie", "authentication=Basic " + Base64.getUrlEncoder().encodeToString((accessKey + ":" + secretKey).getBytes()));

        JSONObject data = new JSONObject();
        data.put("username", userId);

        HttpEntity<String> requestEntity = new HttpEntity<>(data.toJSONString(),headers);

        try {
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(brainUrl, requestEntity, JSONObject.class);

            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                return "禁用brain++【成功】";
            } else {
                return "禁用brain++【失败】";
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            return "禁用brain++【异常】";
        }

    }

}
