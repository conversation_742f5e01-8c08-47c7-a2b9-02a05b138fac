<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>1.5.20.RELEASE</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <groupId>com.megvii</groupId>
  <artifactId>MIT</artifactId>
  <packaging>pom</packaging>
  <version>1.0</version>
  <modules>
    <module>dao</module>
    <module>service</module>
    <module>webapp</module>
  </modules>

  <name>MIT</name>
  <!-- FIXME change it to the project's website -->
  <url>http://www.example.com</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
      <thymeleaf.version>3.0.11.RELEASE</thymeleaf.version>
      <thymeleaf-layout-dialect.version>2.4.1</thymeleaf-layout-dialect.version>
      <thymeleaf-extras-springsecurity4.version>3.0.4.RELEASE</thymeleaf-extras-springsecurity4.version>
      <mybatis-plus-boot-starter>3.2.0</mybatis-plus-boot-starter>
      <mybatis-plus-generator>3.2.0</mybatis-plus-generator>
      <velocity-engine-core>2.1</velocity-engine-core>
  </properties>

  <dependencies>
      <dependency>
          <groupId>com.microsoft.graph</groupId>
          <artifactId>microsoft-graph</artifactId>
          <version>3.8.0</version>
      </dependency>
      <dependency>
          <groupId>com.aliyun</groupId>
          <artifactId>dysmsapi20170525</artifactId>
          <version>2.0.23</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jdbc</artifactId>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-ldap</artifactId>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mail</artifactId>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
      </dependency>
      <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>1.3.4</version>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <scope>runtime</scope>
      </dependency>
      <!--读取邮件模板-->
      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-thymeleaf</artifactId>
      </dependency>
      <dependency>
          <groupId>org.thymeleaf.extras</groupId>
          <artifactId>thymeleaf-extras-java8time</artifactId>
          <version>3.0.0.RELEASE</version>
      </dependency>
      <!--druid连接池-->
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.1.11</version>
      </dependency>
      <!--json工具-->
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
      </dependency>
      <!--分页插件-->
      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper-spring-boot-starter</artifactId>
        <version>1.2.8</version>
      </dependency>
      <!--tomcat外部依赖，打war包使用-->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <scope>provided</scope>
      </dependency>
      <!--xxl-job-->
      <dependency>
        <groupId>com.xuxueli</groupId>
        <artifactId>xxl-job-core</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.8</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-text</artifactId>
        <version>1.6</version>
      </dependency>
      <dependency>
        <groupId>commons-net</groupId>
        <artifactId>commons-net</artifactId>
        <version>3.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>2.6.2</version>
      </dependency>
      <!--<dependency>
          <groupId>com.taobao</groupId>
          <artifactId>dingtalksdk</artifactId>
          <version>1.0</version>
      </dependency>-->
      <!--<dependency>-->
        <!--<groupId>com.github.brainlag</groupId>-->
        <!--<artifactId>nsq-client</artifactId>-->
        <!--<version>1.0.0.RC4</version>-->
      <!--</dependency>-->


      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-all</artifactId>
          <version>5.8.26</version> <!-- 建议使用最新稳定版本 -->
      </dependency>

      <dependency>
        <groupId>com.getsentry.raven</groupId>
        <artifactId>raven-logback</artifactId>
        <version>8.0.3</version>
      </dependency>

      <!--excel-->
      <dependency>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-ooxml</artifactId>
          <version>3.17</version>
      </dependency>
      <dependency>
          <groupId>org.apache.sshd</groupId>
          <artifactId>sshd-sftp</artifactId>
          <version>2.0.0</version>
      </dependency>

      <dependency>
          <groupId>com.jcraft</groupId>
          <artifactId>jsch</artifactId>
          <version>0.1.54</version>
      </dependency>

      <!-- https://mvnrepository.com/artifact/com.trilead/trilead-ssh2 -->
      <dependency>
          <groupId>com.trilead</groupId>
          <artifactId>trilead-ssh2</artifactId>
          <version>1.0.0-build221</version>
      </dependency>

      <!-- https://mvnrepository.com/artifact/commons-fileupload/commons-fileupload -->
      <dependency>
          <groupId>commons-fileupload</groupId>
          <artifactId>commons-fileupload</artifactId>
          <version>1.3</version>
      </dependency>

      <!--携程jar包 添加到本地maven仓库 根据自己添加的路径修改 打包-->
<!--?      <dependency>-->
<!--?        <groupId>com.mycom.xiecheng</groupId>-->
<!--?        <artifactId>openapi</artifactId>-->
<!--?        <version>1.0</version>-->
<!--?      </dependency>-->
      <!-- https://mvnrepository.com/artifact/net.lingala.zip4j/zip4j -->
      <dependency>
          <groupId>net.lingala.zip4j</groupId>
          <artifactId>zip4j</artifactId>
          <version>2.9.1</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
      </dependency>

      <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-all</artifactId>
            <version>1.4.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shiro</groupId>
                    <artifactId>shiro-quartz</artifactId>
                </exclusion>
            </exclusions>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>org.pac4j</groupId>
            <artifactId>pac4j-core</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.pac4j</groupId>
            <artifactId>pac4j-cas</artifactId>
            <version>3.2.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.pac4j</groupId>
            <artifactId>pac4j-jwt</artifactId>
            <version>3.2.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.buji</groupId>
            <artifactId>buji-pac4j</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.pac4j</groupId>
            <artifactId>pac4j-http</artifactId>
            <version>3.2.0</version>
            <scope>compile</scope>
        </dependency>

        <!--开源插件-->
        <dependency>
            <groupId>org.crazycake</groupId>
            <artifactId>shiro-redis</artifactId>
            <version>3.1.0</version>
        </dependency>
      <!--mybatis plus 代码生成器 依赖-->
      <dependency>
          <groupId>com.baomidou</groupId>
          <artifactId>mybatis-plus-generator</artifactId>
          <version>${mybatis-plus-generator}</version>
      </dependency>
      <dependency>
          <groupId>org.apache.velocity</groupId>
          <artifactId>velocity-engine-core</artifactId>
          <version>${velocity-engine-core}</version>
      </dependency>
      <!--mybatis plus-->
      <dependency>
          <groupId>com.baomidou</groupId>
          <artifactId>mybatis-plus-boot-starter</artifactId>
          <version>${mybatis-plus-boot-starter}</version>
      </dependency>
      <dependency>
          <groupId>com.baomidou</groupId>
          <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
          <version>3.2.0</version>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>com.baomidou</groupId>
          <artifactId>mybatis-plus-extension</artifactId>
          <version>3.2.0</version>
      </dependency>
  </dependencies>


</project>
