package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.UserService;
import com.megvii.service.XiechengService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/8/28 21:06
 */
@Service
public class XiechengDimissionOperatorService extends BaseOperatorService {

    @Autowired
    private XiechengDimissionOperatorService xiechengDimissionOperatorService;

    @Autowired
    private UserService userService;

    @Autowired
    private XiechengService xiechengService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        Object userId = paraMap.get("userId");
        if(userId != null){
            return OperateByUserId((String) userId);
        }else {
            List<User> users = userService.getUserByDimissionDate();

            if (users.size() == 0){
                return new OperateResult(1, "没有要处理的离职账号");
            }else{
                StringBuilder msg = new StringBuilder();
                for (int i = 0; i < users.size(); i++){
                    OperateResult operateResult = xiechengDimissionOperatorService.operateOnce(paraMap,users.get(i));
                    msg.append(operateResult.getMsg()+"<br/>");
                }
                return new OperateResult(1,msg.toString());
            }
        }
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        return new OperateResult(1,"");
//        User user = (User)params[0];
//        String msg = xiechengService.syncXiechengInfo(user, false);
//        return new OperateResult(1,msg);
    }

    public OperateResult OperateByUserId(String userId){
        return new OperateResult(1,"");
//        User user = userService.getUserByUserId(userId);
//        if (user != null){
//            String msg = xiechengService.syncXiechengInfo(user, false);
//            return new OperateResult(1,msg);
//        }else {
//            return new OperateResult(1,"人员"+userId+"不存在");
//        }
    }

}
