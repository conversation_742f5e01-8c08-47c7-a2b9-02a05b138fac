package com.megvii.entity.entry;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 与AliyunUserInfo相同 使用多数据源 与旧entity分开 旧代码迁移完成后移除旧entity
 *
 */
@Data
public class EntryUserInfo {
    private int id;
    private String userId;
    private String spell;
    private String cell;
    private String employType;
    private LocalDateTime expectDate;
    private String team;
    private String hrbp;
    private String computer;
    private String os;
    private String oriPassword;
    private String info;
    private String email;
    private String bank;
    private String photoLife;
    private String photoCert;
    private Integer saved;

    private int genderNumber;


}
