<ng-container *ngIf="isShowSelf">
  <nz-breadcrumb>
    <nz-breadcrumb-item> 合同管理 </nz-breadcrumb-item>
    <nz-breadcrumb-item> 新签合同 </nz-breadcrumb-item>
  </nz-breadcrumb>
  <div class="content-block content-item">
    <app-title title="新签合同"></app-title>
    <nz-tabset
      nzSize="large"
      (nzSelectedIndexChange)="selectedIndexChange($event)"
      [nzSelectedIndex]="selectedIndex"
    >
      <nz-tab [nzTitle]="thisWeekTpl">
        <ng-template #thisWeekTpl>
          <nz-badge [nzCount]="thisWeek.total || 0" [nzOffset]="[15, -5]"> 本周入职 </nz-badge>
        </ng-template>
        <app-contract-table type="thisWeek" #thisWeek></app-contract-table>
      </nz-tab>
      <nz-tab [nzTitle]="lastWeekTpl">
        <ng-template #lastWeekTpl>
          <nz-badge [nzCount]="lastWeek.total || 0" [nzOffset]="[15, -5]"> 上周入职 </nz-badge>
        </ng-template>
        <app-contract-table type="lastWeek" #lastWeek></app-contract-table>
      </nz-tab>
      <nz-tab [nzTitle]="notSignTpl">
        <ng-template #notSignTpl>
          <nz-badge [nzCount]="notSign.total || 0" [nzOffset]="[15, -5]"> 已入职未签署 </nz-badge>
        </ng-template>
        <app-contract-table type="notSign" #notSign></app-contract-table>
      </nz-tab>
    </nz-tabset>
  </div>
</ng-container>

<router-outlet (activate)="onActivate($event)" (deactivate)="onDeactivate($event)"> </router-outlet>
