package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.Office365Service;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/8/27 14:43
 */
@Service
public class Office365UpdateProfileOperatorService extends BaseOperatorService {

    @Autowired
    private Office365Service office365Service;

    @Autowired
    private UserService userService;

    @Autowired
    private Office365UpdateProfileOperatorService office365UpdateProfileOperatorService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId=paraMap.get("userId");
        if(userId != null){
            return OperateByUserId((String) userId);
        } else {
            List<User> users = userService.getUserByExpectDate();
            if (users.size() > 0) {
                StringBuilder msg=new StringBuilder();
                for (User user : users) {
                    OperateResult result = office365UpdateProfileOperatorService.operateOnce(paraMap, user);
                    msg.append(result.getMsg()+"<br/>");
                }
                return new OperateResult(1,msg.toString());
            } else {
                return new OperateResult(1, "没有要上传头像的账号");
            }
        }
    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params){
        User user = (User) params[0];
        String result = office365Service.updateUserProfile(user);
        return new OperateResult(1, result);

    }

    public OperateResult OperateByUserId(String userId){
        User user = userService.getUserByUserId(userId);
        if (user != null) {
            String result = office365Service.updateUserProfile(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1,userId + "信息不存在");
        }
    }

}
