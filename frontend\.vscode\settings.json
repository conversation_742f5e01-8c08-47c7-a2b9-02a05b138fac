{
  "files.eol": "\n",
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"],
  "eslint.options": {
    "baseConfig": {
      "extends": [
        "./node_modules/@megvii/conan/conan-lint-env/node_modules/@megvii/eslint-config-conan",
        "./node_modules/@megvii/conan/conan-lint-env/node_modules/@megvii/eslint-config-conan/typescript",
      ]
    },
    "resolvePluginsRelativeTo": "./node_modules/@megvii/conan/conan-lint-env/node_modules/"
  },
  "stylelint.validate": ["css","scss","less","acss"],
  "stylelint.configOverrides": {
    "extends": "stylelint-config-conan/less"
  },
  "stylelint.configBasedir": "./node_modules/@megvii/conan/conan-lint-env/node_modules/",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.fixAll.stylelint": true,
    "source.fixAll.markdownlint": true
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode"
}
