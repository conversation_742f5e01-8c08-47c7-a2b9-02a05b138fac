package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.User;
import lombok.extern.log4j.Log4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
@Log4j
@PropertySource(value = "classpath:mcSms.properties",encoding = "UTF-8")
public class McSMSRestClient {

    Logger logger= LoggerFactory.getLogger(SmsRestClient.class);
    @Value("${ACCESS_KEY_ID}")
    private String accessKeyId;

    @Value("${ACCESS_KEY_SECRET}")
    private String accessKeySecret;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${UNCOMPLETED_NOTICE_TEMPlATE_CODE}")
    private String uncompletedNoticeTemplateCode;

    @Value("${ENTRY_ADDRESS_TEMPlATE_CODE}")
    private String entryAddressTemplateCode;

    @Value("${ENTRY_DAY_TEMPlATE_CODE}")
    private String entryDayTemplateCode;

    @Value("${ENTRY_INFO_TEMPlATE_CODE}")
    private String entryInfoTemplateCode;

    final private String signName = "北京迈驰智行科技";

    public void sendUncompletedNotice(User user,String password) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name",user.getSpell());
        jsonObject.put("username",user.getUserId());
        jsonObject.put("password",password);
        sendMcSMS(user.getCell(),signName,uncompletedNoticeTemplateCode,jsonObject.toJSONString());
    };

    public String sendEntryAddress(User user) throws Exception {
        JSONObject jsonObject = new JSONObject();
        String workBase = user.getWorkBase();
        String address;
        if (workBase.equals("融科")) {
            address = "融科资讯中心A座3层北区前台";
        } else if (workBase.equals("海龙")) {
            address = "海龙大厦H座17层前台";
        } else if (workBase.equals("金隅")) {
            address = "金隅智造S1-1层前台(地址：北京市海淀区建材城中路27号-金隅智造S1-1层)";
        } else {
            return user.getUserId() + "不发送欢迎信息。";
        }
        jsonObject.put("address",address);
        sendMcSMS(user.getCell(),signName,entryAddressTemplateCode,jsonObject.toJSONString());
        return  user.getUserId() + "发送成功";
    };

    public void sendEntryDay(String phoneNumber,String username,String password) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("username",username);
        jsonObject.put("password",password);
        sendMcSMS(phoneNumber,signName,entryDayTemplateCode,jsonObject.toJSONString());
    };

    public void sendEntryInfo(User user, LocalDateTime limitDate) throws Exception {
        String entryDate = DateTimeUtil.date2String4(limitDate);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("entryDate",entryDate);
        jsonObject.put("username",user.getUserId());
        jsonObject.put("password",user.getOriPassword());
        sendMcSMS(user.getCell(),signName,entryInfoTemplateCode,jsonObject.toJSONString());
    };



    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    private static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    private void sendMcSMS(String phoneNumber, String signName, String templateCode,String templateParam) throws Exception {
        Client client = McSMSRestClient.createClient(accessKeyId, accessKeySecret);

        //测试环境短信发给张亮亮
        if(!active.equals("prod")){
            phoneNumber ="15727343789";
        }
        SendSmsRequest sendSmsRequest = new com.aliyun.dysmsapi20170525.models.SendSmsRequest()
                .setPhoneNumbers(phoneNumber)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam(templateParam);
        RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            SendSmsResponse sendSmsResponse =client.sendSmsWithOptions(sendSmsRequest, runtime);
            System.out.println(JSONObject.toJSONString(sendSmsResponse));
            logger.info(phoneNumber + "发送" +templateCode + "成功");
        }  catch (Exception _error) {
            logger.error(phoneNumber + _error.getMessage());
        }
    }

}
