package com.megvii.controller.api;

import com.megvii.entity.opdb.Menu;
import com.megvii.entity.opdb.User;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/9 17:38
 */
@Data
public class LoginVO {

	private String userId;
	private String spell;
	private String team;
	private String workNumber;
	private List<MenuVO> menus;

	public static LoginVO toVO(User user, List<Menu> menus) {
		LoginVO vo = new LoginVO();
		vo.setUserId(user.getUserId());
		vo.setSpell(user.getSpell());
		vo.setWorkNumber(user.getWorkNumber());
		vo.setTeam(user.getTeam().split("-")[user.getTeam().split("-").length - 1]);
		vo.setMenus(MenuVO.toVOs(menus));

		return vo;
	}

}
