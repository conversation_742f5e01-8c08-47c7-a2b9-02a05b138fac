package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/13 16:40
 */
@Service
public class DhrDismissionContractBacklogOperatorService extends BaseOperatorService {

	@Autowired
	private DhrDismissionContractBacklogOperatorService dhrDismissionContractBacklogOperatorService;

	@Autowired
	private DhrService dhrService;

	@Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return dhrDismissionContractBacklogOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = dhrService.getDismissionContractBacklogFromDhr();
        return new OperateResult(1,msg);
    }



}
