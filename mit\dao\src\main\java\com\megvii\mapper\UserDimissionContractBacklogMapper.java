package com.megvii.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.megvii.entity.opdb.UserDimissionContractBacklogPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.megvii.entity.opdb.contract.UserChangeContractBacklog;
import com.megvii.mapper.filter.UserBacklogFilter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
public interface UserDimissionContractBacklogMapper{
    List<UserDimissionContractBacklogPO> selectBacklogByFilter(@Param("filter") UserBacklogFilter filter);

    List<String> selectAllBacklog();

    Boolean addBacklog(UserDimissionContractBacklogPO po);

    UserDimissionContractBacklogPO getBacklogByDismissionId(@Param("dismissionId") String Id);
    Boolean updateBacklogInfo(UserDimissionContractBacklogPO po);
    Boolean updateBacklogflowStatus(UserDimissionContractBacklogPO po);
    Boolean updateContractFile(UserDimissionContractBacklogPO po);
    Boolean deleteOtherBacklog(@Param("dismissionId") String dismissionId,@Param("userId") String userId);

}
