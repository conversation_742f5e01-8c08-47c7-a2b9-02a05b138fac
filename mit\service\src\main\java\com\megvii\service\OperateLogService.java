package com.megvii.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.megvii.entity.opdb.OperateLogDO;
import com.megvii.mapper.OperateLogMapper;
import com.megvii.mapper.filter.PageLogRequestFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作日志service层实现类
 *
 * <AUTHOR>
 * @date 2020/04/17
 */
@Service
public class OperateLogService{

    @Autowired
    private OperateLogMapper operateLogMapper;
    public List<OperateLogDO> pageLogs(PageLogRequestFilter request) {
        return operateLogMapper.getOperateLogs(request);
    }

    public Boolean save(OperateLogDO po){
       return operateLogMapper.save(po);
    }
}
