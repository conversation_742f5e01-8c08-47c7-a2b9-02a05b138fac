package com.megvii.service;

import com.megvii.entity.opdb.MMISMailTemplate;
import com.megvii.mapper.MMISMailTemplateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/10/22 20:16
 */
@Service
public class MMISMailTemplateService {

    @Autowired
    private MMISMailTemplateMapper mmisMailTemplateMapper;

    public MMISMailTemplate getMMISMailTemplateByType(String type) {
        return mmisMailTemplateMapper.selectMailTemplateByType(type);
    }

}
