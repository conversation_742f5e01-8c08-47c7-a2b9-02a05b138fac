package com.megvii.common;

/**
 * String工具
 */
public class StringUtil {


    /**
     * @param src 源字符串 如abc
     * @param len 输出长度  如6
     * @param ch  用来补齐填补的字符，如0
     * @return 最后输出000abc
     */
    public static String padLeft(String src, int len, char ch) {
        int diff = len - src.length();
        if (diff <= 0) {
            return src.substring(src.length()-len,src.length());
        }

        char[] charr = new char[len];
        System.arraycopy(src.toCharArray(), 0, charr, diff, src.length());
        for (int i = 0; i < diff; i++) {
            charr[i] = ch;
        }
        return new String(charr);
    }
    /**
     * @param src 源字符串 如abc
     * @param len 输出长度  如6
     * @param ch  用来补齐填补的字符，如0
     * @return 最后输出abc000
     */
    public static String padRight(String src, int len, char ch) {
        int diff = len - src.length();
        if (diff <= 0) {
            return src.substring(src.length()-len,src.length());
        }

        char[] charr = new char[len];
        System.arraycopy(src.toCharArray(), 0, charr, 0, src.length());
        for (int i = src.length(); i < len; i++) {
            charr[i] = ch;
        }
        return new String(charr);
    }

    /**
     * 首字母大写，in:deleteDate，out:DeleteDate
     */
    public static String upperHeadChar(String in) {
        String head = in.substring(0, 1);
        String out = head.toUpperCase() + in.substring(1, in.length());
        return out;
    }

}
