package com.megvii.service.contract;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.*;
import com.megvii.entity.opdb.contract.SignStatus;
import com.megvii.entity.opdb.contract.UserContractSnapshot;
import com.megvii.entryMapper.EntryMapper;
import com.megvii.service.ItUserInfoInternService;
import com.megvii.service.MMISMegviiConfigService;
import com.megvii.service.TeamService;
import com.megvii.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/26 15:29
 */
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties", encoding = "UTF-8")
@Service
public class ContractRestService {

	Logger logger = LoggerFactory.getLogger(ContractRestService.class);

	@Value("${spring.profiles.active}")
	private String active;
	@Autowired
	private ResourceService resourceService;

	@Autowired
	private MMISMegviiConfigService mmisMegviiConfigService;

	@Autowired
	private ItUserInfoInternService itUserInfoInternService;

	@Autowired
	private EntryMapper entryMapper;

	@Autowired
	private UserService userService;

	@Autowired
	private TeamService teamService;

	@Autowired
	private DhrRestClient dhrRestClient;
	/**
	 * 创建合同需要的body
	 * send 是否立即发送
	 *
	 * @param snapshot
	 * @param send
	 * @return
	 */
	public JSONObject createContractData(UserContractSnapshot snapshot, boolean send, Long documentId) {
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(snapshot.getCompany());

		JSONObject data = new JSONObject();
		data.put("subject", getSubject(snapshot));
		data.put("sn", snapshot.getContractNumber());
		if (snapshot.getContractType().equals("劳动合同")) {
			//如果是续签，取renew_contract_type，否则取contract_type
			if (StringUtils.isNotBlank(snapshot.getSignStatus()) && snapshot.getSignStatus().equals(SignStatus.RE)) {
				data.put("categoryId", companyConfig.getString("renew_contract_type"));
			} else {
				// 获取用户信息，判断是否为ZK人员
				User user = userService.getUserByUserId(snapshot.getUserId());
				if (user != null && user.getCreateTag() != null && user.getCreateTag() == 9) {
					// ZK人员使用contract_type_zk字段，如果不存在则回退使用contract_type
					String categoryId = companyConfig.getString("contract_type_zk");
					if (categoryId == null || categoryId.isEmpty()) {
						categoryId = companyConfig.getString("contract_type");
					}
					data.put("categoryId", categoryId);
				} else {
					// 普通人员使用contract_type字段
					data.put("categoryId", companyConfig.getString("contract_type"));
				}
			}

		} else {
			data.put("categoryId", companyConfig.getString("intern_contract_type"));
		}

		if (documentId != null) {
			JSONArray array = new JSONArray();
			array.add(documentId);
			data.put("documents", array);
		}

		data.put("send", send);
		if (snapshot.getEndDate() != null) {
			data.put("endTime", DateTimeUtil.date2String(snapshot.getEndDate()));
		}
		JSONArray signatories = new JSONArray();
		signatories.add(createCompanySignatories(snapshot.getCompany()));
		signatories.add(createSignatories(snapshot.getSpell(), snapshot.getUserId(), snapshot.getCertNo(), documentId));

		data.put("signatories", signatories);
		data.put("documentParams", createParams(snapshot, companyConfig));
		data.put("tenantName", snapshot.getCompany());

		return data;
	}
	/**
	 * 创建证明类合同需要的body
	 *
	 * @param JsonObject
	 * @return
	 */
	public JSONObject createProveContractData(JSONObject JsonObject) {
		JSONObject data = new JSONObject();
		data.put("subject", getProveSubject(Integer.valueOf(JsonObject.getString("xtype"))));
		data.put("categoryId", getProveCategoryId(JsonObject));
		data.put("send", true);
		JSONArray signatories = new JSONArray();
		signatories.add(createCompanySignatories(JsonObject.getString("Concode")));
		data.put("signatories", signatories);
		data.put("documentParams", createProveParams(JsonObject));
		data.put("tenantName", JsonObject.getString("contract"));

		return data;
	}



	/**
	 * 创建合同需要的body(New)
	 * send 是否立即发送
	 *
	 * @param snapshot
	 * @param send
	 * @return
	 */
	public JSONObject createNewSignContractData(UserContractSnapshot snapshot, boolean send, Long documentId, Long offerDocumentId) {
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(snapshot.getCompany());

		JSONObject data = new JSONObject();
		data.put("subject", getSubject(snapshot));
		data.put("sn", snapshot.getContractNumber());
		if (snapshot.getContractType().equals("劳动合同")) {
			// 获取用户信息，判断是否为ZK人员
			User user = userService.getUserByUserId(snapshot.getUserId());
			if (user != null && user.getCreateTag() != null && user.getCreateTag() == 9) {
				// ZK人员使用contract_type_zk字段，如果不存在则回退使用contract_type
				String categoryId = companyConfig.getString("contract_type_zk");
				if (categoryId == null || categoryId.isEmpty()) {
					categoryId = companyConfig.getString("contract_type");
				}
				data.put("categoryId", categoryId);
			} else {
				// 普通人员使用contract_type字段
				data.put("categoryId", companyConfig.getString("contract_type"));
			}
		} else {
			data.put("categoryId", companyConfig.getString("intern_contract_type"));
		}
		JSONArray array = new JSONArray();
		if (offerDocumentId != null) {
			array.add(offerDocumentId);
		}
//		array.add(offerDocumentId);
		if (documentId != null) {
			array.add(documentId);
		}
		data.put("documents", array);


		data.put("send", send);
		if (snapshot.getEndDate() != null) {
			data.put("endTime", DateTimeUtil.date2String(snapshot.getEndDate()));
		}
		JSONArray signatories = new JSONArray();
		signatories.add(createCompanySignatories(snapshot.getCompany()));
		signatories.add(createNewSignatories(snapshot.getSpell(), snapshot.getUserId(), snapshot.getCertNo(), documentId, offerDocumentId));

		data.put("signatories", signatories);
		data.put("documentParams", createContractParams(snapshot, companyConfig));
		data.put("tenantName", snapshot.getCompany());

		return data;
	}

	public JSONObject createChangeContractData(UserContractSnapshot snapshot, boolean send, Long documentId) {
		JSONObject data = new JSONObject();
		data.put("subject", getSubject(snapshot));
		data.put("sn", snapshot.getContractNumber());
		if (SignStatus.CHC.equals(snapshot.getSignStatus())) {//如果是合同转签
			String newCompanyName = getCompanyNameByCode(snapshot.getNewContract());
			String oldCompanyName = getCompanyNameByCode(snapshot.getContract());
			JSONObject companyConfig1 = mmisMegviiConfigService.getCompanyConfigByName(newCompanyName);
			data.put("categoryId", companyConfig1.getString("chc_contract_type"));
			data.put("documentParams", createCHCContractParams(snapshot, companyConfig1));
			JSONArray signatories = new JSONArray();
			signatories.add(createCompanySignatories(newCompanyName));
			signatories.add(createCompanySignatories2(oldCompanyName));
			signatories.add(createChangeSignatories(snapshot.getSpell(), snapshot.getUserId(), snapshot.getCertNo(), documentId));
			data.put("signatories", signatories);
			data.put("tenantName", newCompanyName);
		} else {//如果是合同变更
			JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(snapshot.getCompany());
			data.put("categoryId", companyConfig.getString("ch_contract_type"));
			data.put("documentParams", createCHContractParams(snapshot, companyConfig));
			JSONArray signatories = new JSONArray();
			signatories.add(createCompanySignatories(snapshot.getCompany()));
			signatories.add(createSignatories(snapshot.getSpell(), snapshot.getUserId(), snapshot.getCertNo(), documentId));
			data.put("signatories", signatories);
			data.put("tenantName", snapshot.getCompany());
		}

		if (documentId != null) {
			JSONArray array = new JSONArray();
			array.add(documentId);
			data.put("documents", array);
		}

		data.put("send", send);
		if (snapshot.getEndDate() != null) {
			data.put("endTime", DateTimeUtil.date2String(snapshot.getEndDate()));
		}


		return data;
	}
	/**
	 * 创建离职合同需要的body
	 * send 是否立即发送 false
	 *
	 * @param snapshot
	 * @param send
	 * @return
	 */
	public JSONObject createDismissionContractData(UserDimissionContractSnapshotPO snapshot, List<UserDimissionContractSnapshotExtendPO> extendPOS, boolean send, Long documentId,User user,UserDimissionContractBacklogPO backlog,JSONObject dismissionStaff) {
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(snapshot.getCompany());
		User login = (User)SecurityUtils.getSubject().getPrincipal();
		JSONObject data = new JSONObject();
		data.put("subject", "离职相关文件");
		data.put("sn", snapshot.getContractNumber());
		String categoryId = getCategoryId(snapshot, backlog);
		data.put("categoryId", categoryId);
		data.put("send", send);
		//todo 暂时写死斯凡为发起人
		data.put("creatorName", login.getSpell());
//		data.put("creatorName", "杨斯凡");
		data.put("creatorContact", login.getCell());
//		data.put("creatorContact", "15011215084");
		JSONArray signatories = new JSONArray();
		signatories.add(createSignatories(snapshot.getSpell(), snapshot.getUserId(), snapshot.getCertNo(), documentId));
		signatories.add(createCompanySignatories(snapshot.getCompany()));
		data.put("signatories", signatories);
		data.put("documentParams", createDimissionContractParams(snapshot, companyConfig, extendPOS,user,backlog,dismissionStaff));
		data.put("tenantName", snapshot.getCompany());

		return data;
	}
	/**
	 * 根据条件获取对应的合同分类id  https://wiki.megvii-inc.com/pages/viewpage.action?pageId=*********
	 * @param JsonObject
	 * @return
	 */
	private String getProveCategoryId(JSONObject JsonObject){
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(JsonObject.getString("contract"));
		String xtype = JsonObject.getString("xtype");
		String templateBaseType = JsonObject.getString("Templatebasetype");
		// 是否满年
		boolean ismynString = false;
		if( !JsonObject.getString("ismyn").equals("null")&& !JsonObject.getString("ismyn").equals("")){
			ismynString = Integer.parseInt(JsonObject.getString("ismyn"))>13;
		}

		if(xtype.equals("3")){
			return companyConfig.getString("internship_contract_type");
		}else if (xtype.equals("2")){
			return companyConfig.getString("incumbency_contract_type");
		}else if (xtype.equals("1")){
			if(templateBaseType.equals("税前年收入")&& !ismynString){
				return companyConfig.getString("income_type1");
			}else if(templateBaseType.equals("税后月收入")||(ismynString&&(templateBaseType.equals("税前年收入")||templateBaseType.equals("税后年收入")))){
				return companyConfig.getString("income_type2");
			}
			return companyConfig.getString("income_type3");
		}
		return "";
	}

	/**
	 * 根据条件获取对应的分类id  https://wiki.megvii-inc.com/pages/viewpage.action?pageId=*********
	 * @param snapshot
	 * @param backlog
	 * @return
	 */
	public String getCategoryId(UserDimissionContractSnapshotPO snapshot,UserDimissionContractBacklogPO backlog){
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(snapshot.getCompany());
		if(backlog.getCompensation()==1){//有补偿金
			if(backlog.getCompetition()==1){//有竞业协议
				if(backlog.getShares()==1){//有股权
					if(snapshot.getStockManage()==1){//保留股权
						if(snapshot.getSpecialCompany()==1){//有特定公司
							return companyConfig.getString("dismission_type7");
						}else {//无特定公司
							return companyConfig.getString("dismission_type5");
						}
					}else {//放弃股权
						if(snapshot.getSpecialCompany()==1){//有特定公司
							return companyConfig.getString("dismission_type8");
						}else {//无特定公司
							return companyConfig.getString("dismission_type6");
						}
					}
				}else{//无股权
					if(snapshot.getSpecialCompany()==1){//有特定公司
						return companyConfig.getString("dismission_type2");
					}else {//无特定公司
						return companyConfig.getString("dismission_type1");
					}
				}
			}else {//无竞业协议
				if(backlog.getShares()==1){//有股权
					if(snapshot.getStockManage()==1){//保留股权
						return companyConfig.getString("dismission_type4");
					}else {//放弃股权
						return companyConfig.getString("dismission_type3");
					}
				}else{//无股权
					return companyConfig.getString("dismission_type9");
				}
			}
		}else {//无补偿金
			if(backlog.getCompetition()==1){//有竞业协议
				if(backlog.getShares()==1){//有股权
					if(snapshot.getStockManage()==1){//保留股权
						if(snapshot.getSpecialCompany()==1){//有特定公司
							return companyConfig.getString("dismission_type16");
						}else {//无特定公司
							return companyConfig.getString("dismission_type14");
						}
					}else {//放弃股权
						if(snapshot.getSpecialCompany()==1){//有特定公司
							return companyConfig.getString("dismission_type17");
						}else {//无特定公司
							return companyConfig.getString("dismission_type15");
						}
					}
				}else{//无股权
					if(snapshot.getSpecialCompany()==1){//有特定公司
						return companyConfig.getString("dismission_type11");
					}else {//无特定公司
						return companyConfig.getString("dismission_type10");
					}
				}
			}else {//无竞业协议
				if(backlog.getShares()==1){//有股权
					if(snapshot.getStockManage()==1){//保留股权
						return companyConfig.getString("dismission_type12");
					}else {//放弃股权
						return companyConfig.getString("dismission_type13");
					}
				}else{//无股权
					return companyConfig.getString("dismission_type18");
				}
			}
		}
	}
	/**
	 * 编辑离职合同草稿，组装数据
	 * @param snapshot
	 * @param extendPOS
	 * @param send
	 * @param documentId
	 * @return
	 */
	public JSONObject editDismissionContractData(UserDimissionContractSnapshotPO snapshot, List<UserDimissionContractSnapshotExtendPO> extendPOS, boolean send, Long documentId,User user,UserDimissionContractBacklogPO backlog,JSONObject dismissionStaff) {
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(snapshot.getCompany());

		JSONObject data = new JSONObject();
		data.put("id", snapshot.getContractId());
		data.put("subject", "离职相关文件");
		data.put("sn", snapshot.getContractNumber());
		JSONArray signatories = new JSONArray();
		signatories.add(createSignatories(snapshot.getSpell(), snapshot.getUserId(), snapshot.getCertNo(), documentId));
		signatories.add(createCompanySignatories(snapshot.getCompany()));
		data.put("signatories", signatories);
		data.put("documentParams", createDimissionContractParams(snapshot, companyConfig, extendPOS,user,backlog,dismissionStaff));

		return data;
	}


	/**
	 * 转签盖章
	 * @param snapshot
	 * @param documentId
	 * @param contractId
	 * @return
	 */
	public JSONObject signByCompany(UserContractSnapshot snapshot, String documentId,String contractId){
		JSONObject data = new JSONObject();
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(snapshot.getCompany());
		data.put("contractId",contractId);
		data.put("tenantName", snapshot.getCompany());
		JSONArray stampers = new JSONArray();
		JSONObject company = new JSONObject();
		company.put("documentId",documentId);
		company.put("type","SEAL_CORPORATE");
		company.put("sealId",companyConfig.getString("seal_id"));
		company.put("page","1");
		company.put("x","0.1");
		company.put("y","0.1");
		stampers.add(company);
		data.put("stampers",stampers);
		return data;
	}

	/**
	 * 离职合同盖章拼装参数
	 * @param companyName
	 * @param documentIds
	 * @param contractId
	 * @return
	 */
	public JSONObject signDismissionByCompany(String companyName, JSONArray documentIds,String contractId){
		JSONObject data = new JSONObject();
		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName(companyName);
		data.put("contractId",contractId);
		data.put("tenantName", companyName);
		JSONArray stampers = new JSONArray();
		for (Object o : documentIds) {
			JSONObject object = (JSONObject)o;
			JSONObject company = new JSONObject();
			company.put("documentId",object.getString("id"));
			company.put("type","SEAL_CORPORATE");
			company.put("sealId",companyConfig.getString("seal_id"));
//			company.put("page","1");
//			company.put("x","0.1");
//			company.put("y","0.1");
			stampers.add(company);
		}

		data.put("stampers",stampers);
		return data;
	}

	/**
	 * 根据公司code eg：101获取公司名称
	 * @param code
	 * @return
	 */
	public String getCompanyNameByCode(String code){
		JSONArray data = resourceService.getResourceJsonFromRedis("CN_CONTRACT");
		if (data == null) {
			data = mmisMegviiConfigService.getJsonarrayByKey("CN_CONTRACT");
		}
		for (Object o : data) {
			JSONObject company = JSONObject.parseObject(o.toString());
			if(company.get("code").equals(code)){
				return company.get("name").toString();
			}
		}
		return "";
	}

	public String getWorkCityByCode(String code){
		JSONArray data = resourceService.getResourceJsonFromRedis("CN_LOCATION");
		if (data == null) {
			data = mmisMegviiConfigService.getJsonarrayByKey("CN_LOCATION");
		}
		for (Object o : data) {
			JSONObject company = JSONObject.parseObject(o.toString());
			if(company.get("code").equals(code)){
				return company.get("name").toString();
			}
		}
		return "";
	}

	/**
	 * 创建证明类合同需要的参数包
	 *
	 * @param jsonObject
	 * @return
	 */

     private JSONArray createProveParams(JSONObject jsonObject) {
	       JSONArray array = new JSONArray();
		   array.add(createParam("badge", jsonObject.getString("badge")));
		   array.add(createParam("name", jsonObject.getString("name")));
		   array.add(createParam("Gender", jsonObject.getString("Gender")));
		   array.add(createParam("Depid", jsonObject.getString("Depid")));
		   array.add(createParam("job", jsonObject.getString("job")));
		   array.add(createParam("joindate", jsonObject.getString("joindate")));
		   array.add(createParam("certtype", jsonObject.getString("certtype")));
		   array.add(createParam("CertNo", jsonObject.getString("CertNo")));
		   array.add(createParam("Templatebasetype", jsonObject.getString("Templatebasetype")));
		   array.add(createParam("xValueDx", jsonObject.getString("xValueDx")));
		   array.add(createParam("xValue", jsonObject.getString("xValue")));
		   array.add(createParam("NZJ", jsonObject.getString("NZJ")));
		   array.add(createParam("Yterm", jsonObject.getString("Yterm")));
		   array.add(createParam("Mterm", jsonObject.getString("Mterm")));
		   array.add(createParam("Dterm", jsonObject.getString("Dterm")));
		   array.add(createParam("LXR", jsonObject.getString("LXR")));
		   array.add(createParam("Concode", jsonObject.getString("Concode")));
		   array.add(createParam("contract", jsonObject.getString("contract")));
		   array.add(createParam("ConAddress", jsonObject.getString("ConAddress")));
		   array.add(createParam("Contel", jsonObject.getString("Contel")));
		   array.add(createParam("YBprac", jsonObject.getString("YBprac")));
		   array.add(createParam("Yeprac", jsonObject.getString("Yeprac")));
		   return array;
     }
	/**
	 * 创建合同需要的参数包
	 *
	 * @param snapshot
	 * @param companyConfig
	 * @return
	 */
	private JSONArray createParams(UserContractSnapshot snapshot, JSONObject companyConfig) {
		JSONArray array = new JSONArray();
		array.add(createParam("jf", snapshot.getCompany()));
		array.add(createParam("jfdz", companyConfig.getString("address")));
		array.add(createParam("jffzr", companyConfig.getString("legal")));
		array.add(createParam("yf", snapshot.getSpell()));
		array.add(createParam("zjlx", snapshot.getCertType()));
		if(active.equals("prod")) {
			array.add(createParam("sfzhm", snapshot.getCertNo()));
		}else{
			array.add(createParam("sfzhm", "429001199411230411"));
		}
		array.add(createParam("yfzz", snapshot.getAddress()));
		array.add(createParam("gz", snapshot.getPosition()));
		array.add(createParam("gzdd", snapshot.getEmployBase()));
		if (snapshot.getContractAttribute().equals("无固定期")) {
			array.add(createParam("yd", "二"));
			array.add(createParam("htkssj", DateTimeUtil.date2String3(snapshot.getStartDate())));
			array.add(createParam("syqkssj", snapshot.getProbationStartDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationStartDate()) : "/"));
			array.add(createParam("syqjssj", snapshot.getProbationEndDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationEndDate()) : "/"));

			array.add(createParam("htksrq", "/"));
			array.add(createParam("htjsrq", "/"));
			array.add(createParam("syqksrq", "/"));
			array.add(createParam("syqjsrq", "/"));
		} else {
			array.add(createParam("yd", "一"));
			array.add(createParam("htksrq", DateTimeUtil.date2String3(snapshot.getStartDate())));
			array.add(createParam("htjsrq", snapshot.getEndDate() != null ? DateTimeUtil.date2String3(snapshot.getEndDate()) : "/"));
			array.add(createParam("syqksrq", snapshot.getProbationStartDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationStartDate()) : "/"));
			array.add(createParam("syqjsrq", snapshot.getProbationEndDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationEndDate()) : "/"));

			array.add(createParam("htkssj", "/"));
			array.add(createParam("syqkssj", "/"));
			array.add(createParam("syqjssj", "/"));
		}

		return array;
	}

	/**
	 * 创建新签合同需要的参数包(新)
	 * 正式员工：原合同、信息收集表、offer
	 * 实习生： 原合同、信息收集表、offer、实习协议、在读声明
	 *
	 * @param snapshot
	 * @param companyConfig
	 * @return
	 */
	private JSONArray createContractParams(UserContractSnapshot snapshot, JSONObject companyConfig) {
		JSONArray array = new JSONArray();
		array.add(createParam("jf", snapshot.getCompany()));
		array.add(createParam("jfdz", companyConfig.getString("address")));
		array.add(createParam("jffzr", companyConfig.getString("legal")));
		array.add(createParam("yf", snapshot.getSpell()));
		array.add(createParam("zjlx", snapshot.getCertType()));
		if(active.equals("prod")) {
			array.add(createParam("sfzhm", snapshot.getCertNo()));
		}else{
			array.add(createParam("sfzhm", "429001199411230411"));
		}
		array.add(createParam("yfzz", snapshot.getAddress()));
		array.add(createParam("gz", snapshot.getPosition()));
		array.add(createParam("gzdd", snapshot.getEmployBase()));
		if (snapshot.getContractAttribute().equals("无固定期")) {
			array.add(createParam("yd", "二"));
			array.add(createParam("htkssj", DateTimeUtil.date2String3(snapshot.getStartDate())));
			array.add(createParam("syqkssj", snapshot.getProbationStartDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationStartDate()) : "/"));
			array.add(createParam("syqjssj", snapshot.getProbationEndDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationEndDate()) : "/"));

			array.add(createParam("htksrq", "/"));
			array.add(createParam("htjsrq", "/"));
			array.add(createParam("syqksrq", "/"));
			array.add(createParam("syqjsrq", "/"));
		} else {
			array.add(createParam("yd", "一"));
			array.add(createParam("htksrq", DateTimeUtil.date2String3(snapshot.getStartDate())));
			array.add(createParam("htjsrq", snapshot.getEndDate() != null ? DateTimeUtil.date2String3(snapshot.getEndDate()) : "/"));
			array.add(createParam("syqksrq", snapshot.getProbationStartDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationStartDate()) : "/"));
			array.add(createParam("syqjsrq", snapshot.getProbationEndDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationEndDate()) : "/"));

			array.add(createParam("htkssj", "/"));
			array.add(createParam("syqkssj", "/"));
			array.add(createParam("syqjssj", "/"));
		}

		EntryUserInfo entryUserInfo = entryMapper.selectBasicInfoByUserid(snapshot.getUserId());
		JSONObject info = JSONObject.parseObject(StringEscapeUtils.unescapeJava(entryUserInfo.getInfo().replace(" ", "")));
		if (info == null) {
			throw new RuntimeException("IT_user_info中人员数据为空");
		}
		User user = userService.getUserByUserId(snapshot.getUserId());
		if (user == null){
			throw new RuntimeException("IT_user中人员数据为空");
		}

		//信息收集表
		//内部账户nbzh
		array.add(createParam("nbzh", snapshot.getUserId()));
		//部门bm
		Team team = teamService.getTeamByCode(user.getTeamCode());
		array.add(createParam("bm", team.getName()));
		//入职日期rzrq
		array.add(createParam("rzrq",  DateTimeUtil.date2String3(user.getExpectDate())));
		//手机号sjh
		array.add(createParam("sjh", user.getCell()));
		//姓名xm,如果user的certName不为null并且不为空，则设置为certName，否则设置为spell
		array.add(createParam("xm", user.getCertName()!=null?user.getCertName():user.getSpell() ));
		//员工类型yglx
		array.add(createParam("yglx", user.getEmployType()));
		//操作系统czxt
		array.add(createParam("czxt", user.getOs()));
		//性别xb
		array.add(createParam("xb", getGender(info)));
		//办公机bgj
		array.add(createParam("bgj", user.getComputer()));
		//国籍gj
		array.add(createParam("gj", dhrRestClient.getCountryValue(info.getString("COUNTRY"))));
		//民族mz
		array.add(createParam("mz", dhrRestClient.getNationValue(info.getString("NATION"))));
		//政治面貌zzmm
		array.add(createParam("zzmm", dhrRestClient.getPartyValue(info.getString("PARTY"))));
		//出生日期csrq
		array.add(createParam("csrq", DateTimeUtil.date2String5(info.getString("BIRTHDAY").split("T")[0])));
		//证件号zjh
		array.add(createParam("zjh", info.getString("CERTNO")));
		//籍贯jg
		array.add(createParam("jg", dhrRestClient.getCityValue(info.getString("RESIDENT"))));
		//户口性质hkxz
		array.add(createParam("hkxz", dhrRestClient.getResidentTypeValue(info.getString("RESIDENTTYPE"))));
		//户口所在地hkszd
		array.add(createParam("hkszd", info.getString("RESIDENTADDRESS")));
		//现居住地址xjzdz
		array.add(createParam("xjzdz", info.getString("ADDRESS")));
		//婚姻状况hyzk
		array.add(createParam("hyzk", dhrRestClient.getMarriageValue(info.getString("MARRIAGE"))));
		//生育状况syzk
		array.add(createParam("syzk", dhrRestClient.getFertilitystateValue(info.getString("FERTILITYSTATE"))));
		//健康状况jkzk
		array.add(createParam("jkzk", dhrRestClient.getHealthValue(info.getString("HEALTH"))));
		//T恤尺码txcm
		array.add(createParam("txcm", dhrRestClient.getTSizeValue(info.getString("TSSIZE"))));
		//兴趣爱好xqah
		array.add(createParam("xqah", info.getString("HOBBY")));
		//首次参加工作日期sccjgzrq
		array.add(createParam("sccjgzrq", StringUtils.isNotEmpty(info.getString("WORKBEGINDATE"))?DateTimeUtil.date2String5(info.getString("WORKBEGINDATE").split("T")[0]):""));
		//是否签订竞业协议sfqdjyxy
		array.add(createParam("sfqdjyxy", StringUtils.equals(info.getString("IFJEXZXY"),"1")?"有":"无"));
		//有无犯罪记录ywfzjl
		array.add(createParam("ywfzjl", StringUtils.equals(info.getString("YWFZJL"),"1")?"有":"无"));
		//签订日期qdrq
		array.add(createParam("qdrq", "/"));

		//主要紧急联系人	zyjjlxr
		//主要紧急联系人-姓名	column1
		//主要紧急联系人-关系	column2
		//主要紧急联系人-手机号 column3
		JSONArray zyjjlxrArray = new JSONArray();
		JSONObject zyjjlxrjsonObject = new JSONObject();
		zyjjlxrjsonObject.put("column1",info.getString("LINKNAME"));
		zyjjlxrjsonObject.put("column2",dhrRestClient.getRelationValue(info.getString("LINKRELATION")));
		zyjjlxrjsonObject.put("column3",info.getString("LINKPHONE"));
		zyjjlxrArray.add(zyjjlxrjsonObject);
		array.add(createParam("zyjjlxr", zyjjlxrArray.toJSONString()));

//		银行开户信息	yhkhxx
//		银行开户信息-开户行	column1
//		银行开户信息-银行卡号	column2
//		银行开户信息-其他   	column3
		JSONArray yhkhxxArray = new JSONArray();
		JSONObject yhkhxxjsonObject = new JSONObject();
		yhkhxxjsonObject.put("column1",(info.getString("BANKNAME") !=null && !info.getString("BANKNAME").equals(""))?dhrRestClient.getBankValue(info.getString("BANKNAME")):"");
		yhkhxxjsonObject.put("column2",info.getString("BANKACCOUNT"));
		yhkhxxjsonObject.put("column3",info.getString("OPENINGBANK"));
		yhkhxxArray.add(yhkhxxjsonObject);
		array.add(createParam("yhkhxx", yhkhxxArray.toJSONString()));

		//教育经历	jyjl
		//教育经历-起止时间column1
		//教育经历-学校	column2
		//教育经历-专业	column3
		//教育经历-学历	column4
		//教育经历-学位	column5
		JSONArray educationList=info.getJSONArray("edu");
		JSONArray jyjlArray = new JSONArray();
		for (int i = 0; i < educationList.size(); i++) {
			JSONObject education = educationList.getJSONObject(i);
			JSONObject jyjljsonObject = new JSONObject();
			jyjljsonObject.put("column1",education.getString("BEGINDATE")+" - "+education.getString("ENDDATE"));
			jyjljsonObject.put("column2",education.getString("SCHOOLNAME"));
			jyjljsonObject.put("column3",education.getString("MAJOR"));
			jyjljsonObject.put("column4",dhrRestClient.getEduTypeValue(education.getString("EDUTYPE")));
			jyjljsonObject.put("column5",dhrRestClient.getDegreeTypeValue(education.getString("DEGREENAME")));
			jyjlArray.add(jyjljsonObject);
		}

		array.add(createParam("jyjl", jyjlArray.toJSONString()));

		//工作经历	gzjl
		//工作经历-起止时间	column1
		//工作经历-公司名称	column2
		//工作经历-职务		column3
		//工作经历-证明人		column4
		//工作经历-联系方式	column5
		JSONArray employeds=info.getJSONArray("working");
		JSONArray gzjlArray = new JSONArray();
		for (int i = 0; i < employeds.size(); i++) {
			JSONObject employed = employeds.getJSONObject(i);
			JSONObject gzjljsonObject = new JSONObject();
			gzjljsonObject.put("column1",employed.getString("BEGINDATE")+" - "+employed.getString("ENDDATE"));
			gzjljsonObject.put("column2",employed.getString("COMPANY"));
			gzjljsonObject.put("column3",employed.getString("JOB"));
			gzjljsonObject.put("column4",employed.getString("REFERENCE"));
			gzjljsonObject.put("column5",employed.getString("TEL"));
			gzjlArray.add(gzjljsonObject);
		}
		array.add(createParam("gzjl", gzjlArray.toJSONString()));

		//家庭信息	jtxx
		//家庭信息-姓名		column1
		//家庭信息-关系		column2
		//家庭信息-出生日期	column3
		JSONArray familys = info.getJSONArray("family");
		JSONArray jtxxArray = new JSONArray();
		for (int i = 0; i < familys.size(); i++) {
			JSONObject family = familys.getJSONObject(i);
			JSONObject jtxxjsonObject = new JSONObject();
			jtxxjsonObject.put("column1",family.getString("FNAME"));
			jtxxjsonObject.put("column2",dhrRestClient.getRelationValue(family.getString("RELATION")));
			jtxxjsonObject.put("column3",family.getString("BIRTHDAY"));
			jtxxArray.add(jtxxjsonObject);
		}
		array.add(createParam("jtxx", jtxxArray.toJSONString()));


		if (!snapshot.getContractType().equals("劳动合同")) {
			// 在读声明
			// 判断入学时间是否小于毕业时间
			if (snapshot.getAdmissionTime().getTime() >= snapshot.getGraduationTime().getTime()) {
				throw new RuntimeException("入学时间不能小于毕业时间");
			}
			//员工姓名	ygxm
			array.add(createParam("ygxm", snapshot.getSpell()));
			//学号	xh
			array.add(createParam("xh", snapshot.getStudentNumber()));
			//入学日期	rxrq
			array.add(createParam("rxrq", DateTimeUtil.date2String4(snapshot.getAdmissionTime())));
			//学校	xx
			array.add(createParam("xx", snapshot.getSchool()));
			//毕业时间	bysj
			array.add(createParam("bysj", DateTimeUtil.date2String4(snapshot.getGraduationTime())));
			//身份证号码	sfzhm
			array.add(createParam("sfzhm",snapshot.getCertNo()));


			//实习协议
			//甲方	jf
			array.remove("jf");
			//甲方地址	jfdz
			array.remove("jfdz");
			//姓名	xm
			array.add(createParam("xm", snapshot.getSpell()));
			//性别	xb
			array.add(createParam("xb", getGender(info)));
			//身份证号	sfzh
			array.add(createParam("sfzh", snapshot.getCertNo()));
			//户口所在地	hkszd
			array.add(createParam("hkszd", info.getString("RESIDENTADDRESS")));
			//常住地址	czdz
			array.add(createParam("czdz", snapshot.getAddress()));
			//亲属联系方式	qslxfs
			array.add(createParam("qslxfs", info.getString("emergency_contact_phone")));
			//所在院校	szyx
			array.add(createParam("szyx", snapshot.getSchool()));
			//毕业时间	bysj
			array.add(createParam("bysj", DateTimeUtil.date2String4(snapshot.getGraduationTime())));
			//实习单位	sxdw
			array.add(createParam("sxdw", "北京旷视科技有限公司"));
			//实习岗位	sxgw
			array.add(createParam("sxgw", "实习生"));
			//实习期限	sxqx
			array.add(createParam("sxqx", "6"));
			//实习开始时间	sxkssj
			LocalDateTime startDate = user.getExpectDate();
			//实习开始时间+6个月-1天
			LocalDateTime endDate = startDate.plusMonths(6).minusDays(1);
			array.add(createParam("sxkssj", DateTimeUtil.date2String3(startDate)));
			//实习结束时间	sxjssj
			array.add(createParam("sxjssj", DateTimeUtil.date2String3(endDate)));
			//顺延时间	sysj
			array.add(createParam("sysj", "6"));
			//实习津贴	sxjt
			array.add(createParam("sxjt", snapshot.getInternshipAllowance()));
			//手机号码	sjhm
			array.add(createParam("sjhm", snapshot.getContractCell()));
			//出生日期	csrq
			array.add(createParam("csrq", DateTimeUtil.date2String5(info.getString("BIRTHDAY").split("T")[0])));
		}


		return array;
	}
	/**
	 *
	 * 从info信息中获取性别
	 * @param jsonInfo
	 * @return
	 */
	private String getGender(JSONObject jsonInfo){
		String gender=jsonInfo.getString("personal_gender");
		return gender==null?"男":(gender.equals("F")?"女":"男");
	}

	/**
	 * 合同变更请求参数
	 * @param snapshot
	 * @param companyConfig
	 * @return
	 */
	private JSONArray createCHContractParams(UserContractSnapshot snapshot, JSONObject companyConfig) {
		JSONArray array = new JSONArray();
		array.add(createParam("jf", snapshot.getCompany()));
		array.add(createParam("jfdz", companyConfig.getString("address")));
		array.add(createParam("yf", snapshot.getSpell()));
		array.add(createParam("sfzhm", snapshot.getCertNo()));
		array.add(createParam("htqsrq", DateTimeUtil.date2String3(snapshot.getStartDate())));
		array.add(createParam("gzdd", getWorkCityByCode(snapshot.getNewWorkCity())));
		array.add(createParam("yhtqsrq", StringUtils.isEmpty(snapshot.getConBeginDate())?"/":getYMDDate(snapshot.getConBeginDate())));
		array.add(createParam("yhtjsrq", StringUtils.isEmpty(snapshot.getConEndDate())?"/":getYMDDate(snapshot.getConEndDate())));
		array.add(createParam("xhtqsrq", StringUtils.isEmpty(snapshot.getNewConBeginDate())?"/":getYMDDate(snapshot.getNewConBeginDate())));
		array.add(createParam("xhtjsrq", StringUtils.isEmpty(snapshot.getNewConEndDate())?"/":getYMDDate(snapshot.getNewConEndDate())));
		array.add(createParam("sxrq", snapshot.getEffectDate()!=null?DateTimeUtil.date2String3(snapshot.getEffectDate()):"/"));
		return array;
	}

	/**
	 * 合同转签请求参数
	 * @param snapshot
	 * @param companyConfig  新公司配置
	 * @return
	 */
	private JSONArray createCHCContractParams(UserContractSnapshot snapshot,JSONObject companyConfig) {
		JSONArray array = new JSONArray();
		String oldCompanyName = getCompanyNameByCode(snapshot.getContract());
		String newCompanyName = getCompanyNameByCode(snapshot.getNewContract());
		array.add(createParam("jfz", oldCompanyName));
		array.add(createParam("yf", snapshot.getSpell()));
		array.add(createParam("jf", newCompanyName));
		array.add(createParam("sfzhm", snapshot.getCertNo()));
		array.add(createParam("htqsrq", getYMDDate(snapshot.getNewConBeginDate())));
		array.add(createParam("yhtqsrq", getYMDDate(snapshot.getConBeginDate())));
		array.add(createParam("gz", snapshot.getPosition()));
		array.add(createParam("jfdz", companyConfig.getString("address")));
		array.add(createParam("jffzr", companyConfig.getString("legal")));
		array.add(createParam("zjlx", snapshot.getCertType()));
		array.add(createParam("yfzz", snapshot.getAddress()));
		array.add(createParam("gzdd", getWorkCityByCode(snapshot.getNewWorkCity())));


		if (snapshot.getContractAttribute().equals("无固定期")) {
			array.add(createParam("yd", "二"));
			array.add(createParam("htjsrq", "/"));
			array.add(createParam("syqkssj", snapshot.getProbationStartDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationStartDate()) : "/"));
			array.add(createParam("syqjssj", snapshot.getProbationEndDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationEndDate()) : "/"));
			array.add(createParam("syqksrq", "/"));
			array.add(createParam("syqjsrq", "/"));
			array.add(createParam("htkssj", getYMDDate(snapshot.getNewConBeginDate())));
			array.add(createParam("htksrq", "/"));
			array.add(createParam("yhtjsrq", "/"));
		} else {
			array.add(createParam("yd", "一"));
			array.add(createParam("htjsrq", getYMDDate(snapshot.getNewConEndDate())));
			array.add(createParam("syqkssj", "/"));
			array.add(createParam("syqjssj", "/"));
			array.add(createParam("syqksrq", snapshot.getProbationStartDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationStartDate()) : "/"));
			array.add(createParam("syqjsrq", snapshot.getProbationEndDate() != null ? DateTimeUtil.date2String3(snapshot.getProbationEndDate()) : "/"));
			array.add(createParam("htkssj", "/"));
			array.add(createParam("htksrq", getYMDDate(snapshot.getNewConBeginDate())));
			array.add(createParam("yhtjsrq", StringUtils.isEmpty(snapshot.getConEndDate())?"/":getYMDDate(snapshot.getConEndDate())));
		}
		return array;
	}

	/**
	 * 离职合同请求参数
	 * @param snapshot
	 * @param companyConfig
	 * 字段名称	传值
	 * 甲方	jf
	 * 乙方	yf
	 * 身份证号	sfzh
	 * 联系地址	lxdz
	 * 联系方式	lxfs
	 * 劳动解除日期	ldjcrq
	 * 社保公积金缴纳截止月	sbgjjjnjzr
	 * 公积金缴纳截止月	gjjjn
	 * 离职日期	lzrq
	 * 部门	bm
	 * 岗位	gw
	 * 入职日期	rzrq
	 * 补偿金额小写	bcjexx
	 * 补偿金额大写	bcjedx
	 * 补偿金发放日期	bcjdbrq
	 * 竞业开始日期	jyksrq
	 * 竞业结束日期	jyjsrq
	 * 私人邮箱	sryx
	 * 竞业公司	jygs
	 * 公司名称	gsmc
	 * 员工姓名	ygxm
	 * 股份签署日期	gfqsrq
	 * SERS保留总数	sersblzs
	 * SERS作废份数	serszffs
	 * 明细表1--生效日期	sxrq
	 * 明细表1---SERS份数	sersfs
	 * 明细表1---等待期	ddq
	 * 月基本薪资	yjbgz
	 * 当月未出勤工作日	dywcq
	 * 有薪假结算	yxjjs
	 * 餐费补贴	cfbz
	 * 其他	qt
	 * 税前工资总额	sqgzze
	 * 扣除社会保险及公积金个人部分	kcshbx
	 * 付款日期	fkrq
	 * 有薪假期天数	yxjqts
	 * 离职月出勤工资	lzycqgz
	 * 备注	bz
	 * @return
	 */
	private JSONArray createDimissionContractParams(UserDimissionContractSnapshotPO snapshot, JSONObject companyConfig, List<UserDimissionContractSnapshotExtendPO> extendPOS,User user,UserDimissionContractBacklogPO backlog,JSONObject dismissionStaff) {
		JSONArray array = new JSONArray();
		array.add(createParam("jf", snapshot.getCompany()));
		array.add(createParam("yf", snapshot.getSpell()));
		array.add(createParam("sfzh", user.getCertNo()));
		array.add(createParam("lxdz", user.getAddress()));
		array.add(createParam("lxfs", user.getCell()));
		array.add(createParam("ldjcrq", snapshot.getDismissionDate()==null?"/":DateTimeUtil.date2String6(snapshot.getDismissionDate())));
		array.add(createParam("sbgjjjnjzr",StringUtils.isEmpty(backlog.getSocialLastTime())?"/":backlog.getSocialLastTime()));
		array.add(createParam("gjjjn",StringUtils.isEmpty(backlog.getAccumulationLastTime())?"/":backlog.getAccumulationLastTime()));
		array.add(createParam("lzrq", snapshot.getDismissionDate()==null?"/":DateTimeUtil.date2String6(snapshot.getDismissionDate())));
		array.add(createParam("bm", backlog.getTopTeam()));
		String position = mmisMegviiConfigService.getPositionByCode(user.getPosition());
		array.add(createParam("gw", StringUtils.isEmpty(position)?"/":position));
		array.add(createParam("rzrq", backlog.getInitialStartTime()==null?"/":DateTimeUtil.dateToString(backlog.getInitialStartTime())));
		array.add(createParam("bcjexx", dismissionStaff.getString("lzbc")));
		array.add(createParam("bcjedx", dismissionStaff.getString("lzbcdx")));
		array.add(createParam("bcjdbrq", dismissionStaff.getString("fkrq")));
		array.add(createParam("bcjffrq", dismissionStaff.getString("sjfkrq")));
		array.add(createParam("jyksrq", backlog.getCompetitionStart()==null?"/":DateTimeUtil.date2String6(backlog.getCompetitionStart())));
		array.add(createParam("jyjsrq", backlog.getCompetitionEnd()==null?"/":DateTimeUtil.date2String6(backlog.getCompetitionEnd())));
		array.add(createParam("sryx", backlog.getSelfEmail()));
		array.add(createParam("jygs", StringUtils.isEmpty(snapshot.getSpecialCompanyTxt())?"/":snapshot.getSpecialCompanyTxt()));
		array.add(createParam("gsmc", snapshot.getCompany()));
		array.add(createParam("ygxm", snapshot.getSpell()));
		array.add(createParam("gfqsrq", snapshot.getSersSignDate()==null?"/":DateTimeUtil.date2String6(snapshot.getSersSignDate())));
		array.add(createParam("sersblzs", snapshot.getSersKeepNumber()==null?"/":snapshot.getSersKeepNumber().toString()));
		array.add(createParam("serszffs", snapshot.getSersInvalidNumber()==null?"/":snapshot.getSersInvalidNumber().toString()));
		JSONArray mxArray = new JSONArray();
		for (UserDimissionContractSnapshotExtendPO extendPO : extendPOS) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("sxrq",extendPO.getEffectDate()==null?"/":DateTimeUtil.date2String6(extendPO.getEffectDate()));
			jsonObject.put("sersfs",extendPO.getSersNum());
			jsonObject.put("ddq",extendPO.getWaitTime());
			mxArray.add(jsonObject);
		}
		array.add(createParam("qqmx", mxArray.toJSONString()));
		array.add(createParam("yjbgz", dismissionStaff.getString("sqyx")));
		array.add(createParam("dywcq", dismissionStaff.getString("wcqts")));
		array.add(createParam("yxjjs", dismissionStaff.getString("yxjjs")));
		array.add(createParam("cfbz", dismissionStaff.getString("cfbt")));
		array.add(createParam("qt", dismissionStaff.getString("qtsq")));
		array.add(createParam("sqgzze", dismissionStaff.getString("sqgzze")));
		array.add(createParam("kcshbx", dismissionStaff.getString("wxyj")));
		array.add(createParam("fkrq", dismissionStaff.getString("fkrq")));
		array.add(createParam("yxjqts", dismissionStaff.getString("yxjts")));
		array.add(createParam("lzycqgz", dismissionStaff.getString("sqsr")));
		array.add(createParam("bz", dismissionStaff.getString("Remark")));
		return array;
	}
	/**
	 * 创建参数data方法
	 *
	 * @param code
	 * @param value
	 * @return
	 */
	private JSONObject createParam(String code, String value) {
		JSONObject data = new JSONObject();
		data.put("name", code);
		data.put("value", value);

		return data;
	}
//	private JSONObject createParam(String code, JSONArray value) {
//		JSONObject data = new JSONObject();
//		data.put("name", code);
//		data.put("value", value);
//		return data;
//	}

	/**
	 * 将2021-01-12 变成2021年01月12日
	 * @param date
	 * @return
	 */
	private String getYMDDate(String date){
		if(StringUtils.isEmpty(date)){
			return "/";
		}
		return date.replaceFirst("-","年").replaceFirst("-","月").concat("日");
	}

	/**
	 * 创建签署人
	 *
	 * @param spell
	 * @param userId
	 * @param cardNo
	 * @return
	 */
	private JSONObject createSignatories(String spell, String userId, String cardNo, Long documentId) {
		JSONObject userData = new JSONObject();
		userData.put("tenantType", "PERSONAL");

		// 获取user对象，然后获取priEmail和createTag字段
		User user = userService.getUserByUserId(userId);
		Integer createTag = user.getCreateTag();
//		String priEmail = user.getPriEmail();


		if(active.equals("prod")){
			String email = "";
			if(createTag!=null && createTag == 9){
//			email = priEmail;
				email = userId + "@qianli-drive.com";
				userData.put("contact", email);
//				userData.put("contact", user.getCell());
//				userData.put("contactType", "MOBILE");
				userData.put("tenantName", spell);
				userData.put("receiverName", spell);
				userData.put("cardNo", cardNo);
			}else{
				email = userId + "@megvii.com";
				userData.put("contact", email);
				userData.put("tenantName", spell);
				userData.put("receiverName", spell);
				userData.put("cardNo", cardNo);
			}
		}else{
			userData.put("contact",  "<EMAIL>");
			userData.put("tenantName", "张亮亮");
			userData.put("receiverName", "张亮亮");
			userData.put("cardNo", cardNo);
		}
		userData.put("serialNo", 2);


		//有附件的加入附件文档ID
		if (documentId != null) {
			JSONArray actions = new JSONArray();
			actions.add(createAction(documentId));
			userData.put("actions", actions);
		}

		return userData;
	}

	/**
	 * 创建Offer签署人
	 *
	 * @param spell
	 * @param userId
	 * @param cardNo
	 * @return
	 */
	private JSONObject createNewSignatories(String spell, String userId, String cardNo, Long documentId, Long offerDocumentId) {
		JSONObject userData = new JSONObject();
		userData.put("tenantType", "PERSONAL");
		User user = userService.getUserByUserId(userId);
		Integer createTag = user.getCreateTag();
		String priEmail = user.getPriEmail();
		logger.info("新签的createTag:{}", createTag);
		logger.info("新签的spell:{}", spell);

		//createTag = 9: 使用私人邮箱priEmail，否则使用userId + "@megvii.com"
//		String email = "";
//		if(createTag == 9){
//			email = priEmail;
//		}else{
//			email = userId + "@megvii.com";
//		}

		if(active.equals("prod")){
			if(createTag!=null && createTag == 9){
				userData.put("contact", user.getCell());
				userData.put("contactType", "MOBILE");
				userData.put("tenantName", spell);
				userData.put("receiverName", spell);
				userData.put("cardNo", cardNo);
			}else{
				userData.put("contact", userId + "@megvii.com");
				userData.put("tenantName", spell);
				userData.put("receiverName", spell);
				userData.put("cardNo", cardNo);
			}

		}else{
			if(createTag!=null && createTag == 9){
				userData.put("contact", "15727343789");
				userData.put("contactType", "MOBILE");
				userData.put("tenantName", "张亮亮");
				userData.put("receiverName", "张亮亮");
				userData.put("cardNo", cardNo);
			}else{
				userData.put("contact",  "<EMAIL>");
				userData.put("tenantName", "张亮亮");
				userData.put("receiverName", "张亮亮");
				userData.put("cardNo", cardNo);
			}

		}
		userData.put("serialNo", 2);


		//有附件的加入附件文档ID
		if (documentId != null) {
			JSONArray actions = new JSONArray();
			actions.add(createAction(documentId));
			userData.put("actions", actions);
		}
		//有offer附件的加入附件文档ID
		if (offerDocumentId != null) {
			JSONArray offerActions = new JSONArray();
			offerActions.add(createOfferAction(offerDocumentId));
			userData.put("actions", offerActions);
		}

		return userData;
	}
	private JSONObject createChangeSignatories(String spell, String userId, String cardNo, Long documentId) {
		JSONObject userData = new JSONObject();
		userData.put("tenantType", "PERSONAL");
		User user = userService.getUserByUserId(userId);
		Integer createTag = user.getCreateTag();

		if(active.equals("prod")){
			if(createTag!=null && createTag == 9){
				userData.put("contact", userId + "@qianli-drive.com");
			}else{
				userData.put("contact", userId + "@megvii.com");
			}
			userData.put("contact", userId + "@megvii.com");
			userData.put("tenantName", spell);
			userData.put("receiverName", spell);
			userData.put("cardNo", cardNo);
		}else{
			userData.put("contact",  "<EMAIL>");
			userData.put("tenantName", "张亮亮");
			userData.put("receiverName", "张亮亮");
			userData.put("cardNo", cardNo);
		}
		userData.put("serialNo", 3);


		//有附件的加入附件文档ID
		if (documentId != null) {
			JSONArray actions = new JSONArray();
			actions.add(createAction(documentId));
			userData.put("actions", actions);
		}

		return userData;
	}
	/**
	 * 创建签署动作
	 *
	 * @param documentId
	 * @return
	 */
	private JSONObject createAction(Long documentId) {
		JSONObject data = new JSONObject();
		data.put("type", "PERSONAL");
		data.put("name", "签名");
		data.put("serialNo", 2);

		JSONArray locations = new JSONArray();
		locations.add(createRect(documentId));

		data.put("locations", locations);
		return data;
	}
	/**
	 * 创建offer签署动作
	 *
	 * @param documentId
	 * @return
	 */
	private JSONObject createOfferAction(Long documentId) {
		JSONObject data = new JSONObject();
		data.put("type", "PERSONAL");
		data.put("name", "offer签名");
		data.put("serialNo", 2);

		JSONArray locations = new JSONArray();
		locations.add(createOfferRect(documentId));

		data.put("locations", locations);
		return data;
	}

	/**
	 * 创建签署位置定位
	 *
	 * @param documentId
	 * @return
	 */
	private JSONObject createRect(Long documentId) {
		JSONObject data = new JSONObject();
		data.put("documentId", documentId);
		data.put("rectType", "SEAL_PERSONAL");
		data.put("page", 1);
		data.put("offsetX", 10.0);
		data.put("offsetY", 10.0);

		return data;
	}

	/**
	 * 创建Offer签署位置定位
	 *
	 * @param documentId
	 * @return
	 */
	private JSONObject createOfferRect(Long documentId) {
		JSONObject data = new JSONObject();
		data.put("documentId", documentId);
		data.put("rectType", "SEAL_PERSONAL");
		data.put("keyword", "签字");
		data.put("keywordIndex", -1);
//		data.put("page", 1);
//		data.put("offsetX", 10.0);
//		data.put("offsetY", 10.0);

		return data;
	}

	/**
	 * 生成证明类合同id
	 *
	 * @return
	 */

	private String generateCategoryId() {
		return null;
	}


	/**
	 * 创建公司签署人
	 *
	 * @param companyName
	 * @return
	 */
	private JSONObject createCompanySignatories(String companyName) {

		JSONObject data = new JSONObject();
		data.put("tenantType", "INNER_COMPANY");
		data.put("tenantName", companyName);
		data.put("serialNo", 1);

		return data;
	}
	private JSONObject createCompanySignatories2(String companyName) {

		JSONObject data = new JSONObject();
		data.put("tenantType", "INNER_COMPANY");
		data.put("tenantName", companyName);
		data.put("serialNo", 2);

		return data;
	}
	/**
	 * 获取标题
	 *
	 * @param snapshot
	 * @return
	 */
	public String getSubject(UserContractSnapshot snapshot) {
		if (!snapshot.getContractType().equals("劳动合同")) {
			return "实习生入职协议";
		} else {
			String title = snapshot.getSignStatus().replace("合同", "");
			return title + "-劳动合同及其他入职协议";
		}
	}

	public String getProveSubject(Integer xtype){
		if(xtype == 1){
			return "收入证明";

		}else if (xtype == 2){
			return "在职证明";

		} else if (xtype == 3){
			return "实习证明";

		}  else {
			return "其他证明";
		}
	}
}
