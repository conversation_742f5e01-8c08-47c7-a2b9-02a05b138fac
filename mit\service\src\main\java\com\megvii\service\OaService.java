package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DhrRestClient;
import com.megvii.client.OaRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.PasswordGenerator;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.TempUser;
import com.megvii.entity.opdb.User;
import java.time.LocalDateTime;
import java.util.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 * @date 2019/10/23 14:52
 */
@Service
@PropertySource(value = "classpath:oa.${spring.profiles.active}.properties",encoding = "UTF-8")
public class OaService {

    private Logger logger= LoggerFactory.getLogger(OaService.class);

    private static Map<String, String> siteHrMap = new HashMap<>();
    static {
        siteHrMap.put("103", "lvxinfang");
        siteHrMap.put("113", "lvxinfang");
        siteHrMap.put("116", "lvxinfang");
        siteHrMap.put("122", "lvxinfang");
        siteHrMap.put("124", "lvxinfang");
        siteHrMap.put("126", "lvxinfang");
        siteHrMap.put("118", "weiyuhui");
        siteHrMap.put("119", "weiyuhui");
        siteHrMap.put("120", "qisi");
    }

    @Value("${OA_WORKFLOWID_ENTRY}")
    private String entryFlowId;

    @Value("${OA_WORKFLOWID_DISMISS}")
    private String dismissFlowId;

    @Value("${OA_WORKFLOWID_REPORT}")
    private String reportFlowId;

    @Value("${OA_WORKFLOWID_INTERN_DISMISS}")
    private String internDismissFlowId;

    @Value("${E9_OA_DIMISSION_WORKFLOWID}")
    private String e9DimissionWorkflowId;

    @Value("${E9_OA_DIMISSION_WORKBASEID}")
    private String e9DimissionWorkbaseId;

    @Value("${E9_OA_DIMISSION_EMPLOYTYPEID}")
    private String e9DimissionEmploytypeId;

    @Autowired
    private OaRestClient oaRestClient;

    @Autowired
    private UserService userService;

    @Autowired
    private DhrRestClient dhrRestClient;

    @Autowired
    private CheckService checkService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private DingdingService dingdingService;

    @Autowired
    private TeamService teamService;

    /**
     * OA回传MIT 修改user信息
     * @param map
     * @return
     */
    public User updateUserInfoFromOA(Map<String, String> map) throws Exception {

        String workNumber = map.get("work_number");

        User user = userService.getUserByWorkNumber(workNumber);
        if (user == null) {
            throw new Exception("OA回传MIT更新人员信息错误:" + workNumber + "不存在");
        }

        user.setUserId(map.get("user_id").replaceAll(" ", ""));
        user.setSpell(map.get("spell"));
        user.setCell(map.get("cell"));
        user.setEmployType(map.get("employ_type"));
        user.setCompany(dhrRestClient.getCompanyCode(map.get("company"))!=null?dhrRestClient.getCompanyCode(map.get("company")):null);
        user.setWorkBase(map.get("work_base"));
        user.setSales(dhrRestClient.getSalesCode(map.get("sales"))!=null?dhrRestClient.getSalesCode(map.get("sales")):"");

        Team team = teamService.getTeamByCode(map.get("team"));
        if (team != null) {
            user.setTeamId(team.getId());
            user.setTeam(team.getName());
            user.setTeamCode(team.getSfCode());
            user.setHrbp(team.getHrg());
        }

        Map<String,Object> dateMap = checkService.getHireDate(DateTimeUtil.date2String2(DateTimeUtil.string2DateYMD(map.get("expect_date"))), user.getEmployType());

        user.setExpectDate((LocalDateTime) dateMap.get("expectDate"));
        user.setRegularDate((LocalDateTime) dateMap.get("regularDate"));
        if ((boolean) dateMap.get("isDelay")) {
            mailSendService.sendDelayEntryMail(user);
            dingdingService.sendEntryDelayHook(user.getSpell(), user.getUserId(), map.get("expect_date"), DateTimeUtil.date2String((LocalDateTime) dateMap.get("expectDate")));
        }

        user.setMemo(map.get("memo")!=null?map.get("memo").replaceAll("<br>", "\n"):null);
        user.setMentor(map.get("mentor"));
        user.setBuddy(map.get("buddy"));
        user.setEmployBase(map.get("employ_base"));
        user.setComputer(map.get("computer"));
        user.setOs(map.get("os"));
        user.setDuty(map.get("duty"));
        user.setEntryFlow("信息收集");
        user.setUpdater(map.get("xgr"));
        user.setTitle(map.get("title")!=null?map.get("title"):"无");
        user.setReferTitle(map.get("refer_title"));
        user.setSource(map.get("source"));
        user.setSeatNumber(map.get("seat_number"));
        user.setSequence(Integer.valueOf(map.get("sequence")));
        user.setOriPassword(PasswordGenerator.productPassword());
        user.setContractCount(0);
        user.setContractStatus("无合同");

        /**
         * 奇奇怪怪的需求 以后找机会删掉 临时写
         *
         */
        if (user.getCompany() != null && user.getCompany().equals("123")) {
            List<String> list = new ArrayList<>();
            list.add("上海");
            list.add("杭州");
            list.add("苏州");
            list.add("南京");
            list.add("宁波");
            list.add("青岛");

            if (list.contains(user.getEmployBase())) {
                user.setSiteHrg("lvxinfang");
            } else {
                user.setSiteHrg(null);
            }
        } else {
            user.setSiteHrg(siteHrMap.get(user.getCompany()));
        }

        String flowTimes = user.getFlowTime();
        if (flowTimes==null || "".equals(flowTimes)){
            user.setFlowTime(","+ DateTimeUtil.date2String4(LocalDateTime.now()));
        }else {
            user.setFlowTime(flowTimes+","+ DateTimeUtil.date2String4(LocalDateTime.now()));
        }

        //给标注人员打标签 OA只能用汉字 是
        if ("是".equals(map.get("biaozhu"))) {
            user.setCreateTag(4);
            //标注不可以有实习生，否则钉钉逻辑会出错。不进行强校验
            if (user.getEmployType().indexOf("实习生")!=-1) {
                logger.error(user.getUserId() + "根据规则标注不可以有实习生");
                mailSendService.sendMail("标注中心员工类型错误", user.getUserId() + "根据规则标注不可以有实习生", "<EMAIL>");
            }
        } else {
        	//仅作用于推送时是标注但回传时不是 兼容竞业人员标签
        	if (user.getCreateTag() != null && user.getCreateTag() == 4) {
        		user.setCreateTag(null);
			}
        }


        userService.updateUser(user);
        logger.info(user.getUserId() +  "*****OA回传MIT保存成功");

        //MIT不存 预入职系统使用
        user.setPriEmail(map.get("email"));

        return user;

    }




    /**
     * 创建OA离职流程 实习生和正式员工流程id不同
     * @param workNumber
     * @param dimissionDate
     * @return
     */
    public boolean createDismissFlow(String workNumber, LocalDateTime dimissionDate, boolean isIntern) {
        MultiValueMap<String, String> params= new LinkedMultiValueMap<String, String>();
        params.add("sqr", workNumber);
        params.add("workcode", workNumber);
        params.add("sqrq", DateTimeUtil.date2String(dimissionDate));

        try {
            if (isIntern) {
                return oaRestClient.sendOAFlow(params, internDismissFlowId, null);
            } else {
                return oaRestClient.sendOAFlow(params, dismissFlowId, null);
            }
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 创建入职流程
     * @param user
     * @return
     */
    public boolean createEntryFlow(User user) {
        MultiValueMap<String, String> params= new LinkedMultiValueMap<String, String>();
        params.add("zwm", user.getSpell());
        params.add("xmqp", user.getUserId());
        params.add("sxs", "");
        params.add("work_number", user.getWorkNumber());
        params.add("yqrzrq", user.getExpectDate()!=null?DateTimeUtil.date2String(user.getExpectDate()):"");
        params.add("rzlx", user.getEmployType());
        params.add("syqjz", user.getRegularDate()!=null?DateTimeUtil.date2String(user.getRegularDate()):"");
        params.add("ygyx", user.getPriEmail()!=null?user.getPriEmail():"");
        params.add("ckdj", user.getReferTitle()!=null?user.getReferTitle():"");
        params.add("zpqd", user.getSource()!=null?user.getSource():"");
        params.add("zj", user.getTitle()!=null?user.getTitle():"");
        params.add("znzd", user.getDuty()!=null?user.getDuty():"");
        params.add("rzdd", user.getEmployBase()!=null?user.getEmployBase():"");
        params.add("lxfs", user.getCell()!=null?user.getCell():"");
        params.add("team", user.getTeamCode());
        params.add("gzdd", user.getWorkBase()!=null?user.getWorkBase():"");
        params.add("xulie", user.getSequence()!=null?user.getSequence().toString():"");

        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
            params.add("biaozhu", "0");
        } else {
            params.add("biaozhu", "1");
        }

        User hrg = userService.getUserByUserId(user.getHrbp());
        params.add("hrg", hrg!=null&&!"已禁用".equals(hrg.getStatus())?hrg.getWorkNumber():"");

        User siteHr = userService.getUserByUserId(user.getSiteHrg());
        params.add("sitehr", siteHr!=null&&!"已禁用".equals(siteHr.getStatus())?siteHr.getWorkNumber():"");

        User buddy = userService.getUserByUserId(user.getBuddy());
        params.add("buddy", buddy!=null&&!"已禁用".equals(buddy.getStatus())?buddy.getWorkNumber():"");

        User mentor = userService.getUserByUserId(user.getMentor());
        params.add("sjzg", mentor!=null&&!"已禁用".equals(mentor.getStatus())?mentor.getWorkNumber():"");

        try {
            return oaRestClient.sendOAFlow(params, entryFlowId, null);
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 创建E9离职流程
     * @param map
     * @return
     */
    public boolean sendE9DimissionFlow(Map<String, String> map) {
        try {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
            params.add("params", createE9DimissionData(map).toJSONString());

            return oaRestClient.sendE9OAFlow(params);
        } catch (Exception e) {
            logger.error(map.get("worknumber") + "发起E9OA流程失败:" + e.getMessage());
            return false;
        }
    }


    /**
     * 组装E9离职流程data
     * @param map
     * @return
     */
    private JSONObject createE9DimissionData(Map<String, String> map) {
        JSONArray mainData = new JSONArray();
        mainData.add(setE9Param("lzr", "$&*#_" + map.get("worknumber")));
        mainData.add(setE9Param("gh", map.get("worknumber")));
        mainData.add(setE9Param("sqrq", map.get("dimissionDate")));
        mainData.add(setE9Param("jjr", "$&*#_" + map.get("handoverWorknumber")));
        mainData.add(setE9Param("gq", "fieldid_" + e9DimissionWorkbaseId + "_" + map.get("workBase")));
        mainData.add(setE9Param("yglx", "fieldid_" + e9DimissionEmploytypeId + "_" + map.get("employType")));

        JSONObject data = new JSONObject();
        data.put("mainData", mainData);
        data.put("requestName", "员工离职流程-" + map.get("dimissionDate") + "-" + map.get("spell"));
        data.put("workflowId", e9DimissionWorkflowId);
        data.put("createUser", "$&*#_199999");

        return data;
    }


    /**
     * set E9 mainData
     * @param key
     * @param val
     * @return
     */
    private JSONObject setE9Param(String key, String val) {
        JSONObject data = new JSONObject();
        data.put("fieldName", key);
        data.put("fieldValue", val);
        return data;
    }

    public String getTempUser(String spell, String cell) {
        return oaRestClient.getTempUserOAId(spell, cell);
    }


    public String outsourcingUpdate(TempUser tempUser, String id) {
        return oaRestClient.outsourcingUpdate(tempUser, id);
    }

}
