package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
//import com.github.brainlag.nsq.NSQProducer;
//import com.github.brainlag.nsq.exceptions.NSQException;
import com.megvii.entity.opdb.GAGroup;
import com.megvii.service.GAGroupService;
import java.util.UUID;
import java.util.concurrent.TimeoutException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Component
@PropertySource(value = "classpath:nsq.${spring.profiles.active}.properties",encoding = "UTF-8")
public class NsqProduceClient {

    private final static String ADD_ACTION = "group_add";
    private final static String DEL_ACTION = "group_del";

    @Value("${NSQ_ADDRESS}")
    private String address;

    @Value("${NSQ_DAEMON_PORT}")
    private Integer nsqDaemonPort;

    @Value("${NSQ_KMS_TOPIC}")
    private String nsqKmsTopic;

    @Value("${OFFICE_MAIL_ADDRESS}")
    private String mailAddress;

    @Autowired
    private GAGroupService gaGroupService;


//    /**
//     * nsq消息队列添加
//     * @param userName
//     * @param groupName
//     * @throws NSQException
//     * @throws TimeoutException
//     */
//    public void nsqGroupAdd(String userName, String groupName) throws NSQException, TimeoutException {
//        GAGroup gaGroup = gaGroupService.getGroupByEmail(groupName + mailAddress);
//
//        if (gaGroup != null && gaGroup.getGid() != null) {
//            JSONObject data = new JSONObject();
//            data.put("username", userName);
//            data.put("groupname", groupName);
//            data.put("gid", gaGroup.getGid());
//            data.put("action", ADD_ACTION);
//            data.put("uuid", UUID.randomUUID().toString());
//
//            produce(data.toString());
//        }
//    }
//
//    public void nsqGroupDel(String userName, String groupName) throws NSQException, TimeoutException {
//        GAGroup gaGroup = gaGroupService.getGroupByEmail(groupName + mailAddress);
//
//        if (gaGroup != null && gaGroup.getGid() != null) {
//            JSONObject data = new JSONObject();
//            data.put("username", userName);
//            data.put("groupname", groupName);
//            data.put("gid", gaGroup.getGid());
//            data.put("action", DEL_ACTION);
//            data.put("uuid", UUID.randomUUID().toString());
//
//            produce(data.toString());
//        }
//    }
//
//
//    private NSQProducer getProducer() {
//        NSQProducer producer = new NSQProducer();
//        producer.addAddress(address, nsqDaemonPort).start();
//        return producer;
//    }
//
//    private void produce(String msg) throws NSQException, TimeoutException {
//        this.getProducer().produce(nsqKmsTopic, msg.getBytes());
//    }

}

