package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.GitService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/3/19 17:40
 */
@Service
public class SyncGitMemberOperatorService extends BaseOperatorService {

	@Autowired
	private SyncGitMemberOperatorService syncGitMemberOperatorService;

	@Autowired
	private GitService gitService;

	 @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return syncGitMemberOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = gitService.syncGroupMember();
        return new OperateResult(1,msg);
    }


}
