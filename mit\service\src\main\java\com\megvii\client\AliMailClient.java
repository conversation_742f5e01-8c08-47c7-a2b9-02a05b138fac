package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

// 阿里云邮件服务Client,对接文档：https://alidocs.dingtalk.com/i/p/nb9XJlNqOArrQGyA/docs/OmLa2Gg0l5BW75amAlrwJvQAdYbnKEek?dontjump=true
@Component
@PropertySource(value = "classpath:config/aliMail.${spring.profiles.active}.properties", encoding = "UTF-8")
public class AliMailClient {
    Logger logger= LoggerFactory.getLogger(AliMailClient.class);

    @Value("${CLIENT_ID}")
    private String mailClientId;

    @Value("${CLIENT_SECRET}")
    private String mailClientSecret;

    @Value("${ACCESS_TARGET}")
    private String accessTarget;
    
    private final String mailMainUrl = "https://alimail-cn.aliyuncs.com";

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    RedisTemplate<String, String> redisTemplate;

    /**
     * 通过group_id 获取group的成员的内部账号
     * @param groupId
     * @return
     */
    public JSONObject getGroupMemberBuyId(String groupId){
        String mailGroupMemberUrl="/v0/ud/getMailGroupsInfo";
        HttpHeaders headers=getAuthHeader();
        MultiValueMap<String, String> data=new LinkedMultiValueMap<>();
//        data.add("group_id",groupId);
        HttpEntity<MultiValueMap<String, String>> httpEntity=new HttpEntity<>(data,headers);
        ResponseEntity<JSONObject> responseEntity= restTemplate.postForEntity(mailMainUrl + mailGroupMemberUrl,httpEntity,JSONObject.class);
        return responseEntity.getBody();
    }

    /**
     * 优先从redis中获取
     * @return  带Authorization认证的请求头
     */
    public HttpHeaders getAuthHeader(){
        Object token=redisTemplate.opsForValue().get("mitexecutor_alimail_token");
        if (token==null){
            token = getToken();
            String tokenString =  ((JSONObject) token).getString("token_type") + " " + ((JSONObject)token).getString("access_token");
            redisTemplate.opsForValue().set("mitexecutor_alimail_token",tokenString);
            redisTemplate.expire("mitexecutor_alimail_token",Integer.parseInt(((JSONObject)token).getString("expires_in")) -100, TimeUnit.SECONDS);
            token = tokenString;
        }
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("authorization",(String)token);

        return headers;
    }

    /**
     * @return  从阿里邮箱获取token
     */
    public JSONObject getToken(){
        String mailTokenUrl="https://alimail-cn.aliyuncs.com/oauth2/v2.0/token";
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> data=new LinkedMultiValueMap<String, String>();
        data.add("client_id",mailClientId);
        data.add("client_secret",mailClientSecret);
        data.add("grant_type","client_credentials");

        HttpEntity<MultiValueMap<String, String>> httpEntity=new HttpEntity<>(data,headers);
        ResponseEntity<JSONObject> responseEntity= restTemplate.postForEntity(mailTokenUrl,httpEntity,JSONObject.class);
        return responseEntity.getBody();
    }

    /**
     * 获取邮件组列表
     */
    public Map<String, JSONObject> getMailGroupList() {
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        JSONObject param = new JSONObject();
        param.put("offset", "0");
        param.put("length", "10000");
        param.put("fields", new ArrayList<>());

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/getMailGroupList", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return null;
        }
        JSONObject dataObject = jsonObject.getJSONObject("data")!=null?jsonObject.getJSONObject("data"):null;
        if (dataObject==null){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return null;
        }
        JSONArray mailGroups = dataObject.getJSONArray("mailgroups");

        Map<String, JSONObject> res = new HashMap<>();
        for (int i = 0; i < mailGroups.size(); i++) {
            JSONObject group = mailGroups.getJSONObject(i);
            String[] mail = group.getString("address").split("@");
            res.put(mail[0].toLowerCase(), group);
        }
        return res;
    }

    /**
     * 获取邮件组成员
     */
    public List<String> getMailGroupMembers(String address) {
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        JSONObject param = new JSONObject();
        param.put("offset", "0");
        param.put("length", "10000");
        param.put("address", address);

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/getMailGroupMembers", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return null;
        }
        JSONObject dataObject = jsonObject.getJSONObject("data")!=null?jsonObject.getJSONObject("data"):null;
        if (dataObject==null){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return null;
        }
        JSONArray members = dataObject.getJSONArray("members");

        List<String> res = new ArrayList<>();

        for (Object member : members) {
            String[] mail = member.toString().split("@");
            res.add(mail[0].toLowerCase());
        }
        return res;
    }
    /*
     * 添加邮件组成员
     * @param group 邮件组地址 例如：<EMAIL>
     * @param membersList 成员地址列表（可以是帐户email地址，可以是邮件组地址），不能为空列表
     */
    public boolean addMailGroupMembers(String group, List<String> membersList) {
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        JSONObject param = new JSONObject();
        param.put("address", group);
        param.put("members", membersList);

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/addMailGroupMembers", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return false;
        }
        return true;


    }

    /*
     * 删除邮件组成员
     * @param group 邮件组地址 例如：<EMAIL>
     * @param membersList 成员地址列表（可以是帐户email地址，可以是邮件组地址），不能为空列表
     */
    public boolean removeMailGroupMembers(String group, List<String> membersList) {
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        JSONObject param = new JSONObject();
        param.put("address", group);
        param.put("members", membersList);

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/removeMailGroupMembers", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return false;
        }
        return true;


    }

    /*
     * 创建邮件组
     * @param address 邮件组地址  例如：<EMAIL>
     * @param name 邮件组名称 例如：测试邮件组
     */
    public boolean createMailGroup(String address,String name) {
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        JSONObject param = new JSONObject();
        param.put("address", address);
        param.put("name", name);

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/createMailGroup", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return false;
        }
        return true;


    }

    /*
     * 判断帐户是否存在
     * @param email email地址  例如：<EMAIL>
     */
    public boolean getAccountsInfo(String email) {
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        List<String> emails = new ArrayList<>();
        emails.add(email);
        JSONObject param = new JSONObject();
        param.put("emails", emails);
        param.put("fields", new ArrayList<>());

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/getAccountsInfo", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return false;
        }
        JSONObject dataObject = jsonObject.getJSONObject("data")!=null?jsonObject.getJSONObject("data"):null;
        if (dataObject==null || dataObject.getJSONArray("dataList")==null || dataObject.getJSONArray("dataList").size()==0){
            logger.info("邮件不存在:{}",email);
            return false;
        }
        return true;


    }
    /*
     * 删除帐户
     * @param email email地址  例如：<EMAIL>
     */
    public String removeAccounts(String email){
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        List<String> emails = new ArrayList<>();
        emails.add(email);
        JSONObject param = new JSONObject();
        param.put("emails", emails);

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/removeAccounts", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return email+"删除帐户删除失败";
        }
        JSONObject dataObject = jsonObject.getJSONObject("data")!=null?jsonObject.getJSONObject("data"):null;
        if (dataObject==null || dataObject.getJSONArray("success")==null || dataObject.getJSONArray("success").size()==0){
            logger.error("删除失败:{}",email+jsonObject.toJSONString());
            return email+"删除帐户删除失败";
        }
        return email+"删除成功";

    }



    /*
     * 获取邮箱帐户所在的邮件组列表
     * @param member 成员邮箱地址
     */
    public JSONArray getMailGroupsByMember(String member) {
        HttpHeaders headers = getAuthHeader();

        JSONObject access = new JSONObject();
        access.put("accessTarget", accessTarget);

        JSONObject param = new JSONObject();
        param.put("member", member);

        JSONObject data = new JSONObject();
        data.put("access", access);
        data.put("param", param);


        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mailMainUrl+"/v0/ud/getMailGroupsByMember", httpEntity, String.class);

        JSONObject jsonObject = responseEntity.getBody()!=null?JSONObject.parseObject(responseEntity.getBody()):null;
        JSONObject statusObject = jsonObject.getJSONObject("status")!=null?jsonObject.getJSONObject("status"):null;
        if (statusObject==null || !statusObject.getString("statusCode").equals("100")){
            logger.error("阿里云邮件服务返回数据异常:{}",responseEntity.getBody());
            return null;
        }
        JSONObject dataObject = jsonObject.getJSONObject("data")!=null?jsonObject.getJSONObject("data"):null;
        return dataObject!=null?dataObject.getJSONArray("addresses"):null;
    }



}
