<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.entryMapper.EntryMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.entry.EntryUserInfo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="spell" property="spell"/>
        <result column="cell" property="cell"/>
        <result column="employ_type" property="employType"/>
        <result column="expect_date" property="expectDate"/>
        <result column="team" property="team"/>
        <result column="computer" property="computer"/>
        <result column="os" property="os"/>
        <result column="ori_password" property="oriPassword"/>
        <result column="info" property="info"/>
        <result column="hrbp" property="hrbp"/>
        <result column="email" property="email"/>
        <result column="bank" property="bank"/>
        <result column="photo_life" property="photoLife"/>
        <result column="photo_cert" property="photoCert"/>
        <result column="saved" property="saved"/>
    </resultMap>

    <select id="selectBasicInfoById" resultMap="BaseResultMap" >
        SELECT id,user_id, info,photo_life,photo_cert,email,bank,saved  FROM IT_user_info WHERE id = #{id}
    </select>

    <select id="selectBasicInfoByUserid" resultMap="BaseResultMap" >
        SELECT id,user_id,saved,info,email,bank FROM IT_user_info WHERE user_id = #{userId}
    </select>

    <select id="selectUserInfoByUserid" resultMap="BaseResultMap" >
        SELECT id,user_id,ori_password,saved,info,photo_life,photo_cert,email,bank FROM IT_user_info WHERE user_id = #{userId}
    </select>

    <insert id="addEntryUserInfo" parameterType="com.megvii.entity.entry.EntryUserInfo" >
        INSERT INTO IT_user_info (
        id,
        user_id,
        spell,
        cell,
        employ_type,
        expect_date,
        team,
        computer,
        os,
        ori_password,
        info,
        hrbp,
        email,
        bank,
        photo_life,
        photo_cert,
        saved
	    )
	    VALUES
		( 0,
		#{userId},
		#{spell},
		#{cell},
		#{employType},
		#{expectDate},
		#{team},
		#{computer},
		#{os},
		#{oriPassword},
		NULL,
		#{hrbp},
		#{email},
		#{bank},
		NULL,
		NULL,
		NULL )
    </insert>

    <update id="updateEntryUserInfo" parameterType="com.megvii.entity.entry.EntryUserInfo">
        update IT_user_info
        <set>
            <if test="spell != null ">
                spell = #{spell},
            </if>
            <if test="cell != null ">
                cell = #{cell},
            </if>
            <if test="email != null ">
                email = #{email},
            </if>
            <if test="employType != null ">
                employ_type = #{employType},
            </if>
            <if test="expectDate != null ">
                expect_date = #{expectDate},
            </if>
            <if test="team != null ">
                team = #{team},
            </if>
            <if test="computer != null ">
                computer = #{computer},
            </if>
            <if test="os != null ">
                os = #{os},
            </if>
            <if test="oriPassword != null ">
                ori_password = #{oriPassword},
            </if>
            <if test="hrbp != null ">
                hrbp = #{hrbp},
            </if>
            <if test="bank != null ">
                bank = #{bank},
            </if>
            <if test="saved == 0 ">
                saved = null,
            </if>
            <if test="saved != 0 ">
                saved = #{saved},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getPhotoCert" parameterType="STRING" resultType="STRING">
        SELECT photo_cert FROM IT_user_info WHERE user_id = #{userId}
    </select>

    <select id="getPhotoLife" parameterType="STRING" resultType="STRING">
        SELECT photo_life FROM IT_user_info WHERE user_id = #{userId}
    </select>

    <update id="deletePsw" parameterType="STRING" >
        update IT_user_info set ori_password = null where user_id = #{userId}
    </update>

    <select id="getSimpleUserInfoListByExpectDate" resultMap="BaseResultMap" >
        select id,user_id,saved from IT_user_info where (saved is null or saved!=2) and info is not null and expect_date &gt;= #{expectTime}
    </select>

</mapper>
