package com.megvii.service;

import com.megvii.entity.opdb.Team;
import com.megvii.mapper.TeamMapper;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

@Service
@PropertySource(value = "classpath:jk.${spring.profiles.active}.properties",encoding = "UTF-8")
public class TeamService {
    private Logger logger= LoggerFactory.getLogger(TeamService.class);

    @Value("${JK_TEAM_CODE}")
	private String jkTeamCode;

    @Autowired
    private TeamMapper teamMapper;

    public Team getTeamByName(String name){
        return teamMapper.selectTeamByName(name);
    }

    public Team getTeamById(int id){
        return teamMapper.selectTeamById(id);
    }
    public Team getTeamByDingDingId(Integer id) {
        return teamMapper.selectTeamByDingDingId(id);
    }

    public Team getTeamByCode(String code) {
        return teamMapper.selectTeamByCode(code);
//        Team team = new Team();
//        try {
//            team =  teamMapper.selectTeamByCode(code);
//            if (team == null) {
//                team = new Team();
//                return team;
//            }
//        }catch (Exception e)
//        {
//            logger.info("team code = " + code + "不存在");
//        }
//        return team;
    }

    public boolean addTeam(Team team) {
        int result = teamMapper.addTeam(team);
        if (result > 0) {
            return true;
        } else {
            return false;
        }

    }

    public boolean updateTeam(Team team) {
        int result = teamMapper.updateTeamById(team);
        if (result > 0) {
            return true;
        } else {
            return false;
        }
    }

    public List<Team> getAllTeam() {
        return teamMapper.selectAllTeam();
    }

    public List<Team> getActiviteTeam() {
        return teamMapper.selectActiviteTeam();
    }

    /**
     * 通过姓名模糊查询
     * @param name
     * @return
     */
    public List<Team> getTeamsByName(String name) {
        return teamMapper.selectTeamsByName(name);
    }

        /**
     * 通过部门code确认该部门是否属于ZK
     * @param code
     * @return
     */
    public boolean isZKTeam(String code) {
        Team team = teamMapper.selectTeamByCode(code);
        if (team == null) {
            logger.info("team code = " + code + "不存在");
            return false;
        }
        String teamFullCode = team.getFullCode();
        //如果部门全路径中包含JK_TEAM_CODE，则认为该部门属于ZK
        return teamFullCode.contains(jkTeamCode); 
    }

            /**
     * 通过部门code确认该部门是否属于ZK
     * @param fullCode
     * @return
     */
    public boolean isZKTeamByFullCode(String fullCode) {
        //如果部门全路径中包含JK_TEAM_CODE，则认为该部门属于ZK
        return fullCode.contains(jkTeamCode); 
    }


}
