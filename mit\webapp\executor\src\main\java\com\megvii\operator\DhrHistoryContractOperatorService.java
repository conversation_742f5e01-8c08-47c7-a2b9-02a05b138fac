package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import com.megvii.service.contract.ContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/17 18:48
 */
@Service
public class DhrHistoryContractOperatorService extends BaseOperatorService {

    @Autowired
    private DhrHistoryContractOperatorService dhrHistoryContractOperatorService;

    @Autowired
    private DhrService dhrService;

    @Autowired
    private ContractService contractService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return dhrHistoryContractOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        List<String> removeMap = contractService.getAllContractNumber();
        String msg = dhrService.getHistoryContractFromDhr(removeMap);
        return new OperateResult(1,msg);
    }



}
