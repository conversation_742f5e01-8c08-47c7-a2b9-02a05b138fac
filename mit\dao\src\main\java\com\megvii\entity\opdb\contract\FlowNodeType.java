package com.megvii.entity.opdb.contract;

import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * FlowNodeType TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FlowNodeType.java
 * @createTime 2021年05月14日 10:18:00
 * @since 2021-05-14
 */
public enum FlowNodeType {
    OFFER("offer节点"),
    CHECK_INFO("信息检查"),
    OA_FLOW("OA流程"),
    COLLECT_INFO("信息收集"),
    WAIT_ENTRY("等待入职"),
    ENTRY("在职");

    private String value;
    public static final Map<String, FlowNodeType> name2Enums;

    static {
        name2Enums = new HashMap<String, FlowNodeType>();
        for (FlowNodeType code2Enum : FlowNodeType.values()) {
            name2Enums.put(code2Enum.value, code2Enum);
        }
    }

    FlowNodeType(String name){
        this.value=name;
    }

    public static Map getAllFlow(){
        Map<String,String> ret= Maps.newHashMap();
        for(FlowNodeType flowNodeType:values()){
            ret.put(flowNodeType.name(),flowNodeType.value);
        }
        return ret;
    }

    public static String getFlowTypeValue(String name) {
        return FlowNodeType.valueOf(name).value;
    }

    public static void main(String[] args) {
        System.out.println(FlowNodeType.CHECK_INFO.value);
    }
}
