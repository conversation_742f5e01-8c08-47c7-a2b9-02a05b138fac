package com.megvii.controller.api;

import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.UserDimissionContractSnapshotPO;
import com.megvii.entity.opdb.contract.ContractStatus;
import com.megvii.entity.opdb.contract.UserContract;
import com.megvii.entity.opdb.contract.UserContractSnapshot;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ListDismissionContractVO {

	private int id;
	private String company;
	private String contractNumber;
	private String contractId;
	private String contractStatus;
	private String signStatus;
	private String userContractFile;
	private String dismissionId;
	public static List<ListDismissionContractVO> toVOs(List<UserContract> contracts, UserDimissionContractSnapshotPO snapshot) {
		List<ListDismissionContractVO> vos = new ArrayList<>();
		if (snapshot != null) {
			vos.add(toVO(snapshot));
		}
		for (UserContract contract : contracts) {
			vos.add(toVO(contract));
		}
		return vos;
	}

	private static ListDismissionContractVO toVO(UserContract contract) {
		ListDismissionContractVO vo = new ListDismissionContractVO();
		vo.setId(contract.getId());
		vo.setContractNumber(contract.getContractNumber());
		vo.setCompany(contract.getCompany());
		vo.setContractStatus(contract.getContractStatus());
		vo.setSignStatus(contract.getSignStatus());
		vo.setContractId(contract.getContractId());
		vo.setUserContractFile(contract.getUserContractFile());
		vo.setDismissionId(contract.getDismissionId());
		return vo;
	}

	private static ListDismissionContractVO toVO(UserDimissionContractSnapshotPO contract) {
		ListDismissionContractVO vo = new ListDismissionContractVO();
		vo.setId(contract.getId());
		vo.setContractNumber(contract.getContractNumber());
		vo.setCompany(contract.getCompany());
		vo.setContractStatus(ContractStatus.DRAFT);
		vo.setSignStatus(contract.getSignStatus());
		vo.setDismissionId(contract.getDimissionId());
		vo.setContractId(contract.getContractId());
		return vo;
	}
}
