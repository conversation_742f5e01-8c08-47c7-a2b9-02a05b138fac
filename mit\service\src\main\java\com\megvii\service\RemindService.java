package com.megvii.service;


import com.megvii.client.CqSMSRestClient;
import com.megvii.client.McSMSRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.TempUser;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/1/8 15:49
 */
@PropertySource(value = "classpath:remind.${spring.profiles.active}.properties",encoding = "UTF-8")
@Service
public class RemindService {

    Logger logger = LoggerFactory.getLogger(RemindService.class);

    private final static String MAIL_SUFFIX_MEGVII="@megvii.com";

    @Value("${TOMORROW_ENTRY_REMIND_MAIL}")
    private String tomorrowEntryRemindMail;

    @Value("${TOMORROW_ENTRY_REMIND_MAIL_HZ}")
    private String tomorrowEntryRemindMailHz;

    @Value("${TODAY_ENTRY_REMIND_MAIL}")
    private String todayEntryRemindMail;
    @Value("${TODAY_ENTRY_REMIND_MAIL_IMG}")
    private String todayEntryRemindMailImg;

    @Value("${NEXT_WEEK_ENTRY_REMIND_MAIL}")
    private String nextWeekEntryRemindMail;

    @Value("${NOT_INFO_REMIND_MAIL}")
    private String notInfoRemindMail;

    @Value("${PROBATION_RECV}")
    private String probationRecv;

    @Value("${TEMP_REMIND_MAIL}")
    private String tempRemindMail;

    @Value("${MIT_ADD_USER_MAIL}")
    private String mitAddUserMail;

    @Autowired
    private SMSService smsService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private UserService userService;

    @Autowired
    private TempUserService tempUserService;

    @Autowired
    private EntryService entryService;

    @Autowired
    private McSMSRestClient mcSMSRestClient;

    @Autowired
    private CqSMSRestClient cqSMSRestClient;

	/**
	 * OA回传MIT  MIT新增员工提醒
	 * @param user
	 */
    public void sendAddUserMail(User user, String[] lccsry) {
        StringBuilder recvs = new StringBuilder();
		recvs.append(mitAddUserMail + ",");

		if (user.getSiteHrg() != null && !user.getSiteHrg().equals("")) {
		    recvs.append(user.getSiteHrg() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (user.getBuddy() != null) {
            recvs.append(user.getBuddy() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (user.getHrbp() != null) {
            recvs.append(user.getHrbp() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (lccsry != null) {
            for (String s : lccsry) {
                recvs.append(s + MAIL_SUFFIX_MEGVII + ",");
            }
        }
        if (user.getMentor() != null) {
            recvs.append(user.getMentor() + MAIL_SUFFIX_MEGVII);
        }

		mailSendService.sendMitAddUserMail(user, recvs.toString());
    }



    /**
     * 发送账号密码短信
     * @param user
     * @return
     */
    public String sendPwdSms(User user) throws Exception {
        StringBuilder msg = new StringBuilder();

        if (user.getDimissionDate() != null) {
            msg.append(user.getUserId() + "存在离职日期");
            return msg.toString();
        }

        if (user.getOriPassword() == null || user.getCell() == null) {
            msg.append(user.getUserId() + "密码或手机号不存在");
            return msg.toString();
        }

        // 先发送邮件到私人邮箱，不区分公司主体，邮件发送失败不影响短信发送
        String emailResult = mailSendService.sendPasswordNotificationMail(user);
        msg.append(emailResult).append("；");

        // 发送短信（保持原有逻辑）
        if (user.getCreateTag() != null && ( user.getCreateTag() == 5 || user.getCreateTag() == 8 ||user.getCreateTag() == 7 ||user.getCreateTag() == 9) ) {
            mcSMSRestClient.sendEntryDay(user.getCell(),user.getUserId(),user.getOriPassword());
        } else if (user.getCreateTag() != null && (user.getCreateTag() == 6)) {
            cqSMSRestClient.sendEntryDay(user.getCell(),user.getUserId(),user.getOriPassword());
        } else{
            smsService.sendEntryPwdSms(user);
            msg.append(user.getUserId() + "短信发送成功");
        }

        return msg.toString();

    }

    /**
     * 留口是否强制执行
     * 判断日期
     * @param expectDate
     * @return
     */
    public String sendTomorrowEntryRemind(String expectDate) {

        if (expectDate == null) {
            if (LocalDateTime.now().getDayOfWeek().equals(DayOfWeek.FRIDAY)) {
                LocalDateTime expect = DateTimeUtil.dateAddInt(LocalDateTime.now(), 3);
                expectDate = DateTimeUtil.date2String(expect);

            } else if (LocalDateTime.now().getDayOfWeek().equals(DayOfWeek.SUNDAY)) {
                return "周一入职提醒周五已发送";
            } else {
                LocalDateTime expect = DateTimeUtil.dateAddInt(LocalDateTime.now(), 1);
                expectDate = DateTimeUtil.date2String(expect);
            }
        }

        UserFilter filter = new UserFilter();
        filter.setExpectDate(expectDate);
        filter.setStatusCode(0);
        filter.setEntryFlowStatus(0);

        List<User> list = userService.getUsersByFilter(filter);

        if (list.size() > 0) {
            return tomorrowEntryRemind(list);
        } else {
            return "没有需要提醒的人";
        }

    }



    /**
     * 发送明日入职提醒邮件，先分别发送每个人的，再发送总的
     * @param users
     * @return
     */
    private String tomorrowEntryRemind(List<User> users) {
        StringBuilder msg = new StringBuilder();

        List<User> hzUserList = new ArrayList<>();
        List<User> bjUserList = new ArrayList<>();

        for (User user : users) {
            msg.append(singleTomorrowEntryRemind(user) + "<br/>");

            if (user.getEmployBase().equals("杭州")) {
                hzUserList.add(user);
            } else {
                bjUserList.add(user);
            }
        }

        //通知ee<EMAIL>
        if (hzUserList.size() > 0) {
            mailSendService.sendTomorrowEntryMail(hzUserList, tomorrowEntryRemindMailHz, null);
            msg.append(tomorrowEntryRemindMailHz + "发送成功<br/>");
        }

        //通知ee<EMAIL>
        if (bjUserList.size() > 0) {
            mailSendService.sendTomorrowEntryMail(bjUserList, tomorrowEntryRemindMail, null);
            msg.append(tomorrowEntryRemindMail + "发送成功<br/>");
        }

        return msg.toString();
    }


    /**
     * 单独邮件提醒相关人员
     * @param user
     * @return
     */
    private String singleTomorrowEntryRemind(User user) {
        StringBuilder recvs = new StringBuilder();
        if (user.getMentor() != null) {
            recvs.append(user.getMentor() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (user.getBuddy() != null) {
            recvs.append(user.getBuddy() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (user.getHrbp() != null) {
            recvs.append(user.getHrbp() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (user.getSiteHrg() != null && !user.getSiteHrg().equals("")) {
            recvs.append(user.getSiteHrg() + MAIL_SUFFIX_MEGVII + ",");
        }

        String tips = "请新员工直属上级/buddy于本日内确认新员工工位后联系各工区前台协助清理工位。\n" +
                "（金隅N6前台-张妤妤  金隅S1前台-刘艳红  融科前台-张迪）";

        if (!recvs.toString().equals("")) {
            List<User> list = new ArrayList<>();
            list.add(user);

            mailSendService.sendTomorrowEntryMail(list, recvs.toString(), tips);
            return user.getUserId() + "single提醒发送成功";
        }


        return user.getUserId() + "收件人不存在";
    }


    /**
     * 发送入职欢迎邮件 check收件人信息
     * @param user
     * @return
     */
    public String userTodayRemind(User user) {
        StringBuilder recvs = new StringBuilder();

        if (user.getCreateTag() != null && (user.getCreateTag() == 4|| user.getCreateTag() == 7|| user.getCreateTag() == 9)) {
            return user.getUserId() + "data++执行中心人员和标注质检不发送提醒<br/>";
        }

        User mentor = userService.getUserByUserId(user.getMentor());
        if (mentor == null) {
            return user.getUserId() + "mentor不存在";
        }
        recvs.append(mentor.getUserId() + MAIL_SUFFIX_MEGVII + ",");

        User buddy = userService.getUserByUserId(user.getBuddy());
        if (buddy == null) {
            return user.getUserId() + "buddy不存在";
        }
        recvs.append(buddy.getUserId() + MAIL_SUFFIX_MEGVII + ",");

        User hrg = userService.getUserByUserId(user.getHrbp());
        if (hrg == null) {
            return user.getUserId() + "hrg不存在";
        }
        recvs.append(hrg.getUserId() + MAIL_SUFFIX_MEGVII + ",");

        recvs.append(user.getUserId() + MAIL_SUFFIX_MEGVII + ",");
        recvs.append(todayEntryRemindMail);
        if(user.getTeam().indexOf("研究院")>-1){
            mailSendService.sendImgEmail("致旷视研究院新员工的欢迎信","",user.getUserId() + MAIL_SUFFIX_MEGVII,todayEntryRemindMailImg);
        }
        return mailSendService.sendTodayWelcomeMail(user, recvs.toString(), mentor.getSpell(), buddy.getSpell(), hrg.getSpell());
    }


    /**
     * 下周入职提醒
     * @return
     */
    public String nextWeekEntryRemind() {
        LocalDateTime nextMonday = DateTimeUtil.getMonDayOfNextWeek(LocalDateTime.now());
        //一周的开始是周日，所以使用周一往上加6天
        LocalDateTime nextSunday = DateTimeUtil.dateAddInt(nextMonday, 6);

        UserFilter filter = new UserFilter();
        filter.setStatusCode(0);
        filter.setEntryFlowStatus(0);
        filter.setExpectDateFrom(DateTimeUtil.date2String(nextMonday));
        filter.setExpectDateTo(DateTimeUtil.date2String(nextSunday));

        List<User> users = userService.getUsersByFilter(filter);
        if (users == null || users.size() == 0) {
            return "没有查到下周入职人员";
        }

        String msg = mailSendService.sendNextWeekEntryMail(users, nextWeekEntryRemindMail);
        return msg;
    }


    /**
     * ER 接收邮件
     * 未完成信息收集员工提醒
     * @return
     */
    public String sendNotInfoRemind() {

        UserFilter filter = new UserFilter();
        filter.setEntryFlowStatus(1);
        filter.setStatusCode(0);
        filter.setExpectDate(DateTimeUtil.date2String(LocalDateTime.now()));

        List<User> users = userService.getUsersByFilter(filter);

        if (users == null || users.size() == 0) {
            return "没有未完成信息收集的员工";
        }


        String msg = mailSendService.sendNotInfoMail(users, notInfoRemindMail);
        return msg;
    }

    /**
     * 入职前一天发送欢迎信息
     * @param user
     * @return
     */
    public String sendWelcomeSms(User user) throws Exception {
        StringBuilder msg = new StringBuilder();
        
        if (user.getDimissionDate() != null) {
            msg.append(user.getUserId() + "存在离职日期");
            return msg.toString();
        }

        if (user.getCell() == null || user.getCell().equals("")) {
            msg.append(user.getUserId() + "手机号不存在");
            return msg.toString();
        }

        // 先发送邮件到私人邮箱，邮件发送失败不影响短信发送
        String emailResult = mailSendService.sendWelcomeNotificationMail(user);
        msg.append(emailResult).append("；");

        // 发送短信（保持原有逻辑）
        String smsResult;
        if (user.getCreateTag() != null && ( user.getCreateTag() == 5 || user.getCreateTag() == 8 ||user.getCreateTag() == 7 ||user.getCreateTag() == 9)) {
            smsResult = mcSMSRestClient.sendEntryAddress(user);
        } else if (user.getCreateTag() != null && (user.getCreateTag() == 6)) {
            smsResult = cqSMSRestClient.sendEntryAddress(user);
        } else {
            smsResult = smsService.sendWelcomeSms(user);
        }
        
        msg.append(smsResult);
        return msg.toString();
    }

    /**
     * 发送入职首周邮件
     * @param user
     * @return
     */
    public String sendOneweekRemind(User user) {
        StringBuilder recvs = new StringBuilder();

        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
            return user.getUserId() + "data++执行中心人员不发送提醒<br/>";
        }

        recvs.append(probationRecv + ",");
        recvs.append(user.getUserId() + MAIL_SUFFIX_MEGVII + ",");

        if (user.getMentor() != null) {
            recvs.append(user.getMentor() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (user.getBuddy() != null) {
            recvs.append(user.getBuddy() + MAIL_SUFFIX_MEGVII + ",");
        }
        if (user.getHrbp() != null) {
            recvs.append(user.getHrbp() + MAIL_SUFFIX_MEGVII );
        }

        String msg = mailSendService.sendOneweekMail(user, recvs.toString());
        return msg;
    }


    /**
     * 外包明日入职提醒
     * @return
     */
    public String sendTempEntryRemind() {
        LocalDateTime expect;
        if (LocalDateTime.now().getDayOfWeek().equals(DayOfWeek.FRIDAY)) {
            expect = DateTimeUtil.dateAddInt(LocalDateTime.now(), 3);
        } else if (LocalDateTime.now().getDayOfWeek().equals(DayOfWeek.SUNDAY)) {
            return "周一入职提醒周五已发送";
        } else {
            expect = DateTimeUtil.dateAddInt(LocalDateTime.now(), 1);
        }

        List<TempUser> list = tempUserService.getTempUserListByEntryTime(expect);
        if (list == null || list.size() == 0) {
            return "没有需要发送邮件的人";
        }

        mailSendService.sendTempEntryMail(list, tempRemindMail);

        return "发送成功";
    }

    /**
     * 员工 接收短信
     * 未完成信息收集员工提醒
     * @return
     */
    public String sendUserInfoSmsRemind(User user) throws Exception {
        StringBuilder msg = new StringBuilder();
        
        if (user.getDimissionDate() != null) {
            msg.append(user.getUserId() + "存在离职日期");
            return msg.toString();
        }

        EntryUserInfo entryUserInfo;
        try {
            entryUserInfo = entryService.selectUserInfoByUserid(user.getUserId());
        } catch (Exception e) {
            logger.error(user.getUserId() + "查询entry数据库异常");
            msg.append(user.getUserId() + "查询entry数据库异常");
            return msg.toString();
        }

        if (entryUserInfo == null) {
            logger.error(user.getUserId() + "未查到entry信息");
            msg.append(user.getUserId() + "未查到entry信息");
            return msg.toString();
        }

        if (entryUserInfo.getSaved() != null && entryUserInfo.getSaved().equals(1)) {
            msg.append(user.getUserId() + "已提交未拉取");
            return msg.toString();
        }

        // 先发送邮件到私人邮箱，邮件发送失败不影响短信发送
        String emailResult = mailSendService.sendUserInfoNotificationMail(user, entryUserInfo.getOriPassword());
        msg.append(emailResult).append("；");

        // 发送短信（保持原有逻辑）
        String smsResult;
        if (user.getCreateTag() != null && ( user.getCreateTag() == 5 || user.getCreateTag() == 8 || user.getCreateTag() == 7)) {
            mcSMSRestClient.sendUncompletedNotice(user, entryUserInfo.getOriPassword());
            smsResult = user.getUserId() + "发送成功";
        } else if (user.getCreateTag() != null && (user.getCreateTag() == 6)){
            cqSMSRestClient.sendUncompletedNotice(user, entryUserInfo.getOriPassword());
            smsResult = user.getUserId() + "发送成功";
        } else{
            smsService.sendUserInfoRemindSms(user, entryUserInfo.getOriPassword());
            smsResult = user.getUserId() + "发送成功";
        }
        
        msg.append(smsResult);
        return msg.toString();
    }


    /**
     * 未完成OA流程人员提醒
     * @return
     */
    public String sendUserNotOaRemind() {
        UserFilter filter = new UserFilter();
        filter.setStatusCode(0);
        filter.setEntryFlowStatus(2);

        if (LocalDateTime.now().getDayOfWeek().equals(DayOfWeek.FRIDAY)) {
            filter.setExpectDate(DateTimeUtil.date2String(DateTimeUtil.dateAddInt(LocalDateTime.now(), 3)));
        } else {
            filter.setExpectDate(DateTimeUtil.date2String(DateTimeUtil.dateAddInt(LocalDateTime.now(), 1)));
        }

        List<User> users = userService.getUsersByFilter(filter);
        if (users == null || users.size() == 0) {
            return "没有未完成OA流程的待入职人员";
        }

        return mailSendService.sendNotOaMail(users, probationRecv);
    }




}
