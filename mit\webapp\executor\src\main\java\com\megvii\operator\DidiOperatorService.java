package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.DidiService;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DidiOperatorService extends BaseOperatorService {

    @Autowired
    private DidiService didiService;

    @Autowired
    private DidiOperatorService didiOperatorService;

    @Autowired
    private UserService userService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId=paraMap.get("userId");
        if(userId != null){
            return OperateByUserId((String) userId);
        } else {
            List<User> users = userService.getUserByExpectDate();
            if (users.size() > 0) {
                StringBuilder msg=new StringBuilder();
                for (User user : users) {
                    OperateResult result = didiOperatorService.operateOnce(paraMap, user);
                    msg.append(result.getMsg()+"<br/>");
                }
                return new OperateResult(1,msg.toString());
            } else {
                return new OperateResult(1, "没有要添加的账号");
            }
        }
    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        User user = (User) params[0];
        if (user!=null){
            String result = didiService.addDidiInformation(user, false);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1, user.getUserId() + "信息不存在");
        }
    }

    public OperateResult OperateByUserId(String userId) throws Exception {
        User user = userService.getUserByUserId(userId);
        if (user!=null){
            String result = didiService.addDidiInformation(user, false);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1,userId + "信息不存在");
        }
    }


}
