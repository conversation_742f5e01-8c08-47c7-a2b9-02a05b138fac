import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { SystemComponent } from './system.component'
import { LogComponent } from './log/log.component'

const routes: Routes = [
  {
    path: '',
    component: SystemComponent,
    children: [
      {
        path: 'log',
        component: LogComponent,
        data: {
          title: '操作日志'
        }
      }
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SystemRoutingModule {}
