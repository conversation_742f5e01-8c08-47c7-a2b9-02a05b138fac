package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.DingdingService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/3/16 15:32
 */
@Service
public class DingdingNotContainSubDeptOperatorService extends BaseOperatorService {

	@Autowired
	private DingdingNotContainSubDeptOperatorService dingdingNotContainSubDeptOperatorService;

	@Autowired
	private DingdingService dingdingService;


	@Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return dingdingNotContainSubDeptOperatorService.operateOnce(paraMap, params);

    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = dingdingService.notContainSubDept();
        return new OperateResult(1,msg);

    }




}
