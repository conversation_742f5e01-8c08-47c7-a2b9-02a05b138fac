package com.megvii;


import com.alibaba.fastjson.JSONObject;
import com.megvii.client.Office365Client;
import com.megvii.entity.opdb.User;
import com.megvii.service.Office365Service;
import com.megvii.service.SyncOfficeService;
import com.megvii.service.UserService;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.HttpClientErrorException;

@RunWith(SpringRunner.class)
@SpringBootTest
public class Office365Test {

    @Autowired
    Office365Client office365Client;

    @Autowired
    private Office365Service office365Service;

    @Autowired
    private SyncOfficeService syncOfficeService;

    @Autowired
    private UserService userService;

//    @Autowired
//    private Office365UpdateProfileOperatorService office365UpdateProfileOperatorService;


    @Test
    public void testToken(){
        office365Client.getAuthHeader();
    }

    @Test
    public void testGetUserInfo(){
        try {
//            office365Client.getUserOfficeInfo("<EMAIL>");
            String str = "?$select=licenseAssignmentStates";
            JSONObject jsonObject = office365Client.getUserOfficeInfoBySelect("<EMAIL>", str);
            JSONObject value = (JSONObject)jsonObject.getJSONArray("licenseAssignmentStates").get(0);
            value.getString("skuId");

        }catch (HttpClientErrorException e){
            if ( e.getRawStatusCode() == 404) {
            }
            e.getStatusCode();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void testModify(){
        office365Client.modifyLocation("<EMAIL>","US", null);
    }

    @Test
    public void testGetGroup() {
        try {

            //1
//            office365Client.getGroupMemberById("f9658537-dfb9-4121-80a9-b5f74a5a0986");

            //777
//            office365Client.getGroupMemberById("8b9efea8-7a54-4270-8345-6e39e2eb1043");



            System.out.println(2);


//            Map<String, JSONObject> map = office365Client.getDeltaGroup();



            System.out.println(1);




//            office365Client.getGroupFromOffice();

//            office365Client.getGroupArrayFromOffice();

//            office365Service.syncOfficeVisibility();




        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testAddService() {
        User user = userService.getUserByUserId("jiangyuhong");

        try {
            office365Service.addUserGroup(user);
        } catch (Exception e) {

        }
    }

//    @Test
//    public void getUserInfo() throws Exception {
//        JSONObject data = office365Client.getUserOfficeInfo("<EMAIL>");
//
//        office365Client.isExistPhotoByUserId(null,data.get("id").toString());
//
//        AliyunUserInfo userInfo = aliyunUserInfoService.getUserInfoByUserId("sunkui");
//
//        ImageFile photoCertFile = FileUtil.getByteFromBase64(userInfo.getPhotoCert(),userInfo.getUserId());
//
//        office365Client.updateUserProfile(null,data.get("id").toString(), photoCertFile.getFileBytes());
//
//    }

//    @Test
//    public void once() {
//        office365UpdateProfileOperatorService.OperateByUserId("sunkui");
//    }

    @Test
    public void testSelectAliyun() {
        List<String> userIds = new ArrayList<>();
        userIds.add("jiangyuhong");
        userIds.add("hanxiao");
        userIds.add("gaoyulin");
        userIds.add("zhangmin02");

//        List<AliyunUserInfo> list = aliyunUserInfoService.getUserInfoListByUserIds(userIds);
    }

    @Test
    public void testDeleteGroup() throws Exception {
        JSONObject userInfo = office365Client.getUserOfficeInfo("<EMAIL>");
        String userGraphId = userInfo.getString("id");

        Map<String, JSONObject> officeGroupMap = office365Client.getGroupFromOffice();
        JSONObject data = officeGroupMap.get("it");


        office365Client.deleteUserGroup(userGraphId, data.getString("id"));

    }


    @Test
    public void testDisableAccounts() {
//        String msg = office365Service.disableInternalAccounts("xujiayan");

        String msg = office365Service.disableAccount("xujiayan");

        System.out.println(1);


    }


    @Test
    public void testTemp() {
        JSONObject data = null;
        try {
            data = office365Client.getUserOfficeInfo("<EMAIL>");
        } catch (Exception e) {

        }
        try {
            InputStream in = new FileInputStream("/Users/<USER>/Desktop/1111.jpg");
            byte[] temp = toByteArray(in);
            in.close();
            office365Client.updateUserProfile("yq", data.get("id").toString(), temp);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testUpdateVisibility() {
        office365Client.updateGroupVisibility("public" ,"8ad575fc-f3bc-4fe0-a24b-ef6e34ae4992", "Private");



    }

    @Test
    public void testSyncOffice() {
        syncOfficeService.syncAllOfficeGroupMembers();
    }


    private byte[] toByteArray(InputStream in) throws IOException {

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        int n = 0;
        while ((n = in.read(buffer)) != -1) {
            out.write(buffer, 0, n);
        }
        return out.toByteArray();
    }


}
