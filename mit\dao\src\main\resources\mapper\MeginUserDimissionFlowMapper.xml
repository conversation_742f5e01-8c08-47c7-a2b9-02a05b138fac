<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.MeginUserDimissionFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.MeginUserDimissionFlowPO">
        <id column="id" property="id" />
        <result column="megin_user_id" property="meginUserId" />
        <result column="uid" property="uid" />
        <result column="oa_id" property="oaId" />
        <result column="user_id" property="userId" />
        <result column="work_number" property="workNumber" />
        <result column="spell" property="spell" />
        <result column="dimission_date" property="dimissionDate" />
        <result column="handover" property="handover" />
        <result column="lea_workcity" property="leaWorkcity" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="flow_status" property="flowStatus" />
        <result column="interview_id" property="interviewId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, megin_user_id, uid, oa_id, user_id, work_number, spell, dimission_date, handover, lea_workcity, update_time, create_time, flow_status,interview_id
    </sql>

    <insert id="insert" parameterType="com.megvii.entity.opdb.MeginUserDimissionFlowPO">
        insert into `megin_user_dimission_flow`(`id`, megin_user_id, uid, oa_id, user_id, work_number, spell, dimission_date, handover, lea_workcity, update_time, create_time, flow_status,interview_id)
        values (
        0,
        #{meginUserId},
        #{uid},
        #{oaId},
        #{userId},
        #{workNumber},
        #{spell},
        #{dimissionDate},
        #{handover},
        #{leaWorkcity},
        #{updateTime},
        now(),
        #{flowStatus},
        #{interviewId}
        )
    </insert>
    <update id="update" parameterType="com.megvii.entity.opdb.MeginUserDimissionFlowPO">
        update megin_user_dimission_flow
        <set>
            <if test="dimissionDate != null ">
                dimission_date = #{dimissionDate},
            </if>
            <if test="createTime != null ">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectByUserId" parameterType="string" resultMap="BaseResultMap">
        select * from megin_user_dimission_flow where user_id=#{userId} and datediff(now(),dimission_date ) &lt;= 60 and datediff(now(),dimission_date ) &gt; 0 order by id desc limit 1;
    </select>
    <select id="getDimissionFlowByUid" parameterType="string" resultMap="BaseResultMap">
        select * from megin_user_dimission_flow where uid=#{uid} order by id desc limit 1;
    </select>
</mapper>
