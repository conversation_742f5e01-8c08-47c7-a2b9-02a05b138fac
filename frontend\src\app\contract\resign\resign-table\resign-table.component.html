<div class="search-container bottom-30 top-20 left-20">
  <span>
    <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">工号:</label>
    <input
      type="text"
      nz-input
      class="right-20"
      [(ngModel)]="staffid"
      (keyup)="$event.which === 13 ? onSearch() : 0"
      placeholder="请输入工号"
    />
  </span>
  <span>
    <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">ID:</label>
    <input
      type="text"
      [(ngModel)]="id"
      (keyup)="$event.which === 13 ? onSearch() : 0"
      class="right-20"
      nz-input
      placeholder="请输入ID"
    />
  </span>
  <span>
    <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">姓名:</label>
    <input
      type="text"
      [(ngModel)]="username"
      (keyup)="$event.which === 13 ? onSearch() : 0"
      class="right-20"
      nz-input
      placeholder="请输入姓名"
    />
  </span>
  <span *ngIf="isSearchExpand">
    <label class="title right-10">一级部门:</label>
    <!-- <nz-select
      class="right-20"
      [(ngModel)]="team"
      nzPlaceHolder="选择部门"
      nzShowSearch
      [nzServerSearch]="true"
      [nzFilterOption]="nzFilterOption"
      (nzOnSearch)="searchTeam($event)"
      [nzShowArrow]="false"
    >
      <nz-option
        *ngFor="let item of listOfTeamOption"
        [nzValue]="item['id']"
        [nzLabel]="item['name']"
      ></nz-option>
    </nz-select> -->
    <input
      type="text"
      [(ngModel)]="team"
      (keyup)="$event.which === 13 ? onSearch() : 0"
      class="right-20"
      nz-input
      placeholder="请输入一级部门"
    />
  </span>
  <span *ngIf="isSearchExpand">
    <label class="title right-10">离职日期:</label>
    <nz-range-picker class="right-20" [(ngModel)]="entryDateRange"></nz-range-picker>
  </span>
  <span class="pull-right">
    <button nz-button class="right-20" (click)="onSearch()" nzType="primary">搜索</button>
    <button nz-button class="right-20" (click)="onReset()">重置</button>
    <button *ngIf="type === 'noHandle'" nz-button (click)="handlePull()" [nzLoading]="pullLoading">
      手动拉取
    </button>
    <button nzType="link" nz-button (click)="onExpand()">
      <i nz-icon [nzType]="isSearchExpand ? 'up' : 'down'" nzTheme="outline"></i>
      {{ isSearchExpand ? '收起' : '展开' }}
    </button>
  </span>
</div>

<nz-table
  #nzTable
  [nzData]="listOfData"
  [nzShowPagination]="true"
  [nzFrontPagination]="false"
  [nzShowSizeChanger]="true"
  (nzPageSizeChange)="pageSizeChange($event)"
  [(nzPageIndex)]="page"
  class="dept-table"
  (nzPageIndexChange)="pageChange()"
  [nzShowQuickJumper]="true"
  [nzShowTotal]="totalTemplate"
  [nzTotal]="total"
  [nzHideOnSinglePage]="false"
  [(nzPageSize)]="pageSize"
  [nzLoading]="loading"
  [nzVirtualScroll]
  [nzScroll]="{ x: '1000px' }"
>
  <thead>
    <tr>
      <th nzShowExpand nzWidth="50px" nzLeft="0px"></th>
      <th nzWidth="130px" nzLeft="50px">姓名</th>
      <th nzWidth="100px" nzLeft="150px">工号</th>
      <th nzWidth="120px" nzLeft="250px">ID</th>
      <th nzWidth="100px">离职日期</th>
      <th nzWidth="130px">初始合同开始日期</th>
      <th nzWidth="130px">一级部门</th>
      <th nzWidth="100px">社保截至月</th>
      <th nzWidth="100px">公积金截至月</th>
      <th nzWidth="100px">补偿金</th>
      <th nzWidth="100px">是否有股份</th>
      <th nzWidth="100px">竞业协议</th>
      <th nzWidth="100px">状态</th>
      <th nzWidth="200px" nzRight="0px">操作</th>
    </tr>
  </thead>
  <tbody>
    <ng-template ngFor let-data [ngForOf]="nzTable.data">
      <tr>
        <td
          [nzShowExpand]="data['status'] != '无合同'"
          [(nzExpand)]="mapOfExpandData[data.id]"
          (nzExpandChange)="userExpandChange($event, data)"
          nzLeft="0px"
        ></td>
        <td nzLeft="50px">
          {{ data.userName }}
          <nz-tag *ngIf="(type == 'noSign' || type == 'noStamp') && data['dimissionTag'] === '已过期'" class="left-16" [nzColor]="'#FDDEDC'">
            <span class="tag-span">已过期</span>
          </nz-tag>
          <nz-tag *ngIf="(type == 'noSign' || type == 'noStamp') && data['dimissionTag'] === '即将过期'" class="left-16" [nzColor]="'#fef3e5'">
            <span class="tag-span1">即将过期</span>
          </nz-tag>
        </td>
        <td nzLeft="150px">{{ data.workNumber }}</td>
        <td nzLeft="250px">{{ data.account }}</td>
        <td>{{ data.dismissionDate }}</td>
        <td>{{ data.initialStartTime }}</td>
        <td>{{ data.topTeam }}</td>
        <td>{{ (data.socialLastTime || '').slice(0, 7) }}</td>
        <td>{{ (data.accumulationLastTime || '').slice(0, 7) }}</td>
        <td>{{ data.compensation === 0 ? '否' : '是' }}</td>
        <td>{{ data.shares === 0 ? '否' : '是' }}</td>
        <td>{{ data.competition === 0 ? '否' : '是' }}</td>
        <td>{{ data.status }}</td>
        <td *ngIf="type == 'noHandle'" nzRight="0px">
          <a class="actionA" (click)="onUpdate(data['account'], data['dismissionId'])">编辑信息</a>
          <a class="actionA" (click)="showUpload(data)">扫描上传</a>
          <!-- <a
            [disabled]="data['status'] === '已导入'"
            [ngClass]="data['status'] !== '已完成' ? 'returnWord' : 'returnGreenWord'"
            (click)="returnDoc(data['dismissionId'], data['status'])"
            >归档</a
          > -->
        </td>
        <td *ngIf="type !== 'noHandle'" nzRight="0px"></td>
      </tr>
      <tr
        *ngFor="let contract of data['contractList']; let indexValue = index"
        [nzExpand]="mapOfExpandData[data.id]"
      >
        <td></td>
        <td colspan="1">{{ contract['signStatus'] }}</td>
        <td colspan="1">{{ contract['contractNumber'] }}</td>
        <td colspan="3">公司： {{ contract['company'] }}</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td colspan="1">
          <span>{{ contract['contractStatus'] }}</span>
        </td>

        <td
          *ngIf="contract['contractStatus'] != '已作废'"
          class="table-oprt"
          nzWidth="200px"
          nzRight="0px"
        >
          <a
            class="actionA"
            *ngIf="type == 'noEmail' && indexValue === 0"
            (click)="sendEmail(contract)"
            >发送邮件</a
          >
          <a class="actionA" (click)="onReviewContract(contract)">查看合同</a>
          <a
            class="actionA"
            *ngIf="(type == 'noSign' || type == 'noStamp') && indexValue === 0"
            (click)="returnWarn(contract['contractId'], data)"
            >撤回</a
          >
          <a
            class="actionA"
            *ngIf="contract['contractStatus'] == '草稿'"
            (click)="deleteSnapshotIn(contract['dismissionId'], data)"
            >删除草稿</a
          >
          <a
            class="actionA"
            *ngIf="type == 'noStamp' && indexValue === 0"
            (click)="goToStamp(data, contract['contractId'], contract['company'])"
            >去盖章</a
          >
        </td>
      </tr>
    </ng-template>
  </tbody>
</nz-table>
<ng-template #totalTemplate let-total> 总共 {{ total }} 条</ng-template>

<nz-modal
  [(nzVisible)]="isUploadVisible"
  nzTitle="线下签署扫描上传"
  (nzOnCancel)="handleCancel()"
  (nzOnOk)="handleOk()"
  [nzOkDisabled]="isOkDisable"
  [nzOkLoading]="signLoading"
  nzWidth="800px"
>
  <form nz-form class="top-20" #basicInfoForm="ngForm">
    <!-- <nz-form-item>
      <nz-form-label nzRequired [nzSpan]="7">解除劳动关系协议书</nz-form-label>
      <nz-form-control
        [nzSpan]="17"
        nzErrorTip="请上传解除劳动关系协议书"
        nzHasFeedback
        nzErrorTip="必须上传解除劳动关系协议书"
      >
        <nz-upload
          [nzAccept]="'.pdf'"
          [nzFileList]="terminFileList"
          [nzBeforeUpload]="beforeTerminUpload"
          [nzRemove]="handleRemove"
          [nzPreview]="handlePreview('terminFileList')"
        >
          <button nz-button class="upload-button" [disabled]="terminFileList.length >= 1">
            <i nz-icon nzType="plus" nzTheme="outline"></i><span>上传</span>
          </button>
          <button disabled class="upload-span">(仅支持一个PDF文件，文件最大为20M)</button>
        </nz-upload>
      </nz-form-control>
    </nz-form-item>-->

    <nz-form-item>
      <div class="upload-div">
        <nz-form-label nzRequired [nzSpan]="7">文件上传</nz-form-label>
        <nz-form-control [nzSpan]="17" nzErrorTip="请上传文件" nzHasFeedback>
          <nz-upload
            [nzAccept]="'.zip,.pdf,.rar'"
            [nzFileList]="fileList"
            [nzRemove]="handleListRemove"
            [nzBeforeUpload]="beforeSignUpload"
            [nzPreview]="handlePreview"
          >
            <button nz-button [nzLoading]="uploadLoading">
              <i nz-icon nzType="plus" nzTheme="outline"></i><span>上传</span>
            </button>
          </nz-upload>
          <p>支持扩展名：.rar .zip .pdf，文件大小不超过20M</p>
        </nz-form-control>
      </div>
    </nz-form-item>
  </form>
</nz-modal>

<nz-modal
  [(nzVisible)]="isStampVisible"
  nzTitle="去盖章"
  [nzOkLoading]="isAdding"
  (nzOnCancel)="handleStampCancel()"
  (nzOnOk)="handleStampOk()"
  nzWidth="900px"
>
  <nz-alert
    nzType="info"
    nzMessage='温馨提示：盖章完成后，合同将会立即生效，请再次确认无误后点击"确认盖章",此条数据将会进入到"待发送邮件"'
    nzShowIcon
  ></nz-alert>
  <h3 class="top-20">基本信息</h3>
  <div class="stampRow" nz-row nzGutter="16">
    <div nz-col class="gutter-row" nzSpan="6">
      <div class="gutter-box">姓名：{{ stampData.stampName }}</div>
    </div>
    <div nz-col class="gutter-row" nzSpan="6">
      <div class="gutter-box">ID: {{ stampData.stampId }}</div>
    </div>
    <div nz-col class="gutter-row" nzSpan="6">
      <div class="gutter-box">离职日期：{{ stampData.stampDate }}</div>
    </div>
  </div>
  <h3 class="top-20">待盖章合同列表</h3>
  <div>
    <ul class="stampUl">
      <li>离职结算单</li>
      <li>
        解除劳动关系协议书（<span>{{ compensation === 0 ? '无补偿' : '有补偿' }}</span>
        <span>{{ competition === 0 ? '无竞业' : '有竞业' }}</span>
        <span>{{ stockManage === 1 ? '有股权' : '无股权' }}）</span>
      </li>
      <li>
        离职证明 （<span>{{ competition === 0 ? '无竞业' : '有竞业' }}）</span>
      </li>
      <li *ngIf="competition === 1">
        竞业发起通知书（<span>{{ specialCompany === 1 ? '有特定公司' : '无特定公司' }}）</span>
      </li>
      <li *ngIf="shares === 1">
        股份经济受益权协议（<span>{{ stockManage === 1 ? '保留' : '放弃' }}）</span>
      </li>
    </ul>
  </div>
</nz-modal>
