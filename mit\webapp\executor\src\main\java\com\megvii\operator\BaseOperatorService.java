package com.megvii.operator;

import com.megvii.common.OperateResult;
import java.util.Map;

/***
 * BaseExcuter接口，对所有Excuter进行约束，开发规范中要求所有被执行器调用的操作类集成此接口
 */
public abstract class BaseOperatorService {

    /***
     * 业务主操作入口
     * @param params 参数
     * @return 执行结果
     */
    abstract OperateResult operateMain(Map paraMap, Object... params) throws Exception;

    /***
     * 业务单次操作
     * @param params 参数
     * @return 执行结果
     */
    protected abstract OperateResult operateOnce(Map paraMap,Object... params) throws Exception;
}
