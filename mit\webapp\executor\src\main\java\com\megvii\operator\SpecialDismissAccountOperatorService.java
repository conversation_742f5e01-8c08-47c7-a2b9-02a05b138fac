package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.DimissionService;
import com.megvii.service.UserService;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/11/4 20:58
 */
@Service
public class SpecialDismissAccountOperatorService extends BaseOperatorService {

    @Autowired
    private UserService userService;

    @Autowired
    private DimissionService dimissionService;


    /**
     *
     * 手动执行离职 必须传参数
     * 主操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
       Object ids = paraMap.get("ids");
       String userIds = (String) ids;

        if (userIds != null){
            StringBuilder msg = new StringBuilder();
            List<String> paras= Arrays.asList(userIds.split(","));
            for (String para : paras) {
                OperateResult result = OperateByUserId(para);
                msg.append(result.getMsg() + "<br/>");
            }

            return new OperateResult(1, msg.toString());
        } else {
            return new OperateResult(1, "参数错误");
        }

    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {

        return new OperateResult(1, "特殊任务单次不执行");

    }

    /**
     *
     * 根据userId禁用权限
     * @param userId
     * @return
     */
    public OperateResult OperateByUserId(String userId){
        User user = userService.getUserByUserId(userId);

        if (user != null){
            String result = dimissionService.dismissAccount(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(0,userId + "信息不存在");
        }
    }


}
