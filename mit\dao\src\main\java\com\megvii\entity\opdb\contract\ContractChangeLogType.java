package com.megvii.entity.opdb.contract;

/**
 * <AUTHOR>
 * @date 2020/4/22 17:22
 */
public interface ContractChangeLogType {

	public final static String CREATE_SPANSHOT = "生成";
	public final static String UPDATE_SPANSHOT = "修改";
	public final static String START_SIGN = "发起签署";
	public final static String CANCEL = "作废";
	public final static String RECALL = "撤回";
	public final static String OFFLINE = "线下签署";

}
