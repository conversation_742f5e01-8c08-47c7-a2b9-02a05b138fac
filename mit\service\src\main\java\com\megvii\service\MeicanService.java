package com.megvii.service;

import com.alibaba.fastjson.JSONObject;
import com.megvii.client.MeicanRestClient;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

@Service
@PropertySource(value = "classpath:meican.properties",encoding = "UTF-8")
public class MeicanService {

    Logger logger= LoggerFactory.getLogger(MeicanService.class);

    @Autowired
    private MeicanRestClient meicanRestClient;

    @Autowired
    private TeamService teamService;

    @Value("${MEICAN_KEY}")
    private String meicanKey;

    /**
     * @param user
     * @param need2add 是否强制添加
     * @return
     */
    public String createMeicanUser(User user, boolean need2add) {

        if (!need2add) {
            //标记1或3的 跳过
            if (user.getCreateTag() != null) {
                if (user.getCreateTag() == 1 || user.getCreateTag() == 3 || user.getCreateTag() == 4 || user.getCreateTag() == 5|| user.getCreateTag() == 7|| user.getCreateTag() == 9) {
                    String message = user.getUserId() + " create_tag = " + user.getCreateTag() + "跳过添加美餐";
                    logger.info(message);
                    return message;
                }
            }
        }

        if (!user.getEmployBase().equals("北京")) {
            return "base地为:" + user.getEmployBase() + "**不添加美餐账号";
        }

        boolean status = user.getStatus() != null &&  user.getStatus().indexOf("已禁用")!=-1;
        if (status) {
            String message = user.getUserId() + " status = " + user.getStatus() + "不添加美餐账号";
            logger.info(message);
            return message;
        }

        long curTime = System.currentTimeMillis();
        String key = meicanKey + curTime;
        String signature = DigestUtils.sha1Hex(key);

        return addMeican(curTime, signature, user.getUserId(), "G01");

        /**
         * 架构断层后meican部门编码会到team或更低
         * 统一添加美餐时的部门，与行政沟通后在meican内的部门并未使用
         * 统一调整为G01
         */

//        if (user.getTeamId() == null) {
//            logger.error(user.getUserId() + "teamId 为 null");
//            return user.getUserId() + "teamId 为 null";
//        }
//
//        Team team = teamService.getTeamById(user.getTeamId());
//        if (team == null) {
//            String message = user.getUserId() + " team_id = null,不添加美餐账号";
//            logger.info(message);
//            return message;
//        }
//
//
//        String[] teamName = team.getName().split("-");
//        String department;
//        if (teamName.length < 2) {
//            Team group = teamService.getTeamByName(teamName[0]);
//            department = group.getSfCode();
//            return addMeican(curTime, signature, user.getUserId(), department);
//        }
//
//        Team group = teamService.getTeamByName(teamName[0]);
//        Team bu = teamService.getTeamByName(teamName[0] + "-" +teamName[1]);
//
//        if (!group.getName().equals(bu.getName())) {
//            department = group.getSfCode() + bu.getSfCode();
//        } else {
//            department = group.getSfCode();
//        }
//
//        return addMeican(curTime, signature, user.getUserId(), department);
    }

    /**
     * 删除美餐用户
     * @param user
     * @return
     */
    public String deleteMeicanUser(User user) {
        long curTime = System.currentTimeMillis();
        String key = meicanKey + curTime;
        String signature = DigestUtils.sha1Hex(key);

        if(deleteMeican(curTime, signature, user.getUserId())) {
            return user.getUserId() + "删除美餐账号成功";
        } else {
            return user.getUserId() + "删除美餐账号失败";
        }
    }

    private String addMeican(long curTime, String signature, String userId, String department) {
        try {
            JSONObject result = meicanRestClient.createMeicanUser(curTime, signature, userId, department);
            if (result.getString("resultCode").equals("OK")) {
                return userId + "添加美餐账号成功";
            } else {
                logger.error(userId + ":" + result.getString("resultDescription"));
                return userId + ":" + result.getString("resultDescription");
            }
        } catch (Exception e) {
            logger.error("美餐账号创建错误", e);
            return "美餐账号创建错误";
        }
    }

    private boolean deleteMeican(Long curTime, String signature, String userId) {
        try {
            JSONObject result = meicanRestClient.deleteMeican(curTime, signature, userId);
            if (result.getString("resultCode").equals("OK")) {
                return true;
            } else {
                logger.error(userId + ":" + result.getString("resultDescription"));
                return false;
            }
        } catch (Exception e) {
            logger.error("美餐账号删除错误", e);
            return false;
        }
    }

}
