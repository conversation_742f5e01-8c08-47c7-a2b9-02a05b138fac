<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserDimissionContractSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.UserDimissionContractSnapshotPO">
        <id column="id" property="id" />
        <result column="dimission_id" property="dimissionId" />
        <result column="user_id" property="userId" />
        <result column="competition" property="competition" />
        <result column="special_company" property="specialCompany" />
        <result column="special_company_txt" property="specialCompanyTxt" />
        <result column="company" property="company" />
        <result column="stock_manage" property="stockManage" />
        <result column="sers_sign_date" property="sersSignDate" />
        <result column="stock_number" property="stockNumber" />
        <result column="sers_keep_number" property="sersKeepNumber" />
        <result column="sers_invalid_number" property="sersInvalidNumber" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="wait_time" property="waitTime" />
        <result column="sign_status" property="signStatus" />
        <result column="contract_number" property="contractNumber" />
        <result column="spell" property="spell" />
        <result column="cert_no" property="certNo" />
        <result column="contract_id" property="contractId" />
        <result column="competition_duration" property="competitionDuration" />
        <result column="competition_start" property="competitionStart" />
        <result column="competition_end" property="competitionEnd" />
        <result column="dismission_date" property="dismissionDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dimission_id, user_id, competition, special_company, special_company_txt, company, stock_manage, sers_sign_date, stock_number, sers_keep_number, sers_invalid_number, create_time, create_user,wait_time,sign_status,contract_number,spell,cert_no,contract_id,competition_duration,competition_start,competition_end,dismission_date
    </sql>

    <insert id="addSnapshot" parameterType="com.megvii.entity.opdb.UserDimissionContractSnapshotPO">
        insert into  user_dimission_contract_snapshot(
        id, dimission_id, user_id, competition, special_company, special_company_txt, company, stock_manage, sers_sign_date, stock_number, sers_keep_number, sers_invalid_number, create_time, create_user,wait_time,sign_status,contract_number,spell,cert_no,contract_id,competition_duration,competition_start,competition_end,dismission_date
        )
        values (
        0,
        #{dimissionId},
        #{userId},
        #{competition},
        #{specialCompany},
        #{specialCompanyTxt},
        #{company},
        #{stockManage},
        #{sersSignDate},
        #{stockNumber},
        #{sersKeepNumber},
        #{sersInvalidNumber},
        #{createTime},
        #{createUser},
        #{waitTime},
        #{signStatus},
        #{contractNumber},
        #{spell},
        #{certNo},
        #{contractId},
        #{competitionDuration},
        #{competitionStart},
        #{competitionEnd},
        #{dismissionDate}
        )
    </insert>
    <select id="getSnapshot" parameterType="string" resultMap="BaseResultMap">
        select * from user_dimission_contract_snapshot where dimission_id=#{dismissionId};
    </select>
    <delete id="deleteSnapshot" parameterType="string">
        delete from user_dimission_contract_snapshot where dimission_id=#{dismissionId};
    </delete>
    <update id="updateSnapshot" parameterType="com.megvii.entity.opdb.UserDimissionContractSnapshotPO">
        update user_dimission_contract_snapshot
        set
       competition = #{competition},
       special_company = #{specialCompany},
       special_company_txt = #{specialCompanyTxt},
       stock_manage = #{stockManage},
       sers_sign_date = #{sersSignDate},
       stock_number = #{stockNumber},
       sers_keep_number = #{sersKeepNumber},
       sers_invalid_number = #{sersInvalidNumber},
       wait_time = #{waitTime}
       where id = #{id}
    </update>
</mapper>
