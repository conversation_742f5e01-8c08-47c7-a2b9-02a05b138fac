package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.common.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2021/6/3 14:38
 */
@Component
@PropertySource(value = "classpath:moka.${spring.profiles.active}.properties",encoding = "UTF-8")
public class MokaRestClient {

	Logger logger = LoggerFactory.getLogger(MokaRestClient.class);

	@Value("${MOKA_BASE_URL}")
	private String mokaBaseUrl;

	@Value("${MOKA_OFFER_URL}")
	private String mokaOfferUrl;

	@Value("${MOKA_OFFER_URL_V2}")
	private String mokaOfferUrlV2;

	@Value("${MOKA_OFFER_FILE_URL}")
	private String mokaOfferFileUrl;

	@Value("${SNAPSHOT_OFFER_FILE_PATH}")
	private String snapshotOfferFilePath;

	@Value("${MOKA_SYNCINFO_URL}")
	private String mokaSyncInfoUrl;

	@Value("${MOKA_USERNAME}")
	private String username;

	@Autowired
	private RestTemplate restTemplate;

	/**
	 * 获取moka 候选人offer信息
	 * 没有数据会500 返回只有一个data
	 * @param offerId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getMokaOffer(String offerId) {
		String url = mokaBaseUrl + mokaOfferUrl + offerId;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", username);

        HttpEntity<String> httpEntity = new HttpEntity<String>(null ,headers);
        JSONObject data = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class).getBody());

		return data;
	}

	/**
	 * 获取moka 候选人offer附件
	 * https://wiki.megvii-inc.com/x/MRuEGQ
	 * @param offerId
	 * @return
	 * @throws Exception
	 */
	public String getMokaOfferFile(String userId,String offerId) {
		String url = mokaBaseUrl + mokaOfferFileUrl;
		String urlV2 = mokaBaseUrl + mokaOfferUrlV2;

		String fileName = offerId + ".pdf";
		String savePath = snapshotOfferFilePath + File.separator + userId + File.separator;

		MultiValueMap<String, String> params= new LinkedMultiValueMap<String, String>();
		params.add("applicationId", offerId);

		HttpHeaders headers=new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
		headers.set("Authorization", username);
		HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<MultiValueMap<String, String>>(params, headers);

		try {
			JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url ,restTemplate.postForEntity(url,  requestEntity, String.class).getBody());
			String downLoadUrl = jsonObject.getJSONObject("data").getString("downLoadUrl");
			if (StringUtils.isEmpty(downLoadUrl)) {
				//不用系统生成offer，人工上传的offer附件
				JSONObject paramsV2 = new JSONObject();
				paramsV2.put("applicationIds", new JSONArray().fluentAdd(offerId));
				headers.setContentType(MediaType.APPLICATION_JSON);

				HttpEntity<String> httpEntity=new HttpEntity<String>(paramsV2.toJSONString(), headers);
				JSONObject jsonObjectV2 = RequestCorvertUtil.getJSONObjectFromStr(urlV2 ,restTemplate.postForEntity(urlV2, httpEntity, String.class).getBody());
				downLoadUrl = jsonObjectV2.getJSONArray("data").getJSONObject(0).getJSONObject("offerInfo").getJSONArray("attachmentList").getJSONObject(0).getString("downloadUrl");
			}
			if (StringUtils.isEmpty(downLoadUrl)) {
				return null;
			}
			return FileUtil.downLoadByUrl(downLoadUrl, fileName, savePath);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(userId + "拉取offer异常:" + e);
			return null;
		}
	}



	public String disableUser(String phone) {
		HttpHeaders requestHeaders = new HttpHeaders();
		requestHeaders.setContentType(MediaType.APPLICATION_JSON);
		requestHeaders.add("Authorization",username);
		JSONObject body= new JSONObject();
		JSONObject info1= new JSONObject();
		JSONArray info = new JSONArray();
		info1.put("phone",phone);
		info1.put("departmentCode", new ArrayList<>());
		info1.put("deactivated",1);
		info1.put("uniqueType","phone");
		info.add(info1);
		body.put("usersInfo",info);
		try {
			HttpEntity<String> requestEntity = new HttpEntity<String>(JSONObject.toJSONString(body), requestHeaders);
			String ret=restTemplate.exchange(mokaBaseUrl+mokaSyncInfoUrl, HttpMethod.POST, requestEntity, String.class).getBody();
			JSONObject jsonObject= JSONObject.parseObject(ret);
			if(jsonObject!=null&&jsonObject.getString("msg").equals("success")){
				return "禁用moka接口成功";
			}else {
				logger.info("禁用调用moka接口失败"+phone+jsonObject);
				return "禁用调用moka接口失败"+phone+jsonObject;
			}
		}catch (Exception e){
			logger.info("入职调用moka接口失败,请求异常"+phone+e.getMessage());
			return "入职调用moka接口失败,请求异常"+phone+e.getMessage();
		}
	}

}
