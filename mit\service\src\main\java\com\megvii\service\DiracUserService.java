package com.megvii.service;

import com.megvii.diracMapper.DiracUserMapper;
import com.megvii.entity.dirac.DiracUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/9/17 15:54
 */
@Service
public class DiracUserService {

    @Autowired
    private DiracUserMapper diracUserMapper;

    public DiracUser selectUserByUserId(String userId) {
        return diracUserMapper.selectUserByUserId(userId);
    }

    public void updateDiracUser(DiracUser diracUser) {
        diracUserMapper.updateDiracUser(diracUser);
    }


}
