package com.megvii.controller.api;

import com.megvii.entity.opdb.Role;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/11 11:58
 */
@Data
public class RoleVO {

	private int id;
	private String roleType;
	private String roleName;
	private String roleCode;

	public static List<RoleVO> toVOs(List<Role> roles) {
		List<RoleVO> vos = new ArrayList<>();
		for (Role role : roles) {
			vos.add(toVO(role));
		}
		return vos;
	}

	private static RoleVO toVO(Role role) {
		RoleVO vo = new RoleVO();
		vo.setId(role.getId());
		vo.setRoleType(role.getRoleType());
		vo.setRoleName(role.getRoleName());
		vo.setRoleCode(role.getRoleCode());
		return vo;
	}


}
