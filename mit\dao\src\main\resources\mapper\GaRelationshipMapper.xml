<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.GaRelationshipMapper">



    <select id="selectGaRelationshipByUnameAndGname" resultType="com.megvii.entity.opdb.GaRelationship" parameterType="java.lang.String" >
        select * from GA_relationship where uname = #{uname} and gname = #{gname}
    </select>

    <delete id="deleteGaRelationship" >
        delete from GA_relationship where id = #{id}
    </delete>

</mapper>