import { Component, OnInit } from '@angular/core'
import { SystemService } from '../system.service'
import { MessageService } from '../../core/services'
@Component({
  selector: 'app-log',
  templateUrl: './log.component.html',
  styleUrls: ['./log.component.less']
})
export class LogComponent implements OnInit {
  page = 1
  total = 0
  pageSize = 10
  loading = false
  isShowSelf = true
  logList = []
  operateUserId = ''
  requestIp = ''
  startTime = ''
  endTime = ''
  requestUrl = ''
  dateRange = []
  dateFormat = 'yyyy-MM-dd'
  constructor(private systemService: SystemService, private messageService: MessageService) {}

  ngOnInit(): void {
    this.getLogList()
  }

  getLogList() {
    let param = {
      current: this.page,
      size: this.pageSize,
      operateUserId: this.operateUserId,
      requestIp: this.requestIp,
      requestUrl: this.requestUrl,
      startTime: this.startTime,
      endTime: this.endTime
    }
    this.loading = true
    this.systemService.getOperateList(param).subscribe((res) => {
      const { code, msg, data, totalCount } = res
      this.loading = false
      if (code === 0) {
        this.logList = data || []
        this.total = totalCount || 0
      } else {
        this.messageService.error(msg)
      }
    })
  }
  onSearch() {
    this.page = 1
    console.log('dataaa', this.dateRange)
    if (this.dateRange.length !== 0) {
      this.startTime =
        !!this.dateRange[0] &&
        this.dateRange[0].getFullYear() +
          '-' +
          (this.dateRange[0].getMonth() + 1) +
          '-' +
          this.dateRange[0].getDate()
      this.endTime =
        !!this.dateRange[1] &&
        this.dateRange[1].getFullYear() +
          '-' +
          (this.dateRange[1].getMonth() + 1) +
          '-' +
          this.dateRange[1].getDate()
    }
    this.getLogList()
  }
  pageSizeChange(e) {
    this.pageSize = e
    this.getLogList()
  }

  pageChange() {
    this.getLogList()
  }
  onActivate(event) {
    this.isShowSelf = false
  }
  onDeactivate(event) {
    this.isShowSelf = true
  }
  onReset() {
    this.page = 1
    this.pageSize = 10
    this.operateUserId = ''
    this.requestIp = ''
    this.requestUrl = ''
    this.startTime = ''
    this.endTime = ''
    this.dateRange = []
    this.getLogList()
  }
}
