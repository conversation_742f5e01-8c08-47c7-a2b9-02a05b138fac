package com.megvii.service;


import com.megvii.common.CommonException;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.TempUser;
import com.megvii.mapper.TempUserMapper;
import java.time.LocalDateTime;
import java.util.List;

import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/26 20:46
 */
@Service
public class TempUserService {
    private Logger logger= LoggerFactory.getLogger(TempUserService.class);
    @Autowired
    private TempUserMapper tempUserMapper;

    @Autowired
    private OaService oaService;

    /**
     * 获取需要禁用账号的外包人员
     * @return
     */
    public List<TempUser> getNeedDisTempUserList() {
        return tempUserMapper.selectNeedDisTempUserList();
    }

    /**
     * 修改外包人员账号状态为 inactive
     * @param userId
     */
    public String updateTempUserStatus(String userId) {
        if (tempUserMapper.updateTempUserStatus(userId) > 0) {
            return "修改状态成功";
        }

        return "修改状态失败";
    }


    /**
     * 通过入场日期查外包list
     * @param date
     * @return
     */
    public List<TempUser> getTempUserListByEntryTime(LocalDateTime date) {
        return tempUserMapper.selectTempUserByEntryTime(DateTimeUtil.date2String(date));
    }

    /**
     * 按手机号查外包人员
     */
    public TempUser getTempUserByPhone(String phone) {
    	List<TempUser> users = tempUserMapper.selectTempByPhone(phone);
    	
    	if(users==null || users.isEmpty()) {
    		throw new CommonException("手机号不存在:"+phone+"，请联系IT支持");
    	}else if(users.size()>1) {
    		throw new CommonException("手机号重复:"+phone+"，请联系IT支持");
    	}
    	
    	return users.get(0);
    }
    
    /**
     * 按账号（邮箱前缀）查外包人员
     */
    public TempUser getTempUserByUserId(String userId) {
    	List<TempUser> users = tempUserMapper.selectTempByUserId(userId);
    	
    	if(users==null || users.isEmpty()) {
    		throw new CommonException("账号不存在:"+userId+"，请联系IT支持");
    	}else if(users.size()>1) {
    		throw new CommonException("账号重复:"+userId+"，请联系IT支持");
    	}
    	
    	return users.get(0);
    }

    public void updateHrgByMentor(String userId, String hrbp) {
        List<TempUser> tempUsers = tempUserMapper.selectTempByMentor(userId);
        if (tempUsers != null && !tempUsers.isEmpty())
        {
            logger.info(tempUsers.toString());
            tempUserMapper.updateHrgByMentor(userId, hrbp);
            for (TempUser tempUser : tempUsers){
                String oaId=tempUser.getOaId();
                if(StringUtil.isBlank(oaId)){
                    oaId=oaService.getTempUser(tempUser.getSpell(), tempUser.getCell());
                }
                oaService.outsourcingUpdate(tempUser,oaId);
            }
        }

    }
}
