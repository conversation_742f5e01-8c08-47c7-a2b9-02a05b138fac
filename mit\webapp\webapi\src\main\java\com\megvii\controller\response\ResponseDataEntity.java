package com.megvii.controller.response;

import java.util.HashMap;
import java.util.Map;


public class ResponseDataEntity {
	
	
	public final static Integer OK = 0;
	public final static Integer ERROR = 1;
	public final static Integer UNAUTHORIZED = 403;
	
	private Integer code;
	private String msg;
	private Object data;
	
	public Map<String,Object> variables = new HashMap<String,Object>();
	
	public ResponseDataEntity() {
		
	}
	public ResponseDataEntity(Integer status_code, String message) {
		this.code = status_code;
		this.msg = message;
	}
	public ResponseDataEntity(Integer status_code, String message, Object data) {
		this.code = status_code;
		this.msg = message;
		this.data = data;
	}
	
	
	public Integer getCode() {
		return code;
	}
	public void setCode(Integer code) {
		this.code = code;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	
}
