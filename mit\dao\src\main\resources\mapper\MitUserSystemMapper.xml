<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.MitUserSystemMapper">
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.MitUserSystem">
        <id column="id" property="id"/>
        <result column="work_number" property="workNumber"/>
        <result column="user_id" property="userId"/>
        <result column="koala_id" property="koalaId"/>
        <result column="pangu_uri" property="panguUri"/>
        <result column="pangu_id" property="panguId"/>
    </resultMap>

    <insert id="insertMitUserSystem" parameterType="com.megvii.entity.opdb.MitUserSystem" >
        INSERT INTO `mit_user_system` (
	        `id`,
            `work_number`,
	        `user_id`,
	        `koala_id`,
        	`pangu_uri`,
        	`pangu_id`
        )
        VALUES
	    (
		    0,
		    #{workNumber},
		    #{userId},
	    	#{koalaId},
		    #{panguUri},
		    #{panguId}
	    );
    </insert>

    <update id="updateMitUserSystem" parameterType="com.megvii.entity.opdb.MitUserSystem">
        update mit_user_system
        <set>
            <if test="koalaId != null ">
                koala_id = #{koalaId},
            </if>
            <if test="panguUri != null ">
                pangu_uri = #{panguUri},
            </if>
            <if test="panguId != null ">
                pangu_id = #{panguId},
            </if>

        </set>
        where id = #{id}
    </update>




    <select id="selectMitUserSystemByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from mit_user_system WHERE user_id = #{userId}
    </select>



</mapper>