<ng-template #totalTemplate let-total> 总共 {{ total }} 条</ng-template>
<ng-container *ngIf="isShowSelf">
  <nz-breadcrumb>
    <nz-breadcrumb-item> 系统管理 </nz-breadcrumb-item>
    <nz-breadcrumb-item> 操作日志 </nz-breadcrumb-item>
  </nz-breadcrumb>
  <div class="content-block content-item">
    <app-title title="操作日志"></app-title>
    <div class="search-container bottom-30 top-20 left-20">
      <span>
        <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }"
          >用户名:</label
        >
        <input
          type="text"
          nz-input
          class="right-20"
          placeholder="请输入用户名"
          [(ngModel)]="operateUserId"
          (keyup)="$event.which === 13 ? onSearch() : 0"
        />
      </span>
      <span>
        <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">ip:</label>
        <input
          type="text"
          class="right-20"
          nz-input
          placeholder="请输入ip"
          [(ngModel)]="requestIp"
          (keyup)="$event.which === 13 ? onSearch() : 0"
        />
      </span>
      <span>
        <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }"
          >请求url:</label
        >
        <input
          type="text"
          class="right-20"
          nz-input
          placeholder="请输入请求url"
          [(ngModel)]="requestUrl"
          (keyup)="$event.which === 13 ? onSearch() : 0"
        />
      </span>
      <span>
        <label class="right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">时间点：</label>
        <nz-range-picker
          class="date-pick right-20"
          [nzFormat]="dateFormat"
          [(ngModel)]="dateRange"
        ></nz-range-picker>
      </span>
      <span class="pull-right">
        <button nz-button class="right-20" (click)="onSearch()" nzType="primary">搜索</button>
        <button nz-button class="right-20" (click)="onReset()">重置</button>
      </span>
    </div>
    <nz-table
      #listTable
      [nzData]="logList"
      [nzShowPagination]="true"
      [nzFrontPagination]="false"
      [nzShowSizeChanger]="true"
      (nzPageSizeChange)="pageSizeChange($event)"
      [(nzPageIndex)]="page"
      class="dept-table"
      (nzPageIndexChange)="pageChange()"
      [nzShowQuickJumper]="true"
      [nzShowTotal]="totalTemplate"
      [nzTotal]="total"
      [(nzPageSize)]="pageSize"
      [nzLoading]="loading"
      [nzHideOnSinglePage]="false"
    >
      <thead>
        <tr>
          <th nzWidth="14%">用户名</th>
          <th nzWidth="14%">操作时间</th>
          <th nzWidth="14%">操作类型</th>
          <th nzWidth="14%">来源ip</th>
          <th nzWidth="15%">请求参数</th>
          <th nzWidth="14%">请求Url</th>
          <th nzWidth="14%">调用耗时</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of listTable.data">
          <td>
            {{ data['operateUserId'] }}
          </td>
          <td>
            {{ data['operateDate'] }}
          </td>
          <td>
            {{ data['operateType'] }}
          </td>
          <td>
            {{ data['requestIp'] }}
          </td>
          <td>
            <span
              *ngIf="data['requestParams'].length > 80"
              nz-tooltip
              nzTooltipTitle="{{ data['requestParams'] }}"
              >{{ data['requestParams'].substring(0, 77) }}...</span
            >
            <span *ngIf="data['requestParams'].length < 80">{{ data['requestParams'] }}</span>
          </td>
          <td>
            {{ data['requestUrl'] }}
          </td>
          <td>
            {{ data['timeConsuming'] }}
          </td>
        </tr>
      </tbody>
    </nz-table>
    <ng-template #totalTemplate let-total> 总共 {{ total }} 条</ng-template>
  </div>
</ng-container>

<router-outlet (activate)="onActivate($event)" (deactivate)="onDeactivate($event)"> </router-outlet>
