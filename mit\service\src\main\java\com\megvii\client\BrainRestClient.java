package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import java.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2020/1/6 17:08
 */
@Component
@PropertySource(value = "classpath:brain.${spring.profiles.active}.properties",encoding = "UTF-8")
public class BrainRestClient {

    private Logger logger= LoggerFactory.getLogger(BrainRestClient.class);

    @Value("${BRAIN_BASE_URL}")
    private String baseUrl;

    @Value("${BRAIN_EXPEL_URL}")
    private String expelUrl;

    @Value("${BRAIN_ACCESSKEY}")
    private String accessKey;

    @Value("${BRAIN_SECRETKEY}")
    private String secretKey;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 离职禁用brain++账号
     * @param userId
     * @param mentor
     * @return
     */
    public String expelBrain(String userId, String mentor) {
        String url = baseUrl + expelUrl;

        HttpHeaders headers=new HttpHeaders();
        headers.set("Cookie", "authentication=Basic " + Base64.getUrlEncoder().encodeToString((accessKey + ":" + secretKey).getBytes()));

        JSONObject data = new JSONObject();
        data.put("username", userId);

        if (mentor != null) {
            data.put("leader", mentor);
        }

        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString() ,headers);

        try {
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url ,requestEntity ,JSONObject.class);

            if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
                return "禁用brain++【成功】";
            } else {
                return "禁用brain++【失败】";
            }
        } catch (Exception e) {
            logger.error(userId + "禁用brain++【异常】");
            return "禁用brain++【异常】";
        }

    }





}
