package com.megvii.mapper.filter;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/10/14 20:43
 */
@Data
public class UserFilter {

    private Integer teamId;
    private String expectDate;
    /**
     * 0 not in (已保留， 已禁用)
     * 1 in     (已保留， 已禁用)
     * 2 = 已禁用
     */
    private Integer statusCode;
    /**
     *
     */
	private String dimissionDateFrom;
	private String dimissionDateTo;
    private String expectDateFrom;
    private String expectDateTo;
    /**
     * 0    entry_flow != OA流程
     * 1    entry_flow == 信息收集
     * 2    entry_flow == OA流程
     * 3    entry_flow == 在职
     */
    private Integer entryFlowStatus;
    private String workNumber;
    private String userId;
    private String spell;
	private String workBase;
	private String employType;
	/**
	 * 0 employType != 劳务派遣
	 */
	private String employTypeCode;
	private String entryFlow;
	private String contractStatus;//当前合同状态
	/**
	 * 0 != 已完成
	 *
	 */
	private String contractStatusCode;
	/**
	 * 0 create_tag != 4 or create_tag is null
	 */
	private String createTagCode;

	/**
	 * 0 is null
	 */
	private Integer dingdingIdCode;



}
