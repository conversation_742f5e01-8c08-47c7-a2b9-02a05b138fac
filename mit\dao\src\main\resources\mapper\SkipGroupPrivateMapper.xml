<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.SkipGroupPrivateMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.SkipGroupPrivate">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
    </resultMap>

    <select id="selectSkipAllGroup" resultType="java.lang.String">
		SELECT `name` FROM skip_group_private
	</select>

</mapper>
