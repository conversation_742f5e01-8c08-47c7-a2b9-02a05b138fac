package com.megvii;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.megvii.entity.opdb.DataUserTemp;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.BacklogStatus;
import com.megvii.mapper.DataUserTempMapper;
import com.megvii.mapper.TeamMapper;
import com.megvii.mapper.UserMapper;
import com.megvii.service.UserService;
import com.megvii.service.contract.ContractBacklogService;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/12/2 18:13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestUser {

    @Autowired
    private UserService userService;

    @Autowired
    private TeamMapper teamMapper;

    @Autowired
    private ContractBacklogService contractBacklogService;

    @Autowired
    private DataUserTempMapper dataUserTempMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private UserMapper userMapper;

    @Test
    public void testTemp() {

        contractBacklogService.selectBacklogByStatus(BacklogStatus.PULL);

         System.out.println(1);

        List<Team> teams = teamMapper.selectTeamsByName("业务赋能");

        System.out.println(1);


         List<User> users222 = userService.getAllUser();

         PageHelper.startPage(14,20);
        List<User> users = userService.getAllUser();

        System.out.println(1);


        PageInfo<User> pageInfo = new PageInfo<User>(users);

        System.out.println(1);

    }


    @Test
    public void testGetDataUserTemp() {
        List<DataUserTemp> list = dataUserTempMapper.getAllDataUserTemp();


        for (DataUserTemp dataUserTemp : list) {
            String newUserId = userService.getUserId(dataUserTemp.getUserId());
            long workCode = redisTemplate.opsForValue().increment("mit_sf_cur_userId_PROD", 1);
            workCode = workCode - 1;

            //修改临时表 提供给DHR
            dataUserTemp.setNewUserId(newUserId);
            dataUserTemp.setWorkNumber(workCode + "");
            dataUserTempMapper.updateDataUserTempById(dataUserTemp);

            User user = new User();
            user.setUserId(newUserId);
            user.setSpell(dataUserTemp.getSpell());
            user.setCell(dataUserTemp.getCell());
            user.setWorkNumber(workCode + "");
            user.setStatus("");
            user.setEmployType("正式员工");
            user.setEntryFlow("在职");
            user.setCreateTag(4);

            Team team = teamMapper.selectTeamByName(dataUserTemp.getTeam());
            if (team != null) {
                user.setTeam(team.getName());
                user.setTeamId(team.getId());
                user.setTeamCode(team.getSfCode());
                user.setHrbp(team.getHrg());
            }
            userMapper.insert(user);


        }

        System.out.println(1);

    }

    @Test
    public void testUpdate() {
        User user = userService.getUserByUserId("xuwenjiekuangshi");

        user.setCreateTag(4);

        userService.updateUser(user);

        System.out.println(1);
    }

}
