//package com.megvii.config;
//
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.aspectj.lang.annotation.Aspect;
//import org.springframework.aop.Advisor;
//import org.springframework.aop.aspectj.AspectJExpressionPointcut;
//import org.springframework.aop.support.DefaultPointcutAdvisor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.transaction.PlatformTransactionManager;
//import org.springframework.transaction.TransactionDefinition;
//import org.springframework.transaction.interceptor.DefaultTransactionAttribute;
//import org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource;
//import org.springframework.transaction.interceptor.TransactionInterceptor;
//
//import java.util.Arrays;
//
///***
// * @Author: sunkui
// * @Date: 2020/5/18
// * @Time: 10:50
// * @Description: 统一全局事务配置
// ***/
//@Slf4j
//@Data
//@Aspect
//@Configuration
//@ConditionalOnExpression("${transaction.enable:true}")
//public class TransactionAdviceConfig {
//    private static final String AOP_POINTCUT_EXPRESSION = "execution (* com.megvii.service..*.*(..))";
//
//    @Autowired
//    private PlatformTransactionManager transactionManager;
//
//    @Value("#{ @environment['transaction.write-prefixs'] ?: null }")
//    private String[] writePrefix;
//
//    @Value("#{ @environment['transaction.method-expression'] ?: null }")
//    private String expression;
//
//    @Bean
//    public TransactionInterceptor txAdvice() {
//
//        DefaultTransactionAttribute txAttribute= new DefaultTransactionAttribute();
//        txAttribute.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
//
//        //只读方法事务，暂时忽略
//        /*DefaultTransactionAttribute txAttr_REQUIRED_READONLY = new DefaultTransactionAttribute();
//        txAttr_REQUIRED_READONLY.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
//        txAttr_REQUIRED_READONLY.setReadOnly(true);*/
//        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
//        Arrays.asList(writePrefix).forEach(prefix->{
//            log.info("增加事务的方法前缀{}",prefix);
//            source.addTransactionalMethod(prefix, txAttribute);
//        });
//        return new TransactionInterceptor(transactionManager, source);
//    }
//
//    @Bean
//    public Advisor txAdviceAdvisor() {
//        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();
//        pointcut.setExpression(StringUtils.isNotEmpty(expression) ? expression:AOP_POINTCUT_EXPRESSION);
//        return new DefaultPointcutAdvisor(pointcut, txAdvice());
//    }
//
//}
