package com.megvii.config;


import javax.annotation.PreDestroy;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@PropertySource("classpath:koala.${spring.profiles.active}.properties")
public class FtpConfiguration {

    Logger logger= LoggerFactory.getLogger(FtpConfiguration.class);
    @Value("${ftp_host}")
    private String host;
    @Value("${ftp_port}")
    private int port= FTPClient.DEFAULT_PORT;
    @Value("${ftp_username}")
    private String username;
    @Value("${ftp_password}")
    private String password;
    @Value("${ftp_bufferSize}")
    private int bufferSize = 1024;
    @Value("${ftp_initialSize}")
    private Integer initialSize = 0;
    @Value("${ftp_encoding}")
    private String encoding;

    private ObjectPool<FTPClient> pool;
    /**
     * 预先加载FTPClient连接到对象池中
     * @param initialSize 初始化连接数
     * @param maxIdle 最大空闲连接数
     */
    private void preLoadingFtpClient(Integer initialSize, int maxIdle) {
        if (initialSize == null || initialSize <= 0) {
            return;
        }

        int size = Math.min(initialSize.intValue(), maxIdle);
        for (int i = 0; i < size; i++) {
            try {
                pool.addObject();
            } catch (Exception e) {
                logger.error("preLoadingFtpClient error...", e);
            }
        }
    }

    @PreDestroy
    public void destroy() {
        if (pool != null) {
            pool.close();
            logger.info("销毁ftpClientPool...");
        }
    }
    /**
     * 根据条件判断不存在ElsService时初始化新bean到SpringIoc
     * @return
     */
    @Bean
    public FtpService ftpService()
            throws Exception{
        logger.info(">>>The IotFtpService Not Found，Execute Creat New Bean.");
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setMinEvictableIdleTimeMillis(60000);
        poolConfig.setSoftMinEvictableIdleTimeMillis(50000);
        poolConfig.setTimeBetweenEvictionRunsMillis(30000);
        pool = new GenericObjectPool<>(new FtpClientPooledObjectFactory(host,port,username,password,encoding,bufferSize), poolConfig);
        preLoadingFtpClient(initialSize, poolConfig.getMaxIdle());
        FtpService ftpService =new FtpService();
        ftpService.setFtpClientPool(pool);
        ftpService.setHasInit(true);
        return ftpService;
    }


    /**
     * FtpClient对象工厂类
     */
    static class FtpClientPooledObjectFactory implements PooledObjectFactory<FTPClient> {
        private Logger log = LoggerFactory.getLogger(this.getClass());
        private String host;
        private int port;
        private String userName;
        private String password;
        private String encoding;
        private int bufferSize;

        public FtpClientPooledObjectFactory(String host, int port, String userName, String password, String encoding,int bufferSize) {
            this.host = host;
            this.port = port;
            this.userName = userName;
            this.password = password;
            this.encoding = encoding;
            this.bufferSize=bufferSize;
        }

        @Override
        public PooledObject<FTPClient> makeObject() throws Exception {
            FTPClient ftpClient = new FTPClient();
            try {
                ftpClient.connect(host, port);
                ftpClient.login(userName, password);
                log.info("连接FTP服务器返回码{}", ftpClient.getReplyCode());
                ftpClient.setBufferSize(bufferSize);
                ftpClient.setControlEncoding(encoding);
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                ftpClient.enterLocalPassiveMode();
                return new DefaultPooledObject<>(ftpClient);
            } catch (Exception e) {
                log.error("建立FTP连接失败", e);
                if (ftpClient.isAvailable()) {
                    ftpClient.disconnect();
                }
                ftpClient = null;
                throw new Exception("建立FTP连接失败", e);
            }
        }

        @Override
        public void destroyObject(PooledObject<FTPClient> p) throws Exception {
            FTPClient ftpClient = getObject(p);
            if (ftpClient != null && ftpClient.isConnected()) {
                ftpClient.disconnect();
            }
        }

        @Override
        public boolean validateObject(PooledObject<FTPClient> p) {
            FTPClient ftpClient = getObject(p);
            if (ftpClient == null || !ftpClient.isConnected()) {
                return false;
            }
            try {
                ftpClient.changeWorkingDirectory("/");
                return true;
            } catch (Exception e) {
                log.error("验证FTP连接失败::{}",e.getMessage());
                return false;
            }
        }

        @Override
        public void activateObject(PooledObject<FTPClient> p) throws Exception {
        }

        @Override
        public void passivateObject(PooledObject<FTPClient> p) throws Exception {
        }

        private FTPClient getObject(PooledObject<FTPClient> p) {
            if (p == null || p.getObject() == null) {
                return null;
            }
            return p.getObject();
        }

    }




}
