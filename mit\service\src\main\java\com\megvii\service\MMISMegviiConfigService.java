package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.entity.opdb.MMISMegviiConfig;
import com.megvii.mapper.MMISMegviiConfigMapper;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class MMISMegviiConfigService {

    @Autowired
    private MMISMegviiConfigMapper mmisMegviiConfigMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 根据公司主体名称获取公司配置 优先从redis中获取 获取失败去查数据库
     * redis 有效期3600S
     * @param name
     * @return
     */
    public JSONObject getCompanyConfigByName(String name) {
        Object f1 = redisTemplate.opsForHash().get("mitplatform_contract_company_config", name);
        if (f1 != null) {
            return (JSONObject) f1;
        }
        Map<String, JSONObject> map = new HashMap<>();
        MMISMegviiConfig config = mmisMegviiConfigMapper.selectMMISMegviiConfigByKey("contract_company");
        if (config != null && config.getValue() != null) {
            JSONArray array = JSONArray.parseArray(config.getValue());
            for (Object o : array) {
                JSONObject data = (JSONObject) o;
                map.put(data.getString("name"), data);
            }
            redisTemplate.opsForHash().putAll("mitplatform_contract_company_config", map);
            redisTemplate.expire("mitplatform_contract_company_config", 3600, TimeUnit.SECONDS);
            return map.get(name);
        } else {
            return null;
        }
    }



    /**
     * 获取岗位名称
     * @param code
     * @return
     */
    public String getPositionByCode(String code) {
		Object f1 = redisTemplate.opsForHash().get("mitexecutor_dhr_position", code);
		return f1!=null?f1.toString():null;
	}

    /**
     * 查询config表的json
     * 中台CN_的key才能使用
     * @param key
     * @return
     */
     public JSONArray getJsonarrayByKey(String key){
        Map<String,Object> map = new HashMap<>();
        MMISMegviiConfig config = mmisMegviiConfigMapper.selectMMISMegviiConfigByKey(key);
        if (config != null && config.getValue() != null) {
            return JSONArray.parseArray(config.getValue());
        } else {
            return null;
        }
    }


    /**
     * 根据key 返回map name-code
     * 转换为jsonobj
     * @param key
     * @return
     */
    public Map<String,Object> getConfigJsonByKey(String key){
        Map<String,Object> map = new HashMap<>();
        MMISMegviiConfig config = mmisMegviiConfigMapper.selectMMISMegviiConfigByKey(key);
        if (config != null && config.getValue() != null) {
            JSONObject value = JSONObject.parseObject(config.getValue());
            map = value.getInnerMap();
        }
        return map;
    }

    /**
     * 根据中台key 返回map code-name
     * 转换为jsonarray
     * @param key
     * @return
     */
    public Map<String,Object> getConfigCodeMapByKey(String key){
        Map<String,Object> map = new HashMap<>();
        MMISMegviiConfig config = mmisMegviiConfigMapper.selectMMISMegviiConfigByKey(key);
        if (config != null && config.getValue() != null) {
            JSONArray value = JSONArray.parseArray(config.getValue());
            for (Object o : value) {
                JSONObject data = (JSONObject) o;
                map.put(data.getString("code"), data.getString("name"));
            }
        }
        return map;
    }


    /**
     * 根据中台key 返回map name-code
     * 转换为jsonarray
     * @param key
     * @return
     */
    public Map<String,Object> getConfigValueMapByKey(String key){
        Map<String,Object> map = new HashMap<>();
        MMISMegviiConfig config = mmisMegviiConfigMapper.selectMMISMegviiConfigByKey(key);
        if (config != null && config.getValue() != null) {
            JSONArray value = JSONArray.parseArray(config.getValue());
            for (Object o : value) {
                JSONObject data = (JSONObject) o;
                map.put(data.getString("name"), data.getString("code"));
            }
        }
        return map;
    }

    /**
     * key = 中文地区名称
     * @return
     */
    public Map<String,Object> getWorkSiteMap(){
        Map<String,Object> workSiteMap=new HashMap<>();
        MMISMegviiConfig siteConfig=mmisMegviiConfigMapper.selectMMISMegviiConfigByKey("WORK_SITE");
        if (siteConfig!=null&&siteConfig.getValue()!=null){
            JSONObject jsonObject= JSONObject.parseObject(StringEscapeUtils.unescapeJava(siteConfig.getValue()));
            workSiteMap=jsonObject.getInnerMap();
        }
        return workSiteMap;
    }

    /**
     * key = SF数字
     * @return
     */
    public Map<String, String> getWorkSiteCodeMap(){
        Map<String, Object> workSiteMap = new HashMap<>();
        Map<String, String> workSiteCodeMap = new HashMap<>();
        MMISMegviiConfig siteConfig = mmisMegviiConfigMapper.selectMMISMegviiConfigByKey("WORK_SITE");
        if (siteConfig != null && siteConfig.getValue() != null){
            JSONObject jsonObject = JSONObject.parseObject(StringEscapeUtils.unescapeJava(siteConfig.getValue()));
            workSiteMap = jsonObject.getInnerMap();

            for (String key : workSiteMap.keySet()) {
                workSiteCodeMap.put(workSiteMap.get(key).toString(), key);
            }
        }
        return workSiteCodeMap;
    }

    /**
     * 添加方法
     * @param config
     */
    public boolean updateMegviiConfig(MMISMegviiConfig config) {
        return mmisMegviiConfigMapper.updateMMISMegviiConfigByKey(config)>0?true:mmisMegviiConfigMapper.addMMISMegviiConfig(config)>0?true:false;
    }



}
