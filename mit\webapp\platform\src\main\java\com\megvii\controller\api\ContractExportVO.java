package com.megvii.controller.api;

import com.megvii.entity.opdb.Column;
import com.megvii.entity.opdb.contract.ContractExportDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ContractExportVO {
	private static final long serialVersionUID=1L;

	@Column(index = 1, name = "工号")
	private String workNumber;
	@Column(index = 2, name = "ID")
	private String userId;
	@Column(index = 3, name = "姓名")
	private String spell;
	@Column(index = 4, name = "入职日期")
	private String expectDate;
	@Column(index = 5, name = "合同类型")
	private String signStatus;
	@Column(index = 6, name = "合同编号")
	private String contractNumber;
	@Column(index = 7, name = "合同主体")
	private String company;
	@Column(index = 8, name = "合同开始时间")
	private String startDate;
	@Column(index = 9, name = "合同结束时间")
	private String endDate;
	@Column(index = 10, name = "当前合同状态")
	private String contractStatus;

	public static List<ContractExportVO> toVOs (List<ContractExportDto> contractExportDtos) {
		List<ContractExportVO> vos = new ArrayList<>();
		for (ContractExportDto contractExportDto : contractExportDtos) {
			ContractExportVO contractExportVO=new ContractExportVO();
			BeanUtils.copyProperties(contractExportDto,contractExportVO);
			vos.add(contractExportVO);
		}
		return vos;
	}
}
