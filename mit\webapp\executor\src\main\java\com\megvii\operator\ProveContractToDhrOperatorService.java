package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import com.megvii.service.contract.ProveContractBacklogService;
import com.megvii.service.contract.ProveContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
@Service
public class ProveContractToDhrOperatorService extends BaseOperatorService{
    @Autowired
    ProveContractToDhrOperatorService proveContractToDhrOperatorService;

    @Autowired
    DhrService dhrService;

    @Autowired
    private ProveContractService proveContractService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        return proveContractToDhrOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        String msg = proveContractService.sendEmailAndPushDHR();
        return new OperateResult(1,msg);
    }
}
