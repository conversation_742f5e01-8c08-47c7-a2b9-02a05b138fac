package com.megvii.job;

import com.megvii.common.OperateResult;
import com.megvii.common.ParameterUtil;
import com.megvii.operator.SayHelloOperateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 测试用例
 */
@JobHandler(value="helloJobHandler")
@Component
public class SayHelloExcutorHandler extends IJobHandler  {

    Logger logger= LoggerFactory.getLogger(SayHelloExcutorHandler.class);
    @Autowired
    SayHelloOperateService sayHelloService;
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try{
            OperateResult result=sayHelloService.operateMain(ParameterUtil.getParaMap(s));
            return new ReturnT(200,result.getMsg());
        }catch (Exception e){
            logger.error("",e);
            throw e;
        }

    }
}
