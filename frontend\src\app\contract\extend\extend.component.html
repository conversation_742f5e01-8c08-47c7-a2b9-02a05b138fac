<ng-container *ngIf="isShowSelf">
  <nz-breadcrumb>
    <nz-breadcrumb-item> 合同管理 </nz-breadcrumb-item>
    <nz-breadcrumb-item> 续签合同 </nz-breadcrumb-item>
  </nz-breadcrumb>
  <div class="content-block content-item">
    <app-title title="续签合同"></app-title>
    <nz-tabset
      nzSize="large"
      (nzSelectedIndexChange)="selectedIndexChange($event)"
      [nzSelectedIndex]="selectedIndex"
    >
      <nz-tab [nzTitle]="extendTpl">
        <ng-template #extendTpl>
          <nz-badge [nzCount]="extend.total || 0" [nzOffset]="[15, -5]"> 待续签 </nz-badge>
        </ng-template>
        <app-contract-table type="extend" #extend></app-contract-table>
      </nz-tab>
      <nz-tab [nzTitle]="voidingTpl">
        <ng-template #voidingTpl>
          <nz-badge [nzCount]="voiding.total || 0" [nzOffset]="[15, -5]"> 待作废 </nz-badge>
        </ng-template>
        <app-contract-table type="voiding" #voiding></app-contract-table>
      </nz-tab>
    </nz-tabset>
  </div>
</ng-container>
<router-outlet (activate)="onActivate($event)" (deactivate)="onDeactivate($event)"> </router-outlet>
