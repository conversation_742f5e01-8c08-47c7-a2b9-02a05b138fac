package com.megvii.controller.jwt.api;

import com.megvii.entity.opdb.Team;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/3/25 18:12
 */
@Data
public class AssetTeamInfoVO {

	private String sfCode;
	private String name;
    private String fullName;
    private String parent;

    public static List<AssetTeamInfoVO> toVOs(List<Team> teams) {
        List<AssetTeamInfoVO> vos = new ArrayList<>();
        for (Team team : teams) {
            vos.add(toVO(team));
        }

        return vos;
    }

    private static AssetTeamInfoVO toVO(Team team) {
        AssetTeamInfoVO vo = new AssetTeamInfoVO();
        vo.setSfCode(team.getSfCode());
        vo.setFullName(team.getName());
        vo.setName(team.getShortName()!=null?team.getShortName():"");
        vo.setParent(team.getParentCode()!=null?team.getParentCode():"");

        return vo;
    }

}
