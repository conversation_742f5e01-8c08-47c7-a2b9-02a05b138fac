<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.lunaMapper.LunaDiracUserMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.luna.LunaDiracUser">
        <id column="id" property="id"/>
        <result column="email" property="email"/>
        <result column="username" property="username"/>
        <result column="password_hash" property="passwordHash"/>
        <result column="confirmed" property="confirmed"/>
        <result column="name" property="name"/>
        <result column="group" property="group"/>
        <result column="location" property="location"/>
        <result column="about_me" property="aboutMe"/>
        <result column="member_since" property="memberSince"/>
        <result column="last_seen" property="lastSeen"/>
        <result column="avatar_hash" property="avatarHash"/>
        <result column="cname" property="cname"/>
        <result column="phone" property="phone"/>
    </resultMap>

    <select id="selectUserByUsername" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from dirac_users where username = #{username}
    </select>

    <update id="updateLunaDiracUser" parameterType="com.megvii.entity.luna.LunaDiracUser">
        update dirac_users
        <set>
            <if test="cname != null ">
                cname = #{cname},
            </if>
            <if test="phone != null ">
                phone = #{phone},
            </if>
            <if test="location != null ">
                location = #{location},
            </if>
        </set>
        where id = #{id}
    </update>



</mapper>