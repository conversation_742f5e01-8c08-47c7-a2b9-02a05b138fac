package com.megvii.mapper;

import com.megvii.entity.opdb.UserDimissionContractSnapshotPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 离职合同快照 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
public interface UserDimissionContractSnapshotMapper {

  Boolean addSnapshot(UserDimissionContractSnapshotPO po);
  Boolean updateSnapshot(UserDimissionContractSnapshotPO po);

  UserDimissionContractSnapshotPO  getSnapshot(@Param("dismissionId") String dismissionId);

  Boolean deleteSnapshot(@Param("dismissionId") String dismissionId);
}
