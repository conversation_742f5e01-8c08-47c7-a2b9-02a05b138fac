package com.megvii.entity.opdb.contract;

import com.megvii.entity.opdb.User;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/10 17:08
 */
@Data
public class UserContractBacklog {

	private int id;
	private String userId;
	private String workNumber;
	/**
	 * 已拉取 从DHR拉取
	 * 已签署 已签署完成
	 * 已完成 DHR已结束操作
	 */
	private String status;
	private LocalDateTime createTime;
	private String updateTime;

	private User user;

	/**
	 * 当前合同截止日期
	 */
	private String endDate;
	private String dimissionTag;
}
