package com.megvii.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/5/12 16:21
 */
@RestController
@RequestMapping("/api/rsa")
public class RSAController {

	@Autowired
	private RedisTemplate redisTemplate;

//	@GetMapping("/key")
//	@ResponseBody
//	public ResponseDataEntity getPublicKey() {
//		ResponseDataEntity response = new ResponseDataEntity();
//		try {
//			Map<String, Object> keyMap = RSACoder.initKey();
//			//公钥
//			byte[] publicKey = RSACoder.getPublicKey(keyMap);
//			//私钥
//			byte[] privateKey = RSACoder.getPrivateKey(keyMap);
//
//			//获取已有map
//			Map<String, String> map = redisTemplate.opsForHash().entries("keyyyyyyyyyyy");
//			//存入redis
//			map.put(Base64.encodeBase64String(publicKey), Base64.encodeBase64String(privateKey));
//			redisTemplate.opsForHash().putAll("keyyyyyyyyyyy",map);
//			redisTemplate.expire("keyyyyyyyyyyy", 120, TimeUnit.SECONDS);
//			response.setCode(ResponseDataEntity.OK);
//			response.setData(Base64.encodeBase64String(publicKey));
//			response.setMsg("success");
//			return response;
//		} catch (Exception e) {
//			response.setCode(ResponseDataEntity.ERROR);
//			response.setMsg("生成失败");
//			return response;
//		}
//	}
//
//
//	@GetMapping("/view")
//	@ResponseBody
//	public ResponseDataEntity getContractView(@RequestParam("param") String param, @RequestParam("key") String key) {
//		ResponseDataEntity response = new ResponseDataEntity();
//
//		try {
//			//获取私钥
//			String privateKey = (String) redisTemplate.opsForHash().get("keyyyyyyyyyyy", key);
//			byte[] userId = RSACoder.decryptByPrivateKey(Base64.decodeBase64(param), Base64.decodeBase64(privateKey));
//
//			response.setCode(ResponseDataEntity.ERROR);
//
//		} catch (Exception e) {
//			response.setCode(ResponseDataEntity.ERROR);
//			response.setMsg(e.getMessage());
//		}
//		return response;
//	}



}
