package com.megvii.cas;

import io.buji.pac4j.filter.SecurityFilter;
import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.http.HttpStatus;

/**
 * @创建人 cl
 * @创建日期 2019/10/16
 * @描述
 */
public class SessionFilter extends SecurityFilter {


    private String[] excludeUrls;
    private String redirect;


    public SessionFilter(String[] excludeUrls, String redirect){
        this.excludeUrls = excludeUrls;
        this.redirect = redirect;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest)servletRequest;
        HttpServletResponse res = (HttpServletResponse)servletResponse;
        String uri = req.getRequestURI();
        //排除掉
        if(!needFilter(uri, excludeUrls)) {
            filterChain.doFilter(req, res);
            return;
        }
        Subject subject = SecurityUtils.getSubject();
        boolean flg = subject.isAuthenticated();
        //session失效
        if(!flg && !req.getRequestURI().contains("/api/V1/login")) {
            if (req.getRequestURI().contains("/api/contract/view")) {
                ((HttpServletResponse) servletResponse).sendRedirect(redirect);
                return;
            }
            subject.logout();
            res.setStatus(HttpStatus.REQUEST_TIMEOUT.value());
            res.setHeader("sessionStatus",String.valueOf(HttpStatus.REQUEST_TIMEOUT.value()));
            return;
        }
        super.doFilter(servletRequest,servletResponse,filterChain);
    }


    private boolean needFilter(String uri, String[] excludeList) {
        if(excludeList == null || excludeList.length == 0) {
            return true;
        }
        for(String exclude : excludeList) {
            if(StringUtils.isNotBlank(exclude) && uri.indexOf(exclude) != -1) {
                return false;
            }
        }
        return true;
    }

}
