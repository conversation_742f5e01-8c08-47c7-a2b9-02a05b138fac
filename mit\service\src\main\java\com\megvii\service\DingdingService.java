package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DingDingRestClient;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@PropertySource(value = "classpath:dingding.${spring.profiles.active}.properties",encoding = "UTF-8")
@Service
public class DingdingService {

    private Logger logger= LoggerFactory.getLogger(DingdingService.class);

    @Value("${DINGDING_IT_SUPPORT_ID}")
    private String itSupportId;

    @Value("${DINGDING_IT_SUPPORT_DEPT_ID}")
    private Integer itSupportDeptId;

    @Value("#{${ROBOT_TOKEN_MAP}}")
    private Map<String,String> robotTokenMap = new HashMap<>();

    @Autowired
    private DingDingRestClient dingDingRestClient;

    @Autowired
    private TeamService teamService;

    @Autowired
    private UserService userService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private LdapService ldapService;

    @Autowired
    private CheckService checkService;


    /**
     * 发送入职日期顺延的hook
     * @param spell
     * @param userId
     * @param oldDate
     * @param newDate
     * @return
     */
    public boolean sendEntryDelayHook(String spell, String userId, String oldDate, String newDate) {
    	JSONObject markdown = new JSONObject();
        markdown.put("title", "[入职日期顺延]");
        markdown.put("text", "### 入职日期顺延 \n  ##### " + spell + " " + userId + "  \n ##### " + oldDate + "->" + newDate);

        JSONObject data = new JSONObject();
        data.put("msgtype", "markdown");
        data.put("markdown", markdown);

        return dingDingRestClient.sendRobotHook(robotTokenMap.get("entryDelay"), data);
	}


    /**
     * 发送OA回传MIT失败的hook
     * @param msg
     * @return
     */
    public boolean sendOa2MitErrorHook(String msg) {
    	JSONObject markdown = new JSONObject();
        markdown.put("title", "[OA回传MIT失败]");
        markdown.put("text", "### OA回传MIT失败  \n  ##### " + msg);

        JSONObject data = new JSONObject();
        data.put("msgtype", "markdown");
        data.put("markdown", markdown);

        return dingDingRestClient.sendRobotHook(robotTokenMap.get("oa2mit"), data);
	}


    /**
     * 发送同步钉钉id失败的消息推送
     * @param user
     * @return
     */
    public boolean sendSyncDingErrorHook(User user) {
    	JSONObject markdown = new JSONObject();
        markdown.put("title", "[拉取钉钉未成功]");
        markdown.put("text", "### 拉取钉钉未成功  \n  ##### " + user.getSpell() + "    " + user.getUserId() +
				"\n  ##### " + user.getTeam() + "  \n ##### " + user.getCell());

        JSONObject data = new JSONObject();
        data.put("msgtype", "markdown");
        data.put("markdown", markdown);

        return dingDingRestClient.sendRobotHook(robotTokenMap.get("syncDing"), data);
	}


    /**
     *
     * @param user
     * @return
     */
    public String addDingdingId(User user) {
        StringBuilder msg = new StringBuilder();
        try {
            String dingdingId = dingDingRestClient.getDingdingIdByMobile(user.getCell());
            user.setDingdingId(dingdingId);
            if (!StringUtils.isBlank(dingdingId)) {
                JSONObject data = dingDingRestClient.getUserDetail(dingdingId);
                if (data == null) {
                    msg.append("钉钉查询" + user.getUserId() + "信息错误");
                    return msg.toString();
                }
                String unionId = data.getString("unionid");
                user.setUnionId(unionId);
            }
            userService.updateUser(user);
            msg.append(user.getUserId() + "修改钉钉id成功");

            //实习生需要单独添加群聊
            if (user.getEmployType().indexOf("实习生")!=-1) {
                msg.append("--" + addUserChat(user));
            }

            return msg.toString();
        } catch (Exception e) {
            sendSyncDingErrorHook(user);
            return e.getMessage();
        }
    }

    /**
     * 钉钉消息推送
     * @param title

     * @param worknumber
     * @return
     * @throws Exception
     */
    public boolean sendDingMsgV2(String title, String msg, String worknumber) throws Exception {
        User user = userService.getUserByWorkNumber(worknumber);
        if (user == null || user.getStatus().equals("已禁用") || user.getDingdingId() == null) {
            throw new Exception(worknumber + "信息异常，无法发送钉钉消息");
        }

        JSONObject markdown = new JSONObject();
        markdown.put("title", title);
        markdown.put("text", msg);

        JSONObject dhrdata = new JSONObject();
        dhrdata.put("msgtype", "markdown");
        dhrdata.put("markdown", markdown);

        return dingDingRestClient.sendTopMsg(dhrdata, user.getDingdingId());
    }


    /**
     * DHR消息推送
     * @param title
     * @param msg
     * @param url
     * @param worknumber
     * @return
     * @throws Exception
     */
    public boolean sendDhrWorkMsg(String title, String msg, String url, String worknumber) throws Exception {
        User user = userService.getUserByWorkNumber(worknumber);
        if (user == null || user.getStatus().equals("已禁用") || user.getDingdingId() == null) {
            throw new Exception(worknumber + "信息异常，无法发送钉钉消息");
        }

        JSONObject markdown = new JSONObject();
        markdown.put("title", title);
        markdown.put("text", "### DHR消息提醒  \n  ##### " + msg + "  \n  [查看详情](" + url + ")");

        JSONObject dhrdata = new JSONObject();
        dhrdata.put("msgtype", "markdown");
        dhrdata.put("markdown", markdown);

        return dingDingRestClient.sendTopMsg(dhrdata, user.getDingdingId());
    }



    /**
     * 修改钉钉上级部门
     * @param code
     * @param parentDingdingId
     * @param dingdingId
     * @param shortName
     * @return
     */
    public String updateDingdingParent(String code, String dingdingId, String parentDingdingId, String shortName) {

//        Team parentTeam = teamService.getTeamByCode(parentCode);
//        if (parentTeam == null) {
//            return code + "上级部门" + parentCode + "不存在";
//        }
//        if (parentTeam.getDingdingId() == null) {
//            return parentTeam.getSfCode() + "钉钉ID不存在";
//        }
        try {
            if (dingDingRestClient.updateDeptParent(dingdingId, parentDingdingId, shortName)) {
                return code + "修改钉钉上级部门[成功]";
            } else {
                return code + "修改钉钉上级部门[失败]";
            }
        } catch (Exception e) {
            return code + "修改钉钉上级部门[异常]";
        }
    }


    /**
     * 修改钉钉包含子部门属性
     * 全量刷
     * @return
     */
    public String notContainSubDept() {
        StringBuilder msg = new StringBuilder();

        List<Team> teams = teamService.getActiviteTeam();
        for (Team team : teams) {
            // yq挂在旷视集团  twb挂在运营支撑体系
            if (team.getName().equals("旷视集团") || team.getName().equals("运营支撑体系")) {
                continue;
            }
            if (team.getDingdingId() == null) {
                continue;
            }

            try {
                JSONObject data = dingDingRestClient.getDeptDetail(Integer.valueOf(team.getDingdingId()));

                if (data.getString("errcode") != null && !data.getString("errcode").equals("0")) {
                    logger.error(team.getName() + "查询钉钉部门属性异常：" + data.getString("errmsg"));
                    msg.append(team.getName() + "查询钉钉部门属性<span style=\"color:#dc143c;\">【异常】</span>：" + data.getString("errmsg") + "</br>");
                    continue;
                }

                if (data.getString("groupContainSubDept") != null && data.getString("groupContainSubDept").equals("true")) {
                    continue;
                }

                String shortName = team.getShortName();
                if (shortName == null) {
                    shortName = team.getName().split("-")[team.getName().split("-").length - 1];
                }

                if (dingDingRestClient.updateDeptContainSubDept(team.getDingdingId(), shortName)) {
                     msg.append(shortName + "******修改钉钉包含子部门属性<span style=\"color:#3eda67;\">【成功】</span></br>");
                } else {
                     msg.append(shortName + "******修改钉钉包含子部门属性<span style=\"color:#dc143c;\">【失败】</span></br>");
                }

            } catch (Exception e) {
                logger.error(team.getName() + "修改钉钉包含子部门属性异常：" + e);
                msg.append(team.getName() + "修改钉钉包含子部门属性<span style=\"color:#dc143c;\">【异常】</span></br>");
                continue;
            }
        }

        if (msg.toString().equals("")) {
            msg.append("没有需要修改包含子部门属性的部门");
        } else {
            mailSendService.sendMail("更新钉钉未开启包含子部门提醒", msg.toString(), "<EMAIL>,<EMAIL>");
        }

        return msg.toString();
    }



    /**
     * 删除钉钉用户
     * @param user
     * @return
     */
    public String deleteDingdingUser(User user) {
        if (user.getDingdingId() == null) {
            return user.getUserId() + "钉钉ID is null";
        }

        try {
            return dingDingRestClient.deleteUser(user.getUserId(), user.getDingdingId());
        } catch (Exception e) {
            logger.error(user.getUserId() + "删除钉钉错误");
            return user.getUserId() + "删除钉钉错误";
        }
    }


    /**
     * 修改钉钉部门,钉钉名称
     * @param user
     * @param oldTeamId 老的钉钉部门id
     * @return
     * @throws Exception
     */
    public boolean updateDingdingUser(User user,Integer oldTeamId) throws Exception {
        Integer[] deptId = new Integer[1];
        StringBuffer spell = new StringBuffer();

        boolean result = checkInfo(user, deptId, spell);
        if (!result) {
            logger.info(user.getUserId() + "根据规则不添加钉钉");
            return false;
        }

        List list = new ArrayList();
        for (int i = 0; i < deptId.length; i ++) {
            list.add(deptId[i]);
        }
        JSONObject userDetail = dingDingRestClient.getUserDetail(user.getDingdingId());
        String dept=userDetail.getString("department");
        List<Integer> oldList = JSONArray.parseArray(dept, Integer.class);//老的钉钉部门，包含手动加的和本身部门
        if(oldTeamId!=null&&list.contains(oldTeamId)){//新老部门一样，代表是实习转正或者改名
            //传老部门就完了
        }else {//不一样代表调整部门，也可能是实习转正
            oldList.addAll(list); //把新部门加进去
            if(oldTeamId!=null){
                Team oldTeam = teamService.getTeamByDingDingId(oldTeamId);
                if(oldTeam!=null&&!oldTeam.getTeamLeader().equals(user.getUserId())){//如果原部门leader不是他，就退出原钉钉部门
                    oldList.remove(oldTeamId); //把老部门删除，保证手动加的部门不变
                }
            }
        }
        return dingDingRestClient.updateUserDept(user.getDingdingId(), spell.toString(), oldList);
    }

    /**
     * 修改用户钉钉部门
     * @param userId
     * @param teamDingId 部门钉钉id
     * @param type  true为增加，false为删除
     * @return
     * @throws Exception
     */
    public String updateUserDingdingDept(String userId,Integer teamDingId,Boolean type) throws Exception {
        User user = userService.getUserByUserId(userId);
        if(user==null||StringUtils.isEmpty(user.getDingdingId())){
            return userId+"：用户不存在，或者钉钉id为空";
        }
        if(teamDingId==null){
            return userId+"待修改钉钉部门为空";
        }
        JSONObject userDetail = dingDingRestClient.getUserDetail(user.getDingdingId());
        String dept=userDetail.getString("department");
        List<Integer> oldList = JSONArray.parseArray(dept, Integer.class);//老的钉钉部门，包含手动加的和本身部门
        if(type){
            if (oldList.contains(teamDingId)){
                return userId + "已存在";
            }else {
                oldList.add(teamDingId);
            }
        }else {
            if (oldList.contains(teamDingId)){
                oldList.remove(teamDingId);
            }else{
                return userId + "不存在";
            }
        }
        Boolean res = dingDingRestClient.updateUserDept(user.getDingdingId(), null, oldList);
        if(res){
            return "";
        }else {
            return "userId:"+userId+"teamDingId:"+teamDingId+"type:"+type+"失败；";
        }

    }

    /**
     * 创建钉钉用户
     * @param user
     * @return
     * @throws Exception
     */
    public String createDingdingUser(User user) throws Exception {
        StringBuilder msg = new StringBuilder();
        Integer[] deptId = new Integer[1];
        StringBuffer spell = new StringBuffer();

        boolean result = checkInfo(user, deptId, spell);
        if (!result) {
            msg.append(user.getUserId() + "根据规则不添加钉钉");
            return msg.toString();
        }

        String dingdingId = dingDingRestClient.createDingTalkUser(spell.toString(), deptId, user.getCell(), user.getWorkNumber(), user.getUserId());
        if (dingdingId != null) {
            JSONObject data = dingDingRestClient.getUserDetail(dingdingId);
            if (data == null) {
                msg.append("钉钉查询" + user.getUserId() + "信息错误");
                return msg.toString();
            }
            String unionId = data.getString("unionid");

            user.setDingdingId(dingdingId);
            user.setUnionId(unionId);
            userService.updateUserDingdingIdAndUnionid(user);
            msg.append(user.getUserId() + "添加钉钉账号成功");

            //实习生需要单独添加群聊
            if (user.getEmployType().indexOf("实习生")!=-1) {
                msg.append("--" + addUserChat(user));
            }

            return msg.toString();
        }

        return user.getUserId() + "添加钉钉请求异常，请查看日志。";
    }


    /**
     * 创建账号后实习生需要单独添加群聊
     * @param user
     * @return
     */
    public String addUserChat (User user) {
        StringBuilder msg = new StringBuilder();
        List<String> dingdingId = new ArrayList<>();
        dingdingId.add(user.getDingdingId());

        //统一先添加实习生群聊
        try {
            dingDingRestClient.addUserChat("chat654e4b414ebb83e17f453cfd870f13a8", dingdingId);
            msg.append("添加实习生群聊成功--");
        } catch (Exception e) {
            logger.error(user.getUserId() + "添加钉钉实习生群聊失败" + e.getMessage());
            msg.append(user.getUserId() + "添加钉钉实习生群聊失败--");
        }

//        //三个部门全职和兼职都要添加群聊
//        if (!(user.getTeam().indexOf("研究院")!=-1 || user.getTeam().indexOf("Brain++")!=-1 || user.getTeam().indexOf("Engine")!=-1)) {
//            //不在三个部门只兼职实习生需要单独添加
//            if (!user.getEmployType().equals("兼职实习生")) {
//                return user.getUserId() + ":" + user.getEmployType() + "--不在单独添加群聊范围";
//            }
//        }

        //目前特殊规则只剩余研究院
        if (!checkService.checkRTeam(user.getTeam())) {
            //不在三个部门只兼职实习生需要单独添加
            if (!user.getEmployType().equals("兼职实习生")) {
                return user.getUserId() + ":" + user.getEmployType() + "--不在单独添加群聊范围";
            }
        }

        List<String> chatIds = new ArrayList<>();
        String message = getUserDingdingChats(user, chatIds);
        if (message != null) {
            return message;
        }

        for (String chatId : chatIds) {
            try {
                dingDingRestClient.addUserChat(chatId, dingdingId);
            } catch (Exception e) {
                logger.error(user.getUserId() + "添加钉钉群聊失败" + e.getMessage());
                return user.getUserId() + "添加钉钉群聊失败" + e.getMessage();
            }
        }
        return user.getUserId() + "添加钉钉群聊成功";
    }

    /**
     * 获取初始群聊ID
     * @param user
     * @param chatIds
     * @return
     */
    public String getUserDingdingChats(User user, List<String> chatIds) {
        if (user.getTeamId() == null || user.getTeam() == null) {
            logger.error(user.getUserId() + "team 为 null 不添加钉钉chat");
            return user.getUserId() + "team 为 null 不添加钉钉chat";
        }

        Team team = teamService.getTeamById(user.getTeamId());
        if (team == null) {
            logger.error(user.getUserId() + "teamId没有关联team 不添加钉钉chat");
            return user.getUserId() + "teamId没有关联team 不添加钉钉chat";
        }

        String[] teamNames = team.getName().split("-");
        StringBuilder teamName = new StringBuilder();

        //循环查询chatId
        for (int i = 0; i < teamNames.length; i ++) {
            teamName.append(teamNames[i]);
            Team bu = teamService.getTeamByName(teamName.toString());
            if (bu != null) {
                chatIds.add(bu.getChatId());
                teamName .append("-");
                continue;
            } else {
                logger.error(user.getUserId() + teamName+  "不存在 不添加钉钉chat");
                return user.getUserId() + "不存在 不添加钉钉chat";
            }
        }

        //标注初始没有旷视群聊和研究院群聊
        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
            return null;
        }

//        if (user.getTeam().indexOf("研究院")!=-1) {
//            if (user.getEmployType().equals("正式员工") || user.getEmployType().equals("劳务派遣")) {
//                chatIds.add("chat26f74a2f9c77523a447720f0d4137a0f");
//            }
//        }

        if (checkService.checkRTeam(user.getTeam())) {
             if (user.getEmployType().equals("正式员工") || user.getEmployType().equals("劳务派遣")) {
                chatIds.add("chat26f74a2f9c77523a447720f0d4137a0f");
            }
        }

		chatIds.add("chata13fa055da565f636840325310d5af97");
        return null;
    }


    /**
     * 检查信息 修改spell
     * @param user
     * @param deptId
     * @param spell
     * @return
     */
    private boolean checkInfo(User user, Integer[] deptId, StringBuffer spell) {
        boolean isIntern = false;
//        if (user.getEmployType() != null && user.getEmployType().equals("兼职实习生") ||
//                (user.getTeam() != null && (user.getTeam().indexOf("研究院")!=-1 || user.getTeam().indexOf("Brain++")!=-1 || user.getTeam().indexOf("Engine")!=-1))
//                        && (user.getEmployType() != null && user.getEmployType().equals("全职实习生"))) {
//            isIntern = true;
//        }

        if (user.getEmployType() != null && user.getEmployType().equals("兼职实习生") ||
                (user.getTeam() != null && checkService.checkRTeam(user.getTeam()))
                        && (user.getEmployType() != null && user.getEmployType().equals("全职实习生"))) {
            isIntern = true;
        }

        if (user.getTeamId() == null) {
            logger.error(user.getUserId() + "teamId 为 null");
            return false;
        }

        Team team = teamService.getTeamById(user.getTeamId());

        if (team != null) {
            if (isIntern && team.getInternId() != null) {
                deptId[0] = Integer.valueOf(team.getInternId());
            } else if (!isIntern && team.getDingdingId() != null) {
                deptId[0] = Integer.valueOf(team.getDingdingId());
            }
        }

        if (deptId[0].equals(1) || deptId.length == 0) {
            return false;
        }
        //如果该部门是ZK部门，则不添加钉钉
        if (team != null && teamService.isZKTeam(team.getSfCode())) {
            return false;
        }

        spell.append(ldapService.getDisplayName(user));

        return true;
    }

    /**
     * 创建钉钉部门 更新部门群聊ID到MIT
     * @param team
     * @param parentDingdingId
     * @return
     */
    public boolean createDingDingDept(Team team, String parentDingdingId, boolean createChat) {
        try {
            //创建部门 获取deptId
            Integer dingdingId = Integer.valueOf(dingDingRestClient.createDept(team.getShortName(), null, parentDingdingId, createChat));
            team.setDingdingId(dingdingId.toString());
            //先保存部门钉钉ID
            teamService.updateTeam(team);
            if(StringUtils.isNotEmpty(team.getTeamLeader())){
                updateUserDingdingDept(team.getTeamLeader(),Integer.valueOf(team.getDingdingId()),true);
            }
            // hrg加入限制最低层级为BU
            Boolean sign = "001".equals(team.getLevel()) || "002".equals(team.getLevel()) || "006".equals(team.getLevel());
            if(StringUtils.isNotEmpty(team.getHrg()) & sign){
                updateUserDingdingDept(team.getHrg(),Integer.valueOf(team.getDingdingId()),true);
            }
            //保存chatId
//            saveChatId(team);
            //补充实习生id
//            if (parentDingdingId != null && !parentDingdingId.equals("1")) {
//                return updateInfoByParentDept(team);
//            }
            return true;
        } catch (Exception e) {
            logger.error(team.getName() + "创建钉钉部门和群聊错误" + e.getMessage());
            return false;
        }
    }

    /**
     * 保存群聊ID
     * @param team
     * @throws Exception
     */
    @Transactional
    public void saveChatId(Team team) throws Exception {
        Integer dingdingId = Integer.valueOf(team.getDingdingId());

        List<Integer> dingdingIds = new ArrayList<>();
        dingdingIds.add(itSupportDeptId);
        dingdingIds.add(dingdingId);
        //将IT支持加入部门
        dingDingRestClient.updateUserDept(itSupportId,null , dingdingIds);
        //暂停1000毫秒  1秒
        Thread.sleep(1000);
        //获取部门群聊ID
        JSONObject data = dingDingRestClient.getDeptDetail(dingdingId);
        String deptGroupChatId = data.getString("deptGroupChatId");
        team.setChatId(deptGroupChatId);
        //操作成功后更新team 防止出错后不保存
        teamService.updateTeam(team);
        //将IT支持T出部门
        dingdingIds.remove(1);
        dingDingRestClient.updateUserDept(itSupportId, null, dingdingIds);
    }


//    /**
//     * 根据上级部门信息更新邮件组，leader，hrg信息
//     * @param team
//     * @return
//     */
//    private boolean updateInfoByParentDept(Team team) throws Exception {
//        Team parentDept = teamService.getTeamByCode(team.getParentCode());
//        if (parentDept != null) {
////            team.setMailGroup(parentDept.getMailGroup());
////            team.setHrg(parentDept.getHrg());
////            team.setGroupLeader(parentDept.getGroupLeader());
//
//            if (team.getName().indexOf("员工体验团队")!=-1 || team.getName().indexOf("财法内控与投融资团队")!=-1 ||
//                    team.getName().indexOf("业务赋能团队")!=-1 || team.getName().indexOf("监察廉政团队")!=-1 ||
//                    team.getName().indexOf("政府事务及战略信息团队")!=-1 || team.getName().indexOf("安全团队")!=-1 ||
//                    team.getName().indexOf("总裁办")!=-1 ) {
//                team.setInternId(team.getDingdingId());
//            } else {
//                team.setInternId(parentDept.getInternId());
//            }
//
//            return teamService.updateTeam(team);
//
//        } else {
//            logger.error(team.getName() + "上级部门不存在");
//            throw new Exception(team.getName() + "上级部门不存在");
//        }
//    }




}
