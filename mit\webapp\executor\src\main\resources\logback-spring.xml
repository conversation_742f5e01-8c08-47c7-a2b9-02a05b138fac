<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径 -->
    <property name="LOG_HOME" value="/data/log" />
    <property name="MAIN_LOG" value="mit" />
    <property name="API" value="api" />
    <property name="AppName" value="executor" />
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}-%msg %n</pattern>
        </encoder>
    </appender>

    <!--sentry 日志-->
    <appender name="Sentry" class="com.getsentry.raven.logback.SentryAppender">
        <dsn>https://7f71af86a93441318cd11c85dc29c68c:<EMAIL>/10</dsn>
        <tags>tag1:value1,tag2:value2</tags>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <!-- Optional, allows to select the ravenFactory -->
        <!--<ravenFactory>net.kencochrane.raven.DefaultRavenFactory</ravenFactory>-->
    </appender>

    <!-- 设置分割 -->
    <appender name="FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 设置按尺寸和时间（同时满足）分割 -->
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/${MAIN_LOG}/${AppName}.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <!-- each file should be at most 10MB, keep 30 days worth of history,
                but at most 3GB -->
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <filter class="com.megvii.filter.MyLogFilter">
        </filter>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}-%msg%n</pattern>
        </encoder>
    </appender>
    <appender name="ApiInterceptor" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/${API}/request.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <!-- each file should be at most 10MB, keep 30 days worth of history,
                but at most 3GB -->
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}-%msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.megvii.aop.LoggingRestClientInterceptor" level="INFO">
        <appender-ref ref="ApiInterceptor"/>
    </logger>
    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
        <appender-ref ref="Sentry" />
    </root>

</configuration>
