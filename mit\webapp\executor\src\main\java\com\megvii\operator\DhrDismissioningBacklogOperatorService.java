package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 拉取离职人员操作
 */
@Service
public class DhrDismissioningBacklogOperatorService extends BaseOperatorService{
    @Autowired
    private DhrService dhrService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        return operateOnce(paraMap,params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        String msg = dhrService.getDimissionProcessBacklogFromDhr();
        return new OperateResult(1,msg);
    }
}
