import { Injectable } from '@angular/core'
import { NzMessageService } from 'ng-zorro-antd'

@Injectable({
  providedIn: 'root'
})
export class MessageService {
  constructor(private _messageService: NzMessageService) {}

  success(content: string, options?: any) {
    this._messageService.create('success', content, options)
  }

  error(content: string, options?: any) {
    this._messageService.create('error', content, options)
  }

  info(content: string, options?: any) {
    this._messageService.create('info', content, options)
  }

  warning(content: string, options?: any) {
    this._messageService.create('warning', content, options)
  }
}
