package com.megvii.controller.api;

import com.megvii.entity.opdb.Team;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/16 16:13
 */
@Data
public class TeamVO {

	private int id;
	private String name;
	private String code;

	public static List<TeamVO> toVOs(List<Team> teams) {
		List<TeamVO> vos = new ArrayList<>();
		for (Team team : teams) {
			vos.add(toVO(team));
		}
		return vos;
	}

	private static TeamVO toVO(Team team) {
		TeamVO vo = new TeamVO();
		vo.setId(team.getId());
		vo.setName(team.getName());
		vo.setCode(team.getSfCode());

		return vo;
	}

}
