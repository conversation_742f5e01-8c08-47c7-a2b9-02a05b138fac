package com.megvii.mapper.filter;

import lombok.Data;
import org.hibernate.validator.constraints.Length;


/**
 * 页查询系统操作日志信息
 *
 * <AUTHOR>
 * @date 2020/04/17
 */
@Data
public class PageLogRequestFilter {

    @Length(max = 40, message = "userName 字符串长度不能超过40")
    private String operateUserId;

    private String startTime;

    private String endTime;

    private String requestUrl;

    private String requestIp;

    private Integer current = 1;

    private Integer size = 10;
}
