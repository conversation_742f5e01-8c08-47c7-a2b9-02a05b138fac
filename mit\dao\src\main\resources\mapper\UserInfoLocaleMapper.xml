<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserInfoLocaleMapper">
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.UserInfoLocale">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="info" property="info"/>
        <result column="email" property="email"/>
        <result column="bank" property="bank"/>
    </resultMap>

    <select id="selectUserInfoLocaleByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_user_info_locale where user_id=#{userId} limit 1
    </select>
    <update id="updateUserInfo" parameterType="com.megvii.entity.opdb.UserInfoLocale">
        update IT_user_info_locale set info=#{info},email=#{email},bank=#{bank} where user_id=#{userId}
    </update>

    <insert id="insertUserInfo" parameterType="com.megvii.entity.opdb.UserInfoLocale">
        insert into IT_user_info_locale(user_id,info,email,bank) values (#{userId},#{info},#{email},#{bank})
    </insert>

    <delete id="deleteUserInfoLocaleByUserId" parameterType="java.lang.String" >
        DELETE FROM IT_user_info_locale WHERE user_id = #{userId}
    </delete>


</mapper>