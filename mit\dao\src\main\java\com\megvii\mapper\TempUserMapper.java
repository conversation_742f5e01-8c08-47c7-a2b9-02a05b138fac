package com.megvii.mapper;


import com.megvii.entity.opdb.TempUser;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2019/12/26 16:12
 */
@Mapper
public interface TempUserMapper {

    /**
     * 获取需要禁用的外包人员名单
     * @return
     */
    List<TempUser> selectNeedDisTempUserList();

    /**
     * 修改外包人员账号属性
     * @param userId
     * @return
     */
    int updateTempUserStatus(String userId);

    /**
     * 根据入职日期查外包人员list
     * @param entryTime
     * @return
     */
    List<TempUser> selectTempUserByEntryTime(String entryTime);

    /**
     * 按手机号查外包人员
     */
    List<TempUser> selectTempByPhone(String phone);
    
    /**
     * 按账号查外包人员
     */
    List<TempUser> selectTempByUserId(String userId);

    /**
     * 按上级查外包人员
     */
    List<TempUser> selectTempByMentor(@Param("userId") String userId);

    void updateHrgByMentor(@Param("userId") String userId,@Param("hrg") String hrg);
}
