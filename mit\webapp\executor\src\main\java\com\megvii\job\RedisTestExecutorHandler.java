package com.megvii.job;


import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@JobHandler(value="redisExecutorHandler")
@Component
public class RedisTestExecutorHandler extends IJobHandler {
    private Logger logger= LoggerFactory.getLogger(RedisTestExecutorHandler.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Override
    public ReturnT<String> execute(String s) throws Exception {

        try{
            long a=redisTemplate.opsForValue().increment("mmhtest",1);
            return new ReturnT(200,"ok");
        }catch (Exception e){
            logger.error("",e);
            throw e;
        }

    }

}
