<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.RoleMenuMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.RoleMenu">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <result column="menu_id" property="menuId"/>
        <result column="menu_code" property="menuCode"/>
        <result column="menu_name" property="menuName"/>
        <result column="usable_status" property="usableStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <insert id="insertRoleMenu" parameterType="com.megvii.entity.opdb.RoleMenu" >
        INSERT INTO `it_mit_role_menu` (
	        `id`,
	        `role_id`,
	        `role_name`,
	        `role_code`,
	        `menu_id`,
	        `menu_code`,
	        `menu_name`,
	        `usable_status`,
	        `create_time`,
	        `create_user`,
	        `update_time`,
	        `update_user`
        )
        VALUES
	    (
		    0,
		    #{roleId},
		    #{roleName},
	    	#{roleCode},
	    	#{menuId},
	    	#{menuCode},
	    	#{menuName},
		    #{usableStatus},
		    NOW(),
		    #{createUser},
		    NOW(),
		    #{updateUser}
	    );
    </insert>

    <update id="updateRoleMenu" parameterType="com.megvii.entity.opdb.RoleMenu">
        update it_mit_role_menu
        <set>
            update_time = NOW(),
            <if test="usableStatus != null ">
                usable_status = #{usableStatus},
            </if>
        </set>
        where id = #{id}
    </update>



</mapper>
