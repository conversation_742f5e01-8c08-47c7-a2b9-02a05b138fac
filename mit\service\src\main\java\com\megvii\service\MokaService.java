package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.MokaRestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/4 10:40
 */
@Service
public class MokaService {

	Logger logger= LoggerFactory.getLogger(MokaService.class);

	@Autowired
	private MokaRestClient mokaRestClient;

	public String disable(String phone) {
		return mokaRestClient.disableUser(phone);
	}
	/**
	 * 获取moka offer 自定义字段map
	 * @param offerId
	 * @return
	 */
	public Map<String, String> getMokaOfferMap(String offerId) {
		Map<String, String> map = new HashMap<>();
		JSONObject data = mokaRestClient.getMokaOffer(offerId);

		JSONArray fields = data.getJSONArray("data").getJSONObject(0).getJSONObject("offer").getJSONArray("customFields");
		for (Object temp : fields) {
			JSONObject field = (JSONObject) temp;
			map.put(field.getString("name"), field.getString("value"));
		}
		return map;
	}

	/**
	 * 获取moka offer 附件
	 * @param offerId
	 * @return
	 */
	public String getMokaOfferFile(String userId,String offerId) {

		return mokaRestClient.getMokaOfferFile(userId,offerId);
	}

	/**
	 * 获取实习津贴
	 * @param offerId
	 * @return
	 */
	public String getInternshipAllowance(String offerId) {
		Map<String, String> map = getMokaOfferMap(offerId);
		if (map.containsKey("建议薪资（元/日）")) {
			return map.get("建议薪资（元/日）");
		}
		return null;
	}




}
