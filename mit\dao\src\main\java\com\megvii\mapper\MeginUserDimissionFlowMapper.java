package com.megvii.mapper;

import com.megvii.entity.opdb.MeginUserDimissionFlowPO;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
public interface MeginUserDimissionFlowMapper {

  Boolean insert (MeginUserDimissionFlowPO po);
  Boolean update (MeginUserDimissionFlowPO po);

  /**
   * 查询大于等于当天60内的离职流程
   * @param userId
   * @return
   */
  MeginUserDimissionFlowPO selectByUserId(String userId);

  MeginUserDimissionFlowPO getDimissionFlowByUid(String uid);

}
