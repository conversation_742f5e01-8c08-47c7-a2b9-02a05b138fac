package com.megvii.service;


import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DingDingRestClient;
import com.megvii.common.CommonException;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.dirac.DiracUser;
import com.megvii.entity.luna.LunaDiracUser;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/23 20:41
 */
@PropertySource(value = "classpath:sf.${spring.profiles.active}.properties",encoding = "UTF-8")
@PropertySource(value = "classpath:group.properties",encoding = "UTF-8")
@Service
public class SyncUserService {

    Logger logger= LoggerFactory.getLogger(SyncUserService.class);

    @Value("#{${mailGroupLocation}}")
    private Map<String,String> mailGroupLocation=new HashMap<>();

    @Value("#{'${specialGroup}'.split(',')}")
    private List<String> specialGroup=new ArrayList<>();

    @Value("#{'${MC_COMPANY_CODE}'.split(',')}")
    private List<String> mcCompanyList;

    @Autowired
    private UserService userService;

    @Autowired
    private LunaDiracUserService lunaDiracUserService;

    @Autowired
    private LdapService ldapService;

    @Autowired
    private DiracUserService diracUserService;

    @Autowired
    private Office365Service office365Service;

    @Autowired
    private DingdingService dingdingService;

    @Autowired
    private MeicanService meicanService;

    @Autowired
    private DingDingRestClient dingDingRestClient;

    @Autowired
    private DidiService didiService;

    @Autowired
    private TeamService teamService;

    @Autowired
    private GroupService groupService;

    @Autowired
    private CheckService checkService;
    @Autowired
    private TempUserService tempUserService;

    /**
     * 比较sf信息
     * @param dhrUser
     * @return
     * @throws Exception
     */
    public String compareUserInfo(User dhrUser) throws Exception {
        StringBuilder message = new StringBuilder();
        User user = userService.getUserByWorkNumber(dhrUser.getWorkNumber());
        if (user == null || user.getStatus().indexOf("已保留")!=-1 || user.getStatus().indexOf("已禁用")!=-1) {
            logger.info(dhrUser.getUserId() + "在MIT中不存在或已禁用，不同步");
            return dhrUser.getUserId() + "在MIT中不存在或已禁用，不同步<br/>";
        }
        String teamResult = compareUserTeam(dhrUser, user);

        try{
            if (teamResult != null) {
                message.append(teamResult + "<br/>");
            }
            if (!dhrUser.getSpell().equals(user.getSpell())) {
                message.append(compareUserFullName(dhrUser, user) + "<br/>");
            }
            if (!dhrUser.getEmployType().equals(user.getEmployType())) {
                message.append(compareUserEmployType(dhrUser, user) + "<br/>");
            }
            if (dhrUser.getCell() != null && !dhrUser.getCell().equals("") && !dhrUser.getCell().equals(user.getCell())) {
                message.append(compareUserCell(dhrUser, user) + "<br/>");
            }
            if (!Objects.equals(user.getExpectDate(), dhrUser.getExpectDate())) {
                message.append(compareUserExpectDate(dhrUser, user) + "<br/>");
            }
            if (!dhrUser.getEmployBase().equals(user.getEmployBase())) {
                message.append(compareUserEmployBase(dhrUser, user) + "<br/>");
            }
            if (!dhrUser.getCompany().equals(user.getCompany())) {
                message.append(compareUserCompany(dhrUser, user) + "<br/>");
            }
        }catch (CommonException e){
            logger.error("{}同步人员报错：{}",dhrUser.getUserId(),e.getMessage());
            return dhrUser.getUserId()+"同步人员报错："+e.getMessage()+"<br/>";
        }

        if (Objects.equals(user.getBuddy(), dhrUser.getBuddy()) && Objects.equals(user.getHrbp(), dhrUser.getHrbp())
                && Objects.equals(user.getSiteHrg(), dhrUser.getSiteHrg()) && Objects.equals(user.getRegularDate(), dhrUser.getRegularDate())
                && Objects.equals(user.getMentor(), dhrUser.getMentor())
                && Objects.equals(user.getBirthday(), dhrUser.getBirthday())
//                && Objects.equals(user.getUpdater(), dhrUser.getUpdater())
                && Objects.equals(user.getCompany(), dhrUser.getCompany())
                && Objects.equals(user.getPosition(), dhrUser.getPosition()) && Objects.equals(user.getTitle(), dhrUser.getTitle())
                && Objects.equals(user.getReferTitle(), dhrUser.getReferTitle()) && Objects.equals(user.getWorkBase(), dhrUser.getWorkBase())
                && Objects.equals(user.getCertType(), dhrUser.getCertType()) && Objects.equals(user.getCertNo(), dhrUser.getCertNo())
                && Objects.equals(user.getAddress(), dhrUser.getAddress())
        ) {
            return message.toString();
        }
        user.setBuddy(dhrUser.getBuddy());
        user.setHrbp(dhrUser.getHrbp());
        user.setSiteHrg(dhrUser.getSiteHrg());
        user.setRegularDate(dhrUser.getRegularDate());
        user.setMentor(dhrUser.getMentor());
//        user.setBirthday(dhrUser.getBirthday());
        user.setUpdater(dhrUser.getUpdater());
        user.setCompany(dhrUser.getCompany());
        user.setPosition(dhrUser.getPosition());
        user.setTitle(dhrUser.getTitle());
        user.setReferTitle(dhrUser.getReferTitle());
        user.setWorkBase(dhrUser.getWorkBase());
        user.setCertType(dhrUser.getCertType());
        user.setCertNo(dhrUser.getCertNo());
        user.setAddress(dhrUser.getAddress());
//        user.setContractCount(dhrUser.getContractCount());

//        message.append(user.getUserId() + "更新basic信息<br/>");
        userService.updateUser(user);
//        if(!Objects.equals(user.getHrbp(), dhrUser.getHrbp())){
//            tempUserService.updateHrgByMentor(user.getUserId(),user.getHrbp());
//        }
        logger.info("人员同步结果：{}",message.toString());
        return message.toString();
    }

    /**
     * 修改ad displayName
     * @param sfUser
     * @param user
     */
    private String updateAdName(User sfUser, User user) {
        StringBuilder message = new StringBuilder();
        try {
            //跳过特殊人员
            if (user.getUserId().equals("brian") || user.getUserId().equals("zhangliangliang03")||
                    user.getUserId().equals("pengjiawei") || user.getUserId().equals("zhengshaohua")||
                    user.getUserId().equals("fangtao02") ||
                    user.getUserId().equals("huyongmei") ||
                    user.getUserId().equals("linxuguang") ||
                    user.getUserId().equals("liyuqi") || user.getUserId().equals("qinhailong")) {
                return sfUser.getUserId() + "特殊人员跳过同步姓名";
            }
            String displayName = ldapService.getDisplayName(user);
            message.append(user.getUserId() + "修改ad displayName:" + displayName);
            ldapService.modifyUser(user.getUserId(), "displayName", displayName);
            ldapService.modifyUser(user.getUserId(), "givenName", displayName.substring(1));
            /**
             * 要不要改givenName
             */

        } catch (Exception e) {
            logger.error(user.getUserId() + "修改ldap displayName 错误:" + e.getMessage());
            message.append(user.getUserId() + "修改ldap displayName 错误:" + e.getMessage());
            return message.toString();
        }
        return message.toString();
    }


    /**
     * 修改钉钉
     * @param sfUser
     * @param user
     * @return
     */
    private String updateUserDingding(User sfUser, User user) {
        StringBuilder message = new StringBuilder();
        try {
            sfUser.setDingdingId(user.getDingdingId());

            message.append(user.getUserId() + "修改钉钉部门姓名" + sfUser.getSpell() + sfUser.getTeam());
            //修改钉钉部门
            Team team = teamService.getTeamById(user.getTeamId());
            if (!dingdingService.updateDingdingUser(sfUser,Integer.valueOf(team.getDingdingId()))) {
                return message.toString();
            }

            //正式员工不需要单独操作群聊
            if (user.getEmployType().indexOf("实习生")==-1) {
                return message.toString();
            }

            message.append(user.getUserId() + "添加钉钉群聊");
            //添加群聊
            dingdingService.addUserChat(sfUser);

            if (user.getTeamId().equals(sfUser.getTeam())) {
                return message.toString();
            }

            //初始群聊
            List<String> realChatIds = new ArrayList<>();
            String result = dingdingService.getUserDingdingChats(user, realChatIds);
            if (result != null) {
                message.append(result);
                return message.toString();
            }
            message.append(user.getUserId() + "获取的钉钉群聊ID是:" + realChatIds);

            List<String> removeChatIds = new ArrayList<>();
//            if (user.getTeam().indexOf("研究院")!=-1) {
//                removeChatIds.add("chat26f74a2f9c77523a447720f0d4137a0f");
//            }

            if (checkService.checkRTeam(user.getTeam())) {
                removeChatIds.add("chat26f74a2f9c77523a447720f0d4137a0f");
            }

            Team oldTeam = teamService.getTeamById(user.getTeamId());
            if (oldTeam == null) {
                logger.error(user.getUserId() + "teamId没有关联team 不添加钉钉chat");
            }

            String[] oldTeamNames = oldTeam.getName().split("-");
            StringBuilder oldTeamName = new StringBuilder();

            //循环查询旧部门chatId
            for (int i = 0; i < oldTeamNames.length; i ++) {
                oldTeamName.append(oldTeamNames[i]);
                Team bu = teamService.getTeamByName(oldTeamName.toString());
                if (bu != null) {
                    removeChatIds.add(bu.getChatId());
                    oldTeamName.append("-");
                    continue;
                } else {
                    logger.error(user.getUserId() + oldTeamName +  "不存在 不添加钉钉chat");
                }
            }

            //旧部门ids中移除初始群聊id
            removeChatIds.removeAll(realChatIds);
            List<String> userDingdingId = new ArrayList<>();
            userDingdingId.add(sfUser.getDingdingId());
            for (String removeChatId : removeChatIds) {
                //删除群聊
                dingDingRestClient.delUserToChat(removeChatId, userDingdingId);
                message.append(user.getUserId() + "删除群聊:" + removeChatId);
            }

        } catch (Exception e) {
            logger.error(user.getUserId() + "同步DHR修改钉钉错误" + e.getMessage());
            message.append(user.getUserId() + "同步DHR修改钉钉错误");
            return message.toString();
        }

        return message.toString();
    }




    /**
     * 比较入职地点
     * @param sfUser
     * @param user
     * @return
     */
    private String compareUserEmployBase(User sfUser, User user) {
        StringBuilder message = new StringBuilder();
        //执行中心只修改 不操作组
        if (user.getCreateTag() != null && (user.getCreateTag() == 4||user.getCreateTag() == 9)) {
            user.setEmployBase(sfUser.getEmployBase());
            userService.updateUser(user);
            message.append(user.getUserId() + "修改employBase:" + user.getEmployBase() + ",");
            return message.toString();
        }
        try {
            try{
                String oldBaseTeam = mailGroupLocation.get(user.getEmployBase());
                if (oldBaseTeam != null) {
                    List<String> group = new ArrayList<>();
                    group.add(oldBaseTeam);
                    message.append(user.getUserId() + "office365 删除组" + group + ",");
                    office365Service.removeUserGroup(user.getUserId(), group);
                }
                if ("北京".equals(sfUser.getEmployBase()) && !"北京".equals(user.getEmployBase())){
                    try{
                        user.setEmployBase("北京");
                        String msg = meicanService.createMeicanUser(user,true); // 开通美餐账号
                        String msg1 = didiService.addDidiInformation(user,false); // 开通滴滴账号
                        message.append(msg);
                        message.append(msg1);
                    }catch (Exception e){
                        logger.error(user.getUserId() + e.getMessage());
                    }
                }
                if (!"北京".equals(sfUser.getEmployBase()) && "北京".equals(user.getEmployBase())){
                    try{
                        String msg = meicanService.deleteMeicanUser(user); // 关闭美餐和滴滴账号
                        String msg1 = didiService.removeDidi(user);
                        message.append(msg);
                        message.append(msg1);
                    }catch (Exception e){
                        logger.error(user.getUserId() + e.getMessage());
                    }
                }
                user.setEmployBase(sfUser.getEmployBase());
                message.append(user.getUserId() + "修改employBase:" + user.getEmployBase() + ",");
                userService.updateUser(user);
                message.append(user.getUserId() + "office365 新增组,");
                office365Service.addUserGroup(user);

                LunaDiracUser lunaDiracUser = lunaDiracUserService.getLunaDiracUserByUsername(user.getUserId());
                if (lunaDiracUser != null) {
                    lunaDiracUser.setLocation(sfUser.getEmployBase());
                    message.append(user.getUserId() + "修改lunaDiracUser location:" + sfUser.getEmployBase() + ",");
                    lunaDiracUserService.updateLunaDiracUser(lunaDiracUser);
                }
            }catch (Exception e){
                logger.error(message.toString());
            }

        } catch (Exception e) {
            logger.error(user.getUserId() + "更新EmployBase错误");
            message.append(user.getUserId() + "更新EmployBase错误");
            return message.toString();
        }
        return message.toString();
    }

    /**
     * 比较合同主体
     * @param sfUser
     * @param user
     * @return
     */
    private String compareUserCompany(User sfUser, User user) {
        if(user.getCompany() == null ||sfUser.getCompany() == null){
            return user.getUserId() + "公司主体不存在，跳过";
        }
        boolean isOriginalMc =mcCompanyList.contains(user.getCompany());
        boolean isNewMc =mcCompanyList.contains(sfUser.getCompany());
        if(isOriginalMc && !isNewMc){
            String mcGroupName = "mc";
            try {
                List<String> userList = ldapService.getGroupMembers(mcGroupName);
                if(userList.contains(user.getUserId())){
                    return ldapService.deleteGroupUser(user.getUserId(), mcGroupName);
                }else{
                    return user.getUserId() + "不在mc组中，跳过；";
                }
            } catch (Exception e) {
                logger.error(user.getUserId() + "移除mc组错误:" + e.getMessage());
                return user.getUserId() + "移除mc组错误";
            }
        }
        if(!isOriginalMc && isNewMc){
            try {
                String userDn = ldapService.getRealUserDn(user.getUserId());
                ldapService.addMemberToGroupByUserDn("mc", userDn);
                return user.getUserId() + "添加mc组";
            }catch (Exception e){
                logger.error(user.getUserId() + "添加mc组错误:" + e.getMessage());
                return user.getUserId() + "添加mc组错误";
            }

        }
        return  user.getUserId() +"mc组状态无变化";
    }


    /**
     * 比较入职日期
     * @param sfUser
     * @param user
     * @return
     */
    private String compareUserExpectDate(User sfUser, User user) {
        LocalDateTime now = LocalDateTime.now();
        int diff = DateTimeUtil.diffDateByDay(now, sfUser.getExpectDate());
        if (diff == 0 || diff == -1 || (diff == 2 && now.getDayOfWeek().equals(DayOfWeek.FRIDAY))) {

        }
        user.setExpectDate(sfUser.getExpectDate());
        userService.updateUser(user);
        return user.getUserId() + "更新expectDate" + sfUser.getExpectDate();
    }

    /**
     * 比较cell
     * @param sfUser
     * @param user
     * @return
     */
    private String compareUserCell(User sfUser, User user) {
        StringBuilder message = new StringBuilder();

        LunaDiracUser lunaDiracUser = lunaDiracUserService.getLunaDiracUserByUsername(user.getUserId());
        if (lunaDiracUser != null) {
            lunaDiracUser.setPhone(sfUser.getCell());
            message.append(user.getUserId() + "更新lunaDiracUser phone:" + sfUser.getCell() + ",");
            lunaDiracUserService.updateLunaDiracUser(lunaDiracUser);
        }
        user.setCell(sfUser.getCell());
        message.append(user.getUserId() + "更新cell:" + user.getCell() + ",");
        userService.updateUser(user);

        message.append(didiService.updateDidiInfo(user));
        return message.toString();
    }


    /**
     * 比较employ_type
     * @param sfUser
     * @param user
     * @return
     */
    private String compareUserEmployType (User sfUser, User user) {
        StringBuilder message = new StringBuilder();
        try {
            if (user.getEmployType() != null && user.getEmployType().indexOf("实习生")!=-1 &&
                    (sfUser.getEmployType().equals("正式员工") || sfUser.getEmployType().equals("劳务派遣"))) {
//                if (sfUser.getTeam().indexOf("研究院")!=-1) {
//                    List<String> group = new ArrayList<>();
//                    group.add("r-intern");
//                    group.add("intern");
//                    message.append(user.getUserId() + "office365 删除组" + group + ",");
//                    office365Service.removeUserGroup(user.getUserId(), group);
//                }
                if (checkService.checkRTeam(sfUser.getTeam())) {
                    List<String> group = new ArrayList<>();
                    group.add("r-intern");
                    group.add("intern");
                    message.append(user.getUserId() + "office365 删除组" + group + ",");
                    office365Service.removeUserGroup(user.getUserId(), group);
                } else {
                    boolean flag = false;
                    for (String special : specialGroup) {
                        if (sfUser.getTeam().equals(special)) {
                            List<String> group = new ArrayList<>();
                            group.add("r-intern");
                            group.add("intern");
                            message.append(user.getUserId() + "office365 删除组" + group + ",");
                            office365Service.removeUserGroup(user.getUserId(), group);
                            flag = true;
                        }
                    }
                    if (!flag) {
                        List<String> group = new ArrayList<>();
                        group.add("intern");
                        message.append(user.getUserId() + "office365 删除组" + group + ",");
                        office365Service.removeUserGroup(user.getUserId(), group);
                    }
                }
                message.append(updateUserDingding(sfUser, user));
                user.setEmployType(sfUser.getEmployType());
                user.setContractCount(0);
                user.setContractStatus("无合同");
                message.append(user.getUserId() + "修改employType:" + user.getEmployType() + ",");
                userService.updateUser(user);
                message.append(user.getUserId() + "office365 添加组,");
                office365Service.addUserGroup(user);
                message.append(updateAdName(sfUser, user) + ",");
                didiService.addDidiInformation(user, false);

                List<String> dingdingId = new ArrayList<>();
                dingdingId.add(user.getDingdingId());
                dingDingRestClient.delUserToChat("chat654e4b414ebb83e17f453cfd870f13a8", dingdingId);
                message.append(user.getUserId() + "删除实习生群聊" + ",");
            } else {
                user.setEmployType(sfUser.getEmployType());
                message.append(user.getUserId() + "修改employType:" + user.getEmployType() + ",");
                userService.updateUser(user);
                didiService.addDidiInformation(user, false);
            }
        } catch (Exception e) {
            logger.error(user.getUserId() + "更新Employ_type错误");
            return user.getUserId() + "更新Employ_type错误";
        }
        return message.toString();
    }



    /**
     * 比较修改名字 修改ldap lunaDiracUser DiracUser
     * @param sfUser
     * @param user
     * @return
     * @throws Exception
     */
    private String compareUserFullName(User sfUser, User user) throws Exception {
        StringBuilder message = new StringBuilder();
        if (sfUser.getSpell() == null || user.getSpell() == null) {
            logger.error(user.getUserId() + "spell is null");
            throw new Exception(user.getUserId() + "spell is null");
        }

        //跳过特殊人员
        if (user.getUserId().equals("brian") || user.getUserId().equals("zhangliangliang03")||
                user.getUserId().equals("pengjiawei") || user.getUserId().equals("zhengshaohua")||
                user.getUserId().equals("fangtao02") ||
                user.getUserId().equals("huyongmei") ||
                user.getUserId().equals("linxuguang") ||
                user.getUserId().equals("liyuqi") || user.getUserId().equals("qinhailong")) {
            return sfUser.getUserId() + "特殊人员跳过同步姓名";
        }

        String lastName = sfUser.getSpell().substring(0,1);

        String displayName = ldapService.getDisplayName(user);
        String givenName = displayName.substring(1);

        //改名字修改ldap
        message.append(user.getUserId() + "修改ldap姓名 sn=" + lastName + ",givenName=" + givenName + ",");
        ldapService.modifyUser(user.getUserId(), "sn", lastName);
        ldapService.modifyUser(user.getUserId(), "givenName", givenName);

        LunaDiracUser lunaDiracUser = lunaDiracUserService.getLunaDiracUserByUsername(user.getUserId());
        if (lunaDiracUser != null) {
            lunaDiracUser.setCname(sfUser.getSpell());
            message.append(user.getUserId() + "修改lunaDiracUser Cname:" + sfUser.getSpell());
            lunaDiracUserService.updateLunaDiracUser(lunaDiracUser);
        }

        DiracUser diracUser = diracUserService.selectUserByUserId(user.getUserId());
        if (diracUser != null) {
            diracUser.setSpell(sfUser.getSpell());
            message.append(user.getUserId() + "修改DiracUser spell:" + sfUser.getSpell());
            diracUserService.updateDiracUser(diracUser);
        }

        logger.info(user.getUserId() + ":" + user.getSpell() + "——>" + sfUser.getSpell());
        message.append(updateAdName(sfUser, user) + ",");
        user.setSpell(sfUser.getSpell());
        message.append(user.getUserId() + "修改spell" + user.getSpell() + ",");
        userService.updateUser(user);
        //修改钉钉姓名
        message.append(user.getUserId() + "修改钉钉名字");
        Team team = teamService.getTeamById(user.getTeamId());
        dingdingService.updateDingdingUser(user,Integer.valueOf(team.getDingdingId()));

        return message.toString();
    }




    /**
     * 比较人员部门变化
     * @param sfUser
     * @param user
     * @return
     * @throws Exception
     */
    private String compareUserTeam(User sfUser, User user) throws Exception {
        StringBuilder message = new StringBuilder();
        Team oldTeam = teamService.getTeamById(user.getTeamId());
        Team newTeam = teamService.getTeamById(sfUser.getTeamId());
        if (oldTeam == null || newTeam == null) {
            logger.error(user.getUserId() + "新部门或者老部门不存在");
            throw new Exception(user.getUserId() + "新部门或者老部门不存在");
        }
            //比较teamId
            if (!user.getTeamId().equals(sfUser.getTeamId())) {
                //旧部门邮件组
                List<String> oldMailGroupList = new ArrayList<>();
                getParentMailGroups(oldTeam, oldMailGroupList);
                //新部门邮件组
                List<String> newMailGroupList = new ArrayList<>();
                getParentMailGroups(newTeam, newMailGroupList);
                oldMailGroupList.removeAll(newMailGroupList);

//                mailGroupList.add(oldTeam.getMailGroup());
//                //递归向上查询邮件组
//                String[] teamNames = oldTeam.getName().split("-");
//                for (int i = 1; i < teamNames.length; i++) {
//                    String parentName = oldTeam.getName().replace("-" + teamNames[teamNames.length - i], "");
//                    Team parentTeam = teamService.getTeamByName(parentName);
//                    //存在父级部门
//                    if (parentTeam != null ) {
//                        if (parentTeam.getMailGroup() != null) {
//                            mailGroupList.add(parentTeam.getMailGroup());
//                        }
//                        if (parentTeam.getProject() != null) {
//                            String[] project = parentTeam.getProject().split(",");
//                            for (int j = 0; j < project.length; j ++) {
//                                mailGroupList.add(project[j]);
//                            }
//                        }
//                    }
//                }
                message.append(user.getUserId() + "修改钉钉部门" + sfUser.getTeam() + ",");
                message.append(updateUserDingding(sfUser, user));
                user.setTeam(newTeam.getName());
                user.setTeamId(newTeam.getId());
                user.setTeamCode(newTeam.getSfCode());
                //先删除
                if (oldMailGroupList.size() > 0) {
                    List<String> list = groupService.getGroupList(user);
                    list.removeAll(groupService.selectAllSecureGroup());
                    oldMailGroupList.removeAll(list);
                    oldMailGroupList.removeAll(groupService.selectAllSecureGroup());
                    message.append(user.getUserId() + "office365删除组" + oldMailGroupList + ",");
                    try {
                        office365Service.removeUserGroup(user.getUserId(), oldMailGroupList);
                    } catch (Exception e) {
                        message.append("删除组错误");
                    }
                }
                //再添加
                message.append(user.getUserId() + "office365增加组" + ",");
                office365Service.addUserGroup(user);
                //修改ldap
                message.append(updateAdName(sfUser, user) + ",");
                message.append(user.getUserId() + "修改AD department" + newTeam.getName() + ",");
                ldapService.modifyUser(user.getUserId(), "department", newTeam.getName());
                userService.updateUser(user);
                logger.info(user.getUserId() + " from " + oldTeam.getSfCode() + " to " + newTeam.getSfCode());
                message.append(user.getUserId() + " from " + oldTeam.getSfCode() + " to " + newTeam.getSfCode() + ",");
                message.append(didiService.updateDidiInfo(user));
                return message.toString();
            } else if (!user.getTeam().equals(sfUser.getTeam())) {
                message.append(user.getUserId() + " from " + user.getTeam() + " to " + newTeam.getName() + ",");
                user.setTeam(newTeam.getName());
                userService.updateUser(user);

                //部门更换名称
                message.append(updateAdName(sfUser, user) + ",");
                message.append(user.getUserId() + "修改AD department,");
                ldapService.modifyUser(user.getUserId(), "department", newTeam.getName());
                message.append(user.getUserId() + "office365增加组,");
                office365Service.addUserGroup(user);
                return message.toString();
            }
        return null;

    }


    /**
     * 看情况切换到 GroupService.getGroupList
     * 递归获取父级的邮件组
     * @param team
     * @param mailGroupList
     * @return
     */
    private void getParentMailGroups(Team team, List<String> mailGroupList) {
        if (team == null) {
            return;
        }
        if (team.getMailGroup() != null && !"".equals(team.getMailGroup())) {
            mailGroupList.add(team.getMailGroup());
        }
        if (team.getProject() != null) {
            String[] project = team.getProject().split(",");
            for (int j = 0; j < project.length; j++) {
                mailGroupList.add(project[j]);
            }
        }
        if (team.getParentCode() != null) {
            getParentMailGroups(teamService.getTeamByCode(team.getParentCode()), mailGroupList);
        }

    }



}
