package com.megvii.controller.jwt;


import com.megvii.common.DateTimeUtil;
import com.megvii.common.IPUtils;
import com.megvii.controller.jwt.api.*;
import com.megvii.controller.response.ResponseDataEntity;
import com.megvii.entity.api.ItUserVO;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.TempUser;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.*;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.jws.soap.SOAPBinding;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019/8/28 11:27
 */
@PropertySource("classpath:jwt.properties")
@RestController
@RequestMapping("/api/V2")
public class JWTController {

    private Logger logger= LoggerFactory.getLogger(JWTController.class);

    @Value("${JOB_CARD_SIGN_KEY}")
    private String jobCardSignKey;

    @Value("${ASSET_SIGN_KEY}")
    private String assetSignKey;

    @Value("${DHR_SIGN_KEY}")
    private String dhrSignKey;

    @Value("${MEGIN_SIGN_KEY}")
    private String meginSignKey;

    @Value("${ACCOUNT_SIGN_KEY}")
    private String accountSignKey;
    
    @Autowired
    private IPWhiteListService ipWhiteListService;

    @Autowired
    private UserService userService;

    @Autowired
    private TeamService teamService;

    @Autowired
    private DingdingService dingdingService;

    @Autowired
	private OaService oaService;
    
    @Autowired
	private TempUserService tempUserService;

    @GetMapping("/date")
    @ResponseBody
    public String getDate() {
        StringBuilder result = new StringBuilder();
        result.append( LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        result.append("*********");
        result.append( LocalDateTime.now());

        return result.toString();
    }


    @PostMapping("/addUser")
	@ResponseBody
	public ResponseDataEntity userEntry(HttpServletRequest request, @RequestBody ItUserVO vo) {
		ResponseDataEntity response = new ResponseDataEntity();

		//过滤ip,若用户在白名单内，则放行
		String ipAddress = IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "megin")) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
		}

		if (!validateSign(vo.getSign(), vo.getTimestamp(), meginSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(vo.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }

		logger.info("megin推送MIT*****" + vo.toString());

		User user = userService.getUserByMokaId(vo.getMokaId());
		if (user != null && vo.getOfferId().equals(user.getOfferId())) {
			response.setCode(ResponseDataEntity.OK);
            response.setMsg(vo.getMokaId() + "已存在");
            return response;
		}

		if (!userService.saveUserV2(vo)) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg(vo.getMokaId() + "插入失败");
			return response;
		}

		user = userService.getUserByMokaId(vo.getMokaId());
		user.setPriEmail(vo.getPriEmail());
		if (oaService.createEntryFlow(user)) {
            response.setCode(ResponseDataEntity.OK);
			response.setMsg(vo.getMokaId() + "推送oa成功");
			return response;
        } else {
            response.setCode(ResponseDataEntity.ERROR);
			response.setMsg(vo.getMokaId() + "推送oa失败");
			return response;
        }
	}



    @PostMapping("/all/userInfo/jobCard")
    @ResponseBody
    public ResponseDataEntity getAllUserInfo4JobCard(HttpServletRequest request, @RequestBody JWTRequest JWTRequest) {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress= IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "jobCard")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(JWTRequest.getSign(), JWTRequest.getTimestamp(), jobCardSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(JWTRequest.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }

        List<User> userList = userService.getAllUser();
        //mc人员不进入工卡系统
        List<User> userListResult = userList.stream().filter(s ->!(s.getCreateTag()!=null&&(s.getCreateTag()==5||s.getCreateTag()==6||s.getCreateTag()==7||s.getCreateTag()==9))).collect(Collectors.toList());
        List<JobCardUserInfoVO> vos = JobCardUserInfoVO.toVOs(userListResult);

        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        response.setData(vos);
        return response;
    }


    @PostMapping("/team/jobCard")
    @ResponseBody
    public ResponseDataEntity getTeam4JobCard(HttpServletRequest request, @RequestBody JWTRequest JWTRequest) {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress= IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "jobCard")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(JWTRequest.getSign(), JWTRequest.getTimestamp(), jobCardSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(JWTRequest.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }

        List<Team> teams = teamService.getAllTeam();
//        Team mcTeam = teamService.getTeamByCode("CL13");
//        List<Team> teamsResult = teams.stream().filter(t->!t.getName().contains(mcTeam.getShortName())).collect(Collectors.toList());
        List<JobCardTeamInfoVO> vos = JobCardTeamInfoVO.toVOs(teams);

        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        response.setData(vos);
        return response;
    }

    @PostMapping("/userInfo/jobCard")
    @ResponseBody
    public ResponseDataEntity getUserInfo4JobCard(HttpServletRequest request, @RequestBody JWTRequest JWTRequest) {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress= IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "jobCard")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(JWTRequest.getSign(), JWTRequest.getTimestamp(), jobCardSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(JWTRequest.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }

        //预入职人员信息
        List<User> entryUsers = userService.getUserListNeedToLdap();
        //mc人员不进入工卡系统
        List<User> entryUsersResult = entryUsers.stream().filter(s ->!(s.getCreateTag()!=null&&(s.getCreateTag()==5||s.getCreateTag()==6||s.getCreateTag()==7||s.getCreateTag()==9))).collect(Collectors.toList());
        List<JobCardUserInfoVO> entryVOs = JobCardUserInfoVO.toVOs(entryUsersResult);

        //状态变化人员信息
        List<User> updateUsers = new ArrayList<>();
        if (JWTRequest.getDay() != null) {
            updateUsers = userService.getUserByUpdateTime(JWTRequest.getDay()).stream().filter(s ->!(s.getCreateTag()!=null&&s.getCreateTag()==5)).collect(Collectors.toList());

        } else {
            updateUsers = userService.getUserByUpdateTime(40).stream().filter(s ->!(s.getCreateTag()!=null&&s.getCreateTag()==5)).collect(Collectors.toList());
        }
        List<JobCardUserInfoVO> updateVOs = JobCardUserInfoVO.toVOs(updateUsers);

        Map<String, List> map = new HashMap<>();
        map.put("entry", entryVOs);
        map.put("update", updateVOs);

        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        response.setData(map);
        return response;
    }

    @PostMapping("/team/asset")
    @ResponseBody
    public ResponseDataEntity getTeamInfo(HttpServletRequest request, @RequestBody JWTRequest JWTRequest) {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress= IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "asset")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(JWTRequest.getSign(), JWTRequest.getTimestamp(), assetSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(JWTRequest.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }

        List<Team> teams = teamService.getActiviteTeam();
        List<AssetTeamInfoVO> vos = AssetTeamInfoVO.toVOs(teams);

        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        response.setData(vos);
        return response;

    }


    @PostMapping("/userInfo/asset")
    @ResponseBody
    public ResponseDataEntity getUserInfo4Asset(HttpServletRequest request, @RequestBody JWTRequest JWTRequest) throws Exception {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress= IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "asset")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(JWTRequest.getSign(), JWTRequest.getTimestamp(), assetSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(JWTRequest.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }

        List<AssetUserInfoVO> vos = new ArrayList<>();

        UserFilter filter = new UserFilter();
        filter.setStatusCode(JWTRequest.getStatus());
        List<User> userList = userService.getUsersByFilter(filter);

        AssetUserInfoVO vo = null;
        for (User user : userList) {
            vo = new AssetUserInfoVO();
            vo.setUserId(user.getUserId());
            vo.setSpell(user.getSpell());
            vo.setStatus(user.getStatus()!=null?user.getStatus():null);
            vo.setSeatNumber(user.getSeatNumber()!=null?user.getSeatNumber():null);
            vo.setWorkBase(user.getWorkBase()!=null?user.getWorkBase():null);
            vo.setEmployBase(user.getEmployBase()!=null?user.getEmployBase():null);

            if ((user.getStatus() != null && (user.getStatus().indexOf("已禁用")!=-1 || user.getStatus().indexOf("已保留")!=-1))
                    || user.getTeamId() == null) {
                if (user.getDimissionDate() != null) {
                    vo.setDimissionDate(DateTimeUtil.date2String4(user.getDimissionDate()));
                }
                vos.add(vo);
                continue;
            }

            Team temp = teamService.getTeamById(user.getTeamId());
            if (temp == null || temp.getName().indexOf("old")!=-1) {
                vos.add(vo);
                continue;
            }

            String[] teamNames = temp.getName().split("-");
            Team group =null;
            try {
                group = teamService.getTeamByName(teamNames[0]);
            }catch(Exception e){
                logger.error("teamName:{}",temp.getName());
                throw new Exception("teamName:"+temp.getName()+",根据部门名称获取部门信息报错："+e.getMessage());
            }
            vo.setGroupCode(group!=null?group.getSfCode():null);

            if (teamNames.length > 1) {
                Team bu = teamService.getTeamByName(teamNames[0] + "-" + teamNames[1]);
                vo.setBuCode(bu!=null?bu.getSfCode():null);
            }

            if (teamNames.length > 2) {
                Team team = teamService.getTeamByName(teamNames[0] + "-" + teamNames[1] + "-" + teamNames[2]);
                vo.setTeamCode(team!=null?team.getSfCode():null);
            }

            if (teamNames.length > 3) {
                Team subTeam = teamService.getTeamByName(teamNames[0] + "-" + teamNames[1] + "-" + teamNames[2] + "-" + teamNames[3]);
                vo.setSubTeamCode(subTeam!=null?subTeam.getSfCode():null);
            }

            if (teamNames.length > 4) {
                Team section = teamService.getTeamByName(teamNames[0] + "-" + teamNames[1] + "-" + teamNames[2] + "-" + teamNames[3] + "-" + teamNames[4]);
                vo.setSectionCode(section!=null?section.getSfCode():null);
            }
            vos.add(vo);
        }

        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        response.setData(vos);
        return response;
    }

    @PostMapping("/sendDingMsg")
    @ResponseBody
    public ResponseDataEntity sendDhrDingdingMsg(HttpServletRequest request, @RequestBody DhrSendMsgRequest dhrRequest) {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress = IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "dhr")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(dhrRequest.getSign(), dhrRequest.getTimestamp(), dhrSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(dhrRequest.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }
        try {
            if (dingdingService.sendDhrWorkMsg(dhrRequest.getTitle(), dhrRequest.getMsg(), dhrRequest.getUrl(), dhrRequest.getWorkNumber())) {
                response.setCode(ResponseDataEntity.OK);
                response.setMsg("success");
            } else {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("发送失败");
            }
        } catch (Exception e) {
            logger.error("DHR发送钉钉通知异常:" + e);
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg(e.getMessage());
        }
        return response;
    }

    @PostMapping("/sendDingMsgV2")
    @ResponseBody
    public ResponseDataEntity sendDhrDingdingMsgV2(HttpServletRequest request, @RequestBody DhrSendMsgRequest dhrRequest) {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress = IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "dhr")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(dhrRequest.getSign(), dhrRequest.getTimestamp(), dhrSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(dhrRequest.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }
        try {
            if (dingdingService.sendDingMsgV2(dhrRequest.getTitle(), dhrRequest.getMsg(), dhrRequest.getWorkNumber())) {
                response.setCode(ResponseDataEntity.OK);
                response.setMsg("success");
            } else {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("发送失败");
            }
        } catch (Exception e) {
            logger.error("DHR发送钉钉通知异常:" + e);
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg(e.getMessage());
        }
        return response;
    }


    //获取外包人员
    @PostMapping("/getTempUser")
    @ResponseBody
    public ResponseDataEntity getTempUser(HttpServletRequest request, @RequestBody ChangePasswordVo changePasswordVo) {
        ResponseDataEntity response = new ResponseDataEntity();
        //过滤ip,若用户在白名单内，则放行
        String ipAddress= IPUtils.getRealIP(request);

        if (!ipWhiteListService.validateIp(ipAddress, "account")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        if (!validateSign(changePasswordVo.getSign(), changePasswordVo.getTimestamp(), accountSignKey)) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("认证失败");
            return response;
        }

        LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf(changePasswordVo.getTimestamp()), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());
        if (duration.toMinutes() > 5) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("时间戳超时");
            return response;
        }

        try {
        	TempUser user;
        	
        	if(StringUtils.isNotBlank(changePasswordVo.getPhone())) {
        		user = tempUserService.getTempUserByPhone(changePasswordVo.getPhone());
        	}else {
        		user = tempUserService.getTempUserByUserId(changePasswordVo.getUserId());
        	}
			response.setData(user);
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg(e.getMessage());
		}
        
        return response;
    }


    private boolean validateSign(String sign, String timestamp, String signKey) {
        String result = DigestUtils.md5Hex(signKey + timestamp);
        if (sign.equals(result)) {
            return true;
        }
        return false;
    }



}
