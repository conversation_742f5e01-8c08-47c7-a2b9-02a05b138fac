package com.megvii.client;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/11/19 16:09
 */
@Component
@PropertySource(value = "classpath:sms.properties",encoding = "UTF-8")
public class SmsRestClient {

    Logger logger=LoggerFactory.getLogger(SmsRestClient.class);

    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${SN}")
    private String sn;

    @Value("${PASSWORD}")
    private String passWord;

    @Value("${SIGN}")
    private String sign;

    @Value("${BASEURL}")
    private String baseUrl;

    @Value("${ACCOUNT_SID}")
    private String accountSid;

    @Value("${ACCOUNT_TOKEN}")
    private String accountToken;

    @Value("${SUB_ACCOUNT_SID}")
    private String subAccountSid;

    @Value("${SUB_ACCOUNT_TOKEN}")
    private String subAccountToken;

    @Value("${APP_ID}")
    private String appId;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 发送短信
     * 必须加【】 否则联通手机号无法收到
     * @param mobile
     * @param content
     */
    public void sendSMS(String mobile, String content) {
        String temp = null;
        try {
            temp = URLEncoder.encode(content, "utf-8");
        } catch (UnsupportedEncodingException e) {
            logger.error(mobile + "密码转换错误！");
        }

        if (!profile.equals("prod")) {
            mobile = "***********";
        }

        String sign = DigestUtils.md5Hex(sn + passWord).toUpperCase();
        String url = baseUrl + "sn=" + sn + "&pwd=" + sign + "&mobile=" + mobile + "&content=" + temp + "&ext=&stime=&rrid=&msgfmt=";

        logger.info("发送信息至:" + mobile + ",content=" + content, "url=" + url);

        try {
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url ,String.class);
            logger.info(responseEntity.getBody());
        } catch (Exception e) {
            logger.error("发送信息至:" + mobile + ",content=" + content + "异常!" + e.getMessage());
        }
    }


}
