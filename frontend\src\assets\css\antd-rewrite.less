.ant-layout {
  min-height: 100%;
}

.ant-layout-content,
.ant-layout-footer {
  background: #f3f3f4;
}

.ant-popover-placement-bottom,
.ant-popover-placement-bottomLeft,
.ant-popover-placement-bottomRight {
  padding-top: 0;
}

.ant-modal-header {
  margin: 0 20px;
  padding: 23px 20px;

  .ant-modal-title {
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
  }
}

.ant-modal-close-x {
  margin-right: 20px;
}

.ant-modal-footer {
  //padding-bottom: 60px;

  .ant-btn-default {
    background-color: black;
    color: #fff;
  }
}

.ant-tabs-bar {
  padding-left: 24px;
  margin: 0;
}

.ant-tabs-nav .ant-tabs-tab {
  &.ant-tabs-tab-active,
  &:not(.ant-tabs-tab-disabled):hover {
    color: #007aff;
    font-weight: unset;
  }
}

.ant-tabs-ink-bar {
  height: 5px;
  background-color: #007aff;
}

.ant-select-dropdown {
  border-radius: 0 0 6px 6px;
}

.ant-select-dropdown-menu-item:first-child {
  border-radius: 0;
}

.ant-select-dropdown-menu-item:hover,
.ant-select-dropdown-menu-item-active {
  background-color: #e8e8e8;
  font-weight: unset;
}

.ant-input,
.ant-modal-content {
  border-radius: 6px;
}

.ant-modal-content textarea {
  border-radius: 6px 6px 0 6px;
  resize: none;
}

.ant-btn {
  border-radius: 16px;
}

.ant-btn-primary,
.ant-btn-primary:focus,
.ant-btn-primary:hover {
  background-color: #007aff;
  border-color: #007aff;
}

.ant-table-thead > tr > th {
  padding: 11px 14px;
  color: #909090;
  font-weight: 400;
}

.ant-table-tbody > tr > td {
  padding-top: 18px;
  padding-bottom: 18px;
}

.ant-table {
  table {
    th,
    td {
      border: 0;
    }

    th {
      padding: 11px 14px;
      color: #909090;
      font-weight: 400;
    }

    .ant-table-thead > tr > th {
      background: rgb(244, 245, 246);

      &:nth-child(1) {
        border-radius: 6px 0 0 6px;
      }

      &:nth-last-child(1) {
        border-radius: 0 6px 6px 0;
      }
    }

    tr {
      &:nth-of-type(even) {
        background: rgba(244, 245, 246, 0.5);
      }

      td {
        padding-top: 18px;
        padding-bottom: 18px;

        &:nth-child(1) {
          border-radius: 6px 0 0 6px;
        }

        &:nth-last-child(1) {
          border-radius: 0 6px 6px 0;
        }
      }
    }
  }
}

//.ant-modal-footer {
//  border-top: none;
//  padding-right: 40px;
//}

.ant-table-pagination.ant-pagination {
  margin: 20px 0;
}

.ant-pagination {
  .ant-pagination-item {
    border-radius: 6px;

    &:hover {
      border-color: #007aff;

      a {
        color: #909090;
      }
    }
  }

  .ant-pagination-item,
  .ant-pagination-next .ant-pagination-item-link,
  .ant-pagination-prev .ant-pagination-item-link,
  .ant-select-selection,
  .ant-pagination-options-quick-jumper input {
    border-color: #909090;
  }

  .ant-pagination-item a,
  .ant-pagination-next a,
  .ant-pagination-prev a,
  .ant-select-arrow,
  .ant-pagination-options-quick-jumper input {
    color: #909090;
  }

  .ant-pagination-item-active {
    border-color: #007aff;
    background: #007aff;

    a {
      color: #fff;
    }
  }
}

.ant-modal-close-x > i > svg {
  fill: #007aff !important;
}

.ant-drawer-close-x > i > svg {
  fill: #007aff !important;
}

.ant-calendar-header .ant-calendar-prev-century-btn,
.ant-calendar-header .ant-calendar-prev-decade-btn,
.ant-calendar-header .ant-calendar-prev-year-btn,
.ant-calendar-header .ant-calendar-prev-month-btn,
.ant-calendar-header .ant-calendar-century-select,
.ant-calendar-header .ant-calendar-decade-select,
.ant-calendar-header .ant-calendar-month-select,
.ant-calendar-header .ant-calendar-year-select,
.ant-calendar-header .ant-calendar-next-month-btn,
.ant-calendar-header .ant-calendar-next-century-btn,
.ant-calendar-header .ant-calendar-next-decade-btn,
.ant-calendar-header .ant-calendar-next-year-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-prev-century-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-prev-decade-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-prev-year-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-next-century-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-next-decade-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-next-year-btn,
.ant-calendar-month-panel-header .ant-calendar-month-panel-century-select,
.ant-calendar-month-panel-header .ant-calendar-month-panel-decade-select,
.ant-calendar-month-panel-header .ant-calendar-month-panel-month-select,
.ant-calendar-month-panel-header .ant-calendar-month-panel-year-select {
  color: #007aff;
}

.ant-calendar-range .ant-calendar-input,
.ant-calendar-range .ant-calendar-time-picker-input {
  color: #000;
}

.ant-calendar-selected-date .ant-calendar-date,
.ant-calendar-selected-end-date .ant-calendar-date,
.ant-calendar-selected-start-date .ant-calendar-date {
  &,
  &:hover {
    background: rgba(0, 122, 255, 1);
    border-radius: 6px;
  }
}

.ant-calendar-range .ant-calendar-in-range-cell::before {
  background: rgba(232, 232, 232, 0.8);
  top: 2px;
}

.ant-calendar-date {
  border-radius: 6px;

  &:hover {
    color: #fff;
    background: rgba(0, 122, 255, 1);
  }
}

.ant-calendar-today .ant-calendar-date {
  background: rgba(255, 255, 255, 1);
  border-radius: 6px;
  border: 1px solid rgba(0, 122, 255, 1);
  font-weight: 400;
  color: rgba(0, 122, 255, 1);
}

.ant-select-dropdown-menu-item:hover,
.ant-select-dropdown-menu-item-active {
  background: rgba(243, 243, 244, 1);
}

.ant-dropdown-menu {
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding-top: 6px !important;
  padding-bottom: 5px !important;

  .ant-dropdown-menu-item {
    height: 32px;
    line-height: 32px;
    border-bottom: 1px solid #f3f3f4;
  }
}

.ant-popover {
  width: 340px;

  .ant-popover-inner {
    border-radius: 6px;
    padding: 0 20px;

    .ant-popover-buttons {
      text-align: center;

      button {
        width: 114px;
      }

      .ant-btn-default {
        color: #fff;
        background: black;
      }
    }

    .ant-popover-message > .anticon {
      color: #ff1866;
    }
  }
}

.ant-calendar-month-panel-cell-disabled .ant-calendar-month-panel-month,
.ant-calendar-month-panel-cell-disabled .ant-calendar-month-panel-month:hover {
  color: #909090;
  background: transparent;
}

.ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month,
.ant-calendar-month-panel-selected-cell .ant-calendar-month-panel-month:hover {
  border-radius: 6px;
}

.ant-calendar-month-panel-selected-cell:not(.ant-calendar-month-panel-cell-disabled)
  .ant-calendar-month-panel-month {
  background: rgba(0, 122, 255, 1);
}

.ant-calendar-selected-day .ant-calendar-date {
  background: rgba(0, 122, 255, 1);
  color: #fff;
}

.ant-tree {
  li {
    .ant-tree-node-content-wrapper {
      height: 27px;
      line-height: 27px;
      color: #000;
    }
  }

  .ant-tree-switcher-icon {
    color: #007aff;
  }
}

.topmenu-dropmenu {
  top: -5px;
  padding: 0;

  .ant-dropdown-menu-item {
    padding: 0;
    text-align: center;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f3f3f4;
    }

    & > a {
      padding: 0;
      margin: 0;
      color: #000;
    }
  }

  .deepBg {
    background: #f3f3f4;
  }
}

.assign-power-drawer {
  min-width: 320px;

  .ant-drawer-body {
    padding: 10px 20px 60px;
  }

  .footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid rgb(232, 232, 232);
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;

    button {
      margin-left: 8px;
    }
  }
}

.save-menu-modal,
.reject-reason-modal {
  .ant-modal-body {
    padding: 20px 24px 35px;

    & > div {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:not(:first-child) {
        margin-top: 15px;
      }

      span {
        flex-basis: 60px;
        color: #000;
      }
    }
  }

  .ant-modal-footer {
    padding: 0 20px 19px 0;
    border-top: none;

    button + button {
      margin-left: 10px;
    }
  }

  .ant-btn {
    padding: 0 17px;
    border-radius: 16px;
  }

  .ant-input,
  .ant-select {
    width: 260px;
  }
}

.review-detail-drawer {
  .ant-drawer-header {
    padding: 24px;
    background: #f3f3f4;
    border-bottom: none;
  }

  .ant-drawer-title {
    font-weight: unset;
    font-size: 16px;
    color: #000;

    .result,
    .committee {
      color: #909090;
      font-size: 14px;

      span {
        margin-left: 20px;
      }
    }

    .result {
      margin-left: 33px;
    }

    .committee {
      float: right;

      span {
        color: #000;
      }
    }
  }

  .chairman-note {
    padding-bottom: 20px;
    border-bottom: 1px solid #909090;

    .title {
      margin-right: 38px;
    }

    .refuse-reason {
      display: inline-block;
      margin-left: 30px;
      width: 200px;
      vertical-align: text-top;
    }
  }

  .res-pass {
    color: #007aff;
  }

  .res-refuse {
    color: #f5222d;
  }

  .opinion-block {
    .title {
      padding: 20px 0;
    }

    .opinion-list {
      .opinion-item {
        margin-bottom: 20px;
      }

      .name {
        display: inline-block;
        width: 100px;
        color: #909090;
      }

      .count {
        display: inline-block;
        width: 82px;
        color: #000;
      }

      .color-user-list {
        width: 300px;
        vertical-align: middle;
      }
    }
  }

  .review-history {
    li {
      line-height: 30px;

      span:last-child {
        float: right;
      }
    }
  }
}

.save-opinion-modal {
  .ant-modal-header {
    border-bottom: none;
    background: #f9fafa;
  }

  .ant-modal-content {
    background: #f9fafa;
  }

  .ant-modal-body {
    padding: 0 24px 10px;
    max-height: 440px;
    overflow-y: auto;
  }

  .ant-modal-footer {
    padding-bottom: 20px;
    border-top: none;
  }

  .batch-item-block {
    width: 100%;
    display: inline-flex;

    span {
      margin-top: 4px;
      margin-left: 4%;
      flex-basis: 20%;
      color: #909090;
    }

    textarea {
      flex-basis: 70%;
    }
  }
}

.save-committee-modal {
  .ant-modal-body {
    padding: 12px 66px 0;
  }

  .ant-modal-footer {
    padding: 20px 48px 40px;
    border-top: none;
  }

  .committee-block {
    .block-item {
      padding-left: 70px;
      border-top: 1px dashed #979797;

      &:first-child {
        border-top: none;
      }

      .title {
        padding-top: 20px;
        color: #000;
      }

      .info-block {
        padding-bottom: 25px;
      }

      .info-item {
        display: flex;
        align-items: center;

        &:not(:first-child) {
          margin-top: 15px;
        }

        & > span {
          flex-shrink: 0;
          width: 100px;
          margin-right: 18px;
          text-align: right;
        }

        .require-icon {
          padding-right: 5px;
          font-style: normal;
          color: #f5222d;
        }

        & > div {
          .box-list {
            display: inline-block;

            .box {
              display: inline-block;
              margin: 8px 20px 8px 0;
              padding: 0 7px;
              height: 24px;
              border: 1px solid #e8e8e8;
              border-radius: 6px;
              line-height: 24px;
              color: #909090;

              img {
                width: 11px;
                vertical-align: unset;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }

  .ant-select {
    width: 220px;
  }
}

.ant-table-tbody > tr > td.ant-table-selection-column,
.ant-table-thead > tr > th.ant-table-selection-column {
  text-align: left;
}

.add-Nominate-modal {
  .ant-table {
    table {
      thead {
        th {
          &:nth-child(1) {
            padding-left: 30px;
          }

          &:nth-last-child(1) {
            padding-right: 30px;
          }
        }
      }

      tbody {
        tr {
          td {
            &:nth-child(1) {
              padding-left: 30px;
            }

            &:nth-last-child(1) {
              padding-right: 30px;
            }
          }
        }
      }
    }
  }
}

.switchRoleDrawer {
  .ant-drawer-header {
    border: none;
    margin-bottom: 5px;
  }

  .ant-drawer-body {
    padding: 0;
  }

  .notHaveTodo,
  .haveTodo {
    display: flex;
    align-items: center;

    & > span {
      display: inline-block;
      width: 10px;
      height: 110px;
      margin-left: 10px;
    }

    & > a {
      width: 290px;
      display: inline-flex;
      align-items: flex-start;
      box-shadow: 0 0 5px rgba(232, 232, 232, 1);

      & > span {
        font-size: 16px;
        display: inline-block;
        height: 110px;
        background-color: #f4f5f6;
        // &:nth-of-type(3) {
        //   width: 10px;
        //   height: 110px;
        //   margin-left: 10px;
        // }

        &:nth-of-type(1) {
          font-weight: 600;
          width: 132px;
          line-height: 110px;
          vertical-align: middle;
          color: #000;
          text-align: center;
          background-size: cover;
          background-repeat: no-repeat;
        }

        &:nth-of-type(2) {
          position: relative;
          text-align: center;
          width: 158px;
          color: black;
          padding-top: 30px;
          padding-left: 60px;
          // box-shadow:0px 0px 5px rgba(232,232,232,1);
          p:nth-of-type(2) {
            font-size: 30px;
            font-weight: 600;
            line-height: 30px;
          }

          p:nth-of-type(1) {
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 16px;
          }
        }
      }

      .right-border {
        position: absolute;
        right: 0;
        bottom: 0;
        top: 0;
        width: 5px;
      }

      &:hover {
        span {
          &:nth-of-type(1) {
            background-color: #fff;
          }
        }
      }
    }
  }

  .notHaveTodo {
    & > a {
      & > span {
        &:nth-of-type(2) {
          background-color: #f4f5f6;
          color: #000;
        }
      }

      &:hover {
        & > span {
          &:nth-of-type(2) {
            background-color: #fff;
          }
        }
      }
    }

    .selectedRole {
      & > span {
        &:nth-of-type(1) {
          background-color: #fff;
          color: #7235ff;
        }

        &:nth-of-type(2) {
          background-color: #fff;
        }
      }
    }
  }

  .haveTodo {
    & > span {
      color: #fff;
    }

    & > a {
      & > span {
        &:nth-of-type(3) {
          color: #fff;
        }
      }
    }

    .selectedRole {
      & > span {
        &:nth-of-type(1) {
          color: #37a5f7;
          background-color: #fff;
        }
      }
    }
  }
}

.todo-list {
  nz-table .ant-table table {
    border-collapse: collapse;
  }
}

.ant-rate-star-full .ant-rate-star-second,
.ant-rate-star-half .ant-rate-star-first {
  color: rgb(27, 184, 244);
}

.cannotGrade {
  .ant-rate-star-first,
  .ant-rate-star-second {
    color: rgb(144, 144, 144);
  }
}

.cantClick {
  // 按钮无法点击，但是有触发时间时的样式
  cursor: not-allowed;
  //background-color: rgb(89,182,252); // todo
}

.ant-modal-confirm {
  width: 340px !important;

  .ant-modal-content {
    .ant-modal-body {
      padding: 30px 30px;

      .ant-modal-confirm-title {
        font-size: 14px;
        font-weight: 400;

        .confirm {
          padding: 0 16px;
        }
      }

      //.ant-modal-confirm-body {
      //  .anticon-question-circle {
      //    display: none;
      //  }
      //
      //  i {
      //    color: #ff1866;
      //    margin-right: 14px;
      //    font-size: 14px;
      //  }
      //}

      .ant-modal-confirm-btns {
        text-align: center;
        float: none;

        button {
          width: 114px;
        }

        button + button {
          margin-left: 20px;
        }

        .ant-btn-default {
          color: #fff;
          background-color: black;
        }
      }
    }
  }
}

.selectedRole {
  cursor: default !important;
}

.ant-breadcrumb {
  padding: 20px;
}
