package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import com.megvii.entity.opdb.Team;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;



@Component
@PropertySource(value = "classpath:dingding.${spring.profiles.active}.properties",encoding = "UTF-8")
public class DingDingRestClient {

    private Logger logger= LoggerFactory.getLogger(DingDingRestClient.class);
    @Value("${DINGDING_TOKEN_URL}")
    private String tokenUrl;

    @Value("${DINGDING_CORP_ID}")
    private String corpId;

    @Value("${DINGDING_CORP_SECRET}")
    private String corpSecret;

    @Value("${DINGDING_MIT_APP_KEY}")
    private String mitAppKey;

    @Value("${DINGDING_MIT_APP_SECRET}")
    private String mitAppSecret;

    @Value("${DINGDING_USER_CREATE_URL}")
    private String userCreateUrl;

    @Value("${DINGDING_GET_USER_DETAIL_URL}")
    private String getUserDetailUrl;

    @Value("${DINGDING_USER_UPDATE_URL}")
    private String userUpdateUrl;

    @Value("${DINGDING_ADD_USER_CHAT_URL}")
    private String addUserChatUrl;

    @Value("${DINGDING_CREATE_DEPT_URL}")
    private String createDeptUrl;

    @Value("${DINGDING_GET_DEPT_DETAIL_URL}")
    private String getDeptDetailUrl;

    @Value("${DINGDING_UPDATE_DEPT_URL}")
    private String updateDeptUrl;

    @Value("${DINGDING_DELETE_DEPT_URL}")
    private String deleteDeptUrl;

    @Value("${DINGDING_USER_DELETE_URL}")
    private String deleteUserUrl;

    @Value("${DINGDING_WORKMSG_DHR_AGENT_ID}")
    private String workmsgDhrAgentId;

    @Value("${DINGDING_WORKMSG_URL}")
    private String workmsgUrl;

    @Value("${DINGDING_COMPANY_DOMAIN}")
    private String domain;

    @Value("${DINGDING_GET_BY_MOBILE_URL}")
    private String getByMobileUrl;

    @Value("${DINGDING_ROBOT_HOOK_URL}")
    private String robotHookUrl;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RestTemplate restTemplate;



//    public boolean tempppppppppp (String dingdingId, String spell) throws Exception {
//        String token = getAuthToken();
//
//        HttpHeaders headers=new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON);
//
//        JSONObject data = new JSONObject();
//        data.put("userid", dingdingId);
//        data.put("name", spell);
//
//        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString(),headers);
//        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity
//                (userUpdateUrl + "?access_token={token}", httpEntity, JSONObject.class, token);
//
//        if (responseEntity.getBody().getString("errcode").equals("0")) {
//            return true;
//        } else {
//            logger.error(responseEntity.getBody().getString("errmsg"));
//            return false;
//        }
//    }


	/**
	 * 发送markdown robothook
	 * token是robot固定参数，不是钉钉认证token
	 * @param token
	 * @param data
	 * @return
	 */
	public boolean sendRobotHook(String token, JSONObject data) {
		String url = robotHookUrl + token;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> httpEntity=new HttpEntity<String>(data.toJSONString(), headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity( url, httpEntity ,JSONObject.class);

		if (responseEntity.getBody().getString("errcode").equals("0")) {
            return true;
        }
		return false;
	}


	/**
	 * 通过mobile获取钉钉id
	 * @param mobile
	 * @return
	 * @throws Exception
	 */
    public String getDingdingIdByMobile(String mobile) throws Exception {
        String token = getAuthTokenV2();

        String url = getByMobileUrl + "?access_token=" + token + "&mobile=" + mobile;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        if (jsonObject.getString("errcode").equals("0")) {
            return jsonObject.getString("userid");
        }
        throw new Exception(mobile + "未获取到钉钉id");
    }



    /**
     * 推送钉钉工作通知
     * 在service层组装msg
     * 后期如果新增的话 将agent_id当做入参就可区分
     * @param msg
     * @param dingdingId
     * @return
     * @throws Exception
     */
    public boolean sendTopMsg(JSONObject msg, String dingdingId) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("agent_id", workmsgDhrAgentId);
//        data.put("userid_list", "163060284422873716,0449334245892812,6408216335252014,1914104911877755,28502903341146109");
        data.put("userid_list", dingdingId);
        data.put("msg", msg);

        HttpEntity<String> httpEntity=new HttpEntity<String>(data.toJSONString(), headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity( workmsgUrl + "?access_token={token}" ,httpEntity ,JSONObject.class, token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            return true;
        } else {
            logger.error(dingdingId + "推送消息失败:" + responseEntity.getBody().getString("errmsg"));
            return false;
        }
    }


    /**
     * 修改上级部门 整体迁移
     * @param
     * @return
     * @throws Exception
     */
    public boolean updateDeptParent(String dingdingId, String parentDingdingId, String name) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("id", dingdingId);
        data.put("parentid", parentDingdingId);
        data.put("name", name);

        HttpEntity<String> httpEntity=new HttpEntity<String>(data.toJSONString(), headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(updateDeptUrl + "?access_token={token}" ,httpEntity ,JSONObject.class, token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            return true;
        } else {
            logger.error("修改钉钉上级异常：" + responseEntity.getBody().getString("errmsg"));
            return false;
        }
    }

    /**
     * 删除钉钉用户
     * @param dingdingId
     * @return
     * @throws Exception
     */
    public String deleteUser(String userId, String dingdingId) throws Exception {
        String token = getAuthToken();

        String url = deleteUserUrl + "?access_token=" + token + "&userid=" + dingdingId;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        if (jsonObject.getString("errcode").equals("0")) {
            return userId + "钉钉删除用户成功!";
        } else {
            String msg = userId + "钉钉删除用户失败:" + jsonObject.getString("errmsg");
            logger.error(msg);
            return msg;
        }
    }


    /**
     * 删除钉钉部门
     * @param deptId
     * @return
     * @throws Exception
     */
    public String deleteDept(String deptId) throws Exception {
        String token = getAuthToken();

        String url = deleteDeptUrl + "?access_token=" + token + "&id=" + deptId;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);

        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        if (jsonObject.getString("errcode").equals("0")) {
            return "钉钉删除部门成功!";
        } else {
            String msg = "钉钉部门" + deptId + "删除失败!" + jsonObject.getString("errmsg");
            logger.error(msg);
            return msg;
        }

    }

    /**
     * @param team
     * @return
     * @throws Exception
     */
    public boolean updateDept(Team team, String name) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("id", team.getDingdingId());

        if (name != null) {
            data.put("name", name);
        } else {
            data.put("name", team.getName());
        }

        HttpEntity<String> httpEntity=new HttpEntity<String>(data.toJSONString(), headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(updateDeptUrl + "?access_token={token}" ,httpEntity ,JSONObject.class, token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            return true;
        } else {
            logger.error(team.getName() + "修改钉钉部门名称错误:" + responseEntity.getBody().getString("errmsg"));
            return false;
        }
    }

    /**
     * 群聊删除用户
     * @param chatId
     * @param userIds
     * @return
     * @throws Exception
     */
    public boolean delUserToChat(String chatId, List<String> userIds) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("chatid", chatId);
        data.put("del_useridlist", userIds);

        HttpEntity<String> httpEntity=new HttpEntity<String>(data.toJSONString(), headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(addUserChatUrl + "?access_token={token}" ,httpEntity ,JSONObject.class, token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            return true;
        } else {
            logger.info(responseEntity.getBody().getString("errmsg"));
            return false;
        }
    }



    /**
     * 查询部门详情
     * @param deptId
     * @return
     * @throws Exception
     */
    public JSONObject getDeptDetail(Integer deptId) throws Exception {
        String token = getAuthToken();

        String url = getDeptDetailUrl + "?access_token=" + token + "&id=" + deptId;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);

        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        return jsonObject;
    }


    /**
     * 创建dept 部门 返回ID
     * 创建成功后使用修改接口修改子部门属性，无论成功失败，返回部门钉钉ID
     * @param name
     * @param order
     * @param parentId
     * @param createChat 是否创建部门群
     * @return
     * @throws Exception
     */
    public String createDept(String name, String order, String parentId, boolean createChat) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("name", name);
        data.put("parentid", parentId);
        data.put("createDeptGroup", createChat);
        if (order != null) {
            data.put("order", order);
        }

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString(),headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(createDeptUrl + "?access_token={token}", httpEntity, JSONObject.class, token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            String dingdingId = responseEntity.getBody().get("id").toString();
            updateDeptContainSubDept(dingdingId, name);
            return dingdingId;
        } else {
            logger.error("创建" + name + "错误" + responseEntity.getBody().get("errmsg"));
            throw new Exception("创建" + name + "错误" + responseEntity.getBody().get("errmsg"));
        }

    }

    /**
     * 修改包含子部门属性
     * @param dingdingId
     * @param name
     * @return
     * @throws Exception
     */
    public boolean updateDeptContainSubDept(String dingdingId, String name) throws Exception {
        String token = getAuthToken();
        HttpHeaders headers = new HttpHeaders();

        JSONObject data = new JSONObject();
        data.put("id", dingdingId);
        data.put("groupContainSubDept", true);

        try {
            HttpEntity<String> tempHttpEntity = new HttpEntity<String>(data.toJSONString(), headers);
            ResponseEntity<JSONObject> tempResponseEntity = restTemplate.postForEntity(updateDeptUrl + "?access_token={token}", tempHttpEntity, JSONObject.class, token);

            if (tempResponseEntity.getBody().getString("errcode").equals("0")) {
                return true;
            } else {
                logger.error(name + "修改包含子部门属性错误");
                return false;
            }
        } catch (Exception e) {
            logger.error(name + "修改包含子部门属性错误" + e);
            return false;
        }

    }



    /**
     * 给user添加chat
     * @param chatId
     * @param userIds
     * @return
     * @throws Exception
     */
    public boolean addUserChat (String chatId, List<String> userIds) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("chatid", chatId);
        data.put("add_useridlist", userIds);

        HttpEntity<String> httpEntity=new HttpEntity<String>(data.toJSONString(),headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(addUserChatUrl + "?access_token={token}",httpEntity,JSONObject.class,token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            return true;
        } else {
            logger.info(responseEntity.getBody().getString("errmsg"));
            return false;
        }
    }


    /**
     * 更新部门人员
     * @param dingdingId  员工钉钉id
     * @param deptDingdingIds   部门钉钉id
     * @return
     * @throws Exception
     */
    public boolean updateUserDept (String dingdingId, String spell, List<Integer> deptDingdingIds) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("userid", dingdingId);
        if (spell != null&&!(dingdingId.equals("1760364622-1474640606")||dingdingId.equals("02610458693634351662")
        ||dingdingId.equals("012322352524253966")||dingdingId.equals("07065917321263772")
                ||dingdingId.equals("0261345863061670903527")
                ||dingdingId.equals("02556736645432591854")
                ||dingdingId.equals("274021112626170983")
                ||dingdingId.equals("275234295426304166"))) {
            data.put("name", spell);
        }
        data.put("department", deptDingdingIds);

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString(),headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity
                (userUpdateUrl + "?access_token={token}", httpEntity, JSONObject.class, token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            return true;
        } else {
            logger.error(responseEntity.getBody().getString("errmsg"));
            return false;
        }
    }


    /**
     * 创建钉钉用户
     * @param spell
     * @param deptIds
     * @param cell
     * @param workNumber
     * @param email
     * @return dingdingId
     * @throws Exception
     */
    public String createDingTalkUser(String spell,Integer[] deptIds,String cell,String workNumber,String userId) throws Exception {
        JSONObject data=new JSONObject();
        data.put("name",spell);
        data.put("department", deptIds);
        data.put("mobile",cell);
        data.put("jobnumber",workNumber);
        data.put("orgEmail", userId + domain);
        String token=null;
        try{
            token=getAuthToken();
        }catch (Exception e){
            logger.error("get token error",e);
            throw new Exception(spell + "获取钉钉token错误");
        }
        HttpEntity<String> httpEntity=new HttpEntity<String>(data.toJSONString(),null);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(userCreateUrl+"?access_token={token}",httpEntity,JSONObject.class,token);

        if (responseEntity.getBody().getString("errcode").equals("0")) {
            String dingdingId = responseEntity.getBody().getString("userid");
            return dingdingId;
        } else {
            logger.error(responseEntity.getBody().getString("errmsg"));
            return null;
        }
    }

    /**
     * 查询钉钉user信息
     * @param dingdingId
     * @return
     * @throws Exception
     */
    public JSONObject getUserDetail(String dingdingId) throws Exception {
        String token = getAuthToken();

        String url = getUserDetailUrl + "?access_token=" + token + "&userid=" + dingdingId;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);

        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        if (jsonObject != null && jsonObject.size() == 2) {
            logger.info(jsonObject.getString("errmsg"));
            return null;
        }

        return jsonObject;
    }


    /**
     *
     * 获取鉴权的header头
     * 优先从redis中获取
     *
     * @return
     * @throws Exception
     */
    public String getAuthToken() throws Exception{
        Object token=redisTemplate.opsForValue().get("mitexecutor_dingding_token");
        if (token==null){
            token =getToken();
            redisTemplate.opsForValue().set("mitexecutor_dingding_token",token);
            redisTemplate.expire("mitexecutor_dingding_token",7000, TimeUnit.SECONDS);
        }


        return (String) token;
    }

    /**
     *
     *
     * 获取token
     * 钉钉APItoken有效期为7200秒
     * @return
     * @throws Exception
     */
    public String getToken() throws Exception{
        JSONObject data=new JSONObject();
        data.put("corpid",corpId);
        data.put("corpsecret",corpSecret);
        ResponseEntity<JSONObject> responseEntity=restTemplate.getForEntity(tokenUrl+"?corpid="+corpId+"&corpsecret="+corpSecret,JSONObject.class);
        JSONObject result=responseEntity.getBody();
        if ("0".equals(result.getString("errcode"))){
            return result.getString("access_token");
        }else {
            throw new Exception("dingding token error");
        }

    }


    /**
     *
     * 获取鉴权的header头
     * 优先从redis中获取
     *
     * @return
     * @throws Exception
     */
    public String getAuthTokenV2() throws Exception{
        Object token=redisTemplate.opsForValue().get("mit:token:dingding");
        if (token==null){
            token = getTokenV2();
            redisTemplate.opsForValue().set("mit:token:dingding",token);
            redisTemplate.expire("mit:token:dingding",7000, TimeUnit.SECONDS);
        }


        return (String) token;
    }

    /**
     *
     *
     * 获取token
     * 钉钉APItoken有效期为7200秒
     * @return
     * @throws Exception
     */
    public String getTokenV2() throws Exception{
        ResponseEntity<JSONObject> responseEntity = restTemplate.getForEntity(tokenUrl+"?appkey="+mitAppKey+"&appsecret="+mitAppSecret,JSONObject.class);
        JSONObject result=responseEntity.getBody();
        if ("0".equals(result.getString("errcode"))){
            return result.getString("access_token");
        }else {
            throw new Exception("dingding token error");
        }
    }



}
