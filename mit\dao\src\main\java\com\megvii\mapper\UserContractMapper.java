package com.megvii.mapper;

import com.megvii.entity.opdb.contract.ContractExportDto;
import com.megvii.entity.opdb.contract.UserContract;
import com.megvii.mapper.filter.UserFilter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/15 15:24
 */
@Repository
public interface UserContractMapper {

	int insertUserContract(UserContract userContract);

	int updateUserContract(UserContract userContract);

	int updateUserCustomizeContract(UserContract userContract);

	int removeContractByContractId(String contractId);

	List<UserContract> selectUserContractByUserId(String userId);
	List<UserContract> getContracts();

	/**
	 * 传入 "工号-"  查询当前最高的编号
	 * @param workNumber
	 * @return
	 */
	String selectContractNumberByWorkNumber(String workNumber);

	// 根据工号查合同解除
	UserContract selectContractByContractNumber(String contractNumber);

	UserContract selectUserNewestContract(String userId);

	UserContract selectUserStandardNewestContract(String userId);

	UserContract selectContractByContractId(String contractId);

	UserContract selectContractById(String id);

	UserContract selectContractByDismissionId(String dismissionId);

	List<String> getAllContractNumber();

	List<UserContract> getContractNoSignStatus();
	List<ContractExportDto> getContractsByFilter(@Param("filter") UserFilter filter);

}
