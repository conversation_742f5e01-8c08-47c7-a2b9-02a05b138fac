package com.megvii.service;

import com.megvii.mapper.IpWhiteListMapper;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/8/28 14:39
 */
@Service
public class IPWhiteListService {

    @Autowired
    private IpWhiteListMapper ipWhiteListMapper;

    public boolean validateIp (String ip, String name) {
        List<String> ips = ipWhiteListMapper.queryWhiteIpByName(name);

        if (ips != null && ips.size() > 0) {
            for (String s : ips) {
                if (ip.equals(s)) {
                    return true;
                }
            }
        }
        return false;
    }




}
