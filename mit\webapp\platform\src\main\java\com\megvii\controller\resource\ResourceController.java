package com.megvii.controller.resource;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.contract.ContractStatus;
import com.megvii.service.MMISMegviiConfigService;
import com.megvii.service.contract.ResourceService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/4/14 17:03
 */
@RestController
@RequestMapping("/resource")
public class ResourceController {

	@Autowired
	private ResourceService resourceService;

	@Autowired
	private MMISMegviiConfigService mmisMegviiConfigService;


	@GetMapping("/employType")
	@ResponseBody
	public ResponseDataEntity getEmployType() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			JSONArray data = resourceService.getResourceJsonFromRedis("CN_EMP_TYPE");
			if (data == null) {
				data = mmisMegviiConfigService.getJsonarrayByKey("CN_EMP_TYPE");
			}
			response.setCode(ResponseDataEntity.OK);
			response.setData(data);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("key列表没查到");
		}

		return response;
	}

	@GetMapping("/company")
	@ResponseBody
	public ResponseDataEntity getCompany() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			JSONArray data = resourceService.getResourceJsonFromRedis("CN_CONTRACT");
			if (data == null) {
				data = mmisMegviiConfigService.getJsonarrayByKey("CN_CONTRACT");
			}
			response.setCode(ResponseDataEntity.OK);
			response.setData(data);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("key列表没查到");
		}

		return response;
	}


	@GetMapping("/location")
	@ResponseBody
	public ResponseDataEntity getLocation() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			JSONArray data = resourceService.getResourceJsonFromRedis("CN_LOCATION");
			if (data == null) {
				data = mmisMegviiConfigService.getJsonarrayByKey("CN_LOCATION");
			}
			response.setCode(ResponseDataEntity.OK);
			response.setData(data);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("key列表没查到");
		}

		return response;
	}

	@GetMapping("/workBase")
	@ResponseBody
	public ResponseDataEntity getWorkBase() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			JSONArray data = resourceService.getResourceJsonFromRedis("CN_OFFICE_PLACE");
			if (data == null) {
				data = mmisMegviiConfigService.getJsonarrayByKey("CN_OFFICE_PLACE");
			}
			response.setCode(ResponseDataEntity.OK);
			response.setData(data);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("key列表没查到");
		}

		return response;
	}

	@GetMapping("/idType")
	@ResponseBody
	public ResponseDataEntity getIdType() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			JSONArray data = resourceService.getResourceJsonFromRedis("CN_IDTYPE");
			if (data == null) {
				data = mmisMegviiConfigService.getJsonarrayByKey("CN_IDTYPE");
			}
			response.setCode(ResponseDataEntity.OK);
			response.setData(data);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("key列表没查到");
		}

		return response;
	}

	@GetMapping("/position")
	@ResponseBody
	public ResponseDataEntity getPosition() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			JSONObject data = resourceService.getPositionFromRedis();
			response.setCode(ResponseDataEntity.OK);
			response.setData(data);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("列表转换失败");
		}

		return response;
	}

	@GetMapping("/contractStatus")
	@ResponseBody
	public ResponseDataEntity getContractStatus() {
		ResponseDataEntity response = new ResponseDataEntity();

		List<String> data = new ArrayList<>();
		for (Map.Entry<String, String> entry : ContractStatus.map.entrySet()) {
			data.add(entry.getValue());
		}

		response.setCode(ResponseDataEntity.OK);
		response.setData(data);
		response.setMsg("success");


		return response;
	}


}
