package com.megvii;

import com.megvii.client.Office365Client;
import com.megvii.entity.opdb.User;
import com.megvii.service.GroupService;
import com.megvii.service.LdapService;
import com.megvii.service.SyncOfficeService;
import com.megvii.service.UserService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class LdapTest {

    @Autowired
    LdapService ldapService;
    @Autowired
    UserService userService;

    @Autowired
    private GroupService groupService;

    @Autowired
    private Office365Client office365Client;

    @Autowired
    private SyncOfficeService syncOfficeService;


    @Test
    public void testGroup() throws Exception {


//        String userDn = ldapService.getUserDn("guojianglin");

        System.out.println(1);


//        ldapService.addMemberToGroup("666", "cn=" + userDn + ",ou=Users,ou=Company");

//        ldapService.deleteGroupUser("qinaiwei", "666");




//        List<String> listssss = ldapService.getGroupMembers("333");
//
        System.out.println(2);
//
//
//        try {
//            Map<String, JSONObject> maps = groupService.getAllOfficeMap();
//            System.out.println(21);
//        } catch (Exception e) {
//            System.out.println(1);
//        }
//
//        System.out.println(2);



//        ldapService.createLdapGroup("bilibili", "lueluelue", "略略略");




//        String a = syncOfficeService.syncAllOfficeGroup();

//        System.out.println(a);


        System.out.println(2);


        Map<String,Object> map= new HashMap<>();
        map.put("1",111);
        map.put("2",111);
        map.put("3",111);
        map.put("4",111);
        List<String> aa= new ArrayList<>();
        aa.add("1");
        aa.add("2");
        List list=map.entrySet().stream().filter(e->!aa.contains(e.getKey())).collect(Collectors.toList());
        System.out.println(list);


        Map newMap = map.entrySet().stream().filter(e->!aa.contains(e.getKey())).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));

        System.out.println(2);


    }

    @Test
    public void testGetGroup() throws Throwable{
//        String s = ldapService.getUserDn("jiangyuhong");
//        String s=ldapService.getGroupDn("csg-all");
//        System.out.println(s);
//        ldapService.modifyGroup("csg-all", "displayname", "BBBBB");

//        ldapService.deleteGroupUser("yupeng", "666");
        User user = userService.getUserByUserId("jiangyuhong");

        List<String> listA = new ArrayList<>();
        listA = groupService.getGroupList(user);

//        List<String> listB = new ArrayList<>();
//        listB = groupService.getUserGroupList(user);

        System.out.println(1);


    }
    @Test
    public void  addMemberToGroup() throws  Throwable{
        ldapService.addMemberToGroup("mc","CN=wangxiaoer03,OU=Users,OU=Company,DC=megvii-demo,DC=com");
    }
    @Test
    public void testLdapAddUser() throws Exception {
        User user = userService.getUserByUserId("zhangliangliang03");
        ldapService.synUser2Ldap(user);
    }

    @Test
    public void testModifyDepartment() {
//        ldapService.modifyUser("caojiayan", "department", "研究院");

        User sfUser = new User();
        sfUser.setSpell("曹嘉彦");

        String firstName = sfUser.getSpell().substring(0,1);
        String lastName = sfUser.getSpell().substring(1, sfUser.getSpell().length());

        ldapService.modifyUser("caojiayan", "sn", firstName);
        ldapService.modifyUser("caojiayan", "givenName", lastName);



    }

    @Test
    public void testGroupTemp() throws Exception {

        ldapService.updatePassword("cassie", "Cxm931109**");

//        String realDn = ldapService.getRealUserDn("liuyang07");
//        String userDn = ldapService.getUserDn("liuyang07");
//
//        System.out.println(1);
//
//        ldapService.deleteGroupUser("liuyang", "maps-users");
//
//        System.out.println(1);

//        User user = userService.getUserByUserId("jiangyuhong");
//
//        List<String> list = ldapService.getGroupList(user);
//        list.removeAll(officeSecureGroups);



    }





}
