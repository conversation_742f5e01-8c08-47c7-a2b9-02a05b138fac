import { Injectable } from '@angular/core'
import { SendRequest } from '../../core/services'

@Injectable({
  providedIn: 'root'
})
export class ResignService {
  constructor(private sendRequest: SendRequest) {}
  // 获取离职合同列表
  getuserDimissionList(data) {
    return this.sendRequest.post('/api/userDimission/backlog/search', data)
  }
  // 查询单个人员页
  getDismissionUserInfo(data) {
    return this.sendRequest.get('/api/userDimission/info', data)
  }
  // 修改员工基本信息
  editUserInform(data) {
    return this.sendRequest.post('/api/userDimission/info', data)
  }
  // 提交待签署合同
  submitContract(data) {
    return this.sendRequest.post('/api/userDimission/snapshot/create', data)
  }
  // 更新待签署合同
  updateContract(data) {
    return this.sendRequest.post('/api/userDimission/snapshot/update', data)
  }
  // 获取待签署合同
  getUserContract(data) {
    return this.sendRequest.get('/api/userDimission/snapshot', data)
  }
  // 删除待草稿
  deleteSnapshot(data) {
    const { dismissionId } = data
    return this.sendRequest.post(
      `/api/userDimission/snapshot/delete?dismissionId=${dismissionId}`,
      {}
    )
  }
  // 查询员工离职合同
  searchUserContract(data) {
    return this.sendRequest.get('/api/userDimission/contract', data)
  }
  // 撤回合同
  returnContract(data) {
    return this.sendRequest.post('/api/userDimission/recall', data)
  }
  // 离职合同盖章
  toStampContract(data) {
    const { contractId, company } = data
    return this.sendRequest.post(
      `/api/userDimission/seal?contractId=${contractId}&company=${company}`,
      {}
    )
  }
  // 离职合同发给员工签署
  signContract(data) {
    return this.sendRequest.post('/api/userDimission/sign', data)
  }
  // 上传文件
  // uploadFile(data, file) {
  //   return this.sendRequest.uploadFile(`/api/userDimission/upload?name=${data.name}`, file)
  // }
  // 离职合同中的上传文件
  uploadFile(data, file) {
    return this.sendRequest.uploadFile(
      `/api/contract/contractFile/upload?userId=${data.id}&fileName=${data.name}`,
      file
    )
  }
  //提交上传文件
  submitFile(data) {
    return this.sendRequest.post('/api/userDimission/sign/offline', data)
  }
  // 发送邮件
  sendEmailForUser(data) {
    return this.sendRequest.post(`/api/userDimission/sendEmail?dismissionId=${data}`, {})
  }
  // 归档
  returnDocument(data) {
    return this.sendRequest.post(`/api/userDimission/complete?dismissionId=${data}`, {})
  }
  // /api/userDimission/getDismissionBacklog  主动拉取离职人员
  handlePullDission() {
    return this.sendRequest.get('/api/userDimission/getDismissionBacklog', {})
  }
  // 合同管理中的扫描上传确认
  updateManageFile(file, data) {
    return this.sendRequest.uploadFile(
      `/api/contract/contractFile/update?id=${file.id}&fileName=${file.name}`,
      data
    )
  }
  // 获取结算单信息
  getMoneyInfo(data) {
    return this.sendRequest.get('/api/userDimission/snapshot/getremark', data)
  }
}
