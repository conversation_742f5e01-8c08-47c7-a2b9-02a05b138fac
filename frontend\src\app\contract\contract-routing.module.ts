import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { ContractComponent } from './contract.component'
import { UserInfoComponent } from './user-info/user-info.component'
import { NewSignComponent } from './new-sign/new-sign.component'
import { MgtComponent } from './mgt/mgt.component'
import { ExtendComponent } from './extend/extend.component'
import { EndorseComponent } from './endorse/endorse.component'
import { ChangeComponent } from './change/change.component'
import { ResignComponent } from './resign/resign.component'
import { ResignUserComponent } from './resign/resign-user/resign-user.component'

const routes: Routes = [
  {
    path: '',
    component: ContractComponent,
    children: [
      {
        path: 'user',
        component: UserInfoComponent,
        data: {
          title: '人员详情',
          keep: false
        }
      },
      {
        path: 'mgt',
        component: MgtComponent,
        data: {
          title: '合同管理',
          keep: true
        },
        children: [
          {
            path: 'user',
            component: UserInfoComponent,
            data: {
              title: '人员详情',
              keep: false
            }
          }
        ]
      },
      {
        path: 'new',
        component: NewSignComponent,
        data: {
          title: '新签合同',
          keep: true
        },
        children: [
          {
            path: 'user',
            component: UserInfoComponent,
            data: {
              title: '人员详情',
              keep: false
            }
          }
        ]
      },
      {
        path: 'extend',
        component: ExtendComponent,
        data: {
          title: '续签合同',
          keep: true
        },
        children: [
          {
            path: 'user',
            component: UserInfoComponent,
            data: {
              title: '人员详情',
              keep: false
            }
          }
        ]
      },
      {
        path: 'endorse',
        component: EndorseComponent,
        data: {
          title: '合同转签',
          keep: true
        },
        children: [
          {
            path: 'user',
            component: UserInfoComponent,
            data: {
              title: '人员详情',
              keep: false
            }
          }
        ]
      },
      {
        path: 'change',
        component: ChangeComponent,
        data: {
          title: '变更合同',
          keep: true
        },
        children: [
          {
            path: 'user',
            component: UserInfoComponent,
            data: {
              title: '人员详情',
              keep: false
            }
          }
        ]
      },
      {
        path: 'resign',
        component: ResignComponent,
        data: {
          title: '离职合同',
          keep: true
        },
        children: [
          {
            path: 'user',
            component: ResignUserComponent,
            data: {
              title: '人员详情',
              keep: false
            }
          }
        ]
      }
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ContractRoutingModule {}
