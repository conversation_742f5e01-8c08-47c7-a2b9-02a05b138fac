package com.megvii.job;


import com.megvii.common.OperateResult;
import com.megvii.operator.SpecialDismissAccountOperatorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 *  * 手动禁用离职人员账号   （已废弃）
 * <AUTHOR>
 * @date 2019/11/4 21:09
 */
@JobHandler(value="SpecialDismissAccountExecutorHandler")
@Component
public class SpecialDismissAccountExecutorHandler extends IJobHandler {

    @Autowired
    private SpecialDismissAccountOperatorService service;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        if (s == null) {
            return new ReturnT(200,"必须填写userId");
        }

        Map map = new HashMap();
        map.put("ids", s);

        OperateResult result= service.operateMain(map);
        if(result.getStatus() == 2){
            return new ReturnT(500,"<span style=\"color:#e83c4c;\"> 主操作执行失败</span> "+result.getMsg());
        }else if(result.getStatus() == 0){
            return new ReturnT(200,"操作完成，但存在失败操作--->"+result.getMsg());
        }else {
            return new ReturnT(200,result.getMsg());
        }
    }

}
