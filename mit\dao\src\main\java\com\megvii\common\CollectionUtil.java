package com.megvii.common;

import java.util.ArrayList;
import java.util.List;

public class CollectionUtil {
    /**
     *
     * List去除重复、去空串
     * @param list
     * @return
     */
    public static List<String> removeDuplicateAndBlank(List<String> list){
        List<String> listTemp = new ArrayList<>();
        for(int i=0;i<list.size();i++){
            if(list.get(i)!=null&&!"".equals(list.get(i))&&!listTemp.contains(list.get(i))){
                listTemp.add(list.get(i));
            }
        }
        return listTemp;
    }
}
