package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.UserService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/6/11 11:54
 */
@Service
public class SpecialAddDingdingIdOperatorService extends BaseOperatorService {

	@Autowired
    private UserService userService;

	/**
     *
     * 手动执行离职 必须传参数
     * 主操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
		Object userId = paraMap.get("userId");
		Object dingdingId = paraMap.get("dingdingId");
		if (userId != null && dingdingId != null) {
			return OperateByUserId((String) userId, (String) dingdingId);
		}
		 return new OperateResult(1, "参数异常");
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {

        return new OperateResult(1, "特殊任务单次不执行");

    }

    /**
     *
     * 根据userId禁用权限
     * @param userId
     * @return
     */
    public OperateResult OperateByUserId(String userId, String dingdingId){
    	User user = userService.getUserByUserId(userId);
    	user.setDingdingId(dingdingId);
    	userService.updateUser(user);
    	return new OperateResult(1, "修改成功");
    }

}
