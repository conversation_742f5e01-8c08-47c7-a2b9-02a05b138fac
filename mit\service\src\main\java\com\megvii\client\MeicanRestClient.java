package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * 美餐接口请求类
 */
@Component
@PropertySource("classpath:meican.properties")
public class MeicanRestClient {

    private Logger logger= LoggerFactory.getLogger(MeicanRestClient.class);

    @Value("${MEICAN_ADD_URL}")
    private String meicanAddUrl;

    @Value("${MEICAN_DEL_URL}")
    private String meicanDelUrl;

    @Value("${MAIL_PREFIX}")
    private String mailPrefix;

    @Autowired
    RestTemplate restTemplate;


    /**
     * meican请求使用表单提交 MultiValueMap
     * @param curTime 时间戳
     * @param signature 签
     * @param userId
     * @param department 部门 SF_CODE
     * @return
     */
    public JSONObject createMeicanUser(Long curTime, String signature, String userId, String department) throws Exception {

        MultiValueMap<String, String> params= new LinkedMultiValueMap<String, String>();
        params.add("timestamp", curTime.toString());
        params.add("signature", signature);
        params.add("email", userId + "@" + mailPrefix);
        params.add("department", department);
        params.add("realName", userId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<MultiValueMap<String, String>>(params, headers);

        for (int i = 0; i < 3; i ++) {
            try {
                JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(meicanAddUrl ,restTemplate.exchange(meicanAddUrl,
                        HttpMethod.POST, requestEntity, String.class).getBody());

                return jsonObject;
            } catch (Exception e) {
                if (i < 2) {
                    logger.error(userId + "添加美餐账号重试" + (i + 1) + "次");
                    continue;
                } else {
                    logger.error(e.getMessage());
                    throw e;
                }
            }
        }
        throw new Exception(userId + "添加美餐账号异常");
    }


    /**
     * @param curTime
     * @param signature
     * @param userId
     * @return
     */
    public JSONObject deleteMeican(Long curTime, String signature, String userId) throws Exception {

        MultiValueMap<String, String> params= new LinkedMultiValueMap<String, String>();
        params.add("timestamp", curTime.toString());
        params.add("signature", signature);
        params.add("email", userId + "@" + mailPrefix);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<MultiValueMap<String, String>>(params, headers);

        for (int i = 0; i < 3; i ++) {
            try {
                JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(meicanDelUrl ,restTemplate.exchange(meicanDelUrl,
                        HttpMethod.POST, requestEntity, String.class).getBody());

                return jsonObject;
            } catch (Exception e) {
                if (i < 2) {
                    logger.error(userId + "禁用美餐账号重试" + (i + 1) + "次");
                    continue;
                } else {
                    logger.error(e.getMessage());
                    throw e;
                }
            }
        }
        throw new Exception(userId + "禁用美餐账号异常");

    }




}
