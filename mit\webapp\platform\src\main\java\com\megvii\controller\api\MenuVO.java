package com.megvii.controller.api;

import com.megvii.entity.opdb.Menu;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/9 17:41
 */
@Data
public class MenuVO {

	private Integer id;
	private String name;
	private String code;
	private String url;
	private int priority;
	private int menuLevel;
	private int parentId;
	private List<MenuVO> children;

	public static List<MenuVO> toVOs(List<Menu> list) {
		List<MenuVO> vos = new ArrayList<>();

		Map<Integer, MenuVO> menuVOMap = new HashMap<>();

		for (Menu menu : list) {
			if (menu.getMenuLevel() == 1) {
				MenuVO vo = toVO(menu);
				menuVOMap.put(menu.getId(), vo);
				continue;
			}
			if (menu.getMenuLevel() == 2) {
				menuVOMap.get(menu.getParentId()).getChildren().add(toVO(menu));
				continue;
			}
		}

		for (Map.Entry<Integer, MenuVO> entry : menuVOMap.entrySet()) {
			vos.add(entry.getValue());
		}
		return vos;
	}


	private static MenuVO toVO(Menu menu) {
		MenuVO vo = new MenuVO();
		vo.setId(menu.getId());
		vo.setName(menu.getMenuName());
		vo.setCode(menu.getMenuCode());
		vo.setUrl(menu.getUrl());
		vo.setPriority(menu.getPriority());
		vo.setMenuLevel(menu.getMenuLevel());
		vo.setParentId(menu.getParentId());
		vo.setChildren(new ArrayList<>());

		return vo;
	}


}
