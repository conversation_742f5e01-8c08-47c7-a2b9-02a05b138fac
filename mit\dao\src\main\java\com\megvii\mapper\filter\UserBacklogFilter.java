package com.megvii.mapper.filter;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/17 15:37
 */
@Data
public class UserBacklogFilter extends UserFilter {

	private String backlogStatus;

	private String xtype;

	private String dismissionDateFrom;
	private String dismissionDateTo;
	private String topTeam;
	private String leaveType;
	/**
	 * 0:待办理 1：员工签署中 2：待盖章 3：待发送邮件 -1：已完成
	 */
	private String flowStatus;
	private String dimissionTag;
}
