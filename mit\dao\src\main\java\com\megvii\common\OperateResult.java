package com.megvii.common;

/**
 * 操作执行结果进行封装
 * status：状态，0子失败，1成功 ,2主任务失败
 * msg 消息
 * object
 */
public class OperateResult {
    private int status=1;
    private String msg;
    private Object object;

    public OperateResult() {
    }

    public OperateResult(int status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }
}
