package com.megvii;

import com.megvii.entity.opdb.User;
import com.megvii.service.MeicanService;
import com.megvii.service.UserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestMeican {

    @Autowired
    private UserService userService;

    @Autowired
    private MeicanService meicanService;


    @Test
    public void testMeican() {
        User user = userService.getUserByUserId("jiangyuhong");


//        boolean result = meicanService.createMeicanUser(user);

    }


}
