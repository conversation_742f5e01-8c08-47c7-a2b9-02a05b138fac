package com.megvii.job;

import com.megvii.common.OperateResult;
import com.megvii.common.ParameterUtil;
import com.megvii.operator.AliyunOperatorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 阿里云数据同步执行器
 *
 * 1.从阿里云获取人员信息保存到IT_user表
 * 2.将个人照片上传至考拉
 * 3.将照片上传至ftp
 */

@JobHandler(value="aliyunExecutorHandler")
@Component
public class AliyunExecutorHandler extends IJobHandler {

    @Autowired
    AliyunOperatorService aliyunOperatorService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        OperateResult result=aliyunOperatorService.operateMain(ParameterUtil.getParaMap(s));
        if(result.getStatus()==2){
            return new ReturnT(500,"<span style=\"color:#e83c4c;\"> 主操作执行失败</span> "+result.getMsg());
        }else if(result.getStatus()==0){
            return new ReturnT(200,"操作完成，但存在失败操作--->"+result.getMsg());
        }else {
            return new ReturnT(200,result.getMsg());
        }
    }
}
