<ng-container *ngIf="isShowSelf">
  <nz-breadcrumb>
    <nz-breadcrumb-item> 合同管理 </nz-breadcrumb-item>
    <nz-breadcrumb-item> 合同变更 </nz-breadcrumb-item>
  </nz-breadcrumb>
  <div class="content-block content-item">
    <app-title title="合同变更"></app-title>
    <nz-tabset
      nzSize="large"
      (nzSelectedIndexChange)="selectedIndexChange($event)"
      [nzSelectedIndex]="selectedIndex"
    >
      <nz-tab [nzTitle]="changeTpl">
        <ng-template #changeTpl>
          <nz-badge [nzCount]="change.total || 0" [nzOffset]="[15, -5]">
            工作地变更
          </nz-badge>
        </ng-template>
        <app-contract-table type="change" #change></app-contract-table>
      </nz-tab>
    </nz-tabset>
  </div>
</ng-container>
<router-outlet
  (activate)="onActivate($event)"
  (deactivate)="onDeactivate($event)"
>
</router-outlet>
