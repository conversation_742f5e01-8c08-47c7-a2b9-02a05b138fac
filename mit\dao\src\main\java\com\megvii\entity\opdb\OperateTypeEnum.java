package com.megvii.entity.opdb;

/**
 * 记录操作类型
 * 目前分为：ADD    新增
 *           UPDATE 修改
 *           QUERY  查询
 *           DELETE 删除
 *         EXTERNAL 外部（第三方系统请求）
 * <AUTHOR>
 * @date 2020/04/17
 */
public enum OperateTypeEnum {

    ADD("ADD"),
    UPDATE("UPDATE"),
    QUERY("QUERY"),
    DEL("DELETE"),
    EXTERNAL("EXTERNAL");

    private String code;

    OperateTypeEnum(String code) {
        this.code = code;
    }

    public String getCode(){
        return code;
    }
}
