package com.megvii.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.megvii.entity.opdb.MeginUserDimissionFlowPO;
import com.megvii.mapper.MeginUserDimissionFlowMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MeginUserDimissionFlowService {

  @Autowired
  private MeginUserDimissionFlowMapper mapper;

  public Boolean insert(MeginUserDimissionFlowPO po){
   return mapper.insert(po);
  }

  public Boolean update(MeginUserDimissionFlowPO po){
    return mapper.update(po);
  }

  public MeginUserDimissionFlowPO selectByUserId(String userId){
    return mapper.selectByUserId(userId);
  }

  public MeginUserDimissionFlowPO getDimissionFlowByUid(String uid) {
    return mapper.getDimissionFlowByUid(uid);
  }
}
