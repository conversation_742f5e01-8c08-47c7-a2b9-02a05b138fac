package com.megvii;

import com.megvii.entity.dirac.DiracUser;
import com.megvii.service.DiracUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/12/2 20:01
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestDiracUser {

    @Autowired
    private DiracUserService diracUserService;

    @Test
    public void testGet() {
        DiracUser diracUser = diracUserService.selectUserByUserId("jiangyuhong");

        System.out.println(1);


    }


}
