package com.megvii.operator;

import com.megvii.common.DateTimeUtil;
import com.megvii.common.MailUtil;
import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.MailTemplateInfo;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.LdapService;
import com.megvii.service.MailSendService;
import com.megvii.service.UserService;
import javax.mail.MessagingException;
import org.thymeleaf.context.Context;
import org.thymeleaf.TemplateEngine;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LdapOperatorService extends BaseOperatorService {
	private Logger logger = LoggerFactory.getLogger(LdapOperatorService.class);

	@Autowired
	private UserService userService;

	@Autowired
	private LdapOperatorService ldapOperatorService;

	@Autowired
	private LdapService ldapService;

	@Autowired
	private MailSendService mailSendService;


	@Override
	public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
		Object userId = paraMap.get("userId");
		Object date = paraMap.get("date");
		List<User> users;

		if (userId != null) {
			return OperateByUserId((String) userId);
		} else if (date != null) {
			UserFilter filter = new UserFilter();
			filter.setExpectDate((String) date);
			users = userService.getUsersByFilter(filter);
		} else {
			users = userService.getUserListNeedToLdap();
		}

		if (users.size() == 0) {
			return new OperateResult(1, "无入职人员");
		} else {
			StringBuilder msg = new StringBuilder();
			for (User user : users) {
				OperateResult operateResult = ldapOperatorService.operateOnce(paraMap, user);
				msg.append(operateResult.getMsg() + "<br/>");
			}
			mailSendService.sendAdMailMsg(users);

			return new OperateResult(1, msg.toString());
		}

	}

	@Override
	protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
		User user = (User) params[0];
		String msg = ldapService.synUser2Ldap(user);
		return new OperateResult(1, msg);
	}

	public OperateResult OperateByUserId(String userId) {
		User user = userService.getUserByUserId(userId);
		if (user != null) {
			String msg = ldapService.synUser2Ldap(user);
			mailSendService.sendAdMailMsg(new ArrayList<User>(){{
					add(user); }});
			return new OperateResult(1, msg);
		} else {
			return new OperateResult(1, "人员" + userId + "不存在");
		}
	}



//		StringBuilder mailContent = new StringBuilder();
//		mailContent.append("<table border=\"1\" cellspacing=\"0\"><tbody><tr class=\"firstRow\"><td width=\"72\" valign=\"top\">工号</td>");
//		mailContent.append("<td valign=\"top\">ID</td>");
//		mailContent.append("<td valign=\"top\">姓名</td>");
//		mailContent.append("<td valign=\"top\">部门</td>");
//		mailContent.append("<td valign=\"top\">入职时间</td>");
//		mailContent.append("<td valign=\"top\">结果</td>");
//		mailContent.append("<td valign=\"top\">详情</td>");
//		mailContent.append("<td valign=\"top\">设备及系统</td>");
//		mailContent.append("<td valign=\"top\">入职地点</td></tr>");

//		for (User user : users) {
//			mailContent.append("<tr><td  valign=\"top\">" + user.getWorkNumber() + "</td>");
//			mailContent.append("<td  valign=\"top\">" + user.getUserId() + "</td>");
//			mailContent.append("<td  valign=\"top\">" + user.getSpell() + "</td>");
//			mailContent.append("<td  valign=\"top\">" + user.getTeam() + "</td>");
//			mailContent.append("<td  valign=\"top\">" + DateTimeUtil.date2String(user.getExpectDate()) + "</td>");
//			if (user.getLdapFlag() == 1) {
//				mailContent.append("<td  valign=\"top\">成功</td>");
//				successCount++;
//			} else if (user.getLdapFlag() == 0) {
//				mailContent.append("<td  valign=\"top\">失败</td>");
//				errorCount++;
//			} else if (user.getLdapFlag() == 2) {
////				mailContent.append("<td  valign=\"top\">已存在，不可重复创建</td>");
//			}
//			mailContent.append("<td  valign=\"top\">" + (user.getOriPassword() == null ? "" : user.getOriPassword()) + "</td>");
//			mailContent.append("<td  valign=\"top\">" + (user.getComputer() == null ? "" : user.getComputer()) + (user.getOs() == null ? "" : user.getOs()) + "</td>");
//			mailContent.append("<td  valign=\"top\">" + (user.getWorkBase() == null ? "" : user.getWorkBase()) + "</td></tr>");
//		}
//		mailContent.append("</tbody></table>");
//		if (successCount > 0 || errorCount > 0)
//			mailSendService.sendMail("MIT创建ldap账号[成功" + successCount + "个][失败" + errorCount + "个]", mailContent.toString(), "<EMAIL>,<EMAIL>,<EMAIL>");




	/**
	 * 周一可创建本周二、周三入职人员的ldap账号
	 * 周二可创建周三、周四入职人员的ldap账号
	 * 周三可创建周四、周五、周六、周日、下周一入职人员的ldap账号（周末一般不入职，但这块逻辑这里不控制）
	 * 周四可创建周五、周六、周日、下周一入职人员的ldap账号
	 * 周五可创建周六、周日、下周一入职人员的ldap账号
	 *
	 * @param now      当前日期
	 * @param hireDate 入职日期
	 * @return
	 */
//	private boolean isNeedToLdap(LocalDateTime now, LocalDateTime hireDate) {
//		boolean isNeedCreate = false;
//		if (now.getDayOfWeek() == DayOfWeek.MONDAY && DateTimeUtil.diffDateByDay(now, hireDate) < 0 && DateTimeUtil.diffDateByDay(now, hireDate) >= -2) {
//			isNeedCreate = true;
//		} else if (now.getDayOfWeek() == DayOfWeek.TUESDAY && DateTimeUtil.diffDateByDay(now, hireDate) < 0 && DateTimeUtil.diffDateByDay(now, hireDate) >= -2) {
//			isNeedCreate = true;
//		} else if (now.getDayOfWeek() == DayOfWeek.WEDNESDAY && DateTimeUtil.diffDateByDay(now, hireDate) < 0 && DateTimeUtil.diffDateByDay(now, hireDate) >= -5) {
//			isNeedCreate = true;
//		} else if (now.getDayOfWeek() == DayOfWeek.THURSDAY && DateTimeUtil.diffDateByDay(now, hireDate) < 0 && DateTimeUtil.diffDateByDay(now, hireDate) >= -4) {
//			isNeedCreate = true;
//		} else if (now.getDayOfWeek() == DayOfWeek.FRIDAY && DateTimeUtil.diffDateByDay(now, hireDate) < 0 && DateTimeUtil.diffDateByDay(now, hireDate) >= -5) {
//			isNeedCreate = true;
//		}
//		return isNeedCreate;
//	}


}
