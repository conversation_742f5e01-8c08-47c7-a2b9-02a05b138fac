import { NgModule } from '@angular/core'
import { Routes, RouterModule, PreloadAllModules } from '@angular/router'
import { NoContentComponent } from './status'

const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./topmenu/topmenu.module').then((m) => m.TopmenuModule)
  },
  {
    path: '**',
    component: NoContentComponent,
    data: {
      title: '404'
    }
  }
]

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      useHash: false,
      preloadingStrategy: PreloadAllModules
    })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule {}
