import { from as observableFrom, Subject } from 'rxjs'
import { Injectable } from '@angular/core'

// 广播服务，用于组件间传递信息
@Injectable({
  providedIn: 'root'
})
export class EventsService {
  private listeners: any
  private eventsSubject: any
  private events: any
  constructor() {
    this.listeners = {}
    this.eventsSubject = new Subject()
    this.events = observableFrom(this.eventsSubject)
    this.events.subscribe(({ name, args }) => {
      if (this.listeners[name]) {
        for (const listener of this.listeners[name]) {
          listener(...args)
        }
      }
    })
  }
  on(name: string, listener: any) {
    if (!this.listeners[name]) {
      this.listeners[name] = []
    }
    this.listeners[name].push(listener)
  }
  broadcast(name: string, ...args: any[]) {
    this.eventsSubject.next({
      name,
      args
    })
  }
}
