const baseName = './node_modules/@megvii/conan/conan-lint-env/node_modules/'

module.exports = {
  extends: [
    baseName + '@megvii/eslint-config-conan',
    baseName + '@megvii/eslint-config-conan/typescript'
  ],
  rules: {
    eqeqeq: 0,
    'no-var': 0,
    'no-void': 0,
    'max-params': 0,
    'no-sequences': 0,
    'guard-for-in': 0,
    'no-return-assign': 0,
    'no-param-reassign': 0,
    'no-implicit-coercion': 0,
    'no-inner-declarations': 0,
    '@typescript-eslint/no-require-imports': 0,
    '@typescript-eslint/prefer-optional-chain': 0,
    '@typescript-eslint/no-unused-expressions': 0,
    '@typescript-eslint/no-this-alias': 0,
    '@typescript-eslint/member-ordering': 0,
    '@typescript-eslint/no-useless-constructor': 0,
    '@typescript-eslint/no-parameter-properties': 0,
    '@typescript-eslint/consistent-type-assertions': 0,
    '@typescript-eslint/explicit-member-accessibility': 0
  }
}
