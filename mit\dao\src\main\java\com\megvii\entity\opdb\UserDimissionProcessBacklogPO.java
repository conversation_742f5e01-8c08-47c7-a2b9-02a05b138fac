package com.megvii.entity.opdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * dhr离职流程快照
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
public class UserDimissionProcessBacklogPO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 离职流程id
     */
    private String dimissionId;

    /**
     * 离职人员
     */
    private String userId;

    /**
     * dhr数据类型：dhr数据类型：1.dhr提交的流程2，er后台添加，3，有流程到期未离职 9，流程中驳回
     */
    private Integer xType;

    /**
     * 离职日期
     */
    private Date dimissionDate;

    /**
     * 状态:1已加入监控组，0.未加入监控组
     */
    private Integer monitorStatus;

    private Date createTime;

    private Date deletedTime;

    private Date updateTime;
}
