<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserProveContactBacklogMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.contract.UserProveContractBacklog">
        <id column="id" property="id"/>
        <id column="dhr_id" property="dhrId"/>
        <result column="user_id" property="userId"/>
        <result column="work_number" property="workNumber"/>
        <result column="status" property="status"/>
        <result column="xtype" property="xtype"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="contract_id" property="contractId"/>
        <result column="approve" property="approve"/>
        <result column="approve_user_id" property="approveUserId"/>
    </resultMap>

    <insert id="insertProveLog" parameterType="com.megvii.entity.opdb.contract.UserProveContractBacklog" >
        INSERT INTO `user_prove_contract_backlog` (
            `id`,
            `user_id`,
            `work_number`,
             `dhr_id`,
            `status`,
              xtype,
            `create_time`,
            `update_time`,
            `contract_id`,
            `approve`,
            `approve_user_id`
        )
        VALUES
            (
                0,

                #{userId},
                #{workNumber},
                #{dhrId},
                #{status},
                #{xtype},
                NOW(),
                NOW(),
                #{contractId},
                #{approve},
                #{approveUserId}
            );
    </insert>

    <update id="updateUserProveContractBacklog" parameterType="com.megvii.entity.opdb.contract.UserProveContractBacklog">
        update `user_prove_contract_backlog`
        <set>
            update_time = NOW(),
            <if test="status != null ">
                status = #{status}
            </if>
        </set>
        where dhr_id = #{dhrId}
    </update>

    <select id="selectProveLogByStatus" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        *
        from user_prove_contract_backlog
        where
            status = #{status}
    </select>

    <select id="selectProveLogByContractId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        *
        from user_prove_contract_backlog
        where
        contract_id = #{contractId}
    </select>

    <select id="selectProveLogByDhrId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        *
        from user_prove_contract_backlog
        where
        dhr_id = #{dhrId}
    </select>

</mapper>
