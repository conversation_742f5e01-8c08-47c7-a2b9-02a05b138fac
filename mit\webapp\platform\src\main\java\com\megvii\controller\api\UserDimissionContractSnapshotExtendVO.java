package com.megvii.controller.api;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Data
public class UserDimissionContractSnapshotExtendVO implements Serializable {

    private static final long serialVersionUID=1L;


    /**
     * 第几笔
     */
    private Integer num;

    /**
     * 生效日期
     */
    private String effectDate;

    /**
     * sers合计分数
     */
    private String sersNum;

    /**
     * 离职人员
     */
    private String userId;
    /**
     * 等待期
     */
    private String waitTime;

}
