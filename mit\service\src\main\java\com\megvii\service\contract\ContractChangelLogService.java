package com.megvii.service.contract;

import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.UserContractChangeLog;
import com.megvii.mapper.UserContractChangeLogMapper;
import java.util.List;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/22 17:28
 */
@Service
public class ContractChangelLogService {

	@Autowired
	private UserContractChangeLogMapper contractChangeLogMapper;

	/**
	 * 插入操作记录 取登录人信息  对XXX的XXX做了XXX
	 * @param userId
	 * @param contractNumber
	 * @param type
	 */
//	@Async
	public void insertChangeLog(String userId, String contractNumber, String type) {
		User user = (User) SecurityUtils.getSubject().getPrincipal();

		UserContractChangeLog log = new UserContractChangeLog();
		log.setUserId(userId);
		log.setContractNumber(contractNumber);
		log.setChangeType(type);
		log.setUpdatorUserId(user.getUserId());
		log.setUpdatorName(user.getSpell());

		contractChangeLogMapper.insertChangeLog(log);
	}

	/**
	 * 查询某个人的全部changelog
	 * @param userId
	 * @return
	 */
	public List<UserContractChangeLog> getLogsByUserId(String userId) {
		return contractChangeLogMapper.selectChangeLogByUserId(userId);
	}






}
