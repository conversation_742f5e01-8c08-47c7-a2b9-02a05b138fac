package com.megvii.common;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.FileHeader;

import java.util.ArrayList;
import java.util.List;

public class ZipUtil {
  /**
   * 删除zip里面指定目录下文件
   * @param file
   * @param removeDir
   * @throws Exception
   */
  public static void removeDirFromZipArchive(String file, String removeDir) throws Exception {
    // 创建ZipFile并设置编码
    ZipFile zipFile  = new ZipFile(file);
    removeDir += "/";
    // 遍历压缩文件中所有的FileHeader, 将指定删除目录下的子文件名保存起来
    @SuppressWarnings("unchecked")
    List<FileHeader> headersList = zipFile.getFileHeaders();
    ArrayList<String> removeHeaderNames = new ArrayList<String>();
    for (FileHeader subHeader : headersList) {
      String subHeaderName = subHeader.getFileName();
      if (subHeaderName.startsWith(removeDir)
          && !subHeaderName.equals(removeDir)) {
        removeHeaderNames.add(subHeaderName);
      }
    }
    // 遍历删除指定目录下的所有子文件(所有子文件删除完毕，该目录自动删除)
    for (String headerNameString : removeHeaderNames) {
      zipFile.removeFile(headerNameString);
    }
  }
}
