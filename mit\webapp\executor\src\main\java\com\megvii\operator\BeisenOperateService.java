package com.megvii.operator;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.BeisenRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.OperateResult;
import com.megvii.service.UserService;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * beisen操作类
 */
@Service
public class BeisenOperateService extends BaseOperatorService{
    private Logger logger= LoggerFactory.getLogger(BeisenOperateService.class);
    @Autowired
    private BeisenOperateService beisenOperateService;
    @Autowired
    private BeisenRestClient beisenRestClient;
    @Autowired
    UserService userService;



    /**主操作
     *  注意：主操作调用operateOnce(Map paraMap, Object... params)，需通过spring托管的对象来执行，否则导致无法被AOP拦截处理
     * @param paraMap job admin参数
     * @param params  自定义参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {

        Map<String,LocalDateTime> dateParams=getDateFromMap(paraMap);
        List<Integer> userList= beisenRestClient.getAllBeisenUserId(dateParams.get("startDate"),dateParams.get("endDate"));
        StringBuilder stringBuilder=new StringBuilder();
        if (userList.size()==0){
            stringBuilder.append("请求成功，但本次操作没从北森接口获取任何数据!");
        }
        for (Integer i:userList){
            logger.info("beiSent同步:"+i);
            if (userService.getUserByBeiSenId(i+"")==null){
                OperateResult operateResult=beisenOperateService.operateOnce(paraMap,i);
                stringBuilder.append(operateResult.getMsg()+"<br/>");
            }else {
                logger.info("beiSenId:"+i+"的人员已存在，不做处理");
                stringBuilder.append("beiSenId:"+i+"的人员已存在，不做处理<br/>");
            }
        }
        return new OperateResult(1,stringBuilder.toString());
    }



    /**一次操作
     * @param paraMap job admin参数
     * @param params  自定义参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        OperateResult result=new OperateResult();

        Map<String, JSONArray> userInfoMap=beisenRestClient.getUserInfo((int)params[0]);

        JSONObject data = (JSONObject) userInfoMap.get("offerInfo").get(0);
        if (data.getJSONArray("OfferCustomInfos").size() == 0) {
            result.setMsg(params[0] + "OfferCustomInfos不存在，跳过拉取");
            return result;
        }

        String msg=userService.saveUser(userInfoMap);
        result.setMsg(msg);
        return result;
    }



    /**
     * 对时间参数解析，如果时间参数为空，startDate取当前时间，endDate为当前时间减去一天
     * @param map
     * @return
     * @throws ParseException
     */
    private Map<String,LocalDateTime> getDateFromMap(Map map) throws Exception {
        Map dateMap=new HashMap();
        try{
            LocalDateTime startDate;
            LocalDateTime endDate;
            Object startDateStr=map.get("startDate");
            Object endDateStr=map.get("endDate");
            if (endDateStr!=null){
                endDate=DateTimeUtil.string2DateYMD((String) endDateStr);
            }else {
                endDate=LocalDateTime.now();
            }
            if (startDateStr!=null){
                startDate=DateTimeUtil.string2DateYMD((String) startDateStr);
            }else {
                startDate= DateTimeUtil.dateAddInt(endDate,-5);
            }
            dateMap.put("startDate",startDate);
            dateMap.put("endDate",endDate);
            return dateMap;
        }catch (Exception e){
            throw new Exception("日期参数解析错误" ,e);
        }
    }
}
