package com.megvii.aop;

import com.megvii.common.OperateResult;
import java.util.Map;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 对操作统一异常处理，统一报警处理
 */
@Component
@Aspect
@Order(2)
public class OperatorInterCeptor {
    Logger logger= LoggerFactory.getLogger(OperatorInterCeptor.class);

    @Pointcut("execution(* com.megvii.operator.*.operateMain(..))")
    public void mainGuardPoint(){}

    @Pointcut("execution(* com.megvii.operator.*.operateOnce(..))")
    public void onceOperateGuardPoint(){}



    /**
     * @param joinPoint 主操作切点
     * @return 操作结果
     */
    @Around("mainGuardPoint()")
    public OperateResult mainThrowGaurd(ProceedingJoinPoint joinPoint) throws Throwable{ ;
        int guardType=getGuardType((Map)joinPoint.getArgs()[0]);
        OperateResult result=null;
        try{
            result= (OperateResult)joinPoint.proceed();
            if(result.getStatus()==0 && guardType==1){
                //报警处理
                //
            }
            return result;
        }catch (Exception e){
            if (guardType==1){
                //报警处理
                //
            }
            logger.error("main operate error",e);
            if(result==null) {
                result=new OperateResult();
            }
            result.setStatus(2);
            result.setMsg(e.getMessage());
            return result;
        }
    }



    /**
     * @param joinPoint 单次操作切点
     * @return 操作结果
     */
    @Around("onceOperateGuardPoint()")
    public OperateResult onceOperateThrowGaurd(ProceedingJoinPoint joinPoint) throws Throwable{
        int guardType=getGuardType((Map)joinPoint.getArgs()[0]);
        OperateResult result=null;
        try{
            result= (OperateResult)joinPoint.proceed();
            return result;
        }catch (Exception e){
            if (guardType==2){
                //报警处理
                //
            }
            logger.error("once operate error",e);
            if(result==null) {
                result=new OperateResult();
            }
            result.setStatus(0);
            result.setMsg(e.getMessage());
            return result;
        }
    }


    /**
     * 从参数里获取报警类型
     * @param map
     * @return
     */
    private int getGuardType(Map map){
        int guardType=1;
        if(map!=null&&map.size()>0){
            try {
                guardType=Integer.parseInt((String) (map.get("guardType")==null?"1":map.get("guardType")));
                if(guardType!=0 ||guardType!=2)
                    guardType=1;
            }catch (Exception e){
                logger.error("参数错误",e);
            }
        }
        return guardType;
    }
}
