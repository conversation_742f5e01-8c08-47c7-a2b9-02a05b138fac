import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ContractComponent } from './contract.component'
import { ContractService } from './contract.service'
import { ContractRoutingModule } from './contract-routing.module'
import { UserInfoComponent } from './user-info/user-info.component'
import { SharedModule } from '../shared.module'
import { MgtComponent } from './mgt/mgt.component'
import { ExtendComponent } from './extend/extend.component'
import { EndorseComponent } from './endorse/endorse.component'
import { ChangeComponent } from './change/change.component'
import { NewSignComponent } from './new-sign/new-sign.component'
import { ResignComponent } from './resign/resign.component'
import { ContractTableComponent } from './component/contract-table/contract-table.component'
import { ResignTableComponent } from './resign/resign-table/resign-table.component'
import { ResignUserComponent } from './resign/resign-user/resign-user.component'

@NgModule({
  declarations: [
    ContractComponent,
    UserInfoComponent,
    MgtComponent,
    ExtendComponent,
    EndorseComponent,
    ChangeComponent,
    NewSignComponent,
    ResignComponent,
    ContractTableComponent,
    ResignTableComponent,
    ResignUserComponent
  ],
  imports: [CommonModule, ContractRoutingModule, SharedModule],
  providers: [ContractService]
})
export class ContractModule {}
