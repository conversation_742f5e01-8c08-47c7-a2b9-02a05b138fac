<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.TeamMapper">
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.Team">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="update_time" property="updateTime"/>
        <result column="mail_group" property="mailGroup"/>
        <result column="sf_code" property="sfCode"/>
        <result column="hrg" property="hrg"/>
        <result column="group_leader" property="groupLeader"/>
        <result column="dingding_id" property="dingdingId"/>
        <result column="intern_id" property="internId"/>
        <result column="chat_id" property="chatId"/>
        <result column="team_leader" property="teamLeader"/>
        <result column="project" property="project"/>
        <result column="waibao_dingding_id" property="waibaoDingDingId"/>
        <result column="parent_code" property="parentCode"/>
        <result column="short_name" property="shortName"/>
        <result column="full_code" property="fullCode"/>
    </resultMap>

    <select id="selectTeamById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select * from IT_team where id=#{id}
    </select>
    <select id="selectTeamByDingDingId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select * from IT_team where dingding_id=#{id}
    </select>
    <select id="selectTeamByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_team where sf_code=#{code}
    </select>
    <select id="selectTeamByName" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_team where name=#{name}
    </select>

    <insert id="addTeam" parameterType="com.megvii.entity.opdb.Team" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO IT_team ( id, NAME, update_time, mail_group, sf_code, hrg, group_leader, team_leader, parent_code, short_name, full_code )
        VALUES ( 0, #{name}, NOW( ), #{mailGroup}, #{sfCode}, #{hrg}, #{groupLeader}, #{teamLeader}, #{parentCode}, #{shortName}, #{fullCode} )
    </insert>

    <update id="updateTeamById" parameterType="com.megvii.entity.opdb.Team" >
        UPDATE IT_team
        <set>
            update_time = NOW(),
            <if test="name != null">
                name = #{name},
            </if>
            <if test="mailGroup != null">
                mail_group = #{mailGroup},
            </if>
            <if test="sfCode != null">
                sf_code = #{sfCode},
            </if>
            <if test="hrg != null">
                hrg = #{hrg},
            </if>
            <if test="groupLeader != null">
                group_leader = #{groupLeader},
            </if>
            <if test="dingdingId != null">
                dingding_id = #{dingdingId},
            </if>
            <if test="internId != null">
                intern_id = #{internId},
            </if>
            <if test="chatId != null">
                chat_id = #{chatId},
            </if>
            <if test="teamLeader != null">
                team_leader = #{teamLeader},
            </if>
            <if test="project != null">
                project = #{project},
            </if>
            <if test="waibaoDingDingId != null">
                waibao_dingding_id = #{waibaoDingDingId},
            </if>
            <if test="parentCode != null">
                parent_code = #{parentCode},
            </if>
            <if test="shortName != null">
                short_name = #{shortName},
            </if>
            <if test="fullCode != null">
                full_code = #{fullCode},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectAllTeam" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_team
    </select>

    <select id="selectActiviteTeam" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT * FROM IT_team WHERE `name` not LIKE ('old%')
    </select>

    <select id="selectTeamsByName" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_team where name LIKE CONCAT ('%', #{name}, '%') AND `name` not LIKE ('old%')
    </select>


</mapper>