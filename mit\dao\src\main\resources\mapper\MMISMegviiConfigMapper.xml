<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.MMISMegviiConfigMapper">
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.MMISMegviiConfig">
        <id column="id" property="id"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
    </resultMap>

    <select id="selectMMISMegviiConfigByKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from MMIS_MegviiConfig where `key`=#{key}
    </select>

    <insert id="addMMISMegviiConfig" parameterType="com.megvii.entity.opdb.MMISMegviiConfig" >
        INSERT INTO MMIS_MegviiConfig
        VALUES (0, #{key}, #{value}, #{code}, #{description} )
    </insert>

    <update id="updateMMISMegviiConfigByKey" parameterType="com.megvii.entity.opdb.MMISMegviiConfig">
        update MMIS_MegviiConfig set value = #{value} where `key` = #{key}
    </update>

</mapper>