package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.entity.opdb.ImageFile;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import javax.mail.internet.ContentType;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.eclipse.jetty.http.HttpHeader;
import org.eclipse.jetty.http.HttpMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import java.io.ByteArrayInputStream;
import org.springframework.core.io.InputStreamResource;

/**
 * <AUTHOR>
 * @date 2021/6/7 17:52
 */
@Component
@PropertySource(value = "classpath:pangu.${spring.profiles.active}.properties",encoding = "UTF-8")
public class PanguRestClient {

	Logger logger= LoggerFactory.getLogger(PanguRestClient.class);

	@Value("#{${PANGU_APPKEY}}")
    private Map<String,String> appkeyMap = new HashMap<>();

	@Value("${PANGU_HOST}")
    private String host;

	@Value("${PANGU_SEARCH_DEVICE_URL}")
    private String searchDeviceUrl;

	@Value("${PANGU_UPLOADIMAGE_URL}")
    private String uploadImageUrl;

	@Value("${PANGU_ADDUSER_URL}")
    private String addUserUrl;

	@Value("${PANGU_DELETEUSER_URL}")
    private String deleteUserUrl;

	@Autowired
	private RestTemplate restTemplate;

	/**
	 * 删除pangu人员 不删除底库
	 * @param data
	 * @param userId
	 * @param appkey
	 * @param nonce
	 * @return
	 * @throws Exception
	 */
	public boolean deleteUser(JSONObject data, String userId, String appkey, String nonce) throws Exception {
		String timeStamp = String.valueOf(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
		String sign = getSign(deleteUserUrl, HttpMethod.POST.asString(), null, data.toJSONString(), appkey, timeStamp, nonce);

		String url = host + deleteUserUrl;
		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString() ,getHeaders(MediaType.APPLICATION_JSON, timeStamp, nonce, appkey, sign));
		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url ,requestEntity ,JSONObject.class);

		if (responseEntity.getStatusCode().equals(HttpStatus.OK) && "0".equals(responseEntity.getBody().getString("code"))) {
			return true;
		}else {
			throw new Exception(userId + "删除盘古失败");
		}
	}



	/**
	 * 绑定盘古人员和底库
	 * @param data
	 * @param userId
	 * @param appkey
	 * @param nonce
	 * @return
	 */
	public boolean addUser(JSONObject data, String userId, String appkey, String nonce) throws Exception {
		String timeStamp = String.valueOf(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
		String sign = getSign(addUserUrl, HttpMethod.POST.asString(), null, data.toJSONString(), appkey, timeStamp, nonce);

		String url = host + addUserUrl;
		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString() ,getHeaders(MediaType.APPLICATION_JSON, timeStamp, nonce, appkey, sign));
		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url ,requestEntity ,JSONObject.class);

		if (responseEntity.getStatusCode().equals(HttpStatus.OK) && "0".equals(responseEntity.getBody().getString("code"))) {
			return true;
		} else {
			throw new Exception(userId + "绑定盘古底库失败");
		}
	}


	/**
	 * 上传照片到底库，返回照片uri
	 * @param imageFile
	 * @param userId
	 * @param appkey
	 * @param nonce
	 * @return
	 * @throws Exception
	 */
	public String uploadImage(ImageFile imageFile, String userId, String appkey, String nonce) {
		String timeStamp = String.valueOf(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
		String sign = getSign(uploadImageUrl, HttpMethod.POST.asString(), null, null, appkey, timeStamp, nonce);

		MultiValueMap<String, Object> form = new LinkedMultiValueMap();
        InputStreamResource fileResource=new InputStreamResource(new ByteArrayInputStream(imageFile.getFileBytes())){
            @Override
            public long contentLength(){
                return imageFile.getFileBytes().length;
            }
            @Override
            public String getFilename(){
                return imageFile.getFileName();
            }
        };
        form.add("file", fileResource);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(form, getHeaders(MediaType.MULTIPART_FORM_DATA, timeStamp, nonce, appkey, sign));

        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(host + uploadImageUrl, requestEntity, JSONObject.class);
        JSONObject data = responseEntity.getBody();

        if ("0".equals(data.getString("code"))) {
        	return data.getJSONObject("data").getString("uri");
		} else {
        	logger.error(userId + "上传盘古底库错误:", StringEscapeUtils.unescapeJava(data.getString("msg")));
//        	盘古可以不需要uri创建人员 所以不抛异常
//            throw new Exception(userId + "上传盘古底库错误");
			return null;
		}
	}

	/**
	 * 获取机房列表
	 * 用来测试签名算法
	 * @param nonce
	 * @param appkey
	 * @return
	 * @throws Exception
	 */
	public JSONObject searchDeviceList(String nonce, String appkey) throws Exception {
		JSONObject data = new JSONObject();
		data.put("deviceType", 3);

		String timeStamp = String.valueOf(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
		String sign = getSign(searchDeviceUrl, HttpMethod.POST.asString(), null, data.toString(), appkey, timeStamp, nonce);
		String url = host + searchDeviceUrl;
		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString() ,getHeaders(MediaType.APPLICATION_JSON, timeStamp, nonce, appkey, sign));
		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url ,requestEntity ,JSONObject.class);

		if (responseEntity.getBody().getString("code").equals("0")) {
			return responseEntity.getBody().getJSONObject("data");
		}

		throw new Exception("XXXX");
	}


	/**
	 * 获取header
	 * @param timestamp
	 * @param nonce
	 * @param appkey
	 * @param sign
	 * @return
	 */
	private HttpHeaders getHeaders(MediaType mediaType, String timestamp, String nonce, String appkey, String sign) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(mediaType);
		headers.add("ctimestamp", String.valueOf(timestamp));
		headers.add("cnonce", nonce);
		headers.add("cappkey", appkey);
		headers.add("csign", sign);

		return headers;
	}


	/**
	 * 签名计算方式
	 * 签名算法
	 * MD5，32位小写
	 * 通过以下几部分组合计算：
	 *
	 * A：请求URI，以/开头，如/v1/api/device/list
	 * B：request的请求method，大写，如GET、POST、PUT、DELETE
	 * C：请求参数按照参数key正序排序后连接的字符串：如请求参数中有akey=1,ckey=2,bkey=3,则连接的参数字符串为：akey=1&bkey=3&ckey=2(注意：参数值采用url编码前的原值)，目前，我们大部分接口都是是有body传参，所以这里是""
	 * D：请求的requestbody中的内容的MD5(32位小写，下同)，如无内容，则为""【注，对于文件上传类url，该字段用空字符串替代】
	 * E： cappkey对应的秘钥，如：appkey1
	 * F：header中的ctimestamp、cnonce、cappkey
	 * sign计算公式：
	 * csign = MD5(A-B-C-D-E-ctimestamp-cnonce-cappkey)
	 */
	private String getSign(String uri, String method, String param, String body, String appkey, String timestamp, String nonce ) {
		String a = uri;
		String b = method;
		String c = param!=null?param:"";
		String d = body!=null?DigestUtils.md5Hex(body).toLowerCase():"";
		String e = appkeyMap.get(appkey);
		String f = timestamp + "-" + nonce + "-" + appkey;

		return DigestUtils.md5Hex(a + "-" + b + "-" + c + "-" + d + "-" + e + "-" + f);
	}



}
