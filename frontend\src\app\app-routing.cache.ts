import {
  RouteReuseStrategy,
  DefaultUrlSerializer,
  ActivatedRouteSnapshot,
  DetachedRouteHandle
} from '@angular/router'

export class SimpleReuseStrategy implements RouteReuseStrategy {
  public static handlers: { [key: string]: DetachedRouteHandle } = {}

  /** 表示对所有路由允许复用 如果你有路由不想利用可以在这加一些业务逻辑判断 */
  public shouldDetach(route: ActivatedRouteSnapshot): boolean {
    return !!route.data.keep
  }

  /** 删除缓存路由快照的方法 */
  public static deleteRouteSnapshot(path: string): void {
    const name = path.replace(/\//g, '_')
    if (SimpleReuseStrategy.handlers[name]) {
      delete SimpleReuseStrategy.handlers[name]
    }
  }

  /** 当路由离开时会触发。按path作为key存储路由快照&组件当前实例对象 */
  public store(route: ActivatedRouteSnapshot, handle: DetachedRouteHandle): void {
    SimpleReuseStrategy.handlers[this.getRouteUrl(route)] = handle
  }

  /** 若 path 在缓存中有的都认为允许还原路由 */
  public shouldAttach(route: ActivatedRouteSnapshot): boolean {
    console.log('SimpleReuseStrategy.handlers', SimpleReuseStrategy.handlers)
    return !!SimpleReuseStrategy.handlers[this.getRouteUrl(route)]
  }

  /** 从缓存中获取快照，若无则返回nul */
  public retrieve_bak(route: ActivatedRouteSnapshot): DetachedRouteHandle {
    if (!route.routeConfig) {
      return null
    }
    return SimpleReuseStrategy.handlers[route.routeConfig.path]
  }

  public retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle {
    return SimpleReuseStrategy.handlers[this.getRouteUrl(route)]
  }

  private getRouteUrl(route: ActivatedRouteSnapshot) {
    return route['_routerState'].url.replace(/\//g, '_')
  }

  /** 进入路由触发，判断是否同一路由 */
  public shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {
    return this.getRouteUrl(future) === this.getRouteUrl(curr)
  }
}
