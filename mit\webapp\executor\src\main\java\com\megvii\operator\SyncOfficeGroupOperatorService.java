package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.SyncOfficeService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/3/5 14:34
 */
@Service
public class SyncOfficeGroupOperatorService extends BaseOperatorService {

    @Autowired
    private SyncOfficeGroupOperatorService syncOfficeGroupOperatorService;

    @Autowired
    private SyncOfficeService syncOfficeService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return syncOfficeGroupOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = syncOfficeService.syncAllOfficeGroup();
        return new OperateResult(1,msg);
    }

}
