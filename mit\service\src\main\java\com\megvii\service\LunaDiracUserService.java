package com.megvii.service;

import com.megvii.entity.luna.LunaDiracUser;
import com.megvii.lunaMapper.LunaDiracUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/9/16 18:51
 */
@Service
public class LunaDiracUserService {

    @Autowired
    private LunaDiracUserMapper lunaDiracUserMapper;

    public LunaDiracUser getLunaDiracUserByUsername(String username) {
        return lunaDiracUserMapper.selectUserByUsername(username);
    }

    public void updateLunaDiracUser(LunaDiracUser lunaDiracUser) {
        lunaDiracUserMapper.updateLunaDiracUser(lunaDiracUser);
    }
}
