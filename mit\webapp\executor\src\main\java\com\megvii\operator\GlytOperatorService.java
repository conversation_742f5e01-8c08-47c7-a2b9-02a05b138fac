package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.service.GlytService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/1/17 15:09
 */
@Service
public class GlytOperatorService extends BaseOperatorService {

    @Autowired
    private GlytOperatorService glytOperatorService;

    @Autowired
    private GlytService glytService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        Object diff = paraMap.get("diff");
        return glytOperatorService.operateOnce(paraMap, diff);

    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        if (params[0] != null) {
            Integer diff = Integer.valueOf((String) params[0]);
            return new OperateResult(1, glytService.updateGlytUsers(diff));
        } else {
             return new OperateResult(1, glytService.updateGlytUsers(null));
        }

    }


}
