## MIT

## 定时任务

executor 

wiki: https://wiki.megvii-inc.com/pages/viewpage.action?pageId=81716650 

## 功能说明

### ZK部门钉钉操作限制功能

#### 功能描述
系统会自动识别ZK部门，并对ZK部门跳过所有钉钉相关操作，包括创建钉钉部门、更新钉钉部门信息、修改上级部门、补充群聊等操作。ZK部门只会更新MIT系统内的数据库信息。

#### 判断逻辑
通过 `TeamService.isZKTeam(String code)` 方法判断部门是否为ZK部门：
- 根据部门code查询部门信息
- 检查部门的全路径代码(`fullCode`)是否包含特定的ZK团队标识码
- 如果包含则认为是ZK部门，跳过所有钉钉操作

#### 涉及功能模块
1. **部门同步** (`SyncZTDataService.compareTeamInfo()`)：
   - 新增部门时跳过钉钉部门创建
   - 上级部门变更时跳过钉钉操作
   - 部门改名时跳过钉钉部门名称更新
   - 更换HRG和TeamLeader时跳过钉钉人员操作
   - 补充群聊ID时跳过群聊创建

2. **部门创建** (`SyncZTDataService.addTeam()`)：
   - 跳过钉钉部门和群聊创建操作

3. **钉钉部门操作** (`SyncZTDataService.addDingdingDept()`)：
   - 在方法入口处直接返回，避免钉钉部门创建

4. **上级部门更新** (`SyncZTDataService.updateParentTeam()`)：
   - 跳过钉钉上级部门修改操作

#### 操作结果
- ZK部门的所有变更只会更新MIT系统内的数据库信息
- 日志中会显示"ZK部门跳过xxx操作"的提示信息
- 确保ZK部门与钉钉系统完全隔离，避免意外的钉钉操作

#### 注意事项
1. ZK部门的判断依赖于部门的全路径代码配置
2. 需要确保ZK团队标识码在配置文件中正确设置
3. ZK部门仍会正常进行MIT系统内的数据同步和更新
4. 此功能不影响非ZK部门的正常钉钉操作

### 账号密码通知功能

#### 功能描述
在发送账号密码短信时，系统会同时向员工的私人邮箱发送相同内容的邮件通知，以提高通知的可靠性。

#### 架构设计
- **服务分离**: 邮件发送逻辑封装在 `MailSendService.sendPasswordNotificationMail()` 方法中
- **职责明确**: `RemindService.sendPwdSms()` 负责协调邮件和短信发送流程
- **异步处理**: 邮件发送采用异步方式，不阻塞短信发送

#### 使用方法
- 调用 `RemindService.sendPwdSms(User user)` 方法
- 系统会先调用 `MailSendService.sendPasswordNotificationMail(User user)` 发送邮件
- 然后发送短信通知
- 即使邮件发送失败，短信发送仍会正常进行

#### 核心方法说明

##### MailSendService.sendPasswordNotificationMail(User user)
- **参数**: User对象，需包含userId、oriPassword、priEmail字段
- **返回值**: 字符串，描述邮件发送结果
- **功能**: 向员工私人邮箱发送账号密码通知邮件
- **容错**: 私人邮箱为空时跳过发送，异常时返回失败信息

##### RemindService.sendPwdSms(User user)
- **参数**: User对象
- **返回值**: 字符串，包含邮件和短信发送状态
- **功能**: 协调账号密码的邮件和短信通知发送

#### 邮件参数说明
- **收件人**: 员工的私人邮箱地址（User.priEmail字段）
- **邮件标题**: "公司单点登录系统账号密码同步"
- **邮件内容**: 与短信内容相同，包含用户名和初始密码信息
- **发送时机**: 在短信发送前执行
- **容错机制**: 邮件发送失败不会影响短信的正常发送

#### 返回值说明
方法返回值会包含邮件和短信的发送状态信息：
- 成功示例："{userId}私人邮箱通知发送成功；{userId}短信发送成功"
- 失败示例："{userId}私人邮箱通知发送失败；{userId}短信发送成功"
- 跳过邮件："{userId}私人邮箱地址为空，跳过邮件发送；{userId}短信发送成功"

#### 注意事项
1. 邮件发送不区分公司主体，所有员工都会收到相同格式的邮件
2. 如果员工的私人邮箱地址为空，将跳过邮件发送
3. 邮件发送采用异步处理，不会阻塞短信发送流程
4. 建议在调用前确保员工的私人邮箱地址已正确填写
5. 邮件发送逻辑独立封装，便于维护和测试

### 入职相关信息通知功能

#### 功能描述
在发送入职欢迎短信时，系统会同时向员工的私人邮箱发送相同内容的邮件通知，包含入职时间、地点等重要信息，确保员工能够及时收到入职通知。

#### 架构设计
- **服务分离**: 邮件发送逻辑封装在 `MailSendService.sendWelcomeNotificationMail()` 方法中
- **内容生成**: `MailSendService.generateWelcomeEmailContent()` 根据用户信息生成邮件内容
- **职责明确**: `RemindService.sendWelcomeSms()` 负责协调邮件和短信发送流程
- **异步处理**: 邮件发送采用异步方式，不阻塞短信发送

#### 使用方法
- 调用 `RemindService.sendWelcomeSms(User user)` 方法
- 系统会先调用 `MailSendService.sendWelcomeNotificationMail(User user)` 发送邮件
- 然后根据用户的 `createTag` 发送相应的短信通知
- 即使邮件发送失败，短信发送仍会正常进行

#### 核心方法说明

##### MailSendService.sendWelcomeNotificationMail(User user)
- **参数**: User对象，需包含workBase、createTag、priEmail字段
- **返回值**: 字符串，描述邮件发送结果
- **功能**: 向员工私人邮箱发送入职相关信息通知邮件
- **容错**: 私人邮箱为空时跳过发送，不发送欢迎信息的情况返回相应状态

##### MailSendService.generateWelcomeEmailContent(User user)
- **参数**: User对象
- **返回值**: 字符串，邮件内容；如果不发送则返回null
- **功能**: 根据用户的createTag和workBase生成相应的邮件内容
- **逻辑**: 与短信发送逻辑完全一致，确保内容同步

##### RemindService.sendWelcomeSms(User user)
- **参数**: User对象
- **返回值**: 字符串，包含邮件和短信发送状态
- **功能**: 协调入职欢迎信息的邮件和短信通知发送

#### 邮件参数说明
- **收件人**: 员工的私人邮箱地址（User.priEmail字段）
- **邮件标题**: "入职相关信息通知"
- **邮件内容**: 与短信内容相同，包含入职时间、地点等信息
- **发送时机**: 在短信发送前执行
- **容错机制**: 邮件发送失败不会影响短信的正常发送

#### 内容规则说明
根据用户的 `createTag` 和 `workBase` 字段生成不同的邮件内容：

1. **createTag为5、8、7时**（使用McSMS模板）：
   - 融科：融科资讯中心A座3层北区前台
   - 海龙：海龙大厦H座17层前台
   - 金隅：金隅智造S1-1层前台

2. **createTag为6时**（使用CqSMS模板）：
   - 与上述地址相同

3. **其他createTag**（使用标准短信模板）：
   - 大恒：苏州街3号大恒科技大厦南座16层前台
   - 海龙：海龙大厦H座17层前台
   - 金隅：金隅智造S1-1层前台（含详细地址）

#### 返回值说明
方法返回值会包含邮件和短信的发送状态信息：
- 成功示例："{userId}私人邮箱通知发送成功；{userId}发送成功"
- 失败示例："{userId}私人邮箱通知发送失败；{userId}发送成功"
- 跳过邮件："{userId}私人邮箱地址为空，跳过邮件发送；{userId}发送成功"
- 不发送："{userId}不发送欢迎信息；{userId}不发送欢迎信息"

#### 注意事项
1. 邮件内容根据用户的createTag和workBase动态生成，与短信内容完全一致
2. 如果员工的私人邮箱地址为空，将跳过邮件发送
3. 如果工作地点不在支持范围内，将不发送邮件和短信
4. 邮件发送采用异步处理，不会阻塞短信发送流程
5. 建议在调用前确保员工的工作地点和私人邮箱地址已正确填写
6. 邮件发送逻辑独立封装，便于维护和扩展新的工作地点

### 入职信息填报未完成提醒功能

#### 功能描述
在发送入职信息填报提醒短信时，系统会同时向员工的私人邮箱发送相同内容的邮件通知，包含登录信息和填报链接，确保员工能够及时完成入职信息填报。

#### 架构设计
- **服务分离**: 邮件发送逻辑封装在 `MailSendService.sendUserInfoNotificationMail()` 方法中
- **内容生成**: `MailSendService.generateUserInfoEmailContent()` 根据用户信息生成邮件内容
- **职责明确**: `RemindService.sendUserInfoSmsRemind()` 负责协调邮件和短信发送流程
- **数据查询**: 需要先查询entry数据库获取密码信息
- **异步处理**: 邮件发送采用异步方式，不阻塞短信发送

#### 使用方法
- 调用 `RemindService.sendUserInfoSmsRemind(User user)` 方法
- 系统会先查询entry数据库获取用户信息和密码
- 然后调用 `MailSendService.sendUserInfoNotificationMail(User user, String password)` 发送邮件
- 最后根据用户的 `createTag` 发送相应的短信通知
- 即使邮件发送失败，短信发送仍会正常进行

#### 核心方法说明

##### MailSendService.sendUserInfoNotificationMail(User user, String password)
- **参数**: User对象（需包含spell、userId、priEmail字段）和密码
- **返回值**: 字符串，描述邮件发送结果
- **功能**: 向员工私人邮箱发送入职信息填报未完成提醒邮件
- **容错**: 私人邮箱为空时跳过发送，异常时返回失败信息

##### MailSendService.generateUserInfoEmailContent(User user, String password)
- **参数**: User对象和密码
- **返回值**: 字符串，邮件内容
- **功能**: 根据用户的createTag生成相应的邮件内容
- **逻辑**: 与短信发送逻辑完全一致，确保内容同步

##### RemindService.sendUserInfoSmsRemind(User user)
- **参数**: User对象
- **返回值**: 字符串，包含邮件和短信发送状态
- **功能**: 协调入职信息填报提醒的邮件和短信通知发送
- **数据处理**: 先查询entry数据库获取密码和状态信息

#### 邮件参数说明
- **收件人**: 员工的私人邮箱地址（User.priEmail字段）
- **邮件标题**: "入职信息填报未完成提醒"
- **邮件内容**: 与短信内容相同，包含姓名、登录信息和填报链接
- **发送时机**: 在短信发送前执行
- **容错机制**: 邮件发送失败不会影响短信的正常发送

#### 内容规则说明
根据用户的 `createTag` 生成不同的邮件内容：

1. **createTag为5、8、7时**（使用McSMS模板）：
   - 简化版提醒，使用"欢迎加入公司"的表述

2. **createTag为6时**（使用CqSMS模板）：
   - 与上述内容相同

3. **其他createTag**（使用标准短信模板）：
   - 完整版提醒，使用"欢迎加入旷视大家庭"的表述

#### 业务逻辑说明
方法会依次进行以下检查和处理：
1. 检查用户离职日期
2. 查询entry数据库获取EntryUserInfo
3. 检查信息收集状态（已提交未拉取则不发送）
4. 发送邮件通知
5. 根据createTag发送对应的短信

#### 返回值说明
方法返回值会包含完整的处理状态信息：
- 成功示例："{userId}私人邮箱通知发送成功；{userId}发送成功"
- 失败示例："{userId}私人邮箱通知发送失败；{userId}发送成功"
- 跳过邮件："{userId}私人邮箱地址为空，跳过邮件发送；{userId}发送成功"
- 异常情况："{userId}查询entry数据库异常" 或 "{userId}未查到entry信息"
- 已完成："{userId}已提交未拉取"

#### 注意事项
1. 邮件内容根据用户的createTag动态生成，与短信内容完全一致
2. 需要先查询entry数据库获取密码信息，如果查询失败则不发送通知
3. 如果用户已提交信息但未拉取，系统将不发送提醒
4. 如果员工的私人邮箱地址为空，将跳过邮件发送
5. 邮件发送采用异步处理，不会阻塞短信发送流程
6. 建议在调用前确保用户已完成基本信息录入和私人邮箱配置
7. 邮件发送逻辑独立封装，便于维护和测试 