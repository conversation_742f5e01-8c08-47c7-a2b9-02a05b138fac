package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/17 18:48
 */
@Service
public class DhrDimissionFlowOperatorService extends BaseOperatorService {

    @Autowired
    private DhrDimissionFlowOperatorService dhrDimissionFlowOperatorService;

    @Autowired
    private DhrService dhrService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return dhrDimissionFlowOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = dhrService.getDimissionArray();
        return new OperateResult(1,msg);
    }



}
