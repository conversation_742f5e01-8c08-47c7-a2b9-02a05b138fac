package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DingDingRestClient;
import com.megvii.client.SfRestClient;
import com.megvii.entity.opdb.Team;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/8/30 11:47
 */
@PropertySource(value = "classpath:mail.properties",encoding = "UTF-8")
@PropertySource(value = "classpath:dingding.${spring.profiles.active}.properties",encoding = "UTF-8")
@Service
public class SyncSfTeamService {

//    Logger logger= LoggerFactory.getLogger(SyncSfTeamService.class);
//
//    private static JSONArray sfGroup;
//    private static JSONArray sfBu;
//    private static JSONArray sfTeam;
//    private static JSONArray sfSubTeam;
//    private static JSONArray sfSeciton;
//
//    @Value("${SYNC_SF_TEAM_MAIL}")
//    private String mailAddress;
//
//
//    @Autowired
//    private SfRestClient sfRestClient;
//
//    @Autowired
//    private DingDingRestClient dingDingRestClient;
//
//    @Autowired
//    private TeamService teamService;
//
//    @Autowired
//    private LdapService ldapService;
//
//    @Autowired
//    private MailSendService mailSendService;
//
//    @Autowired
//    private DingdingService dingdingService;
//
//    @Autowired
//    private SyncTeamService syncTeamService;
//
//
//    /**
//     * 禁用部门主方法
//     * @return
//     */
//    public String syncDisableSfTeam() {
//        init();
//
//        StringBuilder message = new StringBuilder();
//        //获取激活的MIT部门
//        List<Team> teams = teamService.getActiviteTeam();
//        List<String> teamsCode = new ArrayList<>();
//        for (Team team : teams) {
//            if (team.getSfCode() == null) {
//                logger.error(team.getName() + ":sfCode is null");
//                continue;
//            }
//            teamsCode.add(team.getSfCode());
//        }
//        //获取激活的SF部门code
//        List<String> codes = getActivityTeamCode();
//
//        //从MIT激活部门中移除激活的SF部门
//        teamsCode.removeAll(codes);
//
//        if (teamsCode.size() == 0) {
//            message.append("没有要禁用的MIT部门");
//            return message.toString();
//        }
//
//        for (String code : teamsCode) {
//            message.append(syncTeamService.disableTeam(code) + "<br/>");
//        }
//
//        mailSendService.sendMail( "MIT禁用部门提醒", message.toString(), mailAddress);
//
//        return message.toString();
//    }
//
//
//
//    /**
//     * 获取全部激活的SF部门code
//     * @return
//     */
//    private List<String> getActivityTeamCode() {
//        List<String> codes = new ArrayList<>();
//
//        for (int i = 0; i < sfSeciton.size(); i ++) {
//            JSONObject data = (JSONObject) sfSeciton.get(i);
//            //获取激活的SF_GROUP
//            if (data.get("mdfSystemStatus").equals("A")) {
//                codes.add(data.get("externalCode")!=null?data.get("externalCode").toString():null);
//            }
//        }
//
//        for (int i = 0; i < sfSubTeam.size(); i ++) {
//            JSONObject data = (JSONObject) sfSubTeam.get(i);
//            //获取激活的SF_GROUP
//            if (data.get("status").equals("A")) {
//                codes.add(data.get("externalCode")!=null?data.get("externalCode").toString():null);
//            }
//        }
//
//        for (int i = 0; i < sfTeam.size(); i ++) {
//            JSONObject data = (JSONObject) sfTeam.get(i);
//            //获取激活的SF_GROUP
//            if (data.get("status").equals("A")) {
//                codes.add(data.get("externalCode")!=null?data.get("externalCode").toString():null);
//            }
//        }
//
//        for (int i = 0; i < sfBu.size(); i ++) {
//            JSONObject data = (JSONObject) sfBu.get(i);
//            //获取激活的SF_GROUP
//            if (data.get("mdfSystemStatus").equals("A")) {
//                codes.add(data.get("externalCode")!=null?data.get("externalCode").toString():null);
//            }
//        }
//
//        for (int i = 0; i < sfGroup.size(); i ++) {
//            JSONObject data = (JSONObject) sfGroup.get(i);
//            //获取激活的SF_GROUP
//            if (data.get("status").equals("A")) {
//                codes.add(data.get("externalCode")!=null?data.get("externalCode").toString():null);
//            }
//        }
//
//        return codes;
//    }
//
//
//    /**
//     * 新增SF部门
//     * @return
//     */
//    public String syncSfTeam() {
//        init();
//        StringBuilder message = new StringBuilder();
//
//        String groupMsg = syncGroup();
//        if (groupMsg != null && !groupMsg.equals("")) {
//            message.append(groupMsg + "<br/>");
//        }
//
//        String buMsg = syncBu();
//        if (buMsg != null && !buMsg.equals("")) {
//            message.append(buMsg + "<br/>");
//        }
//
//        String teamMsg = syncTeam();
//        if (teamMsg != null && !teamMsg.equals("")) {
//            message.append(teamMsg + "<br/>");
//        }
//
//        String subTeamMsg = syncSubTeam();
//        if (subTeamMsg != null && !subTeamMsg.equals("")) {
//            message.append(subTeamMsg + "<br/>");
//        }
//
//        String sectionMsg = syncSection();
//        if (sectionMsg != null && !sectionMsg.equals("")) {
//            message.append(sectionMsg + "<br/>");
//        }
//
//        if (!message.toString().equals("")) {
//            mailSendService.sendMail( "MIT新增部门", message.toString(), mailAddress);
//        } else {
//            message.append("没有部门变化");
//        }
//
//        return message.toString();
//    }
//
//
//    /**
//     * 同步group主方法
//     * @return
//     */
//    public String syncGroup() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < sfGroup.size(); i ++) {
//            JSONObject data = (JSONObject) sfGroup.get(i);
//            //处理激活的SF_GROUP
//            if (data.get("status").equals("A")) {
//                String result = checkAddGroup(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步BU主方法
//     * @return
//     */
//    public String syncBu() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < sfBu.size(); i ++) {
//            JSONObject data = (JSONObject) sfBu.get(i);
//            //处理激活的SF_BU
//            if (data.get("mdfSystemStatus").equals("A")) {
//                String result = checkAddBu(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步TEAM主方法
//     * @return
//     */
//    public String syncTeam() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < sfTeam.size(); i ++) {
//            JSONObject data = (JSONObject) sfTeam.get(i);
//            //处理激活的SF_team
//            if (data.get("status").equals("A")) {
//                String result = checkAddTeam(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步subTeam主方法
//     * @return
//     */
//    public String syncSubTeam() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < sfSubTeam.size(); i ++) {
//            JSONObject data = (JSONObject) sfSubTeam.get(i);
//            //处理激活的SF_subTeam
//            if (data.get("status").equals("A")) {
//                String result = checkAddSubTeam(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步section主方法
//     * @return
//     */
//    public String syncSection() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < sfSeciton.size(); i ++) {
//            JSONObject data = (JSONObject) sfSeciton.get(i);
//            //处理激活的SF_Section
//            if (data.get("mdfSystemStatus").equals("A")) {
//                String result = checkAddSection(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 处理激活状态的group
//     * 1.不存在->添加->返回处理结果
//     * 2.存在，钉钉ID不存在->false
//     * 3.其他->名称是否修改->返回处理结果
//     * @param data
//     * @return
//     */
//    public String checkAddGroup(JSONObject data) {
//        StringBuilder msg = new StringBuilder();
//
//        String groupName = data.get("name_localized")!=null?data.get("name_localized").toString():null;
//        String sfCode = data.get("externalCode")!=null?data.get("externalCode").toString():null;
//        if (groupName == null) {
//            msg.append("group:" + sfCode + "name为null");
//            logger.error(msg.toString());
//            return msg.toString();
//        }
//
//        //查询group是否存在
//        Team group = teamService.getTeamByCode(sfCode);
//        if (group == null) {
//            group = new Team();
//            group.setName(groupName);
//            group.setMailGroup("");
//            group.setSfCode(sfCode);
//            //创建MIT部门
//            if (teamService.addTeam(group)) {
//                //创建钉钉部门
//                if(dingdingService.createDingDingDept(group, "1")) {
//                    msg.append("MIT新增SF部门" + groupName + "[成功]");
//                    return msg.toString();
//                } else {
//                    msg.append("MIT新增SF部门" + groupName + "[失败]");
//                    return msg.toString();
//                }
//            } else {
//                msg.append("添加MIT部门" + groupName + "错误");
//                logger.error(msg.toString());
//                return msg.toString();
//            }
//        }  else if (group.getDingdingId() == null) {
//            dingdingService.createDingDingDept(group, "1");
//            msg.append(group.getName() + "更新钉钉ID");
//            return msg.toString();
//        } else {
//            if (group.getName().equals("旷视集团")) {
//                return null;
//            }
//            //没有群聊ID
//            if (group.getDingdingId() != null && group.getChatId() == null) {
//                try {
//                    dingdingService.saveChatId(group);
//                } catch (Exception e) {
//                    msg.append(group.getName() + "保存添加群聊错误");
//                    logger.error(msg.toString());
//                    return msg.toString();
//                }
//            }
//            //不需要修改
//            if (group.getName().equals(groupName)) {
//                return null;
//            }
//            //group名称变化 邮件组不是all
//            if (!group.getName().equals(groupName) && !group.getMailGroup().equals("all")) {
//                //修改ldap
//                ldapService.modifyGroup(group.getMailGroup(), "displayname", groupName);
//                ldapService.modifyGroup(group.getMailGroup(), "description", groupName);
//            }
//            msg.append("MIT部门修改名称:" + group.getName() + "->" + groupName);
//            logger.info(msg.toString());
//            group.setName(groupName);
//
//            try {
//                if (dingDingRestClient.updateDept(group, null)) {
//                    msg.append("----修改钉钉部门名称[成功]");
//                } else {
//                    msg.append("----修改钉钉部门名称[失败]");
//                }
//            } catch (Exception e) {
//                msg.append(groupName + "修改钉钉部门名称错误");
//                logger.error(msg.toString());
//            }
//            teamService.updateTeam(group);
//            return msg.toString();
//        }
//    }
//
//    /**
//     * 处理激活状态的BU
//     * @param data
//     * @return
//     */
//    public String checkAddBu(JSONObject data) {
//        String buName = data.get("externalName")!=null?data.get("externalName").toString():null;
//        String sfCode = data.get("externalCode")!=null?data.get("externalCode").toString():null;
//        String belongSfCode = data.get("cust_GroupForOA")!=null?data.get("cust_GroupForOA").toString():null;
//
//        if (buName == null) {
//            logger.error("team:" + sfCode + "name is null");
//            return "team:" + sfCode + "name is null";
//        }
//        if (belongSfCode == null) {
//            logger.error("team:" + sfCode + "上级部门sfCode is null");
//            return "team:" + sfCode + "上级部门sfCode is null";
//        }
//        return checkSfTeam(buName, sfCode, belongSfCode);
//    }
//
//    /**
//     * 处理激活状态的team
//     * @param data
//     * @return
//     */
//    public String checkAddTeam(JSONObject data) {
//        String teamName = data.get("name_localized")!=null?data.get("name_localized").toString():null;
//        String sfCode = data.get("externalCode")!=null?data.get("externalCode").toString():null;
//        String belongSfCode = data.get("cust_BUForOA")!=null?data.get("cust_BUForOA").toString():null;
//
//        if (teamName == null) {
//            logger.error("team:" + sfCode + "name is null");
//            return "team:" + sfCode + "name is null";
//        }
//        if (belongSfCode == null) {
//            logger.error("team:" + sfCode + "上级部门sfCode is null");
//            return "team:" + sfCode + "上级部门sfCode is null";
//        }
//        return checkSfTeam(teamName, sfCode, belongSfCode);
//    }
//
//    /**
//     * 处理激活状态的team
//     * @param data
//     * @return
//     */
//    public String checkAddSubTeam(JSONObject data) {
//        String subTeamName = data.get("name_localized")!=null?data.get("name_localized").toString():null;
//        String sfCode = data.get("externalCode")!=null?data.get("externalCode").toString():null;
//        String belongSfCode = data.get("cust_TeamForOA")!=null?data.get("cust_TeamForOA").toString():null;
//
//        if (subTeamName == null) {
//            logger.error("team:" + sfCode + "name is null");
//            return "team:" + sfCode + "name is null";
//        }
//        if (belongSfCode == null) {
//            logger.error("team:" + sfCode + "上级部门sfCode is null");
//            return "team:" + sfCode + "上级部门sfCode is null";
//        }
//        return checkSfTeam(subTeamName, sfCode, belongSfCode);
//    }
//
//    /**
//     * 处理激活状态的section
//     * @param data
//     * @return
//     */
//    public String checkAddSection(JSONObject data) {
//        String sectionName = data.get("externalName")!=null?data.get("externalName").toString():null;
//        String sfCode = data.get("externalCode")!=null?data.get("externalCode").toString():null;
//        String belongSfCode = data.get("cust_SubTeamForOA")!=null?data.get("cust_SubTeamForOA").toString():null;
//
//        if (sectionName == null) {
//            logger.error("team:" + sfCode + "name is null");
//            return "team:" + sfCode + "name is null";
//        }
//        if (belongSfCode == null) {
//            logger.error("team:" + sfCode + "上级部门sfCode is null");
//            return "team:" + sfCode + "上级部门sfCode is null";
//        }
//        return checkSfTeam(sectionName, sfCode, belongSfCode);
//    }
//
//
//    /**
//     * 处理group以下的部门方法
//     * @param name
//     * @param sfCode
//     * @param belongSfCode
//     * @return
//     */
//    public String checkSfTeam(String name, String sfCode, String belongSfCode) {
//        StringBuilder msg = new StringBuilder();
//        //检查上级部门
//        Team belong = teamService.getTeamByCode(belongSfCode);
//        if (belong == null) {
//            msg.append(name + sfCode + "上级部门不存在");
//            logger.error(msg.toString());
//            return msg.toString();
//        }
//        String fullName = belong.getName() + "-" + name;
//        Team team = teamService.getTeamByCode(sfCode);
//        if (team == null) {
//            team = new Team();
//            team.setName(fullName);
//            team.setMailGroup("");
//            team.setSfCode(sfCode);
//            //创建MIT部门
//            if (teamService.addTeam(team)) {
//                //创建钉钉部门
//                if(dingdingService.createDingDingDept(team, belong.getDingdingId())) {
//                    msg.append("MIT新增SF部门" + name + "[成功]");
//                    return msg.toString();
//                } else {
//                    msg.append("MIT新增SF部门" + name + "[失败]");
//                    return msg.toString();
//                }
//            } else {
//                msg.append("添加MIT部门" + fullName + "错误");
//                logger.error(msg.toString());
//                return msg.toString();
//            }
//        } else if (team.getDingdingId() == null) {
//            dingdingService.createDingDingDept(team, belong.getDingdingId());
//            msg.append(team.getName() + "更新钉钉ID");
//            return msg.toString();
//        } else {
//            //没有群聊ID
//            if (team.getDingdingId() != null && team.getChatId() == null) {
//                try {
//                    dingdingService.saveChatId(team);
//                } catch (Exception e) {
//                    msg.append(team.getName() + "保存添加群聊错误");
//                    logger.error(msg.toString());
//                    return msg.toString();
//                }
//            }
//            //不需要修改
//            if (team.getName().equals(fullName)) {
//                return null;
//            }
//            //名称变化 邮件组不是all
//            if (!team.getName().equals(fullName) && !team.getMailGroup().equals("all")) {
//                //修改ldap
//                ldapService.modifyGroup(team.getMailGroup(), "displayname", name);
//                ldapService.modifyGroup(team.getMailGroup(), "description", fullName);
//            }
//            msg.append("MIT部门修改名称:" + team.getName() + "->" + fullName);
//            team.setName(fullName);
//
//            try {
//                if (dingDingRestClient.updateDept(team, name)) {
//                    msg.append("----修改钉钉部门名称[成功]");
//                } else {
//                    msg.append("----修改钉钉部门名称[失败]");
//                }
//            } catch (Exception e) {
//                msg.append(team + "修改钉钉部门名称:" + name + "错误");
//                logger.error(msg.toString());
//            }
//            teamService.updateTeam(team);
//
//            return msg.toString();
//        }
//    }
//
//
//    /**
//     * 从sf拉取全部部门数据
//     */
//    private void init() {
//        sfGroup = sfRestClient.getGroups();
//        sfBu = sfRestClient.getBus();
//        sfTeam = sfRestClient.getTeams();
//        sfSubTeam = sfRestClient.getSubTeams();
//        sfSeciton = sfRestClient.getSections();
//    }

}
