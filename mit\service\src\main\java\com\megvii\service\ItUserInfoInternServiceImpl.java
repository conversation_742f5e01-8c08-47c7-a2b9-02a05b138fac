package com.megvii.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.megvii.entity.entry.ItUserInfoInternPO;
import com.megvii.entryMapper.ItUserInfoInternMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-5
 */
@Service
public class ItUserInfoInternServiceImpl extends ServiceImpl<ItUserInfoInternMapper, ItUserInfoInternPO> implements ItUserInfoInternService {

  @Autowired
  private ItUserInfoInternMapper mapper;

  @Override
  public ItUserInfoInternPO getInternByUserId(String userId){

    return mapper.selectInternInfoByUserId(userId);


  }
}
