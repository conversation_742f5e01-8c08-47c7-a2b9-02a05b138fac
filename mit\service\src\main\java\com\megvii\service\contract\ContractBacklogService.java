package com.megvii.service.contract;

import com.github.pagehelper.PageHelper;
import com.megvii.entity.opdb.contract.BacklogTag;
import com.megvii.entity.opdb.contract.UserContractBacklog;
import com.megvii.mapper.UserContractBacklogMapper;
import com.megvii.mapper.filter.UserBacklogFilter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/13 14:37
 */
@Service
public class ContractBacklogService {

	@Autowired
	private UserContractBacklogMapper userContractBacklogMapper;


	/**
	 * 更改待续签状态
	 * @param userContractBacklog
	 * @return
	 */
	public boolean updateContractBacklogStatus(UserContractBacklog userContractBacklog) {
		if (userContractBacklogMapper.updateUserContractBacklog(userContractBacklog) > 0) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 更新续签列表离职标签
	 * @param userContractBacklog
	 * @return
	 */
	public boolean updateUserContractBacklogTag(UserContractBacklog userContractBacklog) {
		if (userContractBacklogMapper.updateUserContractBacklogTag(userContractBacklog) > 0) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 创建一条待续签记录
	 * @param userContractBacklog
	 * @return
	 */
	public boolean addContractBacklog(UserContractBacklog userContractBacklog) {
		if(userContractBacklogMapper.insertUserContractBacklog(userContractBacklog) > 0) {
			return true;
		} else {
			return false;
		}
	}


	/**
	 * 查询未处于 已完成 状态的待续签记录 通过user_id 查询 每个人只能有一条
	 * @param userId
	 * @return
	 */
	public UserContractBacklog selectNotFinishBacklogByUserid(String userId) {
		return userContractBacklogMapper.selectNotFinishBacklogByUserId(userId);
	}
	public UserContractBacklog selectBacklogByUserId(String userId) {
		return userContractBacklogMapper.selectBacklogByUserId(userId);
	}

	/**
	 * 通过状态查询 返回map  key:姓名拼音
	 * @param status
	 * @return
	 */
	public Map<String, UserContractBacklog> selectBacklogByStatus(String status) {
		UserBacklogFilter filter = new UserBacklogFilter();
		filter.setBacklogStatus(status);

		List<UserContractBacklog> list = userContractBacklogMapper.selectBacklogByFilter(filter);
		if (list == null || list.size() == 0) {
			return new HashMap<String, UserContractBacklog>();
		}
		Map<String, UserContractBacklog> maps = list.stream().collect(Collectors.toMap(UserContractBacklog::getUserId, Function.identity(), (key1, key2) -> key2));

		return maps;
	}

	public List<UserContractBacklog> getBacklogByFilter(UserBacklogFilter filter) {
		return userContractBacklogMapper.selectBacklogByFilter(filter);
	}
	public List<UserContractBacklog> getWaitTerminateBacklogByUserId(String userId) {
		UserBacklogFilter filter = new UserBacklogFilter();
		filter.setUserId(userId);
		filter.setDimissionTag(BacklogTag.WAITTERMINATE);
		List<UserContractBacklog> users = getBacklogByFilter(filter);
		return userContractBacklogMapper.selectBacklogByFilter(filter);
	}


}
