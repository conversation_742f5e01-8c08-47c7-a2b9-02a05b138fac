package com.megvii.service;

import com.megvii.entity.opdb.GaRelationship;
import com.megvii.mapper.GaRelationshipMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/9/17 21:21
 */
@Service
public class GaRelationshipService {

    @Autowired
    private GaRelationshipMapper gaRelationshipMapper;

    public GaRelationship selectGaRelationshipByUnameAndGname(String uname, String gname) {
        return gaRelationshipMapper.selectGaRelationshipByUnameAndGname(uname, gname);
    }

    public void deleteGaRelationship(int id) {
        gaRelationshipMapper.deleteGaRelationship(id);
    }

}
