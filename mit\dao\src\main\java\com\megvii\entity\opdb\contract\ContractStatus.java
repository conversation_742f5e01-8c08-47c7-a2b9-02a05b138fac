package com.megvii.entity.opdb.contract;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/20 12:11
 */
public class ContractStatus {

	public final static String NOTHING = "无合同";
	public final static String IMPORT = "已导入";
	public final static String DRAFT = "草稿";
	public final static String FILLING = "拟定中";
	public final static String SIGNING = "签署中";
	public final static String COMPLETE = "已完成";
	public final static String REJECTED = "已退回";
	public final static String RECALLED = "已撤回";
	public final static String EXPIRED = "已过期";
	public final static String TERMINATING = "作废中";
	public final static String TERMINATED = "已作废";
	public final static String DELETE = "已删除";
	public final static String FINISHED = "强制完成";

	public static Map<String, String> map = new HashMap<>();

	static {
		map.put("NOTHING", NOTHING);
		map.put("IMPORT", IMPORT);
		map.put("DRAFT", DRAFT);
		map.put("FILLING", FILLING);
		map.put("SIGNING", SIGNING);
		map.put("COMPLETE", COMPLETE);
		map.put("REJECTED", REJECTED);
		map.put("RECALLED", RECALLED);
		map.put("EXPIRED", EXPIRED);
		map.put("TERMINATING", TERMINATING);
		map.put("TERMINATED", TERMINATED);
		map.put("DELETE", DELETE);
		map.put("FINISHED", FINISHED);
	}


}
