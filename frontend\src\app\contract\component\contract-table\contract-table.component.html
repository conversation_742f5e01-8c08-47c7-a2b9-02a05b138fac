<div class="search-container bottom-30 top-20 left-20">
  <span>
    <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">工号:</label>
    <input
      type="text"
      nz-input
      class="right-20"
      placeholder="请输入工号"
      [(ngModel)]="staffid"
      (keyup)="$event.which === 13 ? onSearch() : 0"
    />
  </span>
  <span>
    <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">ID:</label>
    <input
      type="text"
      class="right-20"
      nz-input
      placeholder="请输入ID"
      [(ngModel)]="id"
      (keyup)="$event.which === 13 ? onSearch() : 0"
    />
  </span>
  <span>
    <label class="title right-10" [ngClass]="{ 'pick-up-label': !isSearchExpand }">姓名:</label>
    <input
      type="text"
      class="right-20"
      nz-input
      placeholder="请输入姓名"
      [(ngModel)]="username"
      (keyup)="$event.which === 13 ? onSearch() : 0"
    />
  </span>
  <span *ngIf="isSearchExpand">
    <label class="title right-10">部门:</label>
    <nz-select
      class="right-20"
      [(ngModel)]="team"
      nzPlaceHolder="模糊搜索部门"
      nzShowSearch
      [nzServerSearch]="true"
      [nzFilterOption]="nzFilterOption"
      (nzOnSearch)="searchTeam($event)"
      [nzShowArrow]="false"
    >
      <nz-option
        *ngFor="let item of listOfTeamOption"
        [nzValue]="item['id']"
        [nzLabel]="item['name']"
      ></nz-option>
    </nz-select>
  </span>
  <span *ngIf="isSearchExpand && type !== 'voiding'">
    <label class="title right-10">入职地点:</label>
    <nz-select
      nzShowSearch
      class="right-20"
      nzPlaceHolder="请选择入职地点"
      [(ngModel)]="entryLocation"
    >
      <nz-option nzLabel="全部" nzValue=""></nz-option>
      <nz-option
        *ngFor="let item of listOfLocationOption"
        [nzLabel]="item['name']"
        [nzValue]="item['name']"
      ></nz-option>
    </nz-select>
  </span>
  <span *ngIf="isSearchExpand && type !== 'voiding'">
    <label class="title right-10">入职类型:</label>
    <nz-select [(ngModel)]="entryType" class="right-20" nzPlaceHolder="请选择入职类型">
      <nz-option nzValue="" nzLabel="全部"></nz-option>
      <nz-option
        *ngFor="let item of entryTypeList"
        [nzValue]="item['name']"
        [nzLabel]="item['name']"
      ></nz-option>
    </nz-select>
  </span>
  <span *ngIf="isSearchExpand && type !== 'notSign' && type !== 'voiding'">
    <label class="title right-10">当前状态:</label>
    <nz-select [(ngModel)]="status" class="right-20" nzPlaceHolder="请选择当前状态">
      <nz-option nzValue="" nzLabel="全部"></nz-option>
      <nz-option
        *ngFor="let item of employeeCurrentStatusList"
        [nzValue]="item"
        [nzLabel]="item"
      ></nz-option>
    </nz-select>
  </span>
  <span *ngIf="isSearchExpand && type !== 'notSign' && type !== 'voiding'">
    <label class="title right-10">当前合同状态:</label>
    <nz-select class="right-20" [(ngModel)]="tempContractStatus" nzPlaceHolder="请选择当前合同状态">
      <nz-option nzValue="" nzLabel="全部"></nz-option>
      <nz-option
        *ngFor="let item of contractStatusList"
        [nzValue]="item"
        [nzLabel]="item"
      ></nz-option>
    </nz-select>
  </span>
  <span *ngIf="isSearchExpand && type !== 'thisWeek' && type !== 'lastWeek' && type !== 'voiding'">
    <label class="title right-10">入职日期范围:</label>
    <nz-range-picker class="right-20" [(ngModel)]="entryDateRange"></nz-range-picker>
  </span>
  <span *ngIf="isSearchExpand && type == 'quit'">
    <label class="title right-10">离职日期:</label>
    <nz-range-picker class="right-20" [(ngModel)]="leaveDateRange"></nz-range-picker>
  </span>
  <span class="pull-right">
    <button nz-button class="right-20" nzType="primary" (click)="onSearch()">搜索</button>
    <button nz-button (click)="onReset()">重置</button>
    <button nzType="link" nz-button (click)="onExpand()">
      <i nz-icon [nzType]="isSearchExpand ? 'up' : 'down'" nzTheme="outline"></i>
      {{ isSearchExpand ? '收起' : '展开' }}
    </button>
  </span>
</div>
<div *ngIf="type == 'active'" class="btn-box">
  <button class="btn" nz-button nzType="default" (click)="downloadOut()">导出</button>
</div>
<nz-table
  #listTable
  [nzData]="userList"
  [nzShowPagination]="true"
  [nzFrontPagination]="false"
  class="dept-table"
  [nzShowSizeChanger]="true"
  (nzPageSizeChange)="pageSizeChange($event)"
  [nzLoading]="loading"
  [(nzPageIndex)]="page"
  (nzPageIndexChange)="pageChange()"
  [nzShowQuickJumper]="true"
  [nzShowTotal]="totalTemplate"
  [nzTotal]="total"
  [nzHideOnSinglePage]="false"
  [(nzPageSize)]="pageSize"
  [nzScroll]="{ x: '1000px' }"
>
  <thead>
    <tr>
      <th nzShowExpand nzWidth="50px"></th>
      <th nzWidth="120px">工号</th>
      <th nzWidth="120px">ID</th>
      <th nzWidth="130px">姓名</th>
      <th nzWidth="170px">team</th>
      <th nzWidth="170px">入职地址</th>
      <th *ngIf="type === 'extend'" nzWidth="200px">现合同到期日期</th>
      <th *ngIf="type === 'endorse' || type === 'change'" nzWidth="200px">新合同生效日期</th>
      <th *ngIf="type !== 'extend' && type !== 'endorse' && type !== 'change'" nzWidth="170px">
        入职日期
      </th>
      <th *ngIf="type === 'quit'" nzWidth="170px">离职日期</th>
      <th nzWidth="150px">入职类型</th>
      <th nzWidth="100px">当前状态</th>
      <th nzWidth="100px">当前合同状态</th>
      <th nzWidth="220px" nzRight="0px">操作</th>
    </tr>
  </thead>
  <tbody>
    <ng-template ngFor let-data [ngForOf]="listTable.data">
      <tr class="user-item">
        <td
          [nzShowExpand]="data['contractStatus'] != '无合同'"
          [(nzExpand)]="mapOfExpandData[data.id]"
          (nzExpandChange)="userExpandChange($event, data)"
        ></td>
        <td>{{ data['workNumber'] }}</td>
        <td>{{ data['userId'] }}</td>
        <td>
          {{ data['spell'] }}
          <nz-tag *ngIf="data['dimissionTag'] === '无需续签'" class="left-16" [nzColor]="'#E5F9F1'">
            <span class="tag-span1">{{ data['dimissionTag'] }}</span>
          </nz-tag>
          <nz-tag *ngIf="data['dimissionTag'] === '加急续签'" class="left-16" [nzColor]="'#FDDEDC'">
            <span class="tag-span">{{ data['dimissionTag'] }}</span>
          </nz-tag>
        </td>
        <td [nzTooltipTitle]="data['team']" nzTooltipPlacement="top" nz-tooltip>
          {{ !!data['team'] ? data['team'].split('-')[data['team'].split('-').length - 1] : '' }}
        </td>
        <td>
          <app-short-info [info]="data['workBase']" maxLength="8"></app-short-info>
        </td>
        <td *ngIf="type === 'extend'">{{ data['endDate'] }}</td>
        <td *ngIf="type === 'endorse' || type === 'change'">{{ data['newConBeginDate'] }}</td>
        <td *ngIf="type !== 'extend' && type !== 'endorse' && type !== 'change'">
          {{ data['expectDate'] }}
        </td>
        <td *ngIf="type === 'quit'">
          {{ data['dimissionDate'] }}
        </td>
        <td>{{ data['employType'] }}</td>
        <td>{{ data['entryFlow'] }}</td>
        <td>
          {{ data['contractStatus'] }}
        </td>
        <td class="table-oprt" nzRight="0px">
          <a
            *ngIf="data['employType'] !== '退休返聘' && data['employType'] !== '劳务派遣'"
            href="javascript:void(0);"
            [ngClass]="{ 'disable-color': data['entryFlow'] === 'OA流程' }"
            (click)="data['entryFlow'] !== 'OA流程' && onUpdate(data['userId'])"
            >编辑信息</a
          >
          <a
            href="javascript:void(0);"
            *ngIf="type === 'quit' || type === 'active'"
            (click)="addAgree('', data, 'add')"
            >新增协议</a
          >
          <a *ngIf="checkSignOffline(data)" href="javascript:void(0);" (click)="onSignOffline(data)"
            >线下签署</a
          >
          <!-- <nz-upload
            *ngIf="checkSignOffline(data) && showNew === false"
            [nzAction]="uploadFileAction"
            (nzChange)="handleUploadChange($event)"
            [nzWithCredentials]="true"
          >
            <a href="javascript:void(0);" (click)="onSignOffline2(data)"
              >线下签署</a
            >
          </nz-upload> -->
        </td>
      </tr>
      <tr
        [nzExpand]="mapOfExpandData[data.id]"
        *ngFor="let contract of data['contractList']"
        [ngClass]="{
          'invalid-contract':
            ['草稿', '签署中', '已完成', '已导入'].indexOf(contract['contractStatus']) === -1
        }"
      >
        <td></td>
        <td colspan="3">
          <span class="signStatus">{{ contract['signStatus'] }}</span>
          <span class="right-20">{{ contract['contractNumber'] }}</span>
          <span
            >公司：<app-short-info maxLength="10" [info]="contract['company']"></app-short-info>
          </span>
        </td>
        <td colspan="1">
          <span>开始时间：{{ contract['startDate'] }}</span>
        </td>
        <td colspan="1">
          <span>结束时间：{{ contract['endDate'] }}</span>
        </td>
        <td colspan="1">
          <span
            *ngIf="type === 'quit' || type === 'active' || contract['signStatus'] === '自定义协议'"
            ><app-short-info maxLength="7" [info]="contract['contractTitle']"></app-short-info>
          </span>
        </td>
        <td *ngIf="type === 'quit'" colspan="1"></td>
        <td colspan="2">
          <div
            *ngIf="
              (type === 'quit' || type === 'active') &&
              contract['signStatus'] === '离职合同' &&
              contract['contractId']
            "
          >
            <span class="right-20">私人邮箱：</span>
            <span>{{ contract['selfEmail'] }}</span>
          </div>
        </td>
        <td colspan="1">
          <span>{{ contract['contractStatus'] }}</span>
        </td>
        <td colspan="1" class="table-oprt" nzRight="0px">
          <a
            *ngIf="
              contract['contractStatus'] != '草稿' &&
              contract['contractStatus'] != '未导入' &&
              contract['contractStatus'] != '已导入' &&
              contract['contractId']
            "
            href="javascript:void(0);"
            (click)="
              contract['contractStatus'] != '草稿' &&
                contract['contractStatus'] != '已导入' &&
                onReviewContract(contract)
            "
            >查看合同</a
          >
          <a
            *ngIf="contract['userContractFile']"
            href="javascript:void(0);"
            (click)="ondownfile(contract)"
            >下载合同</a
          >
          <a
            *ngIf="contract['contractStatus'] == '草稿'"
            href="javascript:void(0);"
            (click)="contract['contractStatus'] == '草稿' && onSign(contract, data)"
            >签署合同</a
          >
          <a
            *ngIf="
              contract['contractStatus'] == '已完成' &&
              contract['contractId'] &&
              contract['signStatus'] !== '自定义协议'
            "
            href="javascript:void(0);"
            (click)="contract['contractStatus'] == '已完成' && onInvalid(contract, data)"
            >作废合同</a
          >
          <a
            *ngIf="contract['contractStatus'] == '草稿'"
            href="javascript:void(0);"
            (click)="contract['contractStatus'] == '草稿' && onDelDraft(contract, data)"
            >删除草稿</a
          >
          <a
            *ngIf="contract['contractStatus'] == '签署中'"
            href="javascript:void(0);"
            (click)="onBackOut(contract, data)"
            >撤销合同</a
          >
          <a
            *ngIf="contract['contractStatus'] == '未导入' && (type === 'active' || type === 'quit')"
            href="javascript:void(0);"
            (click)="signUpload(contract, data)"
            >扫描上传</a
          >
          <a
            *ngIf="
              contract['contractId'] == null &&
              contract['contractStatus'] !== '未导入' &&
              contract['contractStatus'] !== '草稿' &&
              contract['signStatus'] !== '自定义协议'
            "
            href="javascript:void(0);"
            (click)="editUpload(contract, data)"
            >编辑</a
          >
          <a
            *ngIf="
              (type === 'active' || type === 'quit') &&
              contract['signStatus'] === '自定义协议' &&
              contract['contractStatus'] !== '已作废' &&
              contract['contractId']
            "
            (click)="editAgreement(contract, data)"
          >
            编辑</a
          >
          <a
            *ngIf="
              contract['contractId'] === null &&
              contract['contractStatus'] === '已完成' &&
              (type === 'active' || type === 'quit') &&
              contract['signStatus'] === '自定义协议' &&
              contract['contractStatus'] !== '已作废'
            "
            (click)="addAgree(contract, data, 'edit')"
          >
            编辑协议</a
          >
          <a
            *ngIf="
              contract['contractStatus'] == '已完成' &&
              contract['contractId'] &&
              (type === 'active' || type === 'quit') &&
              contract['signStatus'] === '自定义协议' &&
              contract['contractStatus'] !== '已作废'
            "
            (click)="onInvalid(contract, data)"
            >作废合同</a
          >
          <!-- contractid为空就是线下签署  线上签署contractid不为空 -->
        </td>
      </tr>
    </ng-template>
  </tbody>
</nz-table>

<ng-template #totalTemplate let-total> 总共 {{ total }} 条</ng-template>

<nz-modal
  [(nzVisible)]="signOfflineVisible"
  nzTitle="选择线下签署后将无法发送电子合同"
  nzOkText="继续"
  nzCancelText="取消"
  (nzOnOk)="handleSignOfflineOk()"
  (nzOnCancel)="handleSignOfflineCancel()"
  [nzOkDisabled]="isSignDisable"
>
  <form nz-form [formGroup]="signOfflineForm">
    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>签约次数</nz-form-label>
      <nz-form-control [nzSpan]="20" nzErrorTip="请输入签约次数">
        <nz-input-number formControlName="contractCount" [nzMin]="0" [nzStep]="1"></nz-input-number>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>签约时间</nz-form-label>
      <nz-form-control [nzSpan]="20" nzErrorTip="请选择签约时间">
        <nz-range-picker formControlName="contractDate"></nz-range-picker>
      </nz-form-control>
    </nz-form-item>
    <div *ngIf="type == 'endorse' || type == 'change'; else normalBlock">
      <nz-form-item>
        <nz-form-label [nzSpan]="4" nzRequired>公司主体</nz-form-label>
        <nz-form-control [nzSpan]="20" nzErrorTip="请选择公司主体">
          <nz-select formControlName="newContract" [nzDisabled]="type == 'change'">
            <nz-option
              *ngFor="let item of companyOfOption"
              [nzValue]="item['code']"
              [nzLabel]="item['name']"
            ></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="4" nzRequired>工作地点</nz-form-label>
        <nz-form-control [nzSpan]="20" nzErrorTip="请选择工作地点">
          <nz-select formControlName="newWorkCity">
            <nz-option
              *ngFor="let item of workBaseOfOption"
              [nzValue]="item['code']"
              [nzLabel]="item['name']"
            ></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>
    <ng-template #normalBlock>
      <nz-form-item>
        <nz-form-label [nzSpan]="4" nzRequired>公司主体</nz-form-label>
        <nz-form-control [nzSpan]="20" nzErrorTip="请选择公司主体">
          <nz-select formControlName="company">
            <nz-option
              *ngFor="let item of companyOfOption"
              [nzValue]="item['name']"
              [nzLabel]="item['name']"
            ></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </ng-template>
    <!-- 上传 -->
    <nz-form-item>
      <nz-form-label [nzSpan]="4" [nzRequired]="true">附件</nz-form-label>
      <nz-form-control
        [nzSpan]="20"
        nzErrorTip="请上传附件"
        [nzValidateStatus]="userContractFileError ? 'error' : ''"
      >
        <nz-upload
          [nzAction]="uploadFileAction"
          [nzAccept]="'.zip,.pdf,.rar'"
          [nzFileList]="uploadFileList"
          [nzBeforeUpload]="beforeUpload"
          [nzRemove]="handleUploadRemove"
          (nzChange)="handleUploadChange($event)"
          [nzWithCredentials]="true"
        >
          <button nz-button nzType="link">上传</button>
        </nz-upload>
        <p>支持扩展名：.rar .zip .pdf，文件大小不超过20M</p>
      </nz-form-control>
    </nz-form-item>
  </form>
</nz-modal>

<nz-modal
  [(nzVisible)]="isUploadVisible"
  nzTitle="线下签署扫描上传"
  nzWidth="800px"
  [nzOkLoading]="signLoading"
  (nzOnCancel)="handleSignCancel()"
  [nzOkDisabled]="isOkDisable"
  (nzOnOk)="handleSignOk()"
>
  <nz-alert
    *ngIf="titleMessage !== ''"
    class="alert-text"
    nzType="info"
    [nzMessage]="titleMessage"
    nzShowIcon
  ></nz-alert>
  <form nz-form class="top-20" #basicInfoForm="ngForm">
    <nz-form-item>
      <div class="upload-div">
        <nz-form-label nzRequired [nzSpan]="7">文件上传</nz-form-label>
        <nz-form-control [nzSpan]="17" nzErrorTip="请上传文件" nzHasFeedback>
          <nz-upload
            [nzFileList]="signFileList"
            [nzAccept]="'.zip,.pdf,.rar'"
            [nzRemove]="handleListRemove"
            [nzBeforeUpload]="beforeSignUpload"
            [nzPreview]="handlePreview"
          >
            <button nz-button [nzLoading]="uploadLoading">
              <i nz-icon nzType="plus" nzTheme="outline"></i><span>上传</span>
            </button>
          </nz-upload>
          <p>支持扩展名：.rar .zip .pdf，文件大小不超过20M</p>
        </nz-form-control>
      </div>
    </nz-form-item>
  </form>
</nz-modal>

<nz-modal
  [(nzVisible)]="isAgreeVisible"
  [nzTitle]="agreeTitle"
  (nzOnCancel)="handleAgreeCancel(addAgreeForm)"
  nzWidth="800px"
  [nzFooter]="null"
>
  <form nz-form class="top-20" #addAgreeForm="ngForm">
    <nz-form-item>
      <nz-form-label [nzSpan]="4">姓名</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        addAgreeFormData.addSpell
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">工号</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        addAgreeFormData.workNumber
      }}</span>
    </nz-form-item>
    <nz-form-item *ngIf="addAgreeFormData.action === 'edit'">
      <nz-form-label [nzSpan]="4">合同编号</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        addAgreeFormData.contractNumber
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">类型</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        addAgreeFormData.type
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>主题</nz-form-label>
      <nz-form-control
        [nzValidateStatus]="addAgreeForm.controls['addAgreeTheme']"
        [nzSpan]="10"
        nzHasFeedback
        nzErrorTip="请选择主题"
        style="margin-left: 40px"
      >
        <nz-select
          nzShowSearch
          nzAllowClear
          [ngModel]="addAgreeTheme"
          name="addAgreeTheme"
          [nzDropdownRender]="render"
          (ngModelChange)="selectChange($event)"
          required
        >
          <nz-option
            *ngFor="let option of agreeArr"
            [nzValue]="option"
            [nzLabel]="option"
          ></nz-option>
        </nz-select>
        <ng-template #render>
          <nz-divider style="margin: 12px 0"></nz-divider>
          <div class="add-item">
            <div>
              <input nz-input placeholder="输入主题" [(ngModel)]="themeValue" name="themeValue" />
              <a class="add-span" (click)="addItem()"><i nz-icon nzType="plus"></i> Add item</a>
            </div>
          </div>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4" nzRequired>公司</nz-form-label>
      <nz-form-control
        [nzValidateStatus]="addAgreeForm.controls['addCompany']"
        [nzSpan]="10"
        nzHasFeedback
        nzErrorTip="请选择公司"
        style="margin-left: 40px"
      >
        <nz-select
          nzShowSearch
          nzPlaceHolder="请选择公司"
          required
          [ngModel]="addCompany"
          name="addCompany"
          (ngModelChange)="selectCompanyChange($event)"
        >
          <nz-option nzLabel="" nzValue=""></nz-option>
          <nz-option
            *ngFor="let option of companyOptions"
            [nzValue]="option"
            [nzLabel]="option"
          ></nz-option>
        </nz-select>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">开始时间</nz-form-label>
      <nz-form-control class="left-40" [nzSpan]="10">
        <nz-date-picker
          style="margin-left: 40px"
          [nzShowToday]="false"
          [nzDisabledDate]="editDisabledStartDate"
          nzPlaceHolder="开始时间"
          [(ngModel)]="addAgreeFormData.startDate"
          name="startDate"
        ></nz-date-picker>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">结束时间</nz-form-label>
      <nz-form-control class="left-40" [nzSpan]="10">
        <nz-date-picker
          style="margin-left: 40px"
          [nzShowToday]="false"
          [nzDisabledDate]="editDisabledEndDate"
          nzPlaceHolder="结束时间"
          [(ngModel)]="addAgreeFormData.endDate"
          name="endDate"
        ></nz-date-picker>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">附件</nz-form-label>
      <nz-form-control [nzSpan]="20">
        <nz-upload
          style="margin-left: 40px"
          [nzAccept]="'.zip,.pdf,.rar'"
          [nzFileList]="agreeFileList"
          [nzRemove]="handleAgreeListRemove"
          [nzBeforeUpload]="beforeAgreeUpload"
          [nzPreview]="previewAgree"
        >
          <button nz-button nzType="link" [nzLoading]="agreeUploadLoading">上传</button>
        </nz-upload>
        <p>支持扩展名：.rar .zip .pdf，文件大小不超过20M</p>
      </nz-form-control>
    </nz-form-item>
    <div class="save-create">
      <button nz-button class="right-20" (click)="handleAgreeCancel(addAgreeForm)">取消</button>
      <button
        type="submit"
        nz-button
        nzType="primary"
        [disabled]="!addAgreeForm.form.valid"
        [nzLoading]="agreeLoading"
        (click)="handleAgreeOk(addAgreeForm)"
      >
        确认
      </button>
    </div>
  </form>
</nz-modal>

<nz-modal
  [(nzVisible)]="editAgreeVisible"
  nzTitle="编辑协议"
  nzWidth="800px"
  [nzOkLoading]="editAgreeLoading"
  (nzOnCancel)="handleEditAgreeCancel()"
  (nzOnOk)="handleEditAgreeOk()"
>
  <form nz-form #agreeForm="ngForm">
    <nz-form-item>
      <nz-form-label [nzSpan]="4">姓名</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        editAgreeForm.agreeName
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">工号</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        editAgreeForm.agreeNumber
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">合同编号</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        editAgreeForm.contractNumber
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">类型</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        editAgreeForm.agreeType
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">主题</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        editAgreeForm.agreeTheme
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">公司</nz-form-label>
      <span style="display: inline-block; height: 40px; line-height: 40px; margin-left: 40px">{{
        editAgreeForm.agreeCompany
      }}</span>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">开始时间</nz-form-label>
      <nz-form-control [nzSpan]="8">
        <nz-date-picker
          style="margin-left: 40px"
          [nzDisabledDate]="disabledStartDate"
          [nzShowToday]="false"
          [(ngModel)]="editAgreeForm.startDate"
          name="startDate"
          nzPlaceHolder="开始时间"
        ></nz-date-picker>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label [nzSpan]="4">结束时间</nz-form-label>
      <nz-form-control class="left-40" [nzSpan]="8">
        <nz-date-picker
          style="margin-left: 40px"
          [nzDisabledDate]="disabledEndDate"
          [nzShowToday]="false"
          [(ngModel)]="editAgreeForm.endDate"
          name="endDate"
          nzPlaceHolder="结束时间"
        ></nz-date-picker>
      </nz-form-control>
    </nz-form-item>
  </form>
</nz-modal>
