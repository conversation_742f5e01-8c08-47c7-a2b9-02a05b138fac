<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.DataKeyConfigMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.DataKeyConfig">
        <id column="id" property="id"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <result column="description" property="description"/>
        <result column="status" property="status"/>
    </resultMap>

    <select id="selectAllKey" resultType="java.lang.String">
		SELECT `key` FROM data_key_config WHERE `Status` = 0
	</select>

</mapper>
