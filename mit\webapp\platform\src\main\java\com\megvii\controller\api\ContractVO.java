package com.megvii.controller.api;

import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.contract.ContractStatus;
import com.megvii.entity.opdb.contract.UserContract;
import com.megvii.entity.opdb.contract.UserContractSnapshot;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:05
 */
@Data
public class ContractVO {

	private int id;
	private String userId;
	private String workNumber;
	private String spell;
	private String contractNumber;
	private String contractId;
	private String contractType;
	private String contractCount;
	private String contractAttribute;
	private String startDate;
	private String endDate;
	private String effectDate;
	private String probationStartDate;
	private String probationEndDate;
	private String company;
	private String position;
	private String employBase;
	private String contractCell;
	private String certType;
	private String certNo;
	private String address;
	private String memo;
	private String contractStatus;
	private String signStatus;
	/**
	 *原签署单位
	 */
	private String contract;
	/**
	 *新签署单位
	 */
	private String newContract;
	/**
	 *原签署开始日期
	 */
	private String conBeginDate;
	/**
	 *原签署截止日期
	 */
	private String conEndDate;
	/**
	 *新签署开始日期
	 */
	private String newConBeginDate;
	/**
	 *新签署结束日期
	 */
	private String newConEndDate;
	/**
	 *原工作地
	 */
	private String workCity;
	/**
	 *新工作地
	 */
	private String newWorkCity;
	/**
	 * 合同文件包地址
	 */
	private String userContractFile;
	private List<String> paths;

	private String offerPath;
	/**
	 * 在读学校
	 */
	private String school;

	/**
	 * 学号
	 */
	private String studentNumber;

	/**
	 * 入学时间
	 */
	private String admissionTime;

	/**
	 * 毕业时间
	 */
	private String graduationTime;

	/**
	 * 实习津贴
	 */
	private String internshipAllowance;

	public static List<ContractVO> toVOs(List<UserContract> contracts) {
		List<ContractVO> vos = new ArrayList<>();
		for (UserContract contract : contracts) {
			vos.add(contractToVO(contract));
		}
		return vos;
	}

	private static ContractVO contractToVO(UserContract contract) {
		ContractVO vo = toVO(contract);
		vo.setContractStatus(contract.getContractStatus());
		vo.setContractId(contract.getContractId());

		return vo;
	}


	public static ContractVO toVO(UserContractSnapshot snapshot) {
		ContractVO vo = new ContractVO();
		vo.setId(snapshot.getId());
		vo.setUserId(snapshot.getUserId());
		vo.setWorkNumber(snapshot.getWorkNumber());
		vo.setSpell(snapshot.getSpell());
		vo.setContractNumber(snapshot.getContractNumber());
		vo.setContractType(snapshot.getContractType());
		vo.setContractCount(snapshot.getContractCount());
		vo.setContractAttribute(snapshot.getContractAttribute());
		vo.setStartDate(snapshot.getStartDate()!=null?DateTimeUtil.date2String(snapshot.getStartDate()):null);
		vo.setEndDate(snapshot.getEndDate()!=null?DateTimeUtil.date2String(snapshot.getEndDate()):null);
		vo.setEffectDate(snapshot.getEffectDate()!=null?DateTimeUtil.date2String(snapshot.getEffectDate()):null);
		vo.setProbationStartDate(snapshot.getProbationStartDate()!=null?DateTimeUtil.date2String(snapshot.getProbationStartDate()):null);
		vo.setProbationEndDate(snapshot.getProbationEndDate()!=null?DateTimeUtil.date2String(snapshot.getProbationEndDate()):null);
		vo.setCompany(snapshot.getCompany());
		vo.setPosition(snapshot.getPosition());
		vo.setEmployBase(snapshot.getEmployBase());
		vo.setContractCell(snapshot.getContractCell());
		vo.setCertType(snapshot.getCertType());
		vo.setCertNo(snapshot.getCertNo());
		vo.setAddress(snapshot.getAddress());
		vo.setMemo(snapshot.getMemo());
		vo.setContractStatus(ContractStatus.DRAFT);
		vo.setSignStatus(snapshot.getSignStatus());
		vo.setContract(snapshot.getContract());
		vo.setNewContract(snapshot.getNewContract());
		vo.setConBeginDate(snapshot.getConBeginDate());
		vo.setConEndDate(snapshot.getConEndDate());
		vo.setNewConBeginDate(snapshot.getNewConBeginDate());
		vo.setNewConEndDate(snapshot.getNewConEndDate());
		vo.setWorkCity(snapshot.getWorkCity());
		vo.setNewWorkCity(snapshot.getNewWorkCity());
		vo.setOfferPath(snapshot.getOfferPath());
		vo.setInternshipAllowance(snapshot.getInternshipAllowance());
		vo.setSchool(snapshot.getSchool());
		vo.setStudentNumber(snapshot.getStudentNumber());
		vo.setAdmissionTime(snapshot.getAdmissionTime()!=null ? DateTimeUtil.date2String7(snapshot.getAdmissionTime()) : null);
		vo.setGraduationTime(snapshot.getGraduationTime()!=null ? DateTimeUtil.date2String7(snapshot.getGraduationTime()) : null);
		if (snapshot.getFilePath() != null && !snapshot.getFilePath().equals("")) {
			List<String> list = new ArrayList<>();
			for (String s : snapshot.getFilePath().split(",")) {
				list.add(s);
			}
			vo.setPaths(list);
		} else {
			vo.setPaths(new ArrayList<>());
		}

		return vo;
	}

}
