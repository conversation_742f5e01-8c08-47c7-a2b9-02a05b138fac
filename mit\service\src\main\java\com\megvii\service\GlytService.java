package com.megvii.service;

import com.jcraft.jsch.SftpException;
import com.megvii.common.SFTPUtil;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/1/16 14:54
 */
@PropertySource(value = "classpath:glyt.${spring.profiles.active}.properties", encoding = "UTF-8")
@Service
public class GlytService {

	private Logger logger = LoggerFactory.getLogger(GlytService.class);

	@Value("${SFTP_USERNAME}")
	private String username;

	@Value("${SFTP_PASSWORD}")
	private String password;

	@Value("${SFTP_HOST}")
	private String host;

	@Value("${SFTP_PORT}")
	private Integer port;

	@Value("${EXCEL_MODEL_PATH}")
	private String modelPath;

	@Value("${EXCEL_LOCAL_NAME}")
	private String localName;

	@Value("${EXCEL_LOCAL_PATH}")
	private String excelLocalPath;

	@Value("${EXCEL_NAME}")
	private String excelName;

	@Value("${EXCEL_PATH}")
	private String excelPath;

	@Value("#{'${COMPANY_BLACK_LIST}'.split(',')}")
	private List<String> companyList = new ArrayList<>();

	@Autowired
	private ExcelService excelService;

	@Autowired
	private UserService userService;

	/**
	 * 更新GLYT人员 isAll 控制是否生成全量excel
	 *
	 * @param diff
	 * @return
	 */
	public String updateGlytUsers(Integer diff) {
		List<User> userList;
		if (diff != null) {
			//初始化
			if (diff == 0) {
				UserFilter filter = new UserFilter();
				filter.setStatusCode(0);
				filter.setEntryFlowStatus(3);
				userList = userService.getUsersByFilter(filter);
			} else {
				userList = userService.getUsersByDiffExpectDimissDate(diff);
			}
		} else {
			userList = userService.getUsersByDiffExpectDimissDate(-5);
		}

		if (userList == null || userList.size() == 0) {
			return "没有需要更新的人员";
		}

		return uploadExcel(userList);

	}


	/**
	 * 1.查询需要更新的人员
	 * 2.生成excel 在本地冗余一份 文件名加时间戳
	 * 3.登录SFTP服务器
	 * 4.上传excel
	 * 5.登出
	 *
	 * @return
	 */
	private String uploadExcel(List<User> users) {
		StringBuilder msg = new StringBuilder();

		File file = excelService.createGlytExcel(users, companyList, modelPath, excelLocalPath, localName + "-" + System.currentTimeMillis() + ".xlsx");
		msg.append("<br/>***生成excel成功<br/>");


		SFTPUtil sftp = new SFTPUtil(username, password, host, port);
		sftp.login();
		msg.append("***登录SFTP服务器成功<br/>");


		InputStream is = null;
		try {
			is = new FileInputStream(file);
			msg.append("***读取excel成功<br/>");
		} catch (FileNotFoundException e) {
			logger.info("***读取excel错误");
			msg.append("***读取excel错误<br/>");
			return msg.toString();
		}

		try {
			if (sftp.upload(excelPath, excelName, is)) {
				msg.append("***上传excel成功<br/>");
			} else {
				msg.append("***上传excel失败<br/>");
			}
		} catch (SftpException e) {
			logger.info("上传excel错误");
			msg.append("***上传excel错误<br/>");
		}

		sftp.logout();

		return msg.toString();
	}


}
