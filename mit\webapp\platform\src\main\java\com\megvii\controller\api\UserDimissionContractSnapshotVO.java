package com.megvii.controller.api;

import lombok.Data;

@Data
public class UserDimissionContractSnapshotVO {
  private Integer id;

  /**
   * 离职结算单id
   */
  private String dimissionId;

  /**
   * 离职人员
   */
  private String userId;

  /**
   * 是否有竞业协议
   */
  private Integer competition;

  /**
   * 是否含有特定公司
   */
  private Integer specialCompany;

  /**
   * 特定公司名称
   */
  private String specialCompanyTxt;

  /**
   * 签约公司
   */
  private String company;

  /**
   * 股权管理方式
   */
  private Integer stockManage;

  /**
   * SERS协议签署日期
   */
  private String sersSignDate;

  /**
   * 笔数选择，1-5笔
   */
  private Integer stockNumber;

  /**
   * SERS保留总数
   */
  private Integer sersKeepNumber;

  /**
   * SERS作废总数
   */
  private Integer sersInvalidNumber;

  private String createTime;

  private String createUser;
  /**
   * 等待期
   */
  private String waitTime;
  /**
   * 签署状态
   */
  private String signStatus;
  /**
   * 合同编号
   */
  private String contractNumber;
  /**
   * 姓名
   */
  private String spell;
  /**
   * 身份证号
   */
  private String certNo;
  /**
   * 合同id
   */
  private String contractId;
  /**
   * 竞业时长：月
   */
  private String competitionDuration;
  /**
   * 竞业开始日期
   */
  private String competitionStart;
  /**
   * 竞业结束日期
   */
  private String competitionEnd;
}
