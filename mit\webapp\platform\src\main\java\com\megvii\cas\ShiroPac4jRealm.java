package com.megvii.cas;


import com.megvii.common.IPUtils;
import com.megvii.entity.opdb.Menu;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserRole;
import com.megvii.service.UserMenuService;
import com.megvii.service.UserService;
import io.buji.pac4j.realm.Pac4jRealm;
import io.buji.pac4j.token.Pac4jToken;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.pac4j.core.profile.CommonProfile;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @创建人 cl
 * @创建日期 2019/10/16
 * @描述
 */
@Slf4j
public class ShiroPac4jRealm extends Pac4jRealm {

    @Autowired
    private UserService userService;

    @Autowired
    private UserMenuService userMenuService;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(final PrincipalCollection principals) {
        User user = (User) super.getAvailablePrincipal(principals);
//        log.info("登录用户：{}", JSONObject.toJSONString(user) );
        // 权限
        List<Menu> menuList = userMenuService.getMenuListByUserId(user.getUserId());
        Set<String> permsSet = new HashSet<String>();
        if(menuList != null && menuList.size() > 0){
            permsSet = menuList.stream().filter(menu -> StringUtils.isNotEmpty(menu.getMenuCode())).map(u -> u.getMenuCode()).collect(Collectors.toSet());
        }
        //角色
        List<UserRole> roles = userMenuService.getRoleByUserId(user.getUserId());
        List<String> roleCodes = new ArrayList<>();
        if (roles != null || roles.size() > 0) {
            for (UserRole role : roles) {
                roleCodes.add(role.getRoleCode());
            }
        }

        SimpleAuthorizationInfo simpleAuthorizationInfo = new SimpleAuthorizationInfo();
        simpleAuthorizationInfo.addStringPermissions(permsSet);
        simpleAuthorizationInfo.addRoles(roleCodes);
        return simpleAuthorizationInfo;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        Pac4jToken token = (Pac4jToken)authenticationToken;
        List<CommonProfile> profiles = token.getProfiles();
        String loginId =profiles.get(0).getId();
        User user = userService.getUserByUserId(loginId);
        if("".equals(loginId.trim())||user==null){
//            throw new UnknownAccountException("用户不存或没权限, 请检查 用户名密码! 请访问 '域名+/api/logout'进行退出单点登录后重新登录");
            throw new UnknownAccountException("User does not exist or does not have permission, Please check username and password! Please visit 'https://dhr.megvii-inc.com/performance/api/logout' to log out of single sign-on and log in again");
        }
        try{
            //写入登录日志
            String ipAddress = IPUtils.getRealIP(httpServletRequest);
//            user.setHost(ipAddress);
            log.info("登录人：{}，登录IP：{}",loginId,ipAddress);
        }catch (Exception e){
        }
        PrincipalCollection principalCollection = new SimplePrincipalCollection(user, this.getName());
        return new SimpleAuthenticationInfo(principalCollection, profiles.hashCode());
    }

    /**
     * 重写方法,清除当前用户的的 授权缓存
     * @param principals
     */
//    @Override
//    public void clearCachedAuthorizationInfo(PrincipalCollection principals) {
//        super.clearCachedAuthorizationInfo(principals);
//    }

    /**
     * 自定义方法：清除所有 授权缓存
     */
//    public void clearAllCachedAuthorizationInfo() {
//        getAuthorizationCache().clear();
//    }


}


