package com.megvii;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.PanguRestClient;
import com.megvii.common.FileUtil;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.ImageFile;
import com.megvii.entity.opdb.User;
import com.megvii.service.EntryService;
import com.megvii.service.PanguService;
import com.megvii.service.UserService;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2021/6/8 10:15
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestPangu {

	@Autowired
	private PanguRestClient panguRestClient;

	@Autowired
	private EntryService entryService;

	@Autowired
	private PanguService panguService;

	@Autowired
	private UserService userService;


	@Test
	public void testGetSign() throws Exception {
//		JSONObject body = new JSONObject();
//		body.put("deviceType", 3);
//
//		long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));

//		String sign = panguRestClient.getSign("/v1/api/device/list", "POST", null, body.toString(), "appkey1", "1622617035", "123456");

//		System.out.println(sign);

		panguRestClient.searchDeviceList("1234567", "appkey1");

		System.out.println(1);

	}


	@Test
	public void testUpload() {
		EntryUserInfo userInfo = entryService.selectUserInfoByUserid("jiangyuhong02");
		ImageFile photoCertFile = FileUtil.getByteFromBase64(userInfo.getPhotoCert(),userInfo.getUserId());

		try {
			panguRestClient.uploadImage(photoCertFile, "jiangyuhong02", "appkey1", "123456");
		} catch (Exception e) {
			System.out.println(1);
		}

	}

	@Test
	public void testAddUser() throws Exception {
//		JSONObject user = new JSONObject();
//		user.put("name", "赵寒");
//		user.put("type", 1);
//		user.put("uuid", "zhaohan_105384");
//		user.put("imageUri", "_ZzEwMF9mb3JldmVyQnVja2V0_fd203c4393a545bcb4639ef4f1d2a066");
//		user.put("code", "105384");
//
//		JSONArray array = new JSONArray();
//		array.add(user);
//
//		JSONObject data = new JSONObject();
//		data.put("personList", array);
//
//		panguRestClient.addUser(data, "jiangyuhong02", "appkey1", "123456");

		System.out.println(1);

		EntryUserInfo userInfo = entryService.selectUserInfoByUserid("jiangyuhong02");
		ImageFile photoCertFile = FileUtil.getByteFromBase64(userInfo.getPhotoCert(),userInfo.getUserId());
		User user1 = userService.getUserByUserId("jiangyuhong02");
		User user2 = userService.getUserByUserId("jiangyuhong");
		User user3 = userService.getUserByUserId("zhaohan");
		User user4 = userService.getUserByUserId("fuqiang");
		User user5 = userService.getUserByUserId("zhangcheng04");

		panguService.addPanguUser(user1, photoCertFile);
		panguService.addPanguUser(user2, photoCertFile);
		panguService.addPanguUser(user3, photoCertFile);
		panguService.addPanguUser(user4, photoCertFile);
		panguService.addPanguUser(user5, photoCertFile);

		System.out.println(1);

	}

	@Test
	public void testDeleteUser() throws Exception {
//		List list = new ArrayList();
//		list.add("jiangyuhong02_55");
//
//		JSONObject data = new JSONObject();
//		data.put("uuidList", list);
//
//		panguRestClient.deleteUser(data, "jiangyuhong02", "appkey1", "123456");

		System.out.println(1);

		String a = panguService.deletePanguUser("jiangyuhong02");
		panguService.deletePanguUser("jiangyuhong");
		panguService.deletePanguUser("zhaohan");
		panguService.deletePanguUser("fuqiang");
		panguService.deletePanguUser("zhangcheng04");

		System.out.println(1);

	}

	@Test
	public void testRandom() {
		ThreadLocalRandom random = ThreadLocalRandom.current();
//		int specialSit = random.nextInt(10);
//
//		String[] array = new String[]{"1","2"};
//		System.out.println(array[random.nextInt(array.length - 1)]);

//		StringBuilder nonce = new StringBuilder();
//
//		for (int i = 0; i < random.nextInt(4,10); i ++) {
//			nonce.append(random.nextInt(10));
//		}
//
//		System.out.println(nonce);

		for (int i = 0; i < 20; i ++) {
			System.out.println(random.nextInt(10000,999999));
		}




	}


}
