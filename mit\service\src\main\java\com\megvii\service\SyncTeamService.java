package com.megvii.service;


import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/25 14:13
 */
@PropertySource(value = "classpath:mail.properties",encoding = "UTF-8")
@Service
public class SyncTeamService {

    Logger logger= LoggerFactory.getLogger(SyncTeamService.class);

    @Value("${SYNC_SF_TEAM_MAIL}")
    private String mailAddress;

    @Autowired
    private TeamService teamService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private UserService userService;

    @Autowired
    private DingdingService dingdingService;
    /**
     * 传入code
     * 检查是否存在在职员工
     * 禁用部门
     * @param code
     * @return
     */
    public String disableTeam(String code) {
        StringBuilder message = new StringBuilder();

        Team team = teamService.getTeamByCode(code);
        if (team == null) {
            message.append("MIT禁用部门错误!查不到code为" + code + "的部门");
            logger.error(message.toString());
            return message.toString();
        }

        UserFilter filter = new UserFilter();
        filter.setTeamId(team.getId());
        filter.setStatusCode(0);

        List<User> userList = userService.getUsersByFilter(filter);
        if (userList.size() > 0) {
            message.append("MIT禁用部门错误!name:" + team.getName() + "code:" + team.getSfCode() + "  存在在职员工。");
            logger.error(message.toString());
            return message.toString();
        }

        String oldName = team.getName();
        message.append("MIT禁用部门:名称:" + oldName + "******code:" + team.getSfCode());

        //jinyong dingding rizhi
        if (team.getDingdingId() == null) {
            message.append("******禁用钉钉部门" + oldName + "[失败],没有钉钉ID");
            logger.error("******禁用钉钉部门" + oldName + "[失败]","名称:" + oldName + "<br>编码:" + team.getSfCode());
            mailSendService.sendMail( "禁用钉钉部门" + oldName + "[失败]!没有钉钉ID","名称:" + oldName + "<br>编码:" + team.getSfCode(), mailAddress);
        } else {
            try {
                message.append("禁用钉钉部门[成功]，代码注释，手动执行");
                //删除钉钉部门同时，删掉部门leader，hrg
                if(StringUtils.isNotEmpty(team.getTeamLeader())){
                    dingdingService.updateUserDingdingDept(team.getTeamLeader(),Integer.valueOf(team.getDingdingId()),false);
                }
                if(StringUtils.isNotEmpty(team.getHrg())){
                    dingdingService.updateUserDingdingDept(team.getHrg(),Integer.valueOf(team.getDingdingId()),false);
                }
//                message.append(dingDingRestClient.deleteDept(team.getDingdingId()));
            } catch (Exception e) {
                logger.error("禁用钉钉部门[错误]");
            }
        }

        team.setName("old-" + oldName);
        team.setSfCode("");
        teamService.updateTeam(team);

        return message.toString();
    }
}
