package com.megvii.entity.opdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志实体
 *
 * <AUTHOR>
 * @date 2020/04/17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_operate_log")
public class OperateLogDO implements Serializable {

    public OperateLogDO(String operateUserId, String requestUrl, String requestParams, String requestIp, String operateType) {
        this.operateUserId = operateUserId;
        this.operateDate = new Date();
        this.requestUrl = requestUrl;
        this.requestParams = requestParams;
        this.requestIp = requestIp;
        this.operateType = operateType;
    }

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作人
     */
    private String operateUserId;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date operateDate;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 请求IP（real）
     */
    private String requestIp;

    /**
     * 响应结果
     */
    private String responseResult;

    /***
     * 操作耗时（单位ms）
     */
    private int timeConsuming;

    @TableField(exist=false)
    private String firstName;

    private String operateType;

}
