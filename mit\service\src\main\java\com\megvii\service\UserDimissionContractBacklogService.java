package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.megvii.client.ContractRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.ParameterUtil;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserDimissionContractBacklogPO;
import com.megvii.entity.opdb.UserDimissionContractSnapshotExtendPO;
import com.megvii.entity.opdb.UserDimissionContractSnapshotPO;
import com.megvii.entity.opdb.contract.*;
import com.megvii.mapper.UserChangeContractBacklogMapper;
import com.megvii.mapper.UserContractMapper;
import com.megvii.mapper.UserDimissionContractBacklogMapper;
import com.megvii.mapper.filter.UserBacklogFilter;
import com.megvii.service.UserDimissionContractBacklogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.megvii.service.contract.ContractChangelLogService;
import com.megvii.service.contract.ContractRestService;
import com.megvii.service.contract.ContractService;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.apache.bcel.generic.RET;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sun.rmi.runtime.Log;

import javax.annotation.Resource;
import java.util.List;
import java.util.logging.Logger;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Service
public class UserDimissionContractBacklogService {
  private org.slf4j.Logger logger = LoggerFactory.getLogger(UserDimissionContractBacklogService.class);
  @Autowired
  private UserDimissionContractBacklogMapper userDimissionContractBacklogMapper;

  @Autowired
  private UserDimissionContractSnapshotService userDimissionContractSnapshotService;

  @Autowired
  private UserDimissionContractSnapshotExtendService userDimissionContractSnapshotExtendService;

  @Autowired
  private ContractRestClient contractRestClient;

  @Autowired
  private ContractRestService contractRestService;

  @Autowired
  private UserContractMapper userContractMapper;

  @Autowired
  private UserService userService;

  @Autowired
  private ContractChangelLogService changelLogService;


  public List<UserDimissionContractBacklogPO> getBacklogByFilter(UserBacklogFilter filter) {
    return userDimissionContractBacklogMapper.selectBacklogByFilter(filter);
  }

  public List<String> selectAllBacklog(){
    return userDimissionContractBacklogMapper.selectAllBacklog();
  }

  public Boolean addBacklog(UserDimissionContractBacklogPO po){
    return userDimissionContractBacklogMapper.addBacklog(po);
  }

  public UserDimissionContractBacklogPO getBacklogByDismissionId(String Id) {
    return userDimissionContractBacklogMapper.getBacklogByDismissionId(Id);
  }

  public Boolean updateBacklogInfo(UserDimissionContractBacklogPO po){
    return userDimissionContractBacklogMapper.updateBacklogInfo(po);
  }

  public Boolean updateBacklogflowStatus(UserDimissionContractBacklogPO po){
    return userDimissionContractBacklogMapper.updateBacklogflowStatus(po);
  }
  public Boolean updateContractFile(UserDimissionContractBacklogPO po){
    return userDimissionContractBacklogMapper.updateContractFile(po);
  }
  /**
   * 创建合同为草稿状态
   *
   * @param snapshot
   * @return
   */
  public String createContractDraft(UserDimissionContractSnapshotPO snapshot,List<UserDimissionContractSnapshotExtendPO> extendPOS,User user,UserDimissionContractBacklogPO backlog,JSONObject dismissionStaff) throws Exception {
    //合同body，不发给员工
    JSONObject data = contractRestService.createDismissionContractData(snapshot, extendPOS,false, null,user,backlog,dismissionStaff);
    //创建并签署合同
    String contractId = contractRestClient.createContract(data);
    return contractId;
  }

  /**
   * 编辑合同，修改草稿合同内容字段
   * @param snapshot
   * @param extendPOS
   * @return
   * @throws Exception
   */
  public Boolean editContractDraft(UserDimissionContractSnapshotPO snapshot,List<UserDimissionContractSnapshotExtendPO> extendPOS,User user,UserDimissionContractBacklogPO backlog,JSONObject dismissionStaff) throws Exception {
    //合同body，不发给员工
    JSONObject data = contractRestService.editDismissionContractData(snapshot, extendPOS,false, null,user,backlog,dismissionStaff);
    //创建并签署合同
    return contractRestClient.editContract(data);
  }

  /**
   * 根据合同id删除草稿状态合同
   * @param contractId
   * @return
   * @throws Exception
   */
  public Boolean deleteContractDraft(String contractId) throws Exception{
    return contractRestClient.deleteContract(contractId);
  }
  /**
   * 将草稿合同起用为正式，发给员工签字
   * @param snapshot
   * @return
   * @throws Exception
   */
  public String sendContractToUser(UserDimissionContractSnapshotPO snapshot) {
    try {
      Boolean aBoolean = contractRestClient.sendContract(snapshot.getContractId());
      if(!aBoolean){
        return "发起签署失败，请联系开发！";
      }
      return afterSign(snapshot);
    }catch (Exception e){
        logger.error("发起签署异常，请联系开发："+e.getMessage());
        return "发起签署异常，请联系开发："+e.getMessage();
    }
  }
  /**
   * 离职员工签署回调
   * @param contract
   * @return
   */
  public Boolean signCallback(UserContract contract){
    UserDimissionContractBacklogPO backlogByDismissionId = getBacklogByDismissionId(contract.getDismissionId());
    backlogByDismissionId.setFlowStatus("2");
    backlogByDismissionId.setStatus(BacklogStatus.WAITSEAL);
    return userDimissionContractBacklogMapper.updateBacklogflowStatus(backlogByDismissionId);
  }
  /**
   * 合同成功发起签署后的操作，此时为员工签署中
   *
   * @param snapshot
   * @return
   */
  @Transactional
  public String afterSign(UserDimissionContractSnapshotPO snapshot) throws Exception {
    String userId = snapshot.getUserId();
    User user = userService.getUserByUserId(userId);
    UserDimissionContractBacklogPO backlog = userDimissionContractBacklogMapper.getBacklogByDismissionId(snapshot.getDimissionId());
    //合同表插入数据
    UserContract contract = new UserContract();
    BeanUtils.copyProperties(snapshot, contract);
//    contract.setContractId(snapshot.getContractId());
    contract.setContractStatus(ContractStatus.SIGNING);
    contract.setContractType("劳动合同");
    contract.setContractAttribute("固定期");
    contract.setContractCount(String.valueOf(user.getContractCount()));
    contract.setCreatorUserId(snapshot.getCreateUser());
    contract.setWorkNumber(user.getWorkNumber());
    contract.setDismissionId(snapshot.getDimissionId());
    contract.setStartDate(DateTimeUtil.getLocalDateTime(backlog.getInitialStartTime()));
    contract.setEndDate(DateTimeUtil.string2DateYMD(DateTimeUtil.dateToString(backlog.getDismissionDate())));
    contract.setEffectDate(DateTimeUtil.string2DateYMD(DateTimeUtil.dateToString(backlog.getDismissionDate())));
    contract.setSelfEmail(backlog.getSelfEmail());
    userContractMapper.insertUserContract(contract);
    //删除快照和附件
    backlog.setFlowStatus("1");
    userDimissionContractBacklogMapper.updateBacklogflowStatus(backlog);//更新离职合同列表流程状态，使其进到下一个tab
    userDimissionContractBacklogMapper.deleteOtherBacklog(backlog.getDismissionId(),backlog.getAccount());
    //正式开始签署后将不能修改合同，所以需要删除快照
    userDimissionContractSnapshotService.deleteSnapshot(snapshot.getDimissionId());
    userDimissionContractSnapshotExtendService.deleteSnapshotExtend(snapshot.getUserId());
    //当前合同状态变更为签署中
    user.setContractStatus(ContractStatus.SIGNING);
    userService.updateUser(user);
    //change log
    changelLogService.insertChangeLog(userId, contract.getContractNumber(), ContractChangeLogType.START_SIGN);

    return "发起签署成功";
  }

  public Boolean deleteOtherBacklog(UserDimissionContractBacklogPO backlog){
    return userDimissionContractBacklogMapper.deleteOtherBacklog(backlog.getDismissionId(),backlog.getAccount());
  }
  /**
   * 员工签字完成后，er调用盖章
   * @param companyName
   * @param contractId
   * @return
   * @throws Exception
   */
  public Boolean sealContract(String companyName, String contractId) throws Exception {
    JSONArray documentId = contractRestClient.getContractDetails(contractId);
    JSONObject jsonObject = contractRestService.signDismissionByCompany(companyName, documentId, contractId);
    Boolean sign = contractRestClient.signByCompany(jsonObject);
    return sign;
  }

  /**
   * 获取合同编号
   * @param workNumber
   * @return
   */
  public String getContractNumber(String workNumber) {
    String contractNumber = userContractMapper.selectContractNumberByWorkNumber(workNumber + "-");
    if (contractNumber == null) {
      return workNumber + "-01";
    } else {
      Integer number = Integer.valueOf(contractNumber.split("-")[1]);
      if (number < 9) {
        return contractNumber.split("-")[0] + "-0" + (number + 1);
      } else {
        return contractNumber.split("-")[0] + "-" + (number + 1);
      }
    }
  }
}
