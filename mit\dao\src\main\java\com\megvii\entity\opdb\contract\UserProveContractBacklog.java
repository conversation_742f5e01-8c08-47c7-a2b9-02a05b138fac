package com.megvii.entity.opdb.contract;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserProveContractBacklog {

    private int id;
    private String dhrId;
    private String userId;
    private String workNumber;
    /**
     * 变更类型:1=收入证明 2 =在职证明 3=实习证明 4=其他证明
     */
    private String xtype;

    /**
     * 已拉取 从DHR拉取
     * 已签署 已签署完成
     * 已完成 DHR已结束操作
     */
    private String status;
    private LocalDateTime createTime;
    private String updateTime;

    private String contractId;

    private String approve;

    //dhr审批人userid
    private String approveUserId;
}
