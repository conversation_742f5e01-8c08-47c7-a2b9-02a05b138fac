package com.megvii;

import com.megvii.client.MeginRestClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2021/7/21 11:43
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestMegin {

	@Autowired
	private MeginRestClient meginRestClient;

	@Test
    public void testDismission() {
		meginRestClient.disableMeginUser("miao");
    }



}
