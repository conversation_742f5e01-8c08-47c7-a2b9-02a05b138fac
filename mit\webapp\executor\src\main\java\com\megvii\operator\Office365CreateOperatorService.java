package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.Office365Service;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class Office365CreateOperatorService extends BaseOperatorService  {

    @Autowired
    private Office365Service office365Service;

    @Autowired
    private UserService userService;

    @Autowired
    private Office365CreateOperatorService office365CreateOperatorService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId=paraMap.get("userId");
        if(userId != null){
            return OperateByUserId((String) userId);
        } else {
            List<User> users = userService.getUserListNeedToOffice365();
            if (users.size() > 0) {
                StringBuilder msg=new StringBuilder();
                for (User user : users) {
                    OperateResult result = office365CreateOperatorService.operateOnce(paraMap, user);
                    msg.append(result.getMsg()+"<br/>");
                }
                return new OperateResult(1,msg.toString());
            } else {
                return new OperateResult(1, "没有要激活的账号");
            }
        }
    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        User user = (User) params[0];
        String result = office365Service.addEmailAcount(user);

        return new OperateResult(1, result);

    }

    public OperateResult OperateByUserId(String userId) throws Exception {
        User user = userService.getUserByUserId(userId);
        if (user != null) {
            String result = office365Service.addEmailAcount(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1,userId + "信息不存在");
        }
    }



}
