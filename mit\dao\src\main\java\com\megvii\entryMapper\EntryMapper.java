package com.megvii.entryMapper;

import com.megvii.entity.entry.EntryUserInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2019/11/18 21:13
 */
public interface EntryMapper {


    /**
     * 根据id查询 IT_user_info
     * @param id
     * @return
     */
    EntryUserInfo selectBasicInfoById(@Param("id") int id);


    /**
     * 根据userId查询 IT_user_info 信息  不包含照片信息
     * @param userId
     * @return
     */
    EntryUserInfo selectBasicInfoByUserid(@Param("userId") String userId);


    /**
     * 根据userId查询 IT_user_info 信息  包含照片信息
     * @param userId
     * @return
     */
    EntryUserInfo selectUserInfoByUserid(@Param("userId") String userId);

    /**
     * 向 IT_user_info 插入数据 OA回传信息后使用
     * @param info
     * @return
     */
    int addEntryUserInfo(EntryUserInfo info);


    /**
     * 回流人员使用旧info记录
     * @param info
     * @return
     */
    int updateEntryUserInfo(EntryUserInfo info);

    /**
     * 查询证件照
     * @param userId
     * @return
     */
    String getPhotoCert(String userId);


    /**
     * 查询生活照
     * @param userId
     * @return
     */
    String getPhotoLife(String userId);

    /**
     *
     * 删除密码
     * @param userId 登录名
     */
    int deletePsw(String userId);


    /**
     * @return   返回大于指定日期expectTime ，IT_user_info表中saved字段不为1的简略数据
     * @Param    入职日期
     *
     */
    List<EntryUserInfo> getSimpleUserInfoListByExpectDate(String expectTime);


}
