package com.megvii;

import com.megvii.client.OaRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.Team;
import com.megvii.service.*;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 * @date 2019/10/23 11:15
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestOA {

    @Autowired
    private OaRestClient oaRestClient;

    @Autowired
    private UserService userService;

    @Autowired
    private OaService oaService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private DingdingService dingdingService;

    @Test
    public void testTemp() {
        MultiValueMap<String, String> params= new LinkedMultiValueMap<String, String>();
        params.add("sqr", "104656");
        params.add("sqrq", DateTimeUtil.date2String(LocalDateTime.now()));


        try {
            oaRestClient.sendOAFlow(params, "441", null);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Test
    public void testEntry() {

        Map map = new HashMap();
        map.put("team", "T497");
        map.put("biaozhu", "是");
        map.put("user_id", "jiangyuhong");
        map.put("spell", "姜宇鸿");



//        dingdingService.sendOa2MitErrorHook( "OA回传部门与tag不匹配,请撤回OA流程修改后从新审批。  \n  ##### " + map.get("user_id") + "  " + map.get("spell"));
//        User user = userService.getUserByUserId("jiangyuhong");
//
//        oaService.createEntryFlow(user);

//        dingdingService.sendEntryDelayHook("姜宇鸿 jiangyuhong 2020-01-01 -> 2020-12-31");

        System.out.println(1);

        String date = DateTimeUtil.date2String2(DateTimeUtil.string2DateYMD("2021-01-04"));

        checkService.getHireDate(date, "正式员工");


//        boolean result = checkService.checkDataTeamAndTag(map);

        System.out.println(1);


    }

    @Test
    public void testTopParent() {

        if (checkService.checkRTeam("数智产业研究院-ISV生态部")) {
            System.out.println(1);
        } else {
            System.out.println(2);
        }



    }

    @Test
    public void testE9() {
        Map<String, String> map = new HashMap<>();
        map.put("worknumber", "104656");
        map.put("dimissionDate", DateTimeUtil.date2String(LocalDateTime.now()));
        map.put("handoverWorknumber", "101326");
        map.put("workBase", "融科");
        map.put("spell", "姜宇鸿");
        map.put("employType", "正式员工");

        if (oaService.sendE9DimissionFlow(map)) {
            System.out.println(2);
        }



        System.out.println(1);

    }


}
