package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.RemindService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/2/18 15:09
 */
@Service
public class TempEntryOperatorService extends BaseOperatorService {

    @Autowired
    private TempEntryOperatorService tempEntryOperatorService;

    @Autowired
    private RemindService remindService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        OperateResult result = tempEntryOperatorService.operateOnce(paraMap, params);

        return new OperateResult(1, result.getMsg());
    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        String result = remindService.sendTempEntryRemind();
        return new OperateResult(1, result);

    }





}
