import { Injectable } from '@angular/core'
import { DatePipe } from '@angular/common'

@Injectable({
  providedIn: 'root'
})
export class UtilsService {
  constructor(private datePipe: DatePipe) {}
  public copy(originalData): any {
    return typeof originalData === 'object'
      ? JSON.parse(JSON.stringify(originalData))
      : originalData
  }

  public paramsSerialize(params: any): string {
    if (typeof params !== 'object') {
      return params
    }
    const result = []
    for (const key in params) {
      if (params.hasOwnProperty(key)) {
        const value = params[key]
        result.push(key + '=' + (typeof value === 'object' ? JSON.stringify(value) : value))
      }
    }
    return result.join('&')
  }

  public mergeParams(...params: any[]): any {
    return params.reduce((prev, next) => Object.assign(prev, next), {})
  }

  public parseDataToString(dateArr: Date[], format: string) {
    return dateArr.length === 2
      ? `${this.datePipe.transform(dateArr[0], format)},${this.datePipe.transform(
          dateArr[1],
          format
        )}`
      : dateArr
  }
  public arrConcatNoRepeat(arr1: any = [], arr2: any = []) {
    arr1.push(...arr2)
    return Array.from(new Set(arr1))
  }
}
