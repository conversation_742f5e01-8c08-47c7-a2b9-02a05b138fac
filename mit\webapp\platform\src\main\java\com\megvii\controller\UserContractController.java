package com.megvii.controller;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.megvii.common.DateTimeUtil;
import com.megvii.controller.api.*;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.ExcelType;
import com.megvii.entity.opdb.OperateTypeEnum;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.*;
import com.megvii.mapper.filter.UserBacklogFilter;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.ExcelService;
import com.megvii.service.UserService;
import com.megvii.service.contract.ChangeContractBacklogService;
import com.megvii.service.contract.ContractBacklogService;
import com.megvii.service.contract.ContractService;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2020/4/16 16:42
 */
@RestController
@RequestMapping("/api/user")
public class UserContractController {

	@Autowired
	private UserService userService;

	@Autowired
	private ContractBacklogService contractBacklogService;
	@Autowired
	private ChangeContractBacklogService changeContractBacklogService;
	@Autowired
	private ContractService contractService;

	@Autowired
	private ExcelService excelService;
	@PostMapping("/info")
	@ResponseBody
	@OperateLog(operType= OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity updateUserInfo(@RequestBody UserRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();

		try {
			User user = userService.getUserByUserId(request.getUserId());
			user.setEmployBase(request.getEmployBase());
			user.setPosition(request.getPosition());
			user.setAddress(request.getAddress());
			user.setCompany(request.getCompany());
			user.setCertType(request.getCertType());
			user.setCertNo(request.getCertNo());
			user.setExpectDate(DateTimeUtil.string2DateYMD(request.getExpectDate()));
			user.setMemo(request.getMemo());

			userService.updateUser(user);
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("保存失败");
		}
		return response;
	}

	@PostMapping("/addContract")
	@ResponseBody
	@OperateLog(operType= OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity addContract(@RequestBody ContractCustomizeVO request){
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			UserContract contract = new UserContract();
			String userId = request.getUserId();
			User user = userService.getUserByUserId(userId);

			contract.setContractSubject(request.getSubject());
			contract.setCompany(request.getCompany());
			contract.setStartDate(!request.getStartDate().isEmpty() ?DateTimeUtil.string2DateYMD(request.getStartDate()) : null);
			contract.setEndDate(!request.getEndDate().isEmpty() ? DateTimeUtil.string2DateYMD(request.getEndDate()) : null);
			contract.setEffectDate(!request.getStartDate().isEmpty() ?DateTimeUtil.string2DateYMD(request.getStartDate()) : null);
            contract.setUserContractFile(request.getUserContractFile());

            contract.setUserId(userId);
			contract.setContract(user.getCompany());
			contract.setEmployBase(user.getEmployBase());
			contract.setCertType(user.getCertType());
			contract.setCertNo(user.getCertNo());
			contract.setAddress(user.getAddress());
			contract.setContractCell(user.getCell());
			contract.setPosition(user.getPosition());
			contract.setSpell(user.getSpell());
			contract.setWorkNumber(user.getWorkNumber());
			contract.setContractAttribute(user.getContractCount() == 3 ? "无固定期":"固定期");
			contract.setContractCount(String.valueOf(user.getContractCount()));
			contract.setContractNumber(contractService.getContractNumberFromUserAndUserContract(userId));
			contract.setSignStatus("自定义协议");
			contract.setContractType("劳动合同");
			contract.setUpdatorUserId("auto");
			contract.setCreatorUserId("");
			contract.setContractStatus(ContractStatus.COMPLETE);

			if (contractService.insertUserContract(contract)){
				user.setContractStatus(ContractStatus.COMPLETE);
				userService.updateUser(user);
			}
			response.setMsg("success");
			response.setCode(ResponseDataEntity.OK);
		}catch (Exception e){
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("保存失败");
		}
		return response;
	}


	@GetMapping("/info")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getUserInfo(@RequestParam("userId")String userId) {
		ResponseDataEntity response = new ResponseDataEntity();

		User user = userService.getUserByUserId(userId);
		if (user != null) {
			response.setCode(ResponseDataEntity.OK);
			response.setData(UserVO.toVO(user));
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg(userId + "不存在");
		}

		return response;
	}


	@PostMapping("/search")
	@ResponseBody
	@RequiresPermissions("MANAGE")
//	@RequiresRoles("CONTRACT_ADMIN")
	public PagingResponse getUserByFilter(@RequestBody UserRequest request) {
		PagingResponse response = new PagingResponse();
		UserFilter filter = new UserFilter();
		filter.setDimissionDateFrom(request.getDismissionDateFrom());
		filter.setDimissionDateTo(request.getDismissionDateTo());
		filter.setExpectDateFrom(request.getStartExpectDate());
		filter.setExpectDateTo(request.getEndExpectDate());
		filter.setWorkNumber(request.getWorkNumber());
		filter.setUserId(request.getUserId());
		filter.setSpell(request.getSpell());
		filter.setTeamId(request.getTeamId()!=null?Integer.valueOf(request.getTeamId()):null);
		filter.setWorkBase(request.getWorkBase());
		filter.setEmployType(request.getEmployType());
		filter.setEntryFlow(request.getEntryFlow());
		filter.setContractStatus(request.getContractStatus());
		filter.setStatusCode(request.getStatus());
		filter.setContractStatusCode(request.getContractStatusCode());
		filter.setEmployTypeCode(request.getEmployTypeCode());
		//标注中心人员使用合同管理
//		filter.setCreateTagCode("0");
		PageHelper.startPage(request.getPageNumber(),request.getPageSize());
		List<User> users = userService.getUsersByFilter(filter);
		PageInfo<User> pageInfo = new PageInfo<User>(users);
		response.setCode(ResponseDataEntity.OK);
		response.setData(UserListVO.toVOs(users));
		response.setMsg("success");
		response.setPageNumber(pageInfo.getPageNum());
		response.setTotalPage(pageInfo.getPages());
		response.setTotalCount(pageInfo.getTotal());

		return response;
	}

	/**
	 * 导出合同
	 * @return
	 */
	@GetMapping("/exportContract")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public void exportContract(@RequestParam("json") String json,HttpServletResponse response) {
		try {
			UserRequest request=JSON.parseObject(json,UserRequest.class);
			UserFilter filter = new UserFilter();
			filter.setExpectDateFrom(request.getStartExpectDate());
			filter.setExpectDateTo(request.getEndExpectDate());
			filter.setWorkNumber(request.getWorkNumber());
			filter.setUserId(request.getUserId());
			filter.setSpell(request.getSpell());
			filter.setTeamId(request.getTeamId()!=null?Integer.valueOf(request.getTeamId()):null);
			filter.setWorkBase(request.getWorkBase());
			filter.setStatusCode(request.getStatus());
			filter.setEmployType(request.getEmployType());
			filter.setEntryFlow(request.getEntryFlow());
			filter.setContractStatus(request.getContractStatus());
			List<ContractExportDto> contracts = contractService.getContractsByFilter(filter);
			List<ContractExportVO> contractExportVOS = ContractExportVO.toVOs(contracts);
			excelService.exportExcel(ContractExportVO.class, ExcelType.XLSX, contractExportVOS, "合同信息表",response);
			response.getOutputStream().flush();
			response.getOutputStream().close();
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	/**
	 * 获取续签列表
	 * @param request
	 * @return
	 */
	@PostMapping("/backlog/search")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public PagingResponse getUserBacklog(@RequestBody UserRequest request) {
		PagingResponse response = new PagingResponse();

		UserBacklogFilter filter = new UserBacklogFilter();
		filter.setExpectDateFrom(request.getStartExpectDate());
		filter.setExpectDateTo(request.getEndExpectDate());
		filter.setWorkNumber(request.getWorkNumber());
		filter.setUserId(request.getUserId());
		filter.setSpell(request.getSpell());
		filter.setTeamId(request.getTeamId()!=null?Integer.valueOf(request.getTeamId()):null);
		filter.setWorkBase(request.getWorkBase());
		filter.setEmployType(request.getEmployType());
		filter.setEntryFlow(request.getEntryFlow());
		filter.setContractStatus(request.getContractStatus());
		filter.setStatusCode(request.getStatus());
		filter.setBacklogStatus(BacklogStatus.PULL);
//		filter.setCreateTagCode("0");

		PageHelper.startPage(request.getPageNumber(),request.getPageSize());
		List<UserContractBacklog> users = contractBacklogService.getBacklogByFilter(filter);
		PageInfo<UserContractBacklog> pageInfo = new PageInfo<UserContractBacklog>(users);
		response.setCode(ResponseDataEntity.OK);
		response.setData(UserListVO.backlogToVOs(users));
		response.setMsg("success");
		response.setPageNumber(pageInfo.getPageNum());
		response.setTotalPage(pageInfo.getPages());
		response.setTotalCount(pageInfo.getTotal());

		return response;

	}

	/**
	 * 获取续签待作废列表
	 * @param request
	 * @return
	 */
	@PostMapping("/backlog/search2")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public PagingResponse getUserBacklog2(@RequestBody UserRequest request) {
		PagingResponse response = new PagingResponse();
		UserBacklogFilter filter = new UserBacklogFilter();
		filter.setWorkNumber(request.getWorkNumber());
		filter.setUserId(request.getUserId());
		filter.setSpell(request.getSpell());
		filter.setTeamId(request.getTeamId()!=null?Integer.valueOf(request.getTeamId()):null);
		filter.setDimissionTag(BacklogTag.WAITTERMINATE);
		PageHelper.startPage(request.getPageNumber(),request.getPageSize());
		List<UserContractBacklog> users = contractBacklogService.getBacklogByFilter(filter);
		PageInfo<UserContractBacklog> pageInfo = new PageInfo<>(users);
		response.setCode(ResponseDataEntity.OK);
		response.setData(UserListVO.backlogToVOs(users));
		response.setMsg("success");
		response.setPageNumber(pageInfo.getPageNum());
		response.setTotalPage(pageInfo.getPages());
		response.setTotalCount(pageInfo.getTotal());
		return response;
	}

	@PostMapping("/backlog/updateBacklogDimissionTag")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity updateBacklogDimissionTag(@RequestParam("userId")String userId){
		UserContractBacklog userContractBacklog = contractBacklogService.selectBacklogByUserId(userId);
		ResponseDataEntity response = new ResponseDataEntity();
		if(userContractBacklog==null){
			response.setCode(ResponseDataEntity.ERROR);
			response.setData("未查到该续签记录："+userId);
			response.setMsg("success");
			return response;
		}
		userContractBacklog.setDimissionTag(BacklogTag.TERMINATED);
		if(contractBacklogService.updateUserContractBacklogTag(userContractBacklog)){
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		}else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData("更新该续签离职状态失败，请联系开发人员："+userId);
			response.setMsg("success");
		}
		return response;
	}

	/**
	 * 获取转签列表
	 * @param request
	 * @return
	 */
	@PostMapping("/changebacklog/search")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public PagingResponse getUserChangeBacklog(@RequestBody UserRequest request) {
		PagingResponse response = new PagingResponse();
//		JSONObject companyConfig = mmisMegviiConfigService.getCompanyConfigByName("北京旷视科技有限公司");
		UserBacklogFilter filter = new UserBacklogFilter();
		filter.setExpectDateFrom(request.getStartExpectDate());
		filter.setExpectDateTo(request.getEndExpectDate());
		filter.setWorkNumber(request.getWorkNumber());
		filter.setUserId(request.getUserId());
		filter.setSpell(request.getSpell());
		filter.setTeamId(request.getTeamId()!=null?Integer.valueOf(request.getTeamId()):null);
		filter.setWorkBase(request.getWorkBase());
		filter.setEmployType(request.getEmployType());
		filter.setEntryFlow(request.getEntryFlow());
		filter.setContractStatus(request.getContractStatus());
		filter.setStatusCode(request.getStatus());
		filter.setBacklogStatus(BacklogStatus.PULL);
		//标注中心人员可以使用合同管理
//		filter.setCreateTagCode("0");
		filter.setXtype("1");
		PageHelper.startPage(request.getPageNumber(),request.getPageSize());
		List<UserChangeContractBacklog> users = changeContractBacklogService.getBacklogByFilter(filter);
		PageInfo<UserChangeContractBacklog> pageInfo = new PageInfo<>(users);
		response.setCode(ResponseDataEntity.OK);
		response.setData(UserListVO.changeBacklogToVOs(users));
		response.setMsg("success");
		response.setPageNumber(pageInfo.getPageNum());
		response.setTotalPage(pageInfo.getPages());
		response.setTotalCount(pageInfo.getTotal());

		return response;

	}

	/**
	 * 获取变更列表
	 * @param request
	 * @return
	 */
	@PostMapping("/changebacklog/search2")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public PagingResponse getUserChangeBacklog2(@RequestBody UserRequest request) {
		PagingResponse response = new PagingResponse();

		UserBacklogFilter filter = new UserBacklogFilter();
		filter.setExpectDateFrom(request.getStartExpectDate());
		filter.setExpectDateTo(request.getEndExpectDate());
		filter.setWorkNumber(request.getWorkNumber());
		filter.setUserId(request.getUserId());
		filter.setSpell(request.getSpell());
		filter.setTeamId(request.getTeamId()!=null?Integer.valueOf(request.getTeamId()):null);
		filter.setWorkBase(request.getWorkBase());
		filter.setEmployType(request.getEmployType());
		filter.setEntryFlow(request.getEntryFlow());
		filter.setContractStatus(request.getContractStatus());
		filter.setStatusCode(request.getStatus());
		filter.setBacklogStatus(BacklogStatus.PULL);
		//标注中心人员可以使用合同管理
//		filter.setCreateTagCode("0");
		filter.setXtype("2");
		PageHelper.startPage(request.getPageNumber(),request.getPageSize());
		List<UserChangeContractBacklog> users = changeContractBacklogService.getBacklogByFilter(filter);
		PageInfo<UserChangeContractBacklog> pageInfo = new PageInfo<>(users);
		response.setCode(ResponseDataEntity.OK);
		response.setData(UserListVO.changeBacklogToVOs(users));
		response.setMsg("success");
		response.setPageNumber(pageInfo.getPageNum());
		response.setTotalPage(pageInfo.getPages());
		response.setTotalCount(pageInfo.getTotal());

		return response;

	}
	@GetMapping("/contract")
	@ResponseBody
	@RequiresPermissions("MANAGE")
    public ResponseDataEntity getUserBacklog(@RequestParam("userId")String userId) {
		ResponseDataEntity response = new ResponseDataEntity();

		UserContractSnapshot snapshot = contractService.getContractSnapshot(userId);
		List<UserContract> contracts = contractService.getContractsByUserId(userId);

		response.setCode(ResponseDataEntity.OK);
		response.setData(ListContractVO.toVOs(contracts, snapshot));
		response.setMsg("success");

		return response;
	}

	/**
	 * 合同信息管理已离职，查询员工合同
	 * @param userId
	 * @return
	 */
	@GetMapping("/manageContract")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getUserContract(@RequestParam("userId")String userId) {
		ResponseDataEntity response = new ResponseDataEntity();

//		UserContractSnapshot snapshot = contractService.getContractSnapshot(userId);
		List<UserContract> contracts = contractService.getContractsByUserId(userId);

		response.setCode(ResponseDataEntity.OK);
		response.setData(ListContractVO.toVOs(contracts, null));
		response.setMsg("success");


		return response;
	}

}
