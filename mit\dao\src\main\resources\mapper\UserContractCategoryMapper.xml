<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserContractCategoryMapper">

    <select id="selectCategoryByCategoryId"  resultType="com.megvii.entity.opdb.contract.UserContractCategory" parameterType="java.lang.String">
        select * from contract_category where category_id = #{categoryId}
    </select>
</mapper>