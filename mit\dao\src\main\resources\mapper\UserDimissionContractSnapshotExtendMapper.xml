<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserDimissionContractSnapshotExtendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.UserDimissionContractSnapshotExtendPO">
        <id column="id" property="id" />
        <result column="num" property="num" />
        <result column="effect_date" property="effectDate" />
        <result column="sers_num" property="sersNum" />
        <result column="user_id" property="userId" />
        <result column="wait_time" property="waitTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, num, effect_date, sers_num, user_id,wait_time
    </sql>

    <insert id="addSnapshotExtend" parameterType="com.megvii.entity.opdb.UserDimissionContractSnapshotExtendPO">
        insert into user_dimission_contract_snapshot_extend (
        id, num, effect_date, sers_num, user_id,wait_time
        )
        values (
        0,
        #{num},
        #{effectDate},
        #{sersNum},
        #{userId},
        #{waitTime}
        )
    </insert>
    <select id="getSnapshotExtend" parameterType="string" resultMap="BaseResultMap">
        select * from user_dimission_contract_snapshot_extend
        where user_id=#{userId};
    </select>
    <delete id="deleteSnapshotExtend" parameterType="string">
        delete from user_dimission_contract_snapshot_extend
        where user_id=#{userId}
    </delete>
</mapper>
