import { NgModule, ModuleWithProviders } from '@angular/core'
import { SharedModule } from '../shared.module'
import { DatePipe } from '@angular/common'

import {
  SendRequest,
  LocalStorage,
  MessageService,
  UtilsService,
  EventsService,
  CreatData
} from './services'

import { NumberFormat } from './pipes'

const SERVICES = [SendRequest, LocalStorage, MessageService, UtilsService, EventsService, CreatData]

const PIPES = [NumberFormat]

@NgModule({
  imports: [SharedModule],
  declarations: [...PIPES], // 声明组件、指令、管道
  providers: [...SERVICES, DatePipe],
  /*
   * 导出核心模块、组件、指令、管道等，注入根目录，其余目录无需再注入
   */
  exports: [...PIPES]
})
export class CoreModule {
  static forRoot(): ModuleWithProviders<CoreModule> {
    return {
      ngModule: CoreModule,
      providers: [...SERVICES] // 核心服务在此导出
    }
  }
}
