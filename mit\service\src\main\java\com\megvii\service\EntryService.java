package com.megvii.service;

import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.PasswordGenerator;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.User;
import com.megvii.entryMapper.EntryMapper;
import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/11/19 10:42
 */
@Service
public class EntryService {

    private Logger logger= LoggerFactory.getLogger(EntryService.class);

    @Autowired
    private EntryMapper entryMapper;

    @Autowired
    private MMISMegviiConfigService mmisMegviiConfigService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private SMSService smsService;

    @Autowired
    private DhrRestClient dhrRestClient;


    /**
     * 根据ID查询
     * @param id
     * @return
     */
    public EntryUserInfo selectUserInfoById(int id) {
        return entryMapper.selectBasicInfoById(id);
    }


    /**
     * @return   返回大于指定日期expectTime ，IT_user_info表中saved字段不为1的简略数据
     * @Param    入职日期
     *
     */
    public List<EntryUserInfo> getSimpleUserInfoListByExpectDate(LocalDateTime localDateTime) {
        return entryMapper.getSimpleUserInfoListByExpectDate(DateTimeUtil.date2String4(localDateTime));
    }



    /**
     * 查询单个人员的全部信息
     * @param userId
     * @return
     */
    public EntryUserInfo selectUserInfoByUserid(String userId) {
        return entryMapper.selectUserInfoByUserid(userId);
    }


    /**
     * 修改阿里云信息保存状态
     * @param id
     */
    public void updateSavedStatus(int id) {
        EntryUserInfo info = new EntryUserInfo();
        info.setId(id);
        info.setSaved(2);

        entryMapper.updateEntryUserInfo(info);

    }


    /**
     * 删除阿里云密码
     * @param userId
     */
    public void deletePSW(String userId) {
        entryMapper.deletePsw(userId);
    }


    /**
     * 查询生活照
     * @param userId
     * @return
     */
    public String getPhotoLife(String userId) {
        return entryMapper.getPhotoLife(userId);
    }


    /**
     * 查询证件照
     * @param userId
     * @return
     */
    public String getPhotoCert(String userId) {
        return entryMapper.getPhotoCert(userId);

    }


    /**
     * 发送信息收集的邮件和短信
     * @param info
     */
    public void sendEntrySmsEmail(User info) {

        mailSendService.sendEntryInfoMail(info, getEntryInfoLimitDate(info.getExpectDate()));
        smsService.sendAliyunEntrySms(info, getEntryInfoLimitDate(info.getExpectDate()));

    }



    /**
     * 发起信息收集 插入entry 数据 IT_user_info
     * @param user
     * @return
     */
    public String insertEntryInfo(User user) {
        if (user.getCreateTag() != null && (user.getCreateTag().equals(2) || user.getCreateTag().equals(3))) {
            logger.info(user.getUserId() + "create_tag is " + user.getCreateTag() + " 跳过发起信息收集");
            return user.getUserId() + "create_tag is " + user.getCreateTag() + " 跳过发起信息收集";
        }

        EntryUserInfo info = new EntryUserInfo();
        info.setUserId(user.getUserId());
        info.setSpell(user.getSpell());
        info.setCell(user.getCell());
        info.setEmail(user.getPriEmail());
        info.setEmployType(user.getEmployType());
        info.setExpectDate(user.getExpectDate());
        info.setTeam(user.getTeam().split("-")[user.getTeam().split("-").length - 1]);
        info.setComputer(user.getComputer());
        info.setOs(user.getOs());
        info.setOriPassword(user.getOriPassword()!=null?user.getOriPassword(): PasswordGenerator.productPassword());
        info.setHrbp(user.getHrbp());
        if (user.getEmployType().indexOf("实习生")!=-1) {
            info.setBank("");
        } else {
            String bank = (String) mmisMegviiConfigService.getConfigJsonByKey("BANK_DICT").get(dhrRestClient.getCompanyValue(user.getCompany()));
            info.setBank(bank!=null?bank:"招商银行");
        }

        EntryUserInfo oldInfo = entryMapper.selectBasicInfoByUserid(user.getUserId());
        //新员工
        if (oldInfo == null) {
            entryMapper.addEntryUserInfo(info);
        } else {
            //回流员工修改旧数据 saved 改为 null
            info.setId(oldInfo.getId());
            info.setSaved(1);
            entryMapper.updateEntryUserInfo(info);
        }

        logger.info(user.getUserId() + "发起信息收集成功");
        return user.getUserId() + "发起信息收集成功";
    }


    /**
     * 计算补全阿里云信息时间
     *
     * @param expectDate
     * @return
     */
    public LocalDateTime getEntryInfoLimitDate(LocalDateTime expectDate) {
        //下午2点
        LocalDateTime limitDate = expectDate.minusHours(-14);

        //当天是周一的 最晚周五填写 其他的前一天
        if (expectDate.getDayOfWeek().equals(DayOfWeek.MONDAY)) {
            return DateTimeUtil.dateAddInt(limitDate, -3);
        } else {
            return DateTimeUtil.dateAddInt(limitDate, -1);
        }
    }




}
