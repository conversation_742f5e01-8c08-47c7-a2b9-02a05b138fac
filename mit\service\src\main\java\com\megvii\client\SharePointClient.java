package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@PropertySource(value = "classpath:microsoft.properties",encoding = "UTF-8")
public class SharePointClient {
    @Value("${OFFICE_TOKEN_URL}")
    private String officeTokenUrl;
    @Value("${OFFICE_CLIENT_ID}")
    private String officeClientId;
    @Value("${OFFICE_CLIENT_SECRET}")
    private String officeClientSecret;
    @Value("${OFFICE_GRANT_TYPE}")
    private String officeGrantType;
    @Value("${OFFICE_SCOPE}")
    private String officeScope;
    @Value("${OFFICE_RESOURCE}")
    private String officeResource;
    @Value("${MAIL_ADDRESS_SUFFIX}")
    private String mailSuffix;
    @Value("${OFFICE_GET_LICENSE}")
    private String getLicenseUrl;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    RedisTemplate redisTemplate;

    private static final String SHAREPOINT_URL = "https://megvii.sharepoint.cn/";
    private static final String FILE_URL = SHAREPOINT_URL + "/_api/web/GetFolderByServerRelativeUrl('/Shared Documents')?&$expand=Folders,Files";
    private static final String LOCAL_PATH = "D:\\sharePoint";
    private static final int BUFFER_SIZE = 4096;
    private static final int THREAD_COUNT = 4;


    public void downloadFilesFromSharePoint() {
        try {

            HttpHeaders headers = getAuthHeader();
            //设置header为json
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

            HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
            JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(FILE_URL,
                    restTemplate.exchange(FILE_URL, HttpMethod.GET,httpEntity,String.class).getBody());
            log.info("resultData:{}",resultData);



            // Parse the JSON response to get file and folder details
            // ...

            // Create directory for current level
            // ...

            // Download files in current directory using multiple threads
//            for (int i = 0; i < files.length(); i++) {
//                JSONObject fileObject = files.getJSONObject(i);
//                String fileName = fileObject.getString("Name");
//                String fileUrl = fileObject.getJSONObject("ServerRelativeUrl").getString("Uri");
//
//                downloadFile(fileUrl, currentDirectory.getAbsolutePath() + File.separator + fileName);
//            }
//
//            // Recursively download files from sub-folders
//            // ...
//
//            connection.disconnect();
//            System.out.println("Files downloaded successfully.");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private  void downloadFile(String fileUrl, String localFilePath) throws Exception {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        // Set SharePoint authentication headers if required
        // connection.setRequestProperty("Authorization", "Bearer <access_token>");

        int fileSize = connection.getContentLength();
        int threadPartSize = (int) Math.ceil((double) fileSize / THREAD_COUNT);

        File downloadedFile = new File(localFilePath);
        RandomAccessFile outputFile = new RandomAccessFile(downloadedFile, "rw");
        outputFile.setLength(fileSize);
        outputFile.close();

        for (int i = 0; i < THREAD_COUNT; i++) {
            int startByte = i * threadPartSize;
            int endByte = Math.min((i + 1) * threadPartSize - 1, fileSize - 1);
            Thread downloadThread = new Thread(new DownloadRunnable(fileUrl, localFilePath, startByte, endByte));
            downloadThread.start();
        }
    }


    private class DownloadRunnable implements Runnable {
        private String fileUrl;
        private String localFilePath;
        private int startByte;
        private int endByte;

        public DownloadRunnable(String fileUrl, String localFilePath, int startByte, int endByte) {
            this.fileUrl = fileUrl;
            this.localFilePath = localFilePath;
            this.startByte = startByte;
            this.endByte = endByte;
        }

        @Override
        public void run() {
            try {
                URL url = new URL(fileUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                // Set SharePoint authentication headers if required
                // connection.setRequestProperty("Authorization", "Bearer <access_token>");

                connection.setRequestProperty("Range", "bytes=" + startByte + "-" + endByte);

                InputStream in = new BufferedInputStream(connection.getInputStream());
                RandomAccessFile raf = new RandomAccessFile(localFilePath, "rw");
                raf.seek(startByte);

                byte[] buffer = new byte[BUFFER_SIZE];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    raf.write(buffer, 0, bytesRead);
                }

                raf.close();
                in.close();
                connection.disconnect();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 优先从redis中获取
     * @return  带Authorization认证的请求头
     */
    public HttpHeaders getAuthHeader(){
        Object token=redisTemplate.opsForValue().get("mitexecutor_microsoft_token");
        if (token==null){
            token =getToken();
            redisTemplate.opsForValue().set("mitexecutor_microsoft_token",token);
            redisTemplate.expire("mitexecutor_microsoft_token",1800, TimeUnit.SECONDS);
        }
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Authorization",(String)token);

        return headers;
    }

    public String getToken(){
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap data=new LinkedMultiValueMap();
        data.add("tenant","organizations");
        data.add("client_id",officeClientId);
        data.add("client_secret",officeClientSecret);
        data.add("grant_type",officeGrantType);
        data.add("scope",officeScope);
        data.add("resource",officeResource);

        HttpEntity<MultiValueMap<String, String>> httpEntity=new HttpEntity<>(data,headers);
        ResponseEntity<JSONObject> responseEntity= restTemplate.postForEntity(officeTokenUrl,httpEntity,JSONObject.class);
        return responseEntity.getBody().getString("token_type")+" "+responseEntity.getBody().getString("access_token");
    }
}
