package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/10/24 17:06
 */
@Component
@PropertySource(value = "classpath:data.${spring.profiles.active}.properties",encoding = "UTF-8")
public class DataRestClient {

    private Logger logger= LoggerFactory.getLogger(DataRestClient.class);

    @Value("${DATA_BASE_URL}")
    private String baseUrl;

    @Value("${DATA_ALL_USERS_URL}")
    private String allUsersUrl;

    @Value("${DATA_ALL_ACTIVITY_TEAM_URL}")
    private String allActivityTeamUrl;

    @Value("${DATA_ALL_POSITION_URL}")
    private String allPositionUrl;

    @Value("${DATA_USERNAME}")
    private String username;

    @Value("${DATA_PASSWORD}")
    private String password;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取中台岗位码表
     * @return
     */
    public JSONArray getAllPosition() {
        HttpHeaders headers=new HttpHeaders();
        headers.set("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString((username + ":" + password).getBytes()));

        HttpEntity<String> httpEntity = new HttpEntity<String>(null ,headers);
        JSONObject data = RequestCorvertUtil.getJSONObjectFromStr(allPositionUrl, restTemplate.exchange(allPositionUrl, HttpMethod.GET, httpEntity, String.class).getBody());

        return data.getJSONObject("body").getJSONArray("rowdata");
    }



    /**
     * 从中台拉取全部激活部门
     * 过滤T310 BD256 特殊部门 不同步
     * @return
     */
    public JSONArray getAllTeam() {
        HttpHeaders headers=new HttpHeaders();
        headers.set("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString((username + ":" + password).getBytes()));

        HttpEntity<String> httpEntity = new HttpEntity<String>(null ,headers);
        JSONObject data = RequestCorvertUtil.getJSONObjectFromStr(allActivityTeamUrl, restTemplate.exchange(allActivityTeamUrl, HttpMethod.GET, httpEntity, String.class).getBody());

        JSONArray result = data.getJSONObject("d").getJSONArray("results");
        return result;
    }


    /**
     * 从中台拉取全部在职员工
     * @return
     */
    public JSONArray getAllUsers() {
        HttpHeaders headers=new HttpHeaders();
        headers.set("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString((username + ":" + password).getBytes()));
        HttpEntity<String> httpEntity = new HttpEntity<String>(null ,headers);
        JSONObject data = RequestCorvertUtil.getJSONObjectFromStr(allUsersUrl, restTemplate.exchange(allUsersUrl, HttpMethod.GET, httpEntity, String.class).getBody());
        JSONArray result = data.getJSONObject("d").getJSONArray("results");
        return result;
    }


    /**
     *
     * @param label
     * @return
     * @throws Exception
     */
    public JSONArray getData(String label) throws Exception {
        String url = baseUrl + "?label=" + label;

        HttpHeaders headers=new HttpHeaders();
        headers.set("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString((username + ":" + password).getBytes()));

        HttpEntity<String> httpEntity = new HttpEntity<String>(null ,headers);
        JSONObject data = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class).getBody());

        if (data.getString("code").equals("200")) {
            return data.getJSONArray("result");
        } else {
            logger.error("拉取中台数据异常:key=" + label + "****msg:" + data.getString("msg"));
            throw new Exception("拉取中台数据异常:key=" + label + "****msg:" + data.getString("msg"));
        }
    }



}
