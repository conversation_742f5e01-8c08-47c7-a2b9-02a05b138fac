package com.megvii.service.contract;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/11/8 15:56
 */
@Service
public class ResourceService {

	Logger logger = LoggerFactory.getLogger(ResourceService.class);

	@Autowired
	private RedisTemplate redisTemplate;

	/**
	 * 根据key列表 从redis中获取对应json
	 *
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public JSONArray getResourceJsonFromRedis(String key) {
		Object data = redisTemplate.opsForValue().get("mitexecutor_dhr_" + key);

		if (data != null) {
			return (JSONArray) data;
		}

		return null;
	}

	public JSONObject getPositionFromRedis() {
		Map<String,String> map = redisTemplate.opsForHash().entries("mitexecutor_dhr_position");
		String json = JSON.toJSONString(map);
        return JSON.parseObject(json);
	}


}
