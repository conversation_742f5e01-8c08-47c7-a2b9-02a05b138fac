package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DidiRestClient;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

@Service
@PropertySource(value = "classpath:didi.properties",encoding = "UTF-8")
public class DidiService {

    Logger logger= LoggerFactory.getLogger(DidiService.class);

    @Value("${DIDI_SYNC_MAIL}")
    private String syncMail;

    @Value("${DIDI_DISABLED_COUNT}")
    private String disabledCount;

    @Value("#{'${DIDI_REGULATION_ID_RONGKE}'.replaceAll(',', '_')}")
    private String regulationIdRongKe;

    @Value("#{'${DIDI_REGULATION_ID_JINYU}'.replaceAll(',', '_')}")
    private String regulationIdJinYu;

    @Value("#{'${DIDI_REGULATION_ID_HAILONG}'.replaceAll(',', '_')}")
    private String regulationIdHaiLong;

    @Value("#{'${DIDI_REGULATION_ID_DAHEN}'.replaceAll(',', '_')}")
    private String regulationIdDaHen;

    @Value("#{'${DIDI_REGULATION_ID_SHUNYI}'.replaceAll(',', '_')}")
    private String regulationIdShunYi;

//    @Value("#{'${DIDI_JINYU_TEAM}'.split(',')}")
//    private List<String> jinyuTeams;

//    @Value("#{'${DIDI_REGULATION_ID_JINYU}'.replaceAll(',', '_')}")
//    private String jinyuRegulationId;

    @Value("#{'${DIDI_KEMAO_USERS}'.split(',')}")
    private List<String> kemaoUsers = new ArrayList<>();

    @Value("#{'${DIDI_REGULATION_ID_KEMAO}'.split(',')}")
    private List<String> regulationIdKemao;

    @Value("#{'${DIDI_KUFANG_USERS}'.split(',')}")
    private List<String> kufangUsers = new ArrayList<>();

    @Value("#{'${DIDI_REGULATION_ID_KUFANG}'.split(',')}")
    private List<String> regulationIdKufang;

    @Value("#{'${DIDI_SKIP_LIST}'.split(',')}")
    private List<String> skipList = new ArrayList<>();

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private UserService userService;

    @Autowired
    private DidiRestClient didiRestClient;

    @Autowired
    private TeamService teamService;

    /**
     * 删除滴滴账号
     * @param user
     * @return
     */
    public String removeDidi(User user) {
        if(!active.equals("prod")){
            return "";
        }
        if (user.getDidiId() == null || user.getDidiId().equals("")) {
            return user.getUserId() + "滴滴ID不存在";
        }

//        if (skipList.contains(user.getWorkNumber())) {
//            return user.getUserId() + "在skip名单内,详情请联系管理员";
//        }

        try {
            List<String> workNumber = new ArrayList<>();
            workNumber.add(user.getDidiId());

            String token = didiRestClient.getAuthToken();
            if (didiRestClient.delMember(token, workNumber)) {
                user.setDidiId("");
                userService.updateUser(user);
                return user.getUserId() + "删除滴滴账号成功";
            } else {
                return user.getUserId() + "删除滴滴账号失败";
            }
        } catch (Exception e) {
            return user.getUserId() + "删除滴滴账号异常";
        }
    }


    /**
     * 修改滴滴信息
     * 1.手机号
     * 2.teamId
     * @param user
     * @return
     */
    public String updateDidiInfo(User user) {
        if(!active.equals("prod")){
            return "";
        }
        if (user.getEmployType().indexOf("实习生")!=-1) {
            return user.getUserId() + "实习生不修改滴滴信息";
        }

        if (user.getCreateTag() != null && user.getCreateTag() == 5) {
            return user.getUserId() + "mach人员不操作滴滴";
        }

        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
            return user.getUserId() + "标注中心不操作滴滴";
        }

        if (user.getCreateTag() != null && user.getCreateTag() == 9) {
            return user.getUserId() + "zk不操作滴滴";
        }

        if (user.getDidiId() == null || user.getDidiId().equals("")) {
            logger.error(user.getUserId() + "滴滴ID不存在");
            return user.getUserId() + "滴滴ID不存在,未修改滴滴信息";
        }

        if (user.getWorkNumber() == null || user.getWorkNumber().equals("")) {
            logger.error(user.getUserId() + "工号不存在");
            return user.getUserId() + "工号不存在,未修改滴滴信息";
        }

        if (user.getTeamId() == null) {
            logger.error(user.getUserId() + "teamId不存在");
            return user.getUserId() + "teamId不存在,未修改滴滴信息";
        }

        String finalRegulationId = getRegulationId(user.getTeamId());

        try {
            String token ="";// didiRestClient.getAuthToken();
            if (didiRestClient.editMember(token, user.getDidiId(), user.getCell(), user.getWorkNumber(), user.getTeamId().toString(), finalRegulationId)) {
                return user.getUserId() + "修改滴滴信息成功";
            } else {
                return user.getUserId() + "修改滴滴信息失败";
            }
        } catch (Exception e) {
            logger.error(user.getUserId() + "修改滴滴信息错误");
            return user.getUserId() + "修改滴滴信息错误";
        }
    }



    /**
     * 入职需要添加滴滴账户的人
     * @param user
     * @param need2add 手动添加跳过一部分筛选
     * @return
     */
    public String addDidiInformation(User user, boolean need2add) throws Exception {
        if(!active.equals("prod")){
            return "";
        }
        if (!need2add) {
            boolean employeeType = user.getEmployType() != null && user.getEmployType().indexOf("实习生")!=-1;
            if (employeeType) {
                String message = user.getUserId() + ":employee_type = " + user.getEmployType() + "不添加滴滴账号。";
                logger.info(message);
                return message;
            }

            boolean status = user.getStatus() != null && (user.getStatus().indexOf("已保留")!=-1 || user.getStatus().indexOf("已禁用")!=-1);
            if (status) {
                String message = user.getUserId() + "status = " + user.getStatus() + "不添加滴滴账号。";
                logger.info(message);
                return message;
            }

            boolean createTag = user.getCreateTag() != null && (user.getCreateTag() == 1 || user.getCreateTag() == 3 || user.getCreateTag() == 4|| user.getCreateTag() == 5|| user.getCreateTag() == 7|| user.getCreateTag() == 9);
            if (createTag) {
                String message = user.getUserId() + "create_tag = " + user.getCreateTag() + "不添加滴滴账号。";
                logger.info(message);
                return message;
            }
        }
        String teamId = "";
        String finalRegulationId = new String();
        if (user.getTeamId() != null) {
            finalRegulationId = getRegulationId(user.getTeamId());
            teamId = user.getTeamId().toString();
        } else {
            finalRegulationId = getRegulationId(null);
        }

        String cell = null;
        if (user.getCell() != null) {
            cell = user.getCell().replaceAll(" ", "");
        }
        if (cell == null || cell.equals("") || cell.length() < 11) {
            String message = user.getUserId() + "手机号不符合格式,不添加滴滴账号。";
            logger.info(message);
            return message;
        }

        if (user.getWorkNumber() == null) {
            String message = user.getUserId() + "work_number = null 不添加滴滴账号。";
            logger.info(message);
            return message;
        }

        String token = didiRestClient.getAuthToken();
        if (user.getDidiId() == null || user.getDidiId().equals("")) {
            String didiId = getDidiMember(user.getCell());
            if (didiId != null) {
                String message = user.getUserId() + "手机号" + user.getCell() + "在滴滴已经存在";
                logger.error(message);
                return message;
            } else {
                JSONObject data = didiRestClient.addMember2Didi(token, teamId, user.getWorkNumber(), user.getCell(), finalRegulationId);

                if (data != null) {
                    if (data.get("errno").equals(0)) {
                        didiId = data.getJSONObject("data").getString("id");
                        user.setDidiId(didiId);
                        userService.updateUserDidiId(user);
                        return user.getUserId() + "添加滴滴账号成功";
                    } else {
                        String message = user.getUserId() + ":" +  data.getString("errmsg");
                        logger.info(message);
                        return message;
                    }
                } else {
                    return "添加滴滴请求没有返回信息，添加失败";
                }
            }
        } else {
            String message = user.getUserId() + "已有滴滴账号，不添加滴滴账号。";
            logger.info(message);
            return message;
        }
    }

    /**
     * 从滴滴服务器查用户滴滴信息
     * @param cell
     * @return
     */
    public String getDidiMember(String cell) throws Exception {
        if(!active.equals("prod")){
            return "";
        }
        String token = didiRestClient.getAuthToken();
        JSONObject data = didiRestClient.getMemberFromDidi(token, null, cell, null, null);

        if (data != null) {
            if (!data.getString("total").equals("0")) {
                JSONObject records = (JSONObject) ((JSONArray) data.get("records")).get(0);
                String didiId = records.getString("id");

                if (didiId != null) {
                    return didiId;
                }
            }
        }
        return null;
    }

    /**
     * 获取员工用车规则
     * 2020-08-21 开始 开通融科金隅全部规则
     * @param teamId
     * @return
     */
    private String getRegulationId(Integer teamId) {
        if(!active.equals("prod")){
            return "";
        }

        StringBuilder regulationId = new StringBuilder();
        regulationId.append(regulationIdJinYu);

        if (teamId == null || teamId.equals("")) {
            return regulationId.toString();
        }

        Team team = teamService.getTeamById(teamId);

        /**
         * 移动业务事业部 G23 只添加大恒规则
         */
        Team mobileTeam = teamService.getTeamByCode("G23");
        if (mobileTeam != null && team.getName().contains(mobileTeam.getName())) {
            return regulationIdDaHen;
        }

        /**
         * MC G50
         * MC添加（金隅、大恒）规则
         */
        Team mcTeam = teamService.getTeamByCode("G50");
        if (mcTeam != null && team.getName().contains(mcTeam.getName())) {
            regulationId.append("_");
            regulationId.append(regulationIdDaHen);
            return regulationId.toString();
        }


        /**
         * 极智感知 BD260
         * 研究院  G05
         * 研究院添加（金隅、大恒）规则
         */
        Team reseachTeam = teamService.getTeamByCode("G05");
        if (reseachTeam != null && team.getName().contains(reseachTeam.getName())) {
            regulationId.append("_");
            regulationId.append(regulationIdDaHen);
            return regulationId.toString();
        }

        return regulationId.toString();
    }



}
