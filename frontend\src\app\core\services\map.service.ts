import { Injectable } from '@angular/core'
import { ApiService } from './api.service'

@Injectable({
  providedIn: 'root'
})
export class MapService {
  constructor(private api: ApiService) {}

  map = {}
  loadingMap = {}
  loadedMap = {}

  getMap(dictCode) {
    return new Promise((resolve, Reject) => {
      if (this.loadingMap[dictCode]) {
        this.checkLoaded(dictCode, resolve)
      } else {
        this.loadingMap[dictCode] = true
        this.api.getDictDetail(dictCode).subscribe((res) => {
          const { code, msg, data } = res
          if (code === 0) {
            this.map[dictCode] = data
            this.loadedMap[dictCode] = true
            resolve(this.map[dictCode])
          } else {
            Reject(msg)
          }
        })
      }
    })
  }

  checkLoaded(dictCode, resolve) {
    if (this.loadedMap[dictCode]) {
      resolve(this.map[dictCode] || [])
    }

    setTimeout(() => {
      this.checkLoaded(dictCode, resolve)
    }, 200)
  }
}
