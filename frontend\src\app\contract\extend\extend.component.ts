import { Component, OnInit, ViewChild } from '@angular/core'
import { ContractTableComponent } from '../component/contract-table/contract-table.component'
import { ParamStoreService } from '../../core/services/paramStore.service'

@Component({
  selector: 'app-extend',
  templateUrl: './extend.component.html',
  styleUrls: ['./extend.component.less']
})
export class ExtendComponent implements OnInit {
  @ViewChild('extend', { static: true }) extend: ContractTableComponent
  @ViewChild('voidng', { static: true }) voiding: ContractTableComponent
  isShowSelf = true
  selectedIndex = 0

  constructor(private paramStoreService: ParamStoreService) {}

  ngOnInit(): void {
    this.selectedIndex = this.paramStoreService.getMap('extend')
  }
  onActivate(event) {
    this.isShowSelf = false
  }
  onDeactivate(event) {
    this.isShowSelf = true
  }

  selectedIndexChange(event) {
    this.selectedIndex = event
    this.paramStoreService.saveMap('extend', event)
  }
}
