package com.megvii.service.contract;

import com.megvii.entity.opdb.contract.BacklogStatus;
import com.megvii.entity.opdb.contract.UserProveContractBacklog;
import com.megvii.service.DhrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties", encoding = "UTF-8")
public class ProveContractService {
    @Autowired
    private ProveContractBacklogService proveContractBacklogService;

    @Autowired
    private DhrService dhrService;

    @Autowired
    private ContractService contractService;

    public String sendEmailAndPushDHR() {
        StringBuilder msg = new StringBuilder();
        msg.append(deleteProveFile());
        List<UserProveContractBacklog> userProveContractBacklogs = proveContractBacklogService.getProveContractBacklogByStatus(BacklogStatus.SIGN);
        if (userProveContractBacklogs == null || userProveContractBacklogs.size() == 0) {
            return "没有证明类合同需要推送";
        }
        for (UserProveContractBacklog proveContractBacklog : userProveContractBacklogs) {
            if (contractService.sendProveEmailAndAttachment(proveContractBacklog)) {
                dhrService.proveContractToDhr(proveContractBacklog);
                proveContractBacklog.setStatus(BacklogStatus.PUSH);
                proveContractBacklogService.updateProveContractBacklogByDhrId(proveContractBacklog);
                msg.append("证明类合同:").append(proveContractBacklog.getUserId()).append("发送成功;<br/>");
            } else {
                msg.append("证明类合同:").append(proveContractBacklog.getContractId()).append("发送失败；<br/>");
            }
        }
        return msg.toString();
    }

    // 删除状态为已推送的证明类合同文件
    private String deleteProveFile() {
        StringBuilder msg = new StringBuilder();
        List<UserProveContractBacklog> userProveContractBacklogs = proveContractBacklogService.getProveContractBacklogByStatus(BacklogStatus.PUSH);
        if (userProveContractBacklogs == null || userProveContractBacklogs.size() == 0) {
            msg.append("没有证明类合同附件需要删除");
            return msg.toString();
        }
        for (UserProveContractBacklog proveContractBacklog : userProveContractBacklogs) {
           msg.append(contractService.deleteContractFile(proveContractBacklog.getContractId())) ;
        }
        return msg.toString();
    }
}
