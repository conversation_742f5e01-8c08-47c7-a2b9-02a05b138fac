:host {
  .contract-detail-container {
    padding: 20px 0;

    &:not(:nth-of-type(1)) {
      border-top: 1px solid #909090;
    }

    .title {
      min-height: 32px;

      h2 {
        display: inline-block;
      }

      & > span {
        display: inline-block;

        &:nth-of-type(1) {
        }

        &:nth-of-type(2) {
        }
      }
    }

    .contract-row {
      display: flex;
      line-height: 40px;
      height: 40px;
      vertical-align: center;

      & > span {
        &:nth-of-type(odd) {
          flex: none;
          flex-basis: 100px;
          text-align: right;
        }

        &:nth-of-type(even) {
          flex: 1 0 400px;
        }
      }
    }
  }

  .save-create {
    text-align: right;
    padding-right: 20px;
  }

  .create-contract {
    border-top: 1px solid #d9d9d9;
    padding-top: 20px;

    .staff-info {
      margin-bottom: 6px;
    }

    .staff-link {
      margin-left: 100px;
      .ant-btn-link {
        padding-left: 0;
        padding-right: 0;
      }
    }
  }

  .oprt-item {
  }
}

:host ::ng-deep {
  .ant-calendar-picker {
    width: 100%;
  }
}
