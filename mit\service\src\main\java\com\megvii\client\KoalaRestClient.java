package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.ImageFile;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/9/5 12:27
 */
@Component
@PropertySource(value = "classpath:koala.${spring.profiles.active}.properties",encoding = "UTF-8")
public class KoalaRestClient {

    Logger logger= LoggerFactory.getLogger(KoalaRestClient.class);

    @Value("${NEW_KOALA_HOST}")
    private String host;

    @Value("${NEW_KOALA_COMPANY_USER}")
    private String companyUser;

    @Value("${NEW_KOALA_COMPANY_PASSWORD}")
    private String companyPassword;

    @Value("${NEW_KOALA_TOKEN_URL}")
    private String tokenUrl;

    @Value("${NEW_KOALA_UPLOAD_BASE_URL}")
    private String uploadBaseUrl;

    @Value("${NEW_KOALA_ADD_USER_URL}")
    private String addUserUrl;

    @Value("${NEW_KOALA_DELETE_USER_URL}")
    private String deleteUserUrl;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 删除koala用户 删除底库
     * @param koalaId
     * @param userId
     * @return
     * @throws Exception
     */
    public String deleteSubject(String koalaId, String userId) throws Exception {
        String token = getAuthToken();
        String url = host + deleteUserUrl + koalaId;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", token);

        HttpEntity httpEntity = new HttpEntity(null, headers);

        ResponseEntity<JSONObject> result = restTemplate.exchange(url, HttpMethod.DELETE, httpEntity, JSONObject.class);
        if (result.getStatusCode().equals(HttpStatus.OK)) {
            return userId + "删除koala成功";
        } else {
            logger.error(userId + "删除koala错误:" + result.getBody().get("desc"));
            throw new Exception(userId + "删除koala错误:" + result.getBody().get("desc"));
        }
    }


    /**
     * 创建koala用户 绑定底库照片 返回subjectId
     * @param koalaId
     * @param userInfo
     * @return
     * @throws Exception
     */
    public String importSubject(int koalaId, EntryUserInfo userInfo) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Authorization", token);

        JSONObject data = new JSONObject();
        JSONArray array = new JSONArray();
        array.add(koalaId);
        data.put("subject_type",0);
        data.put("name", userInfo.getSpell());
        data.put("gender",userInfo.getGenderNumber());
        data.put("photo_ids",array);
        data.put("email",userInfo.getUserId() + "@megvii.com");
        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(),headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(host + addUserUrl, requestEntity, JSONObject.class);

        JSONObject result = responseEntity.getBody();
        if (responseEntity.getStatusCode().equals(HttpStatus.OK) && result.getInteger("code").equals(0)){
            return result.getJSONObject("data").get("id").toString();
        }else {
            logger.error(userInfo.getUserId() + "创建koala用户错误:" + result.get("desc"));
            throw new Exception(userInfo.getUserId() + "创建koala用户错误:" + result.get("desc"));
        }
    }


    /**
     * 上传koala底库 返回id
     * @param imageFile
     * @param userId
     * @return
     * @throws Exception
     */
    public Integer uploadBase(ImageFile imageFile, String userId) throws Exception {
        String token = getAuthToken();

        HttpHeaders headers=new HttpHeaders();
        headers.add("Authorization", token);
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> form = new LinkedMultiValueMap();
        InputStreamResource fileResource=new InputStreamResource(new ByteArrayInputStream(imageFile.getFileBytes())){
            @Override
            public long contentLength(){
                return imageFile.getFileBytes().length;
            }
            @Override
            public String getFilename(){
                return imageFile.getFileName();
            }
        };
        form.add("photo", fileResource);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(form, headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(host + uploadBaseUrl, requestEntity, JSONObject.class);

        JSONObject data = responseEntity.getBody();

        if (responseEntity.getStatusCode().equals(HttpStatus.OK) && data.getInteger("code").equals(0)){
            Integer id = Integer.valueOf(data.getJSONObject("data").get("id").toString());
            return id;
        }else {
            logger.error(userId + "上传koala底库错误:", StringEscapeUtils.unescapeJava(data.getString("desc")));
            throw new Exception(userId + "上传koala底库错误");
        }
    }

    /**
     * 优先从redis中获取token
     * @return
     * @throws Exception
     */
    public String getAuthToken() throws Exception{
        Object token = redisTemplate.opsForValue().get("mitexecutor_koala_token");
        if (token == null){
            token = getToken();
            redisTemplate.opsForValue().set("mitexecutor_koala_token",token);
            redisTemplate.expire("mitexecutor_koala_token",3600, TimeUnit.SECONDS);
        }

        return (String) token;
    }

    /**
     * koala 获取token
     * 使用公司账号
     * @return
     */
    public String getToken() throws Exception {
        String url = host + tokenUrl;

        HttpHeaders headers=new HttpHeaders();
        headers.add("User-Agent","Koala Admin");
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> map = new HashMap<>();
        map.put("username", companyUser);
        map.put("password", companyPassword);
        map.put("auth_token", true);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity(map, headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, requestEntity, JSONObject.class);
        JSONObject data = responseEntity.getBody();

        if (responseEntity.getStatusCode().equals(HttpStatus.OK) && data.get("code").equals(0)) {
            String token = data.getJSONObject("data").get("auth_token").toString();
            return token;
        } else {
            logger.error("koala获取token失败:" + data.get("desc"));
            throw new Exception("koala获取token失败" + data.get("desc"));
        }
    }

}
