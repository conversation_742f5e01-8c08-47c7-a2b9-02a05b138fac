package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.RemindService;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/1/8 17:10
 */
@Service
public class SendPwdSmsOperatorService extends BaseOperatorService{

    @Autowired
    private UserService userService;

    @Autowired
    private RemindService remindService;

    @Autowired
    private SendPwdSmsOperatorService sendPwdSmsOperatorService;


    /**
     *
     * 主操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId = paraMap.get("userId");
        if(userId != null){
            return OperateByUserId((String) userId);
        } else {
            List<User> users = userService.getUserByExpectDate();
            if (users.size() > 0) {
                StringBuilder msg = new StringBuilder();
                for (User user : users) {
                    OperateResult result = sendPwdSmsOperatorService.operateOnce(paraMap, user);
                    msg.append(result.getMsg() + "<br/>");
                }
                return new OperateResult(1,msg.toString());
            } else {
                return new OperateResult(1, "没有要发送提醒账号");
            }
        }
    }


    /**
     *
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        User user = (User) params[0];
        if (user!=null){
            String result = remindService.sendPwdSms(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1, user.getUserId() + "信息不存在");
        }
    }


    /**
     *
     * 根据userId添加单个人的美餐信息
     * @param userId
     * @return
     */
    public OperateResult OperateByUserId(String userId) throws Exception {
        User user = userService.getUserByUserId(userId);

        if (user != null){
            String result = remindService.sendPwdSms(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1,userId + "信息不存在");
        }
    }







}
