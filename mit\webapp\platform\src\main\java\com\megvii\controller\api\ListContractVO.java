package com.megvii.controller.api;

import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.contract.ContractStatus;
import com.megvii.entity.opdb.contract.UserContract;
import com.megvii.entity.opdb.contract.UserContractSnapshot;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/17 17:00
 */
@Data
public class ListContractVO {

	private int id;
	private String userId;
	private String spell;
	private String company;
	private String startDate;
	private String endDate;
	private String contractNumber;
	private String contractId;
	private String contractStatus;
	private String signStatus;
	private String userContractFile;
	private String ContractTitle;
	private String selfEmail;

	public static List<ListContractVO> toVOs(List<UserContract> contracts, UserContractSnapshot snapshot) {
		List<ListContractVO> vos = new ArrayList<>();
		if (snapshot != null) {
			vos.add(toVO(snapshot));
		}
		for (UserContract contract : contracts) {
			vos.add(toVO(contract));
		}

		return vos;
	}


	private static ListContractVO toVO(UserContract contract) {
		ListContractVO vo = new ListContractVO();
		vo.setId(contract.getId());
		vo.setUserId(contract.getUserId());
		vo.setContractNumber(contract.getContractNumber());
		vo.setSpell(contract.getSpell());
		vo.setCompany(contract.getCompany());
		vo.setStartDate(contract.getStartDate()!=null? DateTimeUtil.date2String(contract.getStartDate()):null);
		vo.setEndDate(contract.getEndDate()!=null?DateTimeUtil.date2String(contract.getEndDate()):null);
		vo.setContractStatus(contract.getContractStatus());
		vo.setSignStatus(contract.getSignStatus());
		vo.setContractId(contract.getContractId());
		vo.setUserContractFile(contract.getUserContractFile());
		vo.setContractTitle(contract.getContractSubject());
		vo.setSelfEmail(contract.getSelfEmail());
		return vo;
	}

	private static ListContractVO toVO(UserContractSnapshot contract) {
		ListContractVO vo = new ListContractVO();
		vo.setId(contract.getId());
		vo.setUserId(contract.getUserId());
		vo.setContractNumber(contract.getContractNumber());
		vo.setSpell(contract.getSpell());
		vo.setCompany(contract.getCompany());
		vo.setStartDate(DateTimeUtil.date2String(contract.getStartDate()));
		vo.setEndDate(contract.getEndDate()!=null?DateTimeUtil.date2String(contract.getEndDate()):null);
		vo.setContractStatus(ContractStatus.DRAFT);
		vo.setSignStatus(contract.getSignStatus());

		return vo;
	}




}
