<nz-layout>
  <nz-sider nzCollapsible [(nzCollapsed)]="isCollapsed" [nzTrigger]="null">
    <div class="logo">
      <a [routerLink]="[indexLink]" class="logo"
        ><img src="./assets/img/logo.svg"
      /></a>
    </div>
    <ul
      nz-menu
      nzTheme="dark"
      nzMode="inline"
      [nzInlineCollapsed]="isCollapsed"
    >
      <ng-container
        *ngTemplateOutlet="menuTpl; context: { $implicit: menuTree }"
      ></ng-container>
      <ng-template #menuTpl let-menus>
        <ng-container *ngFor="let menu of menus">
          <li
            *ngIf="!(menu.children && menu.children.length > 0)"
            routerLinkActive="ant-menu-item-selected"
            nz-menu-item
            [nzPaddingLeft]="!!menu['menuLevel'] ? menu['menuLevel'] * 24 : 0"
          >
            <a [routerLink]="menu['url']">{{ menu.name }}</a>
          </li>
          <li
            *ngIf="menu.children && menu.children.length > 0"
            nz-submenu
            [nzPaddingLeft]="!!menu['menuLevel'] ? menu['menuLevel'] * 24 : 0"
            [nzOpen]="menu.open"
            [nzIcon]="menuIconList[menu['code']]"
            [nzTitle]="menu.name"
          >
            <ul>
              <ng-container
                *ngTemplateOutlet="
                  menuTpl;
                  context: { $implicit: menu.children }
                "
              ></ng-container>
            </ul>
          </li>
        </ng-container>
      </ng-template>
    </ul>
  </nz-sider>
  <nz-layout>
    <nz-header>
      <button
        nz-button
        class="left-20"
        nzType="primary"
        (click)="toggleCollapsed()"
      >
        <i nz-icon [nzType]="isCollapsed ? 'menu-unfold' : 'menu-fold'"></i>
      </button>
      <span class="pull-right logout">
        <a href="javascript:void(0);" (click)="logout()"
          ><span class="iconfont iconzhuxiao"></span>&nbsp;注销</a
        >
      </span>
      <span class="pull-right role-name">{{ selRole['roleName'] }}</span>
      <span class="pull-right username right-20">
        Hi&nbsp;
        <span>{{ userName }}{{ !!deptName ? ' - ' + deptName : '' }}</span>
      </span>
      <h1 class="sys-title">mit系统</h1>
      <span
        class="avatar pull-right"
        [style.background-image]="
          user['img'] ? 'url(user[\'img\'])' : 'url(./assets/img/avatar.svg)'
        "
      >
      </span>
    </nz-header>
    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
    <nz-footer>
      <div class="footer">2019 @Copyright 数字化使能部-系统开发组出品</div>
    </nz-footer>
  </nz-layout>
</nz-layout>
<nz-back-top></nz-back-top>
