package com.megvii;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.User;
import com.megvii.job.BeisenExecutorHandler;
import com.megvii.mapper.UserMapper;
import com.megvii.service.BeisenDataParserService;
import com.megvii.service.LdapService;
import com.megvii.service.UserService;
import java.time.LocalDateTime;
import org.json.JSONException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/11/14 11:56
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestBeisen {

    Logger logger = LoggerFactory.getLogger(LdapService.class);
    Logger logger2 = LoggerFactory.getLogger(TestBeisen.class);

    @Autowired
    private BeisenExecutorHandler beisenExecutorHandler;

    @Autowired
    private BeisenDataParserService beisenDataParserService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private UserService userService;

    @Test
    public void testBeisen() {

//        logger.error( "test sentry filter");
//        logger2.error( "test sentry filter");

        System.out.println(1);

        try {
//            beisenExecutorHandler.execute("startDate=2020-11-01,endDate=2020-11-10");
            beisenExecutorHandler.execute(null);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testHireDate() {
//        beisenDataParserService.getHireDate("2019/07/25 00:00:00", "正式员工");

//        String date = DateTimeUtil.date2String2(DateTimeUtil.string2DateYMD("2020-12-29"));


        LocalDateTime date = DateTimeUtil.string2DateYMD("2021-01-10");
        LocalDateTime monthLastDay = DateTimeUtil.getMonthLastDay(date);
        LocalDateTime monday = DateTimeUtil.getMonDayOfWeek(date);
        LocalDateTime wednesday = DateTimeUtil.getWednesdayOfWeek(date);

        if (DateTimeUtil.diffDateByDay(date,monday)>0 && DateTimeUtil.diffDateByDay(date,wednesday)<0){
            date=wednesday;
            System.out.println(1);
        }else if (DateTimeUtil.diffDateByDay(date,wednesday)>0){
            date= DateTimeUtil.getMonDayOfNextWeek(date);
            System.out.println(1);
        }


//         beisenDataParserService.getHireDate(date, "兼职实习生");

        System.out.println(date);
    }

    @Test
    public void getUserId() {

        String userId = "yinqi";

        String othUserId = userMapper.selectMaxUserIdByRegexp(userId);
        String fullId = userMapper.selectMaxFullIdByRegexp(userId);

        if (othUserId != null) {
            try {
                int count = Integer.parseInt(othUserId.replaceAll(userId, ""));
                count += 1;
                if (count < 9) {
                    userId += "0" + count;
                } else {
                    userId += count;
                }
            } catch (Exception e) {
                userId += "02";
            }
        } else if (fullId != null && othUserId == null) {
            userId += "02";
        }

        System.out.println(1);

    }

    @Test
    public void testTemp() throws Exception {

        long uid = redisTemplate.opsForValue().increment("dirac_staff_create_uid_PROD",1);

        System.out.println(1);

//        long workCode = redisTemplate.opsForValue().increment("mit_sf_cur_userId_PROD",1);
//        workCode = workCode - 1;
//
//        System.out.println(workCode);

        User user = userService.getUserByUserId("jiangyuhong");
        JSONObject data = new JSONObject();
        data.put("PropertyName", "extBuddy_107102_94322460");
        data.put("Value", "jiangyuhong02");

        JSONObject data1 = new JSONObject();
        data1.put("PropertyName", "extSiteHR_107102_1797468263");
        data1.put("Value", "jiangyuhong02");




        JSONArray array = new JSONArray();
        array.add(data);
        array.add(data1);


        beisenDataParserService.setBeisenUserParam(user, array);




    }


    @Test
    public void testTemp2() {
        System.out.println(1);
    }

}
