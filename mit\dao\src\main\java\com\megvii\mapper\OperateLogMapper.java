package com.megvii.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.megvii.entity.opdb.OperateLogDO;
import com.megvii.mapper.filter.PageLogRequestFilter;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 操作日志ORM接口
 *
 * <AUTHOR>
 * @date 2020/04/17
 */
public interface OperateLogMapper extends BaseMapper<OperateLogDO> {

    /**
     * 分页获取操作日志信息
     * @param request
     * @return
     */
    List<OperateLogDO> getOperateLogs(@Param("vo") PageLogRequestFilter request);

    Boolean save(OperateLogDO po);
}
