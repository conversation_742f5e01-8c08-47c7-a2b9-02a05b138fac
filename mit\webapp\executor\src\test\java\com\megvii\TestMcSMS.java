package com.megvii;

import com.megvii.client.McSMSRestClient;
import com.megvii.entity.opdb.User;
import com.megvii.service.EntryService;
import com.megvii.service.MailSendService;
import com.megvii.service.UserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestMcSMS {
    @Autowired
    McSMSRestClient mcSMSRestClient;

    @Autowired
    UserService userService;

    @Autowired
    EntryService entryService;

    @Autowired
    MailSendService mailSendService;

    @Test
    public void testSendEntryAddress() throws Exception {
        User user = userService.getUserByUserId("zhangliangliang03");
        mcSMSRestClient.sendEntryAddress(user);

    }

    @Test
    public void testSendEntryDay() throws Exception {
        User user = userService.getUserByUserId("zhangliangliang03");
        mailSendService.sendPasswordNotificationMail(user);
        mcSMSRestClient.sendEntryDay("15727343789","zhangliangliang03","123456");
    }

    @Test
    public void testSendEntryInfo() throws Exception {
        User user = userService.getUserByUserId("zhangliangliang03");
        mailSendService.sendWelcomeNotificationMail(user);
        mcSMSRestClient.sendEntryInfo(user, entryService.getEntryInfoLimitDate(user.getExpectDate()));
    }


    @Test
    public void testSendUncompletedNotice() throws Exception {
        User user = userService.getUserByUserId("zhangliangliang03");
        mailSendService.sendUserInfoNotificationMail(user, "12345");
        mcSMSRestClient.sendUncompletedNotice(user,"1234567");
    }

}
