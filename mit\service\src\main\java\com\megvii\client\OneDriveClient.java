package com.megvii.client;

import com.microsoft.graph.authentication.TokenCredentialAuthProvider;
import com.microsoft.graph.requests.GraphServiceClient;
import com.microsoft.graph.models.DriveItem;
import com.microsoft.graph.models.User;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.List;

public class OneDriveClient {
//    private static final String AUTHORITY = "https://login.microsoftonline.com/{your-tenant-id}";
//    private static final String CLIENT_ID = "{your-client-id}";
//    private static final String CLIENT_SECRET = "{your-client-secret}";
//
//    void downloadFiles(){
//        TokenCredentialAuthProvider authProvider = new TokenCredentialAuthProvider(AUTHORITY, CLIENT_ID, CLIENT_SECRET);
//        GraphServiceClient graphClient = GraphServiceClient.builder().authenticationProvider(authProvider).build();
//
//        List<User> users = graphClient.users().buildRequest().get().getCurrentPage();
//
//        for (User user : users) {
//            downloadFilesFromOneDrive(user.displayName, user.id, graphClient);
//        }
//    }
//
//    private void downloadFilesFromOneDrive(String username, String userId, GraphServiceClient graphClient) {
//        List<DriveItem> items = graphClient
//                .users(userId)
//                .drive()
//                .root()
//                .children()
//                .buildRequest()
//                .get()
//                .getCurrentPage();
//
//        for (DriveItem item : items) {
//            if (item.file != null) {
//                downloadFile(item.id, item.name, username, graphClient);
//            } else if (item.folder != null) {
//                downloadFilesFromOneDrive(username, item.id, graphClient);
//            }
//        }
//    }
//
//    private void downloadFile(String itemId, String itemName, String username, GraphServiceClient graphClient) {
//        try {
//            URL downloadUrl = new URL("https://graph.microsoft.com/v1.0/users/{user-id}/drive/items/{item-id}/content");
////            URL downloadUrl = graphClient
////                    .drives("{user-id}")
//////                    .items(itemId)
//////                    .content()
//////                    .buildRequest()
//////                    .get()
//////                    .getClass()
//////                    .getRawRequest()
//////                    .getUrl();
//
//            InputStream inputStream = downloadUrl.openStream();
//
//            FileOutputStream outputStream = new FileOutputStream("downloaded/" + username + "/" + itemName);
//
//            byte[] buffer = new byte[8192];
//            int length;
//            while ((length = inputStream.read(buffer)) != -1) {
//                outputStream.write(buffer, 0, length);
//            }
//            outputStream.flush();
//
//            outputStream.close();
//            inputStream.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }



}
