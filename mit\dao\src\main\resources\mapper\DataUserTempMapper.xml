<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.DataUserTempMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.DataUserTemp">
         <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="spell" property="spell"/>
        <result column="cell" property="cell"/>
        <result column="team" property="team"/>
        <result column="old_user_id" property="oldUserId"/>
        <result column="new_user_id" property="newUserId"/>
        <result column="workNumber" property="workNumber"/>
    </resultMap>

    <select id="getAllDataUserTemp" resultMap="BaseResultMap">
		SELECT * FROM data_user_temp
	</select>

    <update id="updateDataUserTempById" parameterType="com.megvii.entity.opdb.DataUserTemp">
        update data_user_temp set new_user_id = #{newUserId}, work_number = #{workNumber} where id=#{id}
    </update>

</mapper>
