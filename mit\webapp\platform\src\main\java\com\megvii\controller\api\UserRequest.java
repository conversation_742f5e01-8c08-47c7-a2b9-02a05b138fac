package com.megvii.controller.api;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/16 17:01
 */
@Data
public class UserRequest extends PagingRequest {

	private String startExpectDate;
	private String endExpectDate;
	private String dismissionDateFrom;
	private String dismissionDateTo;
	private String topTeam;
	private String workNumber;
	private String userId;
	private String spell;
	private String leaveType;
	private String flowStatus;
	private String teamId;
	private String workBase;
	private String employType;
	private String employTypeCode;
	private String entryFlow;
	private String contractStatus;
	private String contractStatusCode;
	private Integer status;

	private String employBase;
	private String position;
	private String address;
	private String company;
	private String certType;
	private String certNo;
	private String expectDate;
	private String memo;
	private String cell;

	/**
	 * 离职结算单id
	 */
	private String dismissionId;
	/**
	 * 是否有竞业协议
	 */
	private Integer competition;
	private String selfEmail;

}
