package com.megvii.cas.prod;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Component
@ConfigurationProperties(prefix = "cas")
@Data
public class CasCustomProperties {

    private String secondaryDirectory;

    private String secondaryDomain;

    private String cookieId;

    private String sessionKeyPrefix;

    private Integer sessionTimeout;

}
