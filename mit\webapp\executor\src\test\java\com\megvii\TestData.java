package com.megvii;

import com.megvii.client.DataRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.service.MMISMegviiConfigService;
import com.megvii.service.SyncZTDataService;
import com.megvii.service.SyncZtUserService;
import com.megvii.service.UserMenuService;
import com.megvii.service.contract.ContractService;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/10/24 18:19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestData {

    @Autowired
    private DataRestClient dataRestClient;

    @Autowired
    private SyncZTDataService syncZTDataService;

    @Autowired
	private ContractService contractService;

    @Autowired
    private MMISMegviiConfigService mmisMegviiConfigService;

    @Autowired
    private UserMenuService userMenuService;

    @Autowired
    private SyncZtUserService syncZtUserService;

//    @Autowired
//    private SyncZTDataOperatorService syncZTDataOperatorService;

    @Test
    public void testGet() {
        try {
            dataRestClient.getData("CN_GENDER");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSync() {
//        syncZTDataService.syncDhrDataFromZT();
    }

    @Test
    public void testAllUser() {
//    	String a = contractService.getContractNumber("jiangyuhong", "104656");

        String a = mmisMegviiConfigService.getPositionByCode("200236");

		System.out.println(1);


//        dataRestClient.getAllUsers();


    }

    @Test
    public void testTeam() {

//       syncZTDataService.syncDhrPositionFromZT();

//        syncZtUserService.syncZTUser(null, null);

//        String msg = syncZtUserService.syncZTUser("caohailing", null);
        String msg = syncZTDataService.addZTTeam();


        System.out.println(1);


    }

    @Test
    public void testSyncTemp() throws FileNotFoundException {

//        File file = new File("/Users/<USER>/Desktop/lalala.docx");
//        FileInputStream inputStream = new FileInputStream(file);
//
//        String path = "/Users/<USER>/Desktop";
//
//        FileUtil.saveFile(inputStream, path, "ooo.docx");

//        FileUtil.deleteFile("/Users/<USER>/Desktop/lalala.docx");


//        List<Menu> menus = userMenuService.getMenuListByUserId("jiangyuhong");
        System.out.println(1);
        List<String> roles = new ArrayList<>();
        roles.add("SUPER_ADMIN");
        roles.add("CONTRACT_ADMIN");

        userMenuService.addUserRole("zhangliangliang03", roles);



//        userMenuService.addRoleMenu("CONTRACT_ADMIN", "MANAGE");
//        userMenuService.addRoleMenu("CONTRACT_ADMIN", "NEW");
//        userMenuService.addRoleMenu("CONTRACT_ADMIN", "RE");
//        userMenuService.addRoleMenu("CONTRACT_ADMIN", "CH");
//        userMenuService.addRoleMenu("SUPER_ADMIN", "PERMISSION");
//        userMenuService.addRoleMenu("SUPER_ADMIN", "MENU");
//        userMenuService.addRoleMenu("SUPER_ADMIN", "ROLE");
//        userMenuService.addRoleMenu("SUPER_ADMIN", "USER");





        System.out.println(1);

    }



}
