package com.megvii.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.FileUtil;
import com.megvii.common.IPUtils;
import com.megvii.common.ParameterUtil;
import com.megvii.controller.api.*;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.OperateTypeEnum;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.*;
import com.megvii.mapper.filter.UserBacklogFilter;
import com.megvii.service.EnvService;
import com.megvii.service.IPWhiteListService;
import com.megvii.service.UserService;
import com.megvii.service.contract.ChangeContractBacklogService;
import com.megvii.service.contract.ChangeContractService;
import com.megvii.service.contract.ContractChangelLogService;
import com.megvii.service.contract.ContractService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/20 14:35
 */
@RestController
@RequestMapping("/api/changecontract")
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties",encoding = "UTF-8")
public class ChangeContractController {

	private Logger logger = LoggerFactory.getLogger(ChangeContractController.class);

	@Value("${dhr.redirect}")
	private String dhrRedirect;

	@Value("${SNAPSHOT_FILE_PATH}")
    private String snapshotFilePath;

	@Autowired
	private ContractService contractService;

	@Autowired
	private ChangeContractService changeContractService;

	@Autowired
	private ChangeContractBacklogService changeContractBacklogService;
	@Autowired
	private UserService userService;

	@Autowired
	private ContractChangelLogService contractChangelLogService;

	@Autowired
    private IPWhiteListService ipWhiteListService;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private EnvService service;


	@PostMapping("/sign/offline")
	@ResponseBody
	@OperateLog(operType= OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity signOfflineContract(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		UserContract contract = new UserContract();
		contract.setUserId(request.getUserId());
		contract.setContractCount(request.getContractCount());
		contract.setContractAttribute(request.getContractCount().equals("3")?"无固定期":"固定期");
		contract.setStartDate(DateTimeUtil.string2DateYMD(request.getStartDate()));
		contract.setEndDate(contract.getContractAttribute().equals("无固定期")?null:DateTimeUtil.string2DateYMD(request.getEndDate()));
		contract.setCompany(request.getCompany());
		contract.setNewContract(request.getNewContract());
		contract.setNewWorkCity(request.getNewWorkCity());
		contract.setSignStatus(request.getSignStatus());
		contract.setUserContractFile(request.getUserContractFile());
		if (changeContractService.offlineSign(contract)) {
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
			return response;
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("线下签署失败");
			return response;
		}

	}

	@PostMapping("/sign")
	@ResponseBody
	@OperateLog(operType= OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity signContract(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		UserContractSnapshot snapshot = contractService.getSnapshotById(request.getId());
		try {
			String msg = changeContractService.createSignContract(snapshot);
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
			response.setData(msg);
			return response;

		} catch (Exception e) {
			logger.error(snapshot.getContractNumber() + "发起签署异常" + e);
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("发起签署失败");
			response.setData(e.getMessage());
			return response;
		}
	}

	@GetMapping("/getContractBacklog")
	@ResponseBody
	@OperateLog(operType= OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getContractBacklog(@RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			UserChangeContractBacklog backlog = changeContractBacklogService.selectNotFinishBacklogByUserid(userId);
			// 增加签订次数
			User user = userService.getUserByUserId(userId);
			backlog.setContractCount(user.getContractCount());
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
			response.setData(backlog);
			return response;

		} catch (Exception e) {
			logger.error("获取用户转签失败：" + e);
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("获取用户转签失败");
			response.setData(e.getMessage());
			return response;
		}
	}

}
