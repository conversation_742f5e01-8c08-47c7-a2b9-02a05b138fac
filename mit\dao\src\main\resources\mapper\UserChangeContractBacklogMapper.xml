<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserChangeContractBacklogMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.contract.UserChangeContractBacklog">
        <id column="bl_id" property="id"/>
        <result column="bl_user_id" property="userId"/>
        <result column="bl_work_number" property="workNumber"/>
        <result column="bl_status" property="status"/>
        <result column="bl_xtype" property="xtype"/>
        <result column="bl_contract" property="contract"/>
        <result column="bl_new_contract" property="newContract"/>
        <result column="bl_work_city" property="workCity"/>
        <result column="bl_new_work_city" property="newWorkCity"/>
        <result column="bl_con_begin_date" property="conBeginDate"/>
        <result column="bl_con_end_date" property="conEndDate"/>
        <result column="bl_new_con_begin_date" property="newConBeginDate"/>
        <result column="bl_new_con_end_date" property="newConEndDate"/>
        <result column="bl_effect_date" property="effectDate"/>
        <result column="bl_create_time" property="createTime"/>
        <result column="bl_update_time" property="updateTime"/>

        <association property="user" resultMap="userResult" javaType="com.megvii.entity.opdb.User" />
    </resultMap>

    <resultMap id="userResult" type="com.megvii.entity.opdb.User">
        <id column="us_id" property="id"/>
        <result column="us_user_id" property="userId"/>
        <result column="us_spell" property="spell"/>
        <result column="us_cell" property="cell"/>
        <result column="us_expect_date" property="expectDate"/>
        <result column="us_hire_date" property="hireDate"/>
        <result column="us_updater" property="updater"/>
        <result column="us_team" property="team"/>
        <result column="us_computer" property="computer"/>
        <result column="us_os" property="os"/>
        <result column="us_mentor" property="mentor"/>
        <result column="us_buddy" property="buddy"/>
        <result column="us_seat_number" property="seatNumber"/>
        <result column="us_tshirt_size" property="tshirtSize"/>
        <result column="us_employ_type" property="employType"/>
        <result column="us_status" property="status"/>
        <result column="us_memo" property="memo"/>
        <result column="us_update_time" property="updateTime"/>
        <result column="us_hrbp" property="hrbp"/>
        <result column="us_regular_date" property="regularDate"/>
        <result column="us_probation_doc_url" property="probationDocUrl"/>
        <result column="us_dimission_date" property="dimissionDate"/>
        <result column="us_employ_base" property="employBase"/>
        <result column="us_fullname" property="fullname"/>
        <result column="us_work_number" property="workNumber"/>
        <result column="us_company" property="company"/>
        <result column="us_team_code" property="teamCode"/>
        <result column="us_dingding_id" property="dingdingId"/>
        <result column="us_full_id" property="fullId"/>
        <result column="us_level" property="level"/>
        <result column="us_beisen_id" property="beisenId"/>
        <result column="us_ori_password" property="oriPassword"/>
        <result column="us_birthday" property="birthday"/>
        <result column="us_position" property="position"/>
        <result column="us_site_hrg" property="siteHrg"/>
        <result column="us_didi_id" property="didiId"/>
        <result column="us_unionid" property="unionId"/>
        <result column="us_team_id" property="teamId"/>
        <result column="us_entry_flow" property="entryFlow"/>
        <result column="us_duty" property="duty"/>
        <result column="us_title" property="title"/>
        <result column="us_source" property="source"/>
        <result column="us_work_base" property="workBase"/>
        <result column="us_didi_disabled" property="didiDisabled"/>
        <result column="us_create_tag" property="createTag"/>
        <result column="us_flow_time" property="flowTime"/>
        <result column="us_koala_id" property="koalaId"/>
        <result column="us_refer_title" property="referTitle"/>
        <result column="us_sequence" property="sequence"/>
        <result column="us_protect" property="protect"/>
        <result column="us_cert_type" property="certType"/>
        <result column="us_cert_no" property="certNo"/>
        <result column="us_address" property="address"/>
        <result column="us_contract_count" property="contractCount"/>
        <result column="us_contract_status" property="contractStatus"/>
    </resultMap>

    <sql id="backlogColumns" >
        bl.id AS bl_id,
        bl.user_id AS bl_user_id,
        bl.work_number AS bl_work_number,
        bl.status AS bl_status,
        bl.xtype AS bl_xtype,
        bl.contract AS bl_contract,
        bl.new_contract AS bl_new_contract,
        bl.work_city AS bl_work_city,
        bl.new_work_city AS bl_new_work_city,
        bl.con_begin_date AS bl_con_begin_date,
        bl.con_end_date AS bl_con_end_date,
        bl.new_con_begin_date AS bl_new_con_begin_date,
        bl.new_con_end_date AS bl_new_con_end_date,
        bl.effect_date AS bl_effect_date,
        bl.create_time AS bl_create_time,
        bl.update_time AS bl_update_time
    </sql>

    <sql id="userColumns" >
        us.id AS us_id,
        us.user_id AS us_user_id,
        us.spell AS us_spell,
        us.work_number AS us_work_number,
        us.team AS us_team,
        us.work_base AS us_work_base,
        us.expect_date AS us_expect_date,
        us.employ_type AS us_employ_type,
        us.entry_flow AS us_entry_flow,
        us.contract_status AS us_contract_status,
        us.status AS us_status
    </sql>


    <insert id="insertUserChangeContractBacklog" parameterType="com.megvii.entity.opdb.contract.UserChangeContractBacklog" >
        INSERT INTO `user_change_contract_backlog` (
	        `id`,
	        `user_id`,
	        `work_number`,
	        `status`, xtype, contract, new_contract, work_city, new_work_city, con_begin_date, con_end_date, new_con_begin_date, new_con_end_date, effect_date,
	        `create_time`,
	        `update_time`
        )
        VALUES
	    (
		    0,
		    #{userId},
		    #{workNumber},
	    	#{status},
	    	#{xtype},
	    	#{contract},
	    	#{newContract},
	    	#{workCity},
	    	#{newWorkCity},
	    	#{conBeginDate},
	    	#{conEndDate},
	    	#{newConBeginDate},
	    	#{newConEndDate},
	    	#{effectDate},
		    NOW(),
		    NOW()
	    );
    </insert>

    <update id="updateUserChangeContractBacklog" parameterType="com.megvii.entity.opdb.contract.UserChangeContractBacklog">
        update user_change_contract_backlog
        <set>
            update_time = NOW(),
            <if test="status != null ">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateUserChangeContractBacklogInfo" parameterType="com.megvii.entity.opdb.contract.UserChangeContractBacklog">
        update user_change_contract_backlog
        <set>
            update_time = NOW(),
            <if test="contract != null ">
                contract = #{contract},
            </if>
            <if test="newContract != null ">
                new_contract = #{newContract},
            </if>
            <if test="conBeginDate != null ">
                con_begin_date = #{conBeginDate},
            </if>
            <if test="conEndDate != null ">
                con_end_date = #{conEndDate},
            </if>
            <if test="newConBeginDate != null ">
                new_con_begin_date = #{newConBeginDate},
            </if>
            <if test="newConEndDate != null ">
                new_con_end_date = #{newConEndDate},
            </if>
            <if test="effectDate != null ">
                effect_date = #{effectDate},
            </if>
            <if test="workCity != null ">
                work_city = #{workCity},
            </if>
            <if test="newWorkCity != null ">
                new_work_city = #{newWorkCity},
            </if>
        </set>
        where id = #{id}
    </update>
    <select id="selectBacklogByFilter" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="backlogColumns" />,
        <include refid="userColumns" />
        from user_change_contract_backlog bl
        INNER JOIN IT_user us ON bl.user_id = us.user_id
        <where>
            <if test="filter.expectDate != null ">
                AND DATE_FORMAT(us.expect_date, '%Y-%m-%d' ) = DATE_FORMAT(#{filter.expectDate}, '%Y-%m-%d' )
            </if>
            <if test="filter.entryFlowStatus != null ">
                <if test="filter.entryFlowStatus == 0 ">
                    AND us.entry_flow != 'OA流程'
                </if>
                <if test="filter.entryFlowStatus == 1 ">
                    AND us.entry_flow = '信息收集'
                </if>
                <if test="filter.entryFlowStatus == 2 ">
                    AND us.entry_flow = 'OA流程'
                </if>
                <if test="filter.entryFlowStatus == 3 ">
                    AND us.entry_flow = '在职'
                </if>
            </if>
            <if test="filter.teamId != null ">
                AND us.team_id = #{filter.teamId}
            </if>
            <if test="filter.statusCode != null ">
                <if test="filter.statusCode == 0 ">
                    AND us.status not in ('已保留','已禁用')
                </if>
                <if test="filter.statusCode == 1 ">
                    AND us.status in ('已保留','已禁用')
                </if>
                <if test="filter.statusCode == 2 ">
                    AND us.status = '已禁用'
                </if>
            </if>
            <if test="filter.expectDateFrom != null ">
                AND DATE_FORMAT( us.expect_date, '%Y-%m-%d' ) &gt;=  DATE_FORMAT( #{filter.expectDateFrom} , '%Y-%m-%d' )
            </if>
            <if test="filter.expectDateTo != null ">
                AND DATE_FORMAT( us.expect_date, '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{filter.expectDateTo}, '%Y-%m-%d' )
            </if>
            <if test="filter.workNumber != null ">
                AND us.work_number = #{filter.workNumber}
            </if>
            <if test="filter.userId != null ">
                AND us.user_id = #{filter.userId}
            </if>
            <if test="filter.spell != null ">
                AND us.spell = #{filter.spell}
            </if>
            <if test="filter.workBase != null ">
                AND us.work_base = #{filter.workBase}
            </if>
            <if test="filter.employType != null ">
                AND us.employ_type = #{filter.employType}
            </if>
            <if test="filter.entryFlow != null ">
                AND us.entry_flow = #{filter.entryFlow}
            </if>
            <if test="filter.contractStatus != null ">
                AND us.contract_status = #{filter.contractStatus}
            </if>
            <if test="filter.backlogStatus != null ">
                AND bl.status = #{filter.backlogStatus}
            </if>
            <if test="filter.xtype != null ">
                AND bl.xtype = #{filter.xtype}
            </if>
        </where>
        order by bl.new_con_begin_date
    </select>


    <select id="selectNotFinishBacklogByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="backlogColumns" />
        from user_change_contract_backlog bl where user_id = #{userId} and status != '已完成'
    </select>


</mapper>
