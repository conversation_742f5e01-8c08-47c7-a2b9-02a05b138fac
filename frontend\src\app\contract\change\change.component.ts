import { Component, OnInit, ViewChild } from '@angular/core'
import { ContractTableComponent } from '../component/contract-table/contract-table.component'
import { ParamStoreService } from '../../core/services/paramStore.service'

@Component({
  selector: 'app-change',
  templateUrl: './change.component.html',
  styleUrls: ['./change.component.less']
})
export class ChangeComponent implements OnInit {
  @ViewChild('change', { static: true }) change: ContractTableComponent
  isShowSelf = true
  selectedIndex = 0

  constructor(private paramStoreService: ParamStoreService) {}

  ngOnInit(): void {
    this.selectedIndex = this.paramStoreService.getMap('change')
  }
  onActivate(event) {
    this.isShowSelf = false
  }
  onDeactivate(event) {
    this.isShowSelf = true
  }

  selectedIndexChange(event) {
    this.selectedIndex = event
    this.paramStoreService.saveMap('change', event)
  }
}
