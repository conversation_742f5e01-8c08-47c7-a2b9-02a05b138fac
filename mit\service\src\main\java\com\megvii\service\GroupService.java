package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.Office365Client;
import com.megvii.common.CollectionUtil;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.SecureGroupMapper;
import com.megvii.mapper.SkipGroupPrivateMapper;
import com.megvii.mapper.SkipGroupSyncMapper;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/2/28 10:26
 */
@Service
@PropertySource(value = "classpath:group.properties",encoding = "UTF-8")
@PropertySource(value = "classpath:mc.${spring.profiles.active}.properties",encoding = "UTF-8")
public class GroupService {

    Logger logger= LoggerFactory.getLogger(GroupService.class);

    @Value("#{${mailGroupLocation}}")
    private Map<String,String> mailGroupLocation=new HashMap<>();

    @Value("#{'${specialGroup}'.split(',')}")
    private List<String> specialGroup=new ArrayList<>();

    @Value("${DATA_BIAOZHU_TEAM_CODE}")
    private String biaozhuTeamCode;

    @Value("#{'${MC_COMPANY_CODE}'.split(',')}")
    private List<String> mcCompanyList;

    @Autowired
    private SecureGroupMapper secureGroupMapper;

    @Autowired
    private SkipGroupPrivateMapper skipGroupPrivateMapper;

    @Autowired
    private SkipGroupSyncMapper skipGroupSyncMapper;

    @Autowired
    private TeamService teamService;

    @Autowired
    private Office365Client office365Client;

    @Autowired
    private LdapService ldapService;

    @Autowired
    private CheckService checkService;

    /**
     * 查询office同步任务需要跳过的组
     * @return
     */
    public List<String> selectSkipSyncGroup() {
        return skipGroupSyncMapper.selectSkipSyncGroup();
    }


    /**
     * 查询全部安全组
     * @return
     */
    public List<String> selectAllSecureGroup() {
        return secureGroupMapper.selectAllSecureGroup();
    }


    /**
     * 查询skip修改属性的组
     * @return
     */
    public List<String> getSkipGroups() {
        return skipGroupPrivateMapper.selectSkipAllGroup();
    }


    /**
     * 获取全部365组
     * @return
     * @throws UnsupportedEncodingException
     */
    public JSONArray getAllOfficeArray() throws UnsupportedEncodingException {
        return office365Client.getGroupArrayFromOffice();

    }

    /**
     * 获取全部office365组
     * @return
     * @throws UnsupportedEncodingException
     */
    public Map<String, JSONObject> getAllOfficeMap() throws Exception {
        return office365Client.getGroupFromOffice();
    }



    /**
     * 获取ldap Company Groups 路径下的全部组
     * 跳过文件夹
     * @return
     */
    public List<String> getLdapGroupList() {

        List<String> list = new ArrayList<>();
        for (String s : ldapService.getAllGroup()) {
            if (s.indexOf("OU=")!=-1) {
                continue;
            } else {
                list.add(s.replace("CN=", "").toLowerCase());
            }
        }

        return list;
    }


//    /**
//     *
//     * 获取人员初始化组信息，包括部门邮件组
//     * @param user
//     * @return
//     */
//    public List<String> getGroupList(User user) {
//        List<String> groupList = new ArrayList<>();
//        //data标注中心只添加 biaozhuTeamCode 对应部门的office组
//        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
//            Team team = teamService.getTeamByCode(biaozhuTeamCode);
//            if (team != null && team.getMailGroup() != null) {
//                groupList.add(team.getMailGroup());
//            }
//            groupList = CollectionUtil.removeDuplicateAndBlank(groupList);
//            return groupList;
//        } else if (user.getTeam() != null) {
//            String[] userTeams = user.getTeam().split("-");
//            String temp = "";
//            for (int i = 0; i < userTeams.length; i++) {
//                temp += "-" + userTeams[i];
//                Team team = teamService.getTeamByName(temp.substring(1));
//                if (team != null) {
//                    if (team.getMailGroup() != null && !"".equals(team.getMailGroup())) {
//                        groupList.add(team.getMailGroup());
//                    }
//                    if ("face-recognition".equals(team.getMailGroup()) && user.getEmployType() != null && user.getEmployType().contains("正式员工")) {
//                        groupList.add("fr-data");
//                        groupList.add("face-recognition-fte");
//                    }
//                    if (team.getProject() != null) {
//                        String[] projectTemp = team.getProject().split(",");
//                        for (int j = 0; j < projectTemp.length; j++) {
//                            groupList.add(projectTemp[j]);
//                        }
//                    }
//
//                }
//
//            }
//            /**
//             * 西雅图研究院部门取消，没有国外入职人员，取消添加逻辑，全部人员添加cnall邮件组
//             */
////            if ((user.getTeam()!=null&&!user.getTeam().contains("西雅图研究院"))&&(user.getEmployBase()!=null&&!user.getEmployBase().contains("西雅图"))){
////                String locationGroup= mailGroupLocation.get(user.getEmployBase());
////                groupList.add((locationGroup!=null&&!"".equals(locationGroup))?locationGroup:"bjall");
////                groupList.add("cnall");
////            }
//
//            String locationGroup = mailGroupLocation.get(user.getEmployBase());
//            groupList.add((locationGroup != null && !"".equals(locationGroup)) ? locationGroup : "bjall");
//            groupList.add("cnall");
//            groupList.add("all");
//            groupList.add("jira-users");
//            groupList.add((user.getEmployType() != null && user.getEmployType().contains("实习生")) ? "intern" : "fte");
//            if (user.getTeam() != null && user.getTeam().contains("研究院")) {
//                groupList.add((user.getEmployType() != null && (user.getEmployType().contains("正式员工") || user.getEmployType().contains("劳务派遣"))) ? "r-fulltime" : "intern");
//                groupList.add("research");
//                groupList.add("research_ldap");
//                groupList.add("git_users");
//                groupList.add("maps-users");
//            } else {
//                for (int i = 0; i < specialGroup.size(); i++) {
//                    if (user.getTeam() != null && user.getTeam().contains(specialGroup.get(i))) {
//                        groupList.add((user.getEmployType() != null && (user.getEmployType().contains("正式员工") || user.getEmployType().contains("劳务派遣"))) ? "r-fulltime" : "intern");
//                        groupList.add("research");
//                        groupList.add("research_ldap");
//                        groupList.add("git_users");
//                        groupList.add("maps-users");
//                        break;
//                    }
//                }
//            }
//
//
//        } else {
//            logger.info("getGroupList:" + user.getUserId() + "的team为null");
//        }
//        groupList = CollectionUtil.removeDuplicateAndBlank(groupList);
//        return groupList;
//    }


//    public List<String> getUserOfficeGroup() {
//
//    }
//
//    public List<String> getUserLdapGroup() {
//
//    }

    /**
     * 获取员工全部组
     * 包含ad组和office组
     * @param user
     * @return
     */
    public List<String> getGroupList(User user) {
        List<String> groupList = new ArrayList<>();
        //根据公司主体判断mc的人员，添加mc的群组
        if (user.getCompany() != null && mcCompanyList.contains(user.getCompany())) {
            groupList.add("mc");
        }
        //标注人员只添加标注的组
        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
            Team team = teamService.getTeamByCode(biaozhuTeamCode);
            if (team != null && team.getMailGroup() != null) {
                groupList.add(team.getMailGroup());
            }
            return groupList;
        }
        Team team = teamService.getTeamById(user.getTeamId());

        //部门树邮件组和项目组
        getParentMailGroups(user, team, groupList);
        //特殊规则组
        getSpecialGroup(user, groupList);
        //通用规则组
        if(!(user.getCreateTag()!=null && user.getCreateTag()==7)){
            getGeneralGroup(user, groupList);
        }

        groupList = CollectionUtil.removeDuplicateAndBlank(groupList);
        return groupList;
    }


    /**
     * 递归获取父级的邮件组,project组
     * @param team
     * @param mailGroupList
     * @return
     */
    private void getParentMailGroups(User user, Team team, List<String> mailGroupList) {
        if (team.getMailGroup() != null && !"".equals(team.getMailGroup())) {
            mailGroupList.add(team.getMailGroup());
            if ("face-recognition".equals(team.getMailGroup()) && user.getEmployType() != null && user.getEmployType().contains("正式员工")) {
                mailGroupList.add("fr-data");
                mailGroupList.add("face-recognition-fte");
            }
        }

        if (team.getProject() != null) {
            String[] project = team.getProject().split(",");
            for (int i = 0; i < project.length; i++) {
                mailGroupList.add(project[i]);
            }
        }
        if (team.getParentCode() != null && !"G01".equals(team.getParentCode())) {
            Team parentTeam = teamService.getTeamByCode(team.getParentCode());
            if (parentTeam == null) {
                logger.error(user.getUserId() + "****[" + user.getTeam() + "]获取部门树异常"+team.getParentCode());
                return;
            }
            getParentMailGroups(user, parentTeam, mailGroupList);
        }

    }

    /**
     * 一般组
     * @param user
     * @param groupList
     */
    private void getGeneralGroup(User user, List<String> groupList) {
        //通用需要添加的组
        String locationGroup = mailGroupLocation.get(user.getEmployBase());
        groupList.add((locationGroup != null && !"".equals(locationGroup)) ? locationGroup : "bjall");
        groupList.add("cnall");
        groupList.add("all");
        groupList.add("jira-users");
        groupList.add((user.getEmployType() != null && user.getEmployType().contains("实习生")) ? "intern" : "fte");
    }

    /**
     * 邮件组特殊逻辑
     * @param user
     * @param groupList
     */
    private void getSpecialGroup(User user, List<String> groupList) {
//        if (user.getTeam() != null && user.getTeam().contains("研究院")) {
//            groupList.add((user.getEmployType() != null && (user.getEmployType().contains("正式员工") || user.getEmployType().contains("劳务派遣"))) ? "r-fulltime" : "intern");
//            groupList.add("research");
//            groupList.add("research_ldap");
//            groupList.add("git_users");
//            groupList.add("maps-users");
//        }

        if (user.getTeam() != null && checkService.checkRTeam(user.getTeam())) {
            groupList.add((user.getEmployType() != null && (user.getEmployType().contains("正式员工") || user.getEmployType().contains("劳务派遣"))) ? "r-fulltime" : "intern");
            groupList.add("research");
            groupList.add("research_ldap");
            groupList.add("git_users");
            groupList.add("maps-users");
        }

    }



}
