package com.megvii.controller;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.megvii.common.DateTimeUtil;
import com.megvii.controller.api.*;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.*;
import com.megvii.entity.opdb.contract.BacklogStatus;
import com.megvii.entity.opdb.contract.ContractStatus;
import com.megvii.entity.opdb.contract.UserContract;
import com.megvii.entity.opdb.contract.UserContractBacklog;
import com.megvii.mapper.UserContractMapper;
import com.megvii.mapper.filter.UserBacklogFilter;
import com.megvii.service.*;
import com.megvii.service.contract.ContractBacklogService;
import com.megvii.service.contract.ContractService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@RestController
@RequestMapping("/api/userDimission")
public class UserDimissionContractBacklogController {
    private Logger logger = LoggerFactory.getLogger(ContractController.class);
    @Autowired
    private UserDimissionContractBacklogService userDimissionContractBacklogService;
    @Autowired
    private UserService userService;

    @Autowired
    private UserDimissionContractSnapshotService userDimissionContractSnapshotService;
    @Autowired
    private UserDimissionContractSnapshotExtendService userDimissionContractSnapshotExtendService;
    @Autowired
    private ContractService contractService;
    @Autowired
    private DhrService dhrService;
    @Autowired
    private ContractBacklogService contractBacklogService;
    @Autowired
    private UserContractMapper userContractMapper;

    /**
     * 获取离职合同列表
     *
     * @param request
     * @return
     */
    @PostMapping("/backlog/search")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.QUERY)
    @RequiresPermissions("DISMISSION")
    public PagingResponse getUserChangeBacklog(@RequestBody UserRequest request) {
        PagingResponse response = new PagingResponse();
        UserBacklogFilter filter = new UserBacklogFilter();
        filter.setDismissionDateFrom(request.getDismissionDateFrom());
        filter.setDismissionDateTo(request.getDismissionDateTo());
        filter.setWorkNumber(request.getWorkNumber());
        filter.setUserId(request.getUserId());
        filter.setSpell(request.getSpell());
        filter.setTopTeam(request.getTopTeam());
        filter.setLeaveType(request.getLeaveType());
        filter.setFlowStatus(request.getFlowStatus());
        PageHelper.startPage(request.getPageNumber(), request.getPageSize());
        List<UserDimissionContractBacklogPO> backlog = userDimissionContractBacklogService.getBacklogByFilter(filter);
        //DHR-2731 发起签署日期+50天，标识“即将过期”；发起签署日期+60天，标识“已过期”
        //标签标识在人员上，若人员有多个离职合同，则取最新时间的已盖章合同过期状态
        //getFlowStatus 等于1或2时，才需要判断
        if (backlog != null && backlog.size() > 0) {
            if (request.getFlowStatus()!=null && (request.getFlowStatus().equals("1") || request.getFlowStatus().equals("2"))){
                for (UserDimissionContractBacklogPO userDimissionContractBacklogPO : backlog) {
                    if (userDimissionContractBacklogPO.getContractCreateTime()!=null){
                        Date contractCreateTime = userDimissionContractBacklogPO.getContractCreateTime();
                        Date date = DateTimeUtil.addDay(contractCreateTime, 50);
                        Date date1 = DateTimeUtil.addDay(contractCreateTime, 60);
                        Date now = new Date();
//                        System.out.println("now:"+now);
//                        System.out.println("date:"+date);
//                        System.out.println("date1:"+date1);
                        if (now.after(date) && now.before(date1)){
                            userDimissionContractBacklogPO.setDimissionTag("即将过期");
                        }else if (now.after(date1)){
                            userDimissionContractBacklogPO.setDimissionTag("已过期");
                        }
                    }
                }
            }
        }


//    dhrService.getDismissionContractBacklogFromDhr();//同步dhr数据，临时使用
        PageInfo<UserDimissionContractBacklogPO> pageInfo = new PageInfo<>(backlog);
        response.setCode(ResponseDataEntity.OK);
        response.setData(backlog);
        response.setMsg("success");
        response.setPageNumber(pageInfo.getPageNum());
        response.setTotalPage(pageInfo.getPages());
        response.setTotalCount(pageInfo.getTotal());
        return response;
    }

    /**
     * 获取离职员工基本信息
     *
     * @param userId
     * @param dismissionId
     * @return
     */
    @GetMapping("/info")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.QUERY)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity getUserInfo(@RequestParam("userId") String userId, @RequestParam("dismissionId") String dismissionId) {
        ResponseDataEntity response = new ResponseDataEntity();
        User user = userService.getUserByUserId(userId);
        if (user == null) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setData(null);
            response.setMsg("用户" + userId + "不存在");
            return response;
        }
        UserDimissionContractBacklogPO backlog = userDimissionContractBacklogService.getBacklogByDismissionId(dismissionId);
        if (backlog == null) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setData(null);
            response.setMsg("离职结算单" + dismissionId + "不存在");
            return response;
        }
        UserDto userDto = new UserDto();
        BeanUtils.copyProperties(user, userDto);
        userDto.setCompetition(backlog.getCompetition());
        userDto.setSelfEmail(backlog.getSelfEmail());
        userDto.setDimissionDate(DateTimeUtil.string2DateYMD(DateTimeUtil.dateToString(backlog.getDismissionDate())));
        userDto.setCompensation(backlog.getCompensation());
        userDto.setShares(backlog.getShares());
        response.setCode(ResponseDataEntity.OK);
        response.setData(UserVO.dtoToVO(userDto));
        response.setMsg("success");
        return response;
    }

    /**
     * 保存员工基本信息
     *
     * @param request
     * @return
     */
    @PostMapping("/info")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.UPDATE)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity updateUserInfo(@RequestBody UserRequest request) {
        ResponseDataEntity response = new ResponseDataEntity();

        try {
            User user = userService.getUserByUserId(request.getUserId());
            UserDimissionContractBacklogPO backlog = userDimissionContractBacklogService.getBacklogByDismissionId(request.getDismissionId());
            backlog.setCompetition(request.getCompetition());
            backlog.setSelfEmail(request.getSelfEmail());
            user.setAddress(request.getAddress());
            user.setCertNo(request.getCertNo());
            user.setCell(request.getCell());
            userService.updateUser(user);
            userDimissionContractBacklogService.updateBacklogInfo(backlog);
            response.setCode(ResponseDataEntity.OK);
            response.setMsg("success");
        } catch (Exception e) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setData(null);
            response.setMsg("保存失败");
        }
        return response;
    }

    /**
     * 获取离职合同快照
     *
     * @param dismissionId
     * @return
     */
    @GetMapping("/snapshot")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.QUERY)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity getSnapshot(@RequestParam("dismissionId") String dismissionId) {
        ResponseDataEntity response = new ResponseDataEntity();
        UserDimissionContractSnapshotPO snapshot = userDimissionContractSnapshotService.getSnapshot(dismissionId);
        if (snapshot != null) {
            List<UserDimissionContractSnapshotExtendPO> snapshotExtend = userDimissionContractSnapshotExtendService.getSnapshotExtend(snapshot.getUserId());
            DismissionSnapshotVO response1 = new DismissionSnapshotVO();
            UserDimissionContractSnapshotVO snapshotVO = new UserDimissionContractSnapshotVO();
            BeanUtils.copyProperties(snapshot, snapshotVO);
            snapshotVO.setSersSignDate(DateTimeUtil.dateToString(snapshot.getSersSignDate()));
            response1.setSnapshot(snapshotVO);
            List<UserDimissionContractSnapshotExtendVO> extendVOS = extendPO2VO(snapshotExtend);
            response1.setExtendVOS(extendVOS);
            response.setData(response1);
            response.setCode(ResponseDataEntity.OK);
            response.setMsg("success");
            return response;
        } else {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("未生成快照");
            return response;
        }
    }

    /**
     * 创建离职合同快照,并发起合同为草稿
     *
     * @param request
     * @return
     */
    @PostMapping("/snapshot/create")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.ADD)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity createSnapshot(@RequestBody DismissionSnapshotVO request) throws Exception {
        ResponseDataEntity response = new ResponseDataEntity();
        try {
            User user = userService.getUserByUserId(request.getSnapshot().getUserId());
            if (user.getContractStatus().equals(ContractStatus.SIGNING)) {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("当前合同状态不允许创建快照！");
                return response;
            }
            if (request.getSnapshot() == null) {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("缺少快照信息！");
                return response;
            }
            user.setContractStatus(ContractStatus.DRAFT);
            User login = (User) SecurityUtils.getSubject().getPrincipal();
            UserDimissionContractSnapshotPO snapshotPO = new UserDimissionContractSnapshotPO();
            BeanUtils.copyProperties(request.getSnapshot(), snapshotPO);
            snapshotPO.setSersSignDate(!StringUtils.isEmpty(request.getSnapshot().getSersSignDate()) ? DateTimeUtil.stringToDate(request.getSnapshot().getSersSignDate()) : null);
            UserDimissionContractBacklogPO backlog = userDimissionContractBacklogService.getBacklogByDismissionId(snapshotPO.getDimissionId());
            if (backlog == null) {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("缺少合同结算单，无法生成快照！");
                return response;
            }
            snapshotPO.setCompany(backlog.getCompany());
            snapshotPO.setCompetitionDuration(backlog.getCompetitionDuration());
            snapshotPO.setCompetitionStart(backlog.getCompetitionStart());
            snapshotPO.setCompetitionEnd(backlog.getCompetitionEnd());
            snapshotPO.setCreateTime(new Date());
            snapshotPO.setCreateUser(login.getUserId());
            snapshotPO.setSignStatus("离职合同");
            snapshotPO.setSpell(user.getSpell());
            snapshotPO.setDismissionDate(backlog.getDismissionDate());
            List<UserDimissionContractSnapshotExtendPO> extendS = extendVO2PO(request.getExtendVOS());
            //发起契约所合同草稿，将合同Id回填快照
            JSONObject dismissionStaff = dhrService.getDismissionStaff(snapshotPO.getDimissionId());
            String contractId = userDimissionContractBacklogService.createContractDraft(snapshotPO, extendS, user, backlog, dismissionStaff);
            snapshotPO.setContractId(contractId);
            String contractNumber = userDimissionContractBacklogService.getContractNumber(user.getWorkNumber());
            snapshotPO.setContractNumber(contractNumber);
            userDimissionContractSnapshotService.deleteSnapshot(snapshotPO.getDimissionId());//删除原来可能存在的快照
            userDimissionContractSnapshotService.addSnapshot(snapshotPO);
            userDimissionContractSnapshotExtendService.updateSnapshotExtend(extendS, snapshotPO.getUserId());
            backlog.setStatus(BacklogStatus.DRAFT);
            backlog.setSpecialCompany(snapshotPO.getSpecialCompany());
            backlog.setStockManage(snapshotPO.getStockManage());
            userDimissionContractBacklogService.updateBacklogflowStatus(backlog);
            userService.updateUser(user);//更新用户合同状态
            response.setCode(ResponseDataEntity.OK);
            response.setMsg("success");
            return response;
        } catch (Exception e) {
            response.setCode(ResponseDataEntity.ERROR);
            e.printStackTrace();
            response.setMsg("创建快照时发生异常：" + e.getMessage());
            return response;
        }
    }

    @GetMapping("/snapshot/getremark")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.QUERY)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity getSnapshotMark(String dismissionid) throws Exception {
        ResponseDataEntity response = new ResponseDataEntity();
        JSONObject dismissionStaff = dhrService.getDismissionStaff(dismissionid);
        String lzbc = dismissionStaff.getString("lzbc");
        String remark = dismissionStaff.getString("Remark");
        Map<String, String> hashmap = new HashMap<>();
        hashmap.put("lzbc", lzbc);
        hashmap.put("remark", remark);
        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        response.setData(hashmap);
        return response;
    }

    @PostMapping("/snapshot/update")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.UPDATE)
    @RequiresPermissions("DISMISSION")
    @Transactional
    public ResponseDataEntity updateSnapshot(@RequestBody DismissionSnapshotVO request) {
        ResponseDataEntity response = new ResponseDataEntity();
        User user = userService.getUserByUserId(request.getSnapshot().getUserId());
        if (user.getContractStatus().equals(ContractStatus.SIGNING) || user.getContractStatus().equals(ContractStatus.TERMINATING)) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("当前合同状态不允许创建快照！");
            return response;
        }
        UserDimissionContractSnapshotVO vo = request.getSnapshot();
        UserDimissionContractSnapshotPO snapshot = userDimissionContractSnapshotService.getSnapshot(vo.getDimissionId());
        if (snapshot == null) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("请先生成快照！");
            return response;
        } else {
            try {
                snapshot.setCompetition(vo.getCompetition());
                snapshot.setSpecialCompany(vo.getSpecialCompany());
                snapshot.setSpecialCompanyTxt(vo.getSpecialCompanyTxt());
                snapshot.setStockManage(vo.getStockManage());
                snapshot.setSersSignDate(vo.getSersSignDate() != null ? DateTimeUtil.stringToDate(vo.getSersSignDate()) : null);
                snapshot.setStockNumber(vo.getStockNumber());
                snapshot.setSersKeepNumber(vo.getSersKeepNumber());
                snapshot.setSersInvalidNumber(vo.getSersInvalidNumber());
                snapshot.setWaitTime(vo.getWaitTime());
                List<UserDimissionContractSnapshotExtendPO> extendS = extendVO2PO(request.getExtendVOS());
                JSONObject dismissionStaff = dhrService.getDismissionStaff(vo.getDimissionId());
                UserDimissionContractBacklogPO backlog = userDimissionContractBacklogService.getBacklogByDismissionId(vo.getDimissionId());
                if (!userDimissionContractBacklogService.editContractDraft(snapshot, extendS, user, backlog, dismissionStaff)) {
                    response.setCode(ResponseDataEntity.ERROR);
                    response.setMsg("更新快照异常");
                }
                userDimissionContractSnapshotService.updateSnapshot(snapshot);
                userDimissionContractSnapshotExtendService.updateSnapshotExtend(extendS, vo.getUserId());
                backlog.setSpecialCompany(snapshot.getSpecialCompany());
                backlog.setStockManage(snapshot.getStockManage());
                userDimissionContractBacklogService.updateBacklogInfo(backlog);
            } catch (Exception e) {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("更新快照异常：" + e.getMessage());
                return response;
            }
        }
        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        return response;
    }

    @PostMapping("/snapshot/delete")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.DEL)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity deleteSpanshot(@RequestParam("dismissionId") String dismissionId) {
        ResponseDataEntity response = new ResponseDataEntity();
        UserDimissionContractSnapshotPO snapshot = userDimissionContractSnapshotService.getSnapshot(dismissionId);
        if (snapshot != null) {
            try {
                userDimissionContractBacklogService.deleteContractDraft(snapshot.getContractId());
                userDimissionContractSnapshotService.deleteSnapshot(dismissionId);
                userDimissionContractSnapshotExtendService.deleteSnapshotExtend(snapshot.getUserId());
                UserDimissionContractBacklogPO backlog = userDimissionContractBacklogService.getBacklogByDismissionId(dismissionId);
                backlog.setStatus(BacklogStatus.PULL);
                userDimissionContractBacklogService.updateBacklogflowStatus(backlog);
                String contractStatus = contractService.removeDimSnapshotUpdateStatus(snapshot.getUserId());
                if (contractStatus != null) {
                    response.setCode(ResponseDataEntity.OK);
                    response.setData(contractStatus);
                    response.setMsg("success");
                    return response;
                } else {
                    response.setCode(ResponseDataEntity.ERROR);
                    response.setMsg("删除离职快照失败");
                    return response;
                }
            } catch (Exception e) {
                logger.error("删除合同快照异常：dismissionId：" + dismissionId + ",异常：" + e.getMessage());
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("更新快照异常：" + e.getMessage());
                return response;
            }
        }
        response.setCode(ResponseDataEntity.ERROR);
        response.setMsg("所删除快照不存在");
        return response;
    }

    /**
     * 获取该员工所有离职合同及快照
     *
     * @param userId
     * @param dismissionId
     * @return
     */
    @GetMapping("/contract")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.QUERY)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity getUserBacklog(@RequestParam("userId") String userId, @RequestParam("dismissionId") String dismissionId) {
        ResponseDataEntity response = new ResponseDataEntity();
        UserDimissionContractSnapshotPO snapshot = userDimissionContractSnapshotService.getSnapshot(dismissionId);
        List<UserContract> contracts = contractService.getContractsByUserId(userId);
        List<UserContract> contractsNew = contracts.stream().filter(e -> "离职合同".equals(e.getSignStatus())).collect(Collectors.toList());//只显示离职合同
        response.setCode(ResponseDataEntity.OK);
        response.setData(ListDismissionContractVO.toVOs(contractsNew, snapshot));
        response.setMsg("success");
        return response;
    }

    /**
     * 发给员工签字
     *
     * @param request
     * @return
     */
    @PostMapping("/sign")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.UPDATE)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity signContract(@RequestBody ContractRequest request) {
        ResponseDataEntity response = new ResponseDataEntity();
        UserDimissionContractSnapshotPO snapshot = userDimissionContractSnapshotService.getSnapshot(request.getId().toString());
        List<UserContractBacklog> waitTerminateBacklogByUserId = contractBacklogService.getWaitTerminateBacklogByUserId(snapshot.getUserId());
        if (waitTerminateBacklogByUserId.size() > 0) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("存在待作废合同，请作废后再发起签署");
            return response;
        }
        try {
            String msg = userDimissionContractBacklogService.sendContractToUser(snapshot);
            UserDimissionContractBacklogPO backlog = userDimissionContractBacklogService.getBacklogByDismissionId(request.getId().toString());
            backlog.setStatus(BacklogStatus.SIGNING);
            backlog.setContractCreateTime(new Date());
            userDimissionContractBacklogService.updateBacklogflowStatus(backlog);
            response.setCode(ResponseDataEntity.OK);
            response.setMsg("success");
            response.setData(msg);
            return response;
        } catch (Exception e) {
            logger.error(snapshot.getContractNumber() + "发起离职签署异常" + e);
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("发起离职签署失败:" + e.getMessage());
            response.setData(e.getMessage());
            return response;
        }
    }

    /**
     * 发起盖章请求
     *
     * @param contractId 合同id
     * @param company    公司名称
     * @return
     */
    @PostMapping("/seal")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.ADD)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity sealContract(@RequestParam("contractId") String contractId, @RequestParam("company") String company) {
        ResponseDataEntity response = new ResponseDataEntity();
        try {
            Boolean msg = userDimissionContractBacklogService.sealContract(company, contractId);
            response.setCode(ResponseDataEntity.OK);
            response.setMsg("success");
            response.setData(msg);
            return response;
        } catch (Exception e) {
            logger.error(contractId + "发起离职签署盖章异常" + e);
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("发起离职签署盖章失败:" + e.getMessage());
            response.setData(e.getMessage());
            return response;
        }
    }

    /**
     * 撤回合同
     *
     * @param request
     * @return
     */
    @PostMapping("/recall")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.UPDATE)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity recallContract(@RequestBody ContractRequest request) {
        ResponseDataEntity response = new ResponseDataEntity();
        try {
            if (contractService.recallDismissionContract(request.getContractId())) {
                response.setCode(ResponseDataEntity.OK);
                response.setMsg("success");
            } else {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("撤回失败");
            }
        } catch (Exception e) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg(e.getMessage());
        }
        return response;
    }

    /**
     * 作废合同
     *
     * @param request
     * @return
     */
    @PostMapping("/cancel")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.UPDATE)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity cancelContract(@RequestBody ContractRequest request) {
        ResponseDataEntity response = new ResponseDataEntity();
        try {
            if (contractService.cancelContract(request.getContractId())) {
                response.setCode(ResponseDataEntity.OK);
                response.setMsg("success");
            } else {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("作废失败");
            }
        } catch (Exception e) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg(e.getMessage());
        }
        return response;
    }


    @PostMapping("/sign/offline")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.UPDATE)
    @RequiresPermissions("MANAGE")
    public ResponseDataEntity signOfflineContract(@RequestBody DismissionContractFileVO request) {
        ResponseDataEntity response = new ResponseDataEntity();
        UserDimissionContractBacklogPO backlogByDismissionId = userDimissionContractBacklogService.getBacklogByDismissionId(request.getDismissionId());
        List<UserContractBacklog> waitTerminateBacklogByUserId = contractBacklogService.getWaitTerminateBacklogByUserId(backlogByDismissionId.getAccount());
        if (waitTerminateBacklogByUserId.size() > 0) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("存在待作废合同，请作废后再上传合同");
            return response;
        }
        if (backlogByDismissionId != null) {
            backlogByDismissionId.setFlowStatus("-1");
            backlogByDismissionId.setStatus(BacklogStatus.FINISH);
            try {
                //创建合同数据修改人员状态
                contractService.dismissionOfflineSign(backlogByDismissionId, request.getUserContractFile());
                dhrService.dimissionContractToDhr(backlogByDismissionId.getAccount(), request.getDismissionId());
                userDimissionContractBacklogService.deleteOtherBacklog(backlogByDismissionId);
                userDimissionContractBacklogService.updateBacklogflowStatus(backlogByDismissionId);
                response.setCode(ResponseDataEntity.OK);
                response.setMsg("success");
                return response;
            } catch (Exception e) {
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("归档失败：" + e.getMessage());
                return response;
            }
        } else {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("未查到该离职结算单");
            return response;
        }

    }
//  /**
//   * 线下签署归档
//   * @param dismissionId
//   * @return
//   */
//  @PostMapping("/complete")
//  @ResponseBody
//  @RequiresPermissions("DISMISSION")
//  public ResponseDataEntity complete(@RequestParam("dismissionId") String dismissionId) {
//    ResponseDataEntity response = new ResponseDataEntity();
//    UserDimissionContractBacklogPO backlogByDismissionId = userDimissionContractBacklogService.getBacklogByDismissionId(dismissionId);
//    if (backlogByDismissionId != null) {
//      backlogByDismissionId.setFlowStatus("-1");
//      backlogByDismissionId.setStatus(BacklogStatus.FINISH);
//      try {
//        //创建合同数据修改人员状态
//        contractService.dismissionOfflineSign(backlogByDismissionId);
//        userDimissionContractBacklogService.updateBacklogflowStatus(backlogByDismissionId);
//        response.setCode(ResponseDataEntity.OK);
//        response.setMsg("success");
//        return response;
//      }catch (Exception e){
//        response.setCode(ResponseDataEntity.ERROR);
//        response.setMsg("归档失败："+e.getMessage());
//        return response;
//      }
//    }else {
//      response.setCode(ResponseDataEntity.ERROR);
//      response.setMsg("未查到该离职结算单");
//      return response;
//    }
//  }

    /**
     * 线上签署发送邮件
     *
     * @param dismissionId
     * @return
     */
    @PostMapping("/sendEmail")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.UPDATE)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity sendEmail(@RequestParam("dismissionId") String dismissionId) {
        ResponseDataEntity response = new ResponseDataEntity();
        UserContract contractByDismissionId = contractService.getContractByDismissionId(dismissionId);
        if (contractByDismissionId == null) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("查询合同信息失败");
            return response;
        }
        UserDimissionContractBacklogPO backlogByDismissionId = userDimissionContractBacklogService.getBacklogByDismissionId(dismissionId);
        Boolean aBoolean = contractService.sendEmailAndAttachment(contractByDismissionId.getContractId(), backlogByDismissionId);
        if (!aBoolean) {
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("发送邮件失败");
            return response;
        }
        dhrService.dimissionContractToDhr(backlogByDismissionId.getAccount(), dismissionId);
        backlogByDismissionId.setFlowStatus("-1");
        backlogByDismissionId.setStatus(BacklogStatus.FINISH);
        userDimissionContractBacklogService.updateBacklogflowStatus(backlogByDismissionId);
        contractByDismissionId.setContractStatus(ContractStatus.COMPLETE);
        contractService.updateUserContract(contractByDismissionId);
        User user = userService.getUserByUserId(backlogByDismissionId.getAccount());
        user.setDimissionDate(DateTimeUtil.string2DateYMD(DateTimeUtil.dateToString(backlogByDismissionId.getDismissionDate())));
        userService.updateUser(user);
        if (userContractMapper.selectUserStandardNewestContract(contractByDismissionId.getUserId()).getContractNumber().equals(contractByDismissionId.getContractNumber())){
            user.setContractStatus(ContractStatus.COMPLETE);
            userService.updateUser(user);
        }
        response.setCode(ResponseDataEntity.OK);
        response.setMsg("success");
        return response;
    }

    /**
     * 主动拉取dhr待离职人员
     *
     * @return
     */
    @GetMapping("/getDismissionBacklog")
    @ResponseBody
    @OperateLog(operType = OperateTypeEnum.QUERY)
    @RequiresPermissions("DISMISSION")
    public ResponseDataEntity getDismissionBacklog() {
        ResponseDataEntity response = new ResponseDataEntity();
        String msg = dhrService.getDismissionContractBacklogFromDhr();
        response.setCode(ResponseDataEntity.OK);
        response.setData(msg);
        response.setMsg("success");
        return response;
    }

    public List<UserDimissionContractSnapshotExtendPO> extendVO2PO(List<UserDimissionContractSnapshotExtendVO> list) {
        List<UserDimissionContractSnapshotExtendPO> extendS = new ArrayList();
        if (list == null) {
            return extendS;
        }
        for (UserDimissionContractSnapshotExtendVO extendVO : list) {
            UserDimissionContractSnapshotExtendPO extend = new UserDimissionContractSnapshotExtendPO();
            BeanUtils.copyProperties(extendVO, extend);
            extend.setEffectDate(DateTimeUtil.stringToDate(extendVO.getEffectDate()));
            extendS.add(extend);
        }
        return extendS;
    }

    public List<UserDimissionContractSnapshotExtendVO> extendPO2VO(List<UserDimissionContractSnapshotExtendPO> list) {
        List<UserDimissionContractSnapshotExtendVO> extendVOS = new ArrayList();
        if (list == null) {
            return extendVOS;
        }
        for (UserDimissionContractSnapshotExtendPO po : list) {
            UserDimissionContractSnapshotExtendVO extendVO = new UserDimissionContractSnapshotExtendVO();
            BeanUtils.copyProperties(po, extendVO);
            extendVO.setEffectDate(DateTimeUtil.dateToString(po.getEffectDate()));
            extendVOS.add(extendVO);
        }
        return extendVOS;
    }
}

