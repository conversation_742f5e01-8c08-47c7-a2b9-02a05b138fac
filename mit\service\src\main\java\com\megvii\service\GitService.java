package com.megvii.service;


import com.megvii.client.AliMailClient;
import com.megvii.client.GitRestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/30 14:07
 */
@Service
@PropertySource("classpath:git.properties")
public class GitService {

    Logger logger= LoggerFactory.getLogger(GitService.class);

    @Value("${GIT_PD_URL}")
    private String gitPdUrl;

    //账号名称：MIT-AccountOperate-liudongfang 使用的东方的pd账号生成，英策指导生成规则
    @Value("${GIT_PD_TOKEN}")
    private String gitPdToken;

    @Value("${GIT_PD_GROUP_ID}")
    private String gitPdGroupId;

    @Value("${GIT_CORE_URL}")
    private String gitCoreUrl;

    @Value("${GIT_CORE_TOKEN}")
    private String gitCoreToken;

    @Value("${GIT_CORE_GROUP_ID}")
    private String gitCoreGroupId;

    @Value("${GIT_V_URL}")
    private String gitVUrl;

    @Value("${GIT_V_TOKEN}")
    private String gitVToken;

    @Autowired
    private GitRestClient gitRestClient;

    @Autowired
	private Office365Service office365Service;


    @Autowired
    private LdapService ldapService;


    @Autowired
    AliMailClient aliMailClient;

	/**
	 * 同步git组成员
	 * pd 和 core
	 * 最后单独同步一个组的成员
	 * @return
	 */
    public String syncGroupMember() {
        StringBuilder msg = new StringBuilder();

        List<String> pdUsers = getPdUsers();
        List<String> coreUsers = getCoreUsers();
        List<String> adUsers = ldapService.getSecureGroupMembers("all");

        if (pdUsers == null || pdUsers.size() == 0 || coreUsers == null || coreUsers.size() == 0 || adUsers == null || adUsers.size() == 0) {
            return "同步git成员获取用户异常";
        }

        msg.append(syncMember(pdUsers, adUsers, "pd"));
        msg.append(syncMember(coreUsers, adUsers, "core"));
        msg.append(syncCore2Git());

        return msg.toString();
    }

	/**
	 * 同步coreteam中的人到git_users
	 * @return
	 */
    private String syncCore2Git() {
    	List<String> coreUsers = ldapService.getGroupMembers("coreteam");
    	List<String> gitUsers = ldapService.getGroupMembers("git_users");

    	coreUsers.removeAll(gitUsers);
    	if (coreUsers.size() == 0) {
    		return "";
		}

    	StringBuilder msg = new StringBuilder();
    	msg.append(coreUsers + "需要同步到git_users");
		for (String coreUser : coreUsers) {
			try {
//				msg.append(office365Service.addUser2Group(coreUser, "git_users"));
                List<String> members = new ArrayList<>();
                members.add(coreUsers + "@megvii.com");
                if (aliMailClient.addMailGroupMembers("<EMAIL>", members)) {
                    msg.append("添加组git_users成功</br>");
                }else {
                    msg.append("添加组git_users失败</br>");
                }

				String userDn = ldapService.getRealUserDn(coreUser);
				ldapService.addMemberToGroupByUserDn("git_users", userDn);

				msg.append(coreUser + "同步到git_users【成功】");
			} catch (Exception e) {
				logger.error(coreUser + "同步到git_users【异常】" + e);
				msg.append(coreUser + "同步到git_users【异常】");
			}
		}
		return msg.toString();
	}



	/**
	 * 同步git组人员 新增和删除
	 * @param gitUsers
	 * @param adUsers
	 * @param git
	 * @return
	 */
    private String syncMember(List<String> gitUsers, List<String> adUsers, String git) {
        StringBuilder msg = new StringBuilder();

        List<String> addUsers = new ArrayList<>();
        addUsers.addAll(adUsers);
        addUsers.removeAll(gitUsers);

        if (addUsers.size() == 0) {
            msg.append(git + "没有需要添加的人员");
        } else {
            msg.append(addGitUser(addUsers, git));
        }

        List<String> delUsers = new ArrayList<>();
        delUsers.addAll(gitUsers);
        delUsers.removeAll(adUsers);

        if (delUsers.size() == 0) {
            msg.append(git + "没有需要删除的人员");
        } else {
            msg.append(delGitUser(delUsers, git));
        }

        return msg.toString();
    }


    /**
     * 删除git组成员
     * @param delUsers
     * @param git
     * @return
     */
    private String delGitUser(List<String> delUsers, String git) {
        StringBuilder msg = new StringBuilder();

        String url = null;
        String token = null;
        String groupId = null;

        if (git.equals("core")) {
            url = gitCoreUrl;
            token = gitCoreToken;
            groupId = gitCoreGroupId;
        } else {
            url = gitPdUrl;
            token = gitPdToken;
            groupId = gitPdGroupId;
        }

        for (String delUser : delUsers) {
            Map map = gitRestClient.getGitUserInfo(delUser, url, token);
            if (map == null) {
                continue;
            }
			msg.append(delUsers + "删除git-" + git + "组</br>");
            if (gitRestClient.delGitUser(url, token, groupId, map.get("id").toString())) {
                msg.append(delUser + "删除git-" + git + "【成功】</br>");
            } else {
                msg.append(delUser + "删除git-" + git + "组【失败】</br>");
            }
        }
        return msg.toString();

    }


    /**
     * 添加git组成员
     * @param addUsers
     * @param git
     * @return
     */
    private String addGitUser(List<String> addUsers, String git) {
        StringBuilder msg = new StringBuilder();

        String url = null;
        String token = null;
        String groupId = null;
        String access = null;

        if (git.equals("core")) {
            url = gitCoreUrl;
            token = gitCoreToken;
            groupId = gitCoreGroupId;
            access = "20";
        } else {
            url = gitPdUrl;
            token = gitPdToken;
            groupId = gitPdGroupId;
            access = "20";
        }

        for (String addUser : addUsers) {
            Map map = gitRestClient.getGitUserInfo(addUser, url, token);
            if (map == null) {
                continue;
            }
			msg.append(addUser + "添加git-" + git + "组</br>");
            if (gitRestClient.addGitUser(url, token, groupId, map.get("id").toString(), access)) {
                msg.append(addUser + "添加git-" + git + "组<span style=\"color:#3eda67;\">【成功】</span></br>");
            } else {
                msg.append(addUser + "添加git-" + git + "组<span style=\"color:#dc143c;\">【失败】</span></br>");
            }
        }

        return msg.toString();
    }


    /**
     * 禁用git-v
     * @param userId
     * @return
     */
    public String disableGitV(String userId) {
        StringBuilder msg = new StringBuilder();
        msg.append(userId);

        try {
            String vId = getActiveGitUserId(userId, gitVUrl, gitVToken);
            if (vId != null) {
                msg.append(gitRestClient.disableGit(vId, gitVUrl, gitVToken)?"禁用git-v【成功】":"禁用git-v【失败】");
            } else {
                msg.append("git-v账户不存在");
            }
        } catch (Exception e) {
            msg.append("禁用git-v失败" + e.getMessage());
        }

        return msg.toString();

    }


    /**
     * 正式员工禁用git   1.pd 2.core
     * @param userId
     * @return
     */
    public String disableGitPdCore(String userId) {
        StringBuilder msg = new StringBuilder();
        msg.append(userId);

        try {
            String pdId = getActiveGitUserId(userId, gitPdUrl, gitPdToken);
            if (pdId != null) {
                msg.append(gitRestClient.disableGit(pdId, gitPdUrl, gitPdToken)?"禁用git-pd【成功】":"禁用git-pd【失败】");
            } else {
                msg.append("git-pd账户不存在");
            }
        } catch (Exception e) {
            msg.append("禁用git-pd失败" + e.getMessage());
        }

        msg.append("******");

        try {
            String coreId = getActiveGitUserId(userId, gitCoreUrl, gitCoreToken);
            if (coreId != null) {
                msg.append(gitRestClient.disableGit(coreId, gitCoreUrl, gitCoreToken)?"禁用git-core【成功】":"禁用git-core【失败】");
            } else {
                msg.append("git-core账户不存在");
            }
        } catch (Exception e) {
            msg.append("禁用git-core失败" + e.getMessage());
        }

        return msg.toString();
    }


    /**
     * 获取激活用户的userId
     * @param userId
     * @param url
     * @param token
     * @return
     * @throws Exception
     */
    private String getActiveGitUserId(String userId, String url, String token) throws Exception {
        try {
            Map map = gitRestClient.getGitUserInfo(userId, url, token);
            if (map == null || map.size() == 0) {
                return null;
            }
            if (map.get("state").equals("active")) {
                return map.get("id").toString();
            } else {
                throw new Exception(userId + "账户处于" + map.get("state") + "状态");
            }
        } catch (Exception e) {
            logger.error("获取git用户" + userId + "异常:" + e.getMessage());
            throw new Exception("获取git用户" + userId + "异常:" + e.getMessage());
        }
    }

    /**
     * 获取git pd 人员list
     * @return
     */
    private List<String> getPdUsers() {
        return gitRestClient.getGitUsers(gitPdUrl, gitPdGroupId, gitPdToken);
    }

    /**
     * 获取git core 人员list
     * @return
     */
    private List<String> getCoreUsers() {
        return gitRestClient.getGitUsers(gitCoreUrl, gitCoreGroupId, gitCoreToken);
    }


}
