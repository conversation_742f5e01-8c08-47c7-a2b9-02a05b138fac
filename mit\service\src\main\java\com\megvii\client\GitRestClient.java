package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/10/29 18:34
 */
@Component
public class GitRestClient {

	Logger logger= LoggerFactory.getLogger(GitRestClient.class);

	@Autowired
	private RestTemplate restTemplate;


	/**
	 * 删除git组成员
	 * @param gitUrl
	 * @param token
	 * @param groupId
	 * @param gitUserId
	 * @return
	 */
	public boolean delGitUser(String gitUrl, String token, String groupId, String gitUserId) {
		String url = gitUrl + "/groups/" + groupId + "/members/" + gitUserId;

		HttpHeaders headers = new HttpHeaders();
		headers.set("PRIVATE-TOKEN", token);
		headers.setContentType(MediaType.APPLICATION_JSON);

		HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);

		try {
			ResponseEntity<JSONObject> result = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, JSONObject.class);
			return true;
		} catch (Exception e) {
			logger.error("删除git组异常" + e);
			return false;
		}

	}

	/**
	 * 给git组增加成员
	 * @param gitUrl
	 * @param token
	 * @param groupId
	 * @param gitUserId
	 * @param groupAccess
	 * @return
	 */
	public boolean addGitUser(String gitUrl, String token, String groupId, String gitUserId, String groupAccess) {
		String url = gitUrl + "/groups/" + groupId + "/members";

		HttpHeaders headers = new HttpHeaders();
		headers.set("PRIVATE-TOKEN", token);
		headers.setContentType(MediaType.APPLICATION_JSON);

		JSONObject data = new JSONObject();
		data.put("user_id", gitUserId);
		data.put("access_level", groupAccess);

		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(), headers);

		try {
			ResponseEntity<JSONObject> result = restTemplate.exchange(url, HttpMethod.POST, requestEntity, JSONObject.class);
			return true;
		} catch (Exception e) {
			logger.error("添加git组异常" + e);
			return false;
		}

	}


	/**
	 * 获取git组成员
	 *
	 * @param gitUrl
	 * @param groupId
	 * @param token
	 * @return
	 */
	public List<String> getGitUsers(String gitUrl, String groupId, String token) {
		List<String> users = new ArrayList<>();
		int page = 0;
		String url = gitUrl + "/groups/" + groupId + "/members?per_page=50&page=";

		HttpHeaders headers = new HttpHeaders();
		headers.set("PRIVATE-TOKEN", token);

		HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);
		while (true) {
			ResponseEntity<JSONArray> result = restTemplate.exchange(url + page, HttpMethod.GET, requestEntity, JSONArray.class);

			JSONArray array = result.getBody();
			if (array.size() == 0) {
				break;
			}

			for (Object o : array) {
				users.add(((LinkedHashMap) o).get("username").toString());
			}
			page += 1;

		}

		return users;
	}


	/**
	 * 锁定git账号
	 *
	 * @param id
	 * @param gitUrl
	 * @param token
	 * @return
	 */
	public boolean disableGit(String id, String gitUrl, String token) {
		String url = gitUrl + "/users/" + id + "/block";

		HttpHeaders headers = new HttpHeaders();
		headers.set("PRIVATE-TOKEN", token);

		HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);
		ResponseEntity<String> result = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

		if (result.getStatusCode().equals(HttpStatus.CREATED)) {
			if (result.getBody().equals("true")) {
				return true;
			}
		}
		return false;
	}


	/**
	 * 获取git user信息
	 * pd core V 根据url和token区分
	 *
	 * @param userId
	 * @param gitUrl
	 * @param token
	 * @return
	 */
	public Map getGitUserInfo(String userId, String gitUrl, String token) {
		String url = gitUrl + "/users?username=" + userId;

		HttpHeaders headers = new HttpHeaders();
		headers.set("PRIVATE-TOKEN", token);

		HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);
		ResponseEntity<JSONArray> result = restTemplate.exchange(url, HttpMethod.GET, requestEntity, JSONArray.class);

		if (result.getStatusCode().equals(HttpStatus.OK)) {
			if (result.getBody().size() > 0) {
				Map map = (Map) result.getBody().get(0);
				return map;
			}
		}
		return null;
	}


}
