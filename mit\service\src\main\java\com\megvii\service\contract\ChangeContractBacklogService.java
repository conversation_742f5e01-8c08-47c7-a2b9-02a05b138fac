package com.megvii.service.contract;

import com.megvii.entity.opdb.contract.UserChangeContractBacklog;
import com.megvii.mapper.UserChangeContractBacklogMapper;
import com.megvii.mapper.filter.UserBacklogFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/13 14:37
 */
@Service
public class ChangeContractBacklogService {

	@Autowired
	private UserChangeContractBacklogMapper UserChangeContractBacklogMapper;


	/**
	 * 更改待续签状态
	 * @param UserChangeContractBacklog
	 * @return
	 */
	public boolean updateContractBacklogStatus(UserChangeContractBacklog UserChangeContractBacklog) {
		if (UserChangeContractBacklogMapper.updateUserChangeContractBacklog(UserChangeContractBacklog) > 0) {
			return true;
		} else {
			return false;
		}
	}
	public boolean updateContractBacklogInfo(UserChangeContractBacklog UserChangeContractBacklog) {
		if (UserChangeContractBacklogMapper.updateUserChangeContractBacklogInfo(UserChangeContractBacklog) > 0) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 创建一条待续签记录
	 * @param UserChangeContractBacklog
	 * @return
	 */
	public boolean addContractBacklog(UserChangeContractBacklog UserChangeContractBacklog) {
		if(UserChangeContractBacklogMapper.insertUserChangeContractBacklog(UserChangeContractBacklog) > 0) {
			return true;
		} else {
			return false;
		}
	}


	/**
	 * 查询未处于 已完成 状态的待续签记录 通过user_id 查询 每个人只能有一条
	 * @param userId
	 * @return
	 */
	public UserChangeContractBacklog selectNotFinishBacklogByUserid(String userId) {
		return UserChangeContractBacklogMapper.selectNotFinishBacklogByUserId(userId);
	}

	/**
	 * 通过状态查询 返回map  key:姓名拼音
	 * @param status
	 * @return
	 */
	public Map<String, UserChangeContractBacklog> selectBacklogByStatus(String status) {
		UserBacklogFilter filter = new UserBacklogFilter();
		filter.setBacklogStatus(status);

		List<UserChangeContractBacklog> list = UserChangeContractBacklogMapper.selectBacklogByFilter(filter);
		if (list == null || list.size() == 0) {
			return new HashMap<String, UserChangeContractBacklog>();
		}
		Map<String, UserChangeContractBacklog> maps = list.stream().collect(Collectors.toMap(UserChangeContractBacklog::getUserId, Function.identity(), (key1, key2) -> key2));

		return maps;
	}

	public List<UserChangeContractBacklog> getBacklogByFilter(UserBacklogFilter filter) {
		return UserChangeContractBacklogMapper.selectBacklogByFilter(filter);
	}


}
