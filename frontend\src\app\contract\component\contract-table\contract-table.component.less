:host {
  .search-container {
    overflow: hidden;

    .ant-input,
    .ant-select {
      width: 166px;
    }

    .ant-btn {
      width: 100px;
    }

    & > span {
      display: inline-block;
      margin-bottom: 10px;

      & > label {
        display: inline-block;
        width: 100px;
        text-align: right;
      }
    }

    .pick-up-label {
      width: 40px !important;
    }
  }

  .invalid-contract {
    td > span {
      color: #d9d9d9;
    }
  }

  .signStatus {
    display: inline-block;
    min-width: 80px;
  }
  .tag-span {
    // width: 48px;
    // height: 16px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ff0000;
    // line-height: 16px;
  }
  .tag-span1 {
    // width: 48px;
    // height: 16px;
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #00c172;
    // line-height: 16px;
  }
}

:host::ng-deep {
  .user-item {
    background: rgb(255, 255, 255) !important;
  }

  .ant-table table th {
    padding: 11px 5px;
  }

  .ant-table-tbody > tr > td {
    padding: 16px 5px;
  }
  .btn-box {
    width: 100%;
    height: 50px;
    position: relative;
    .btn {
      position: absolute;
      right: 20px;
    }
  }
  .ant-popover {
    width: 150px !important;
  }
  .agree-span {
    display: inline-block;
    height: 40px;
    line-height: 40px;
  }
}

.add-item {
  padding: 5px;
  .ant-input {
    width: 200px;
  }
  .add-span {
    margin-left: 10px;
    cursor: pointer;
  }
}
.save-create {
  text-align: right;
  padding-right: 20px;
}
