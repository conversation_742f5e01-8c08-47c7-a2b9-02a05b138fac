package com.megvii.controller.api;

import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.contract.UserContractChangeLog;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/22 17:59
 */
@Data
public class ContractChangeLogVO {

	private String userId;
	private String contractNumber;
	private String updatorUserId;
	private String updatorName;
	private String updateTime;
	private String changeType;

	public static List<ContractChangeLogVO> toVOs(List<UserContractChangeLog> logs) {
		List<ContractChangeLogVO> vos = new ArrayList<>();
		for (UserContractChangeLog log : logs) {
			vos.add(toVO(log));
		}
		return vos;
	}


	private static ContractChangeLogVO toVO(UserContractChangeLog log) {
		ContractChangeLogVO vo = new ContractChangeLogVO();
		vo.setUserId(log.getUserId());
		vo.setContractNumber(log.getContractNumber());
		vo.setUpdatorUserId(log.getUpdatorUserId());
		vo.setUpdatorName(log.getUpdatorName());
		vo.setUpdateTime(DateTimeUtil.date2String(log.getUpdateTime()));
		vo.setChangeType(log.getChangeType());

		return vo;
	}

}
