package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DataRestClient;
import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/23 20:48
 */
@Service
public class SyncZtUserService {

    private Logger logger= LoggerFactory.getLogger(SyncZtUserService.class);

    @Autowired
    private UserService userService;

    @Autowired
    private DhrRestClient dhrRestClient;

    @Autowired
    private SyncUserService syncUserService;

    @Autowired
    private DataRestClient dataRestClient;

    @Autowired
    private TeamService teamService;

    @Autowired
    private MMISMegviiConfigService mmisMegviiConfigService;

    /**
     * 从中台同步岗位
     * @return
     */
    public String syncZtUserPosition() {
        StringBuilder msg = new StringBuilder();

        List<User> userList = getZTUserList(null, null);
        if (userList == null || userList.size() == 0) {
            msg.append("没有从中台同步到人员数据");
            return msg.toString();
        }

        for (User user : userList) {
            try {
                //比较岗位变化
                User oldUser = userService.getUserByWorkNumber(user.getWorkNumber());
                if (oldUser == null || oldUser.getStatus().indexOf("已保留") != -1 || oldUser.getStatus().indexOf("已禁用") != -1) {
                    continue;
                }
                if (!Objects.equals(user.getPosition(), oldUser.getPosition())) {
                    oldUser.setPosition(user.getPosition());
                    userService.updateUser(oldUser);
                    msg.append(user.getUserId() + "修改岗位：" + oldUser.getPosition() + "<br/>");
                    continue;
                }
            } catch (Exception e) {
                logger.error(user.getUserId() + "修改岗位异常" + e);
                msg.append(user.getUserId() + "修改岗位异常" + e);
                continue;
            }
        }
        return msg.toString();
    }



    /**
     * 同步中台人员信息
     * @param userId
     * @return
     */
    public String syncZTUser(String userId, String teamCode) {
        StringBuilder message = new StringBuilder();

        List<User> userList = getZTUserList(userId, teamCode);
        if (userList == null || userList.size() == 0) {
            message.append("没有从中台同步到人员数据");
            return message.toString();
        }
        for (User user : userList) {
            try {
//               比较人员信息变化
                String result = syncUserService.compareUserInfo(user);
                if (result != null && !result.equals("")) {
                    message.append(result);
                }

            } catch (Exception e) {
                logger.error(user.getUserId() + "同步中台信息错误："+e.getMessage());
                message.append(user.getUserId() + "同步中台信息错误" + "<br/>");
                continue;
            }
        }
        return message.toString();
    }



    /**
     * 拉取中台人员信息，转化为userList
     * @param userId
     * @return
     */
    private List<User> getZTUserList(String userId, String teamCode) {
        JSONArray ztUsers = dataRestClient.getAllUsers();
        List<User> userList = new ArrayList<>();

        for (Object ztUser : ztUsers) {
            JSONObject data = (JSONObject) ztUser;

            if (userId != null) {
                if (!data.getString("username").equals(userId)) {
                    continue;
                }
            }

            if (teamCode != null) {
                if (!data.getString("teamId").equals(teamCode)) {
                    continue;
                }
            }

            try {
                userList.add(getZTUserInfo(data));
            } catch (Exception e) {
                logger.error(data.getString("username") + "中台信息转化错误:" + e.getMessage());
                continue;
            }
        }
        return userList;
    }



    /**
     * 将中台人员json 转换为user对象
     * @param data
     * @return
     */
    private User getZTUserInfo(JSONObject data) throws Exception {
        User user = new User();

        user.setWorkNumber(data.getString("userId"));
        user.setUserId(data.getString("username"));
        user.setSpell(data.getString("spell"));
        user.setCell(data.getString("cellPhone"));
        user.setEmployBase(data.getString("location"));
        user.setBuddy(data.getString("buddy"));
        user.setHrbp(data.getString("hrbp"));
        user.setSiteHrg(data.getString("siteHr"));

        user.setExpectDate(data.getString("expectDate")!=null?DateTimeUtil.string2DateYMD2(data.getString("expectDate")):null);
        user.setRegularDate(data.getString("regularDate")!=null?DateTimeUtil.string2DateYMD2(data.getString("regularDate")):null);
        user.setEmployType(dhrRestClient.getEmpTypeValue(data.getString("employeeType")));
        user.setMentor(data.getString("mentor"));
        Team team = teamService.getTeamByCode(data.getString("teamId"));
        if (team == null) {
            throw new Exception(user.getUserId() + "***team不存在!code = " + data.getString("teamId"));
        }
        user.setTeam(team.getName());
        user.setTeamId(team.getId());

        if (user.getHrbp() == null || user.getHrbp().equals("")) {
            user.setHrbp(team.getHrg());
        }

        user.setBirthday(data.getString("birthday")!=null?DateTimeUtil.string2DateYMD2(data.getString("birthday")):null);
        user.setUpdateTime(null);

        if ("admin".equals(data.getString("updater"))) {
            user.setUpdater("admin");
        } else {
            User updater = userService.getUserByWorkNumber(data.getString("updater"));
            if (updater == null) {
                throw new Exception(user.getUserId() + "***updater不存在!code = " + data.getString("updater"));
            }
            user.setUpdater(updater.getUserId());
        }
        user.setCompany(data.getString("company"));
        user.setPosition(data.getString("position"));
        user.setTitle(data.getString("title"));
        user.setReferTitle(data.getString("referTitle"));
        user.setWorkBase(data.getString("workBase"));
        user.setCertType(dhrRestClient.getCertTypeValue(data.getString("cert_type")));
        user.setCertNo(data.getString("cert_no"));
        user.setAddress(data.getString("address"));
        user.setContractCount(data.getString("contract_count")!=null?Integer.valueOf(data.getString("contract_count")):0);

        return user;
    }

}
