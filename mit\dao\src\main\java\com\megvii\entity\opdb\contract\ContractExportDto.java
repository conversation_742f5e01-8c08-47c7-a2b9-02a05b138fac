package com.megvii.entity.opdb.contract;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ContractExportDto {
	private static final long serialVersionUID=1L;

	private String workNumber;
	private String userId;
	private String spell;
	private String expectDate;
	private String signStatus;
	private String contractNumber;
	private String company;
	private String startDate;
	private String endDate;
	private String contractStatus;
}
