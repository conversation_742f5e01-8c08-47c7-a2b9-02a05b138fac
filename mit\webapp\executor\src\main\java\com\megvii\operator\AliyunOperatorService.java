package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.service.AliyunService;
import com.megvii.service.EntryService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 阿里云操作类
 */
@Service
public class AliyunOperatorService extends BaseOperatorService {

    @Autowired
    private AliyunService aliyunService;

    @Autowired
    private AliyunOperatorService aliyunOperatorService;

    @Autowired
    private EntryService entryService;

    /**
     *
     * 主操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId=paraMap.get("userId");
        if(userId!=null){
            return OperateByUserId((String) userId);
        }else {
            LocalDateTime dateTime=LocalDateTime.now().minusDays(10);   //同步十天内数据
//            List<AliyunUserInfo> userInfoList=aliyunUserInfoService.getSimpleUserInfoListByExpectDate(dateTime);
            List<EntryUserInfo> userInfoList = entryService.getSimpleUserInfoListByExpectDate(dateTime);
            StringBuilder msg=new StringBuilder();
            for (EntryUserInfo userInfo:userInfoList){
                OperateResult operateResult=aliyunOperatorService.operateOnce(paraMap,userInfo);
                msg.append(operateResult.getMsg()+"<br/>");
            }
            return new OperateResult(1,msg.toString());
        }
    }

    /**
     *
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        EntryUserInfo userInfo=(EntryUserInfo) params[0];


        String msg = aliyunService.synData(entryService.selectUserInfoById(userInfo.getId()),false);
        return new OperateResult(1,msg);
    }

    /**
     *
     * 根据userId操作单个人的信息
     * @param userId
     * @return
     */
    public OperateResult OperateByUserId(String userId){
//        AliyunUserInfo userInfo=aliyunUserInfoService.getUserInfoByUserId(userId);
        EntryUserInfo entryUserInfo = entryService.selectUserInfoByUserid(userId);

        if (entryUserInfo != null){
            String msg = aliyunService.synData(entryUserInfo,true);
            return new OperateResult(1,msg);
        }else {
            return new OperateResult(1,"人员信息不存在");
        }
    }
}
