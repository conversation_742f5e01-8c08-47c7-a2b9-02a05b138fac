package com.megvii.common;

import java.io.File;
import java.io.InputStream;
import java.util.Date;
import java.util.Properties;
import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MailUtil {

    private static Properties props;
    private static Logger logger= LoggerFactory.getLogger(MailUtil.class);


    static {
        loadProps();
    }
    synchronized static private void loadProps(){
        props = new Properties();
        InputStream in = null;
        in = MailUtil.class.getClassLoader().getResourceAsStream("mail.properties");
        try{
            props.load(in);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
    public static void sendHtmlEmail(String subject,String content,String to) throws MessagingException {
        Session session=Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(props.getProperty("mail.from"), props.getProperty("mail.password"));
            }
        });
        MimeMessage message=new MimeMessage(session);
        message.setSubject(subject,"utf-8");
        message.setFrom(new InternetAddress(props.getProperty("mail.from")));
        //收件人
        InternetAddress[] addressesTo = null;
        //测试时不发送
        if(props.getProperty("mail.test").equals("true")){
            logger.info("email****************test");
            addressesTo = new InternetAddress[1];
            addressesTo[0] = new InternetAddress("<EMAIL>");
        }else{
            if (to != null && to.trim().length() > 0) {
                String[] receiveList = to.split(",");
                int receiveCount = receiveList.length;
                if (receiveCount > 0) {
                    addressesTo = new InternetAddress[receiveCount];
                    for (int i = 0; i < receiveCount; i++) {
                        if(!"".equals(receiveList[i]))
                            addressesTo[i] = new InternetAddress(receiveList[i]);
                    }
                }
            }else {
                logger.error("收件人为空！"+subject);
                return;
            }
        }
        message.setRecipients(MimeMessage.RecipientType.TO,addressesTo);
        message.setSentDate(new Date());
        Multipart mainPart = new MimeMultipart();
        BodyPart html = new MimeBodyPart();
        html.setContent(content,"text/html; charset=utf-8");
        mainPart.addBodyPart(html);
        message.setContent(mainPart);
        Transport.send(message);
    }
    public static void sendAttachmentEmail(String subject,String content,String to,String file) throws Exception {
        Session session=Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(props.getProperty("mail.from"), props.getProperty("mail.password"));
            }
        });
        MimeMessage message=new MimeMessage(session);
        message.setSubject(subject,"utf-8");
        message.setFrom(new InternetAddress(props.getProperty("mail.from")));
        //收件人
        InternetAddress[] addressesTo = null;
        //测试时不发送
        if(props.getProperty("mail.test").equals("true")){
            logger.info("email****************test");
            addressesTo = new InternetAddress[4];
            addressesTo[0] = new InternetAddress("<EMAIL>");
            addressesTo[1] = new InternetAddress("<EMAIL>");
            addressesTo[2] = new InternetAddress("<EMAIL>");
            addressesTo[3] = new InternetAddress("<EMAIL>");
//            addressesTo[2] = new InternetAddress("<EMAIL>");
//            addressesTo[3] = new InternetAddress("<EMAIL>");
//            addressesTo[4] = new InternetAddress("<EMAIL>");
//            addressesTo[5] = new InternetAddress("<EMAIL>");
//            addressesTo[6] = new InternetAddress("<EMAIL>");
//            addressesTo[7] = new InternetAddress("<EMAIL>");
        }else{
            if (to != null && to.trim().length() > 0) {
                String[] receiveList = to.split(",");
                int receiveCount = receiveList.length;
                if (receiveCount > 0) {
                    addressesTo = new InternetAddress[receiveCount];
                    for (int i = 0; i < receiveCount; i++) {
                        if(!"".equals(receiveList[i]))
                            addressesTo[i] = new InternetAddress(receiveList[i]);
                    }
                }
            }else {
                logger.error("收件人为空！"+subject);
                return;
            }
        }
        message.setRecipients(MimeMessage.RecipientType.TO,addressesTo);
        message.setSentDate(new Date());
        Multipart mainPart = new MimeMultipart();
        BodyPart html = new MimeBodyPart();
        html.setContent(content,"text/html; charset=utf-8");
        mainPart.addBodyPart(html);
        MimeBodyPart mimeBodyPart = new MimeBodyPart();
        mimeBodyPart.attachFile(file);
        mainPart.addBodyPart(mimeBodyPart);
        message.setContent(mainPart);
        Transport.send(message);
    }
    public static void sendImgEmail(String subject,String content,String to,String file) throws Exception {
        Session session=Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(props.getProperty("mail.from"), props.getProperty("mail.password"));
            }
        });
        MimeMessage message=new MimeMessage(session);
        message.setSubject(subject,"utf-8");
        message.setFrom(new InternetAddress(props.getProperty("mail.from")));
        //收件人
        InternetAddress[] addressesTo = null;
        //测试时不发送
        if(props.getProperty("mail.test").equals("true")){
            logger.info("email****************test");
            addressesTo = new InternetAddress[2];
            addressesTo[0] = new InternetAddress("<EMAIL>");
            addressesTo[1] = new InternetAddress("<EMAIL>");
        }else{
            if (to != null && to.trim().length() > 0) {
                String[] receiveList = to.split(",");
                int receiveCount = receiveList.length;
                if (receiveCount > 0) {
                    addressesTo = new InternetAddress[receiveCount];
                    for (int i = 0; i < receiveCount; i++) {
                        if(!"".equals(receiveList[i]))
                            addressesTo[i] = new InternetAddress(receiveList[i]);
                    }
                }
            }else {
                logger.error("收件人为空！"+subject);
                return;
            }
        }
        /**
         * 设置收件人地址（可以增加多个收件人、抄送、密送），即下面这一行代码书写多行
         * MimeMessage.RecipientType.TO:发送
         * MimeMessage.RecipientType.CC：抄送
         * MimeMessage.RecipientType.BCC：密送
         */
        message.setRecipients(MimeMessage.RecipientType.TO, addressesTo);

        //设置邮件正文
        //msg.setContent("测试发邮件！", "text/html;charset=UTF-8");
        // 5. 创建图片"节点"
        MimeBodyPart image = new MimeBodyPart();
        // 读取本地文件
        DataHandler dh = new DataHandler(new FileDataSource(new File(file)));
        // 将图片数据添加到"节点"
        image.setDataHandler(dh);
        // 为"节点"设置一个唯一编号（在文本"节点"将引用该ID）
        image.setContentID("mailTestPic");
        // 6. 创建文本"节点"
        MimeBodyPart text = new MimeBodyPart();
        // 这里添加图片的方式是将整个图片包含到邮件内容中, 实际上也可以以 http 链接的形式添加网络图片
        text.setContent(content+"<br/><img src='cid:mailTestPic'/></a>", "text/html;charset=UTF-8");
        // 7. （文本+图片）设置 文本 和 图片"节点"的关系（将 文本 和 图片"节点"合成一个混合"节点"）
        MimeMultipart mm_text_image = new MimeMultipart();
        mm_text_image.addBodyPart(text);
        mm_text_image.addBodyPart(image);
        mm_text_image.setSubType("related"); // 关联关系

        // 8. 将 文本+图片 的混合"节点"封装成一个普通"节点"
        // 最终添加到邮件的 Content 是由多个 BodyPart 组成的 Multipart, 所以我们需要的是 BodyPart,
        // 上面的 mailTestPic 并非 BodyPart, 所有要把 mm_text_image 封装成一个 BodyPart
        MimeBodyPart text_image = new MimeBodyPart();
        text_image.setContent(mm_text_image);


        MimeMultipart mm = new MimeMultipart();
        mm.addBodyPart(text_image);
        mm.setSubType("mixed");  // 混合关系

        // 11. 设置整个邮件的关系（将最终的混合"节点"作为邮件的内容添加到邮件对象）
        message.setContent(mm);

        //设置邮件的发送时间,默认立即发送
        message.setSentDate(new Date());
        Transport.send(message);
    }
    public static void main(String[] args) {
        try{
            sendHtmlEmail("测试","<p>哈哈哈哈</p></p><font size=\"3\" color=\"red\">快求绕</font></p>","<EMAIL>");
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
