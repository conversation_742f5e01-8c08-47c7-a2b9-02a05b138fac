import { Component, OnInit, ViewChild } from '@angular/core'
import { ContractTableComponent } from '../component/contract-table/contract-table.component'
import { ParamStoreService } from '../../core/services/paramStore.service'

@Component({
  selector: 'app-new-sign',
  templateUrl: './new-sign.component.html',
  styleUrls: ['./new-sign.component.less']
})
export class NewSignComponent implements OnInit {
  isShowSelf = true
  selectedIndex = 0

  @ViewChild('thisWeek', { static: true }) thisWeek: ContractTableComponent
  @ViewChild('lastWeek', { static: true }) lastWeek: ContractTableComponent
  @ViewChild('notSign', { static: true }) notSign: ContractTableComponent

  constructor(private paramStoreService: ParamStoreService) {}

  ngOnInit(): void {
    this.selectedIndex = this.paramStoreService.getMap('newSign')
  }
  onActivate(event) {
    this.isShowSelf = false
  }
  onDeactivate(event) {
    this.isShowSelf = true
  }

  selectedIndexChange(event) {
    this.selectedIndex = event
    this.paramStoreService.saveMap('newSign', event)
  }
}
