import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { SystemComponent } from './system.component'
import { LogComponent } from './log/log.component'
import { SystemRoutingModule } from './system-routing.module'
import { SharedModule } from '../shared.module'
import { SendRequest } from '../core/services'
import { SystemService } from './system.service'

@NgModule({
  declarations: [SystemComponent, LogComponent],
  imports: [CommonModule, SharedModule, SystemRoutingModule],
  providers: [SendRequest, SystemService]
})
export class SystemModule {}
