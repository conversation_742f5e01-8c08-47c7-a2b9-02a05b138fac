package com.megvii.aop;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;

/**
 * 对POST请求做统一日志处理
 */
public class LoggingRestClientInterceptor implements ClientHttpRequestInterceptor {
    Logger logger= LoggerFactory.getLogger(LoggingRestClientInterceptor.class);

    @Override
    public ClientHttpResponse intercept(HttpRequest httpRequest, byte[] bytes, ClientHttpRequestExecution clientHttpRequestExecution) throws IOException {
        UUID uuid=UUID.randomUUID();


        ClientHttpResponse response = clientHttpRequestExecution.execute(httpRequest, bytes);
        BufferingClientHttpResponseWrapper responseWrapper=new BufferingClientHttpResponseWrapper(response);
        //不记录日志
        if (!httpRequest.getMethod().equals(HttpMethod.GET) &&
                !httpRequest.getURI().toString().contains("/members") &&
                !httpRequest.getURI().toString().contains("/photo/$value") &&
                !httpRequest.getURI().toString().contains("/VerifyLogin.jsp") &&
                !httpRequest.getURI().toString().contains("/KayangWebApi/Data/PutData")
        ) {
            infoRequest(httpRequest,bytes,uuid);
            infoResponse(responseWrapper,uuid);
        }
        return responseWrapper;
    }

    private void infoRequest (HttpRequest request, byte[] bytes,UUID uuid) {
        logger.info(uuid.toString()+"===========================请求开始===========================");
        logger.info("URI         :"+request.getURI());
        logger.info("Method      : {}", request.getMethod());
        try {
            boolean a=request.getHeaders().getContentType().getType().equals(MediaType.MULTIPART_FORM_DATA.getType());
            if (a){
                logger.info("Request body: {}", "文件");
            }else {
                logger.info("Request body: {}", new String(bytes, "UTF-8"));
            }

        }catch (Exception e){
            logger.error(request.getURI() + "记录请求日志异常:" + e.getMessage());
        }
        logger.info(uuid.toString()+"===========================请求结束===========================\n\n");
    }

    private void infoResponse(ClientHttpResponse response,UUID uuid){
        logger.info(uuid.toString()+"===========================结果 begin===========================");
        try{

            logger.info("Status code  : {}", response.getStatusCode());
            logger.info("Status text  : {}", response.getStatusText());
            logger.info("Headers      : {}", response.getHeaders());
            logger.info("Response body: {}", StreamUtils.copyToString(response.getBody(), Charset.defaultCharset()));
        }catch (Exception e){
            logger.error(e.getMessage());
        }
        logger.info(uuid.toString()+"===========================结果 end===========================\n\n");

    }


    final class BufferingClientHttpResponseWrapper implements ClientHttpResponse {
        private final ClientHttpResponse response;
        private byte[] body;

        BufferingClientHttpResponseWrapper(ClientHttpResponse response) {
            this.response = response;
        }

        public HttpStatus getStatusCode() throws IOException {
            return this.response.getStatusCode();
        }

        public int getRawStatusCode() throws IOException {
            return this.response.getRawStatusCode();
        }

        public String getStatusText() throws IOException {
            return this.response.getStatusText();
        }

        public HttpHeaders getHeaders() {
            return this.response.getHeaders();
        }

        public InputStream getBody() throws IOException {
            if (this.body == null) {
                this.body = StreamUtils.copyToByteArray(this.response.getBody());
            }

            return new ByteArrayInputStream(this.body);
        }

        public void close() {
            this.response.close();
        }
    }

}
