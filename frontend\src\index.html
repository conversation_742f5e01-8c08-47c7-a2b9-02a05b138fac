<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>MIT</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <!--<link rel="stylesheet" href="./assets/css/iconfont/iconfont.css">-->
    <!--<script src="./assets/css/iconfont/iconfont.js"></script>-->
    <style>
      .browser-dialog {
        box-sizing: border-box;
        padding: 0 0 24px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        font-variant: tabular-nums;
        line-height: 1.5;
        list-style: none;
        font-feature-settings: 'tnum';
        position: absolute;
        top: 100px;
        left: 50%;
        margin-left: -260px;
        width: auto;
        pointer-events: none;
        z-index: 999;
      }

      .browser-modal-title {
        margin: 0;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        word-wrap: break-word;
      }

      .browser-modal-content {
        width: 520px;
        position: relative;
        background-color: #fff;
        background-clip: padding-box;
        border: 0;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        pointer-events: auto;
        margin: 0 auto;
      }

      .browser-modal-close {
        position: absolute;
        top: 15px;
        right: 20px;
        padding: 0;
        color: rgba(0, 0, 0, 0.45);
        border: 0;
        outline: 0;
        font-size: 16px;
        background: none;
        transform: scaleX(1.5);
        cursor: pointer;
      }

      .browser-modal-header {
        padding: 16px 24px;
        color: rgba(0, 0, 0, 0.65);
        background: #fff;
        border-bottom: 1px solid #e8e8e8;
        border-radius: 4px 4px 0 0;
      }

      .browser-modal-body {
        padding: 24px;
        font-size: 14px;
        line-height: 1.5;
        word-wrap: break-word;
      }
      .browser-modal-body p {
        line-height: 30px;
      }
    </style>
  </head>
  <body>
    <script>
      (function () {
        var chromeBrowser = 'https://www.google.com/intl/zh-CN/chrome/';
        var safariBrowser = 'https://support.apple.com/zh-cn/HT204416';
        var firefoxBrowser = 'https://www.firefox.com.cn/';
        var Sys = {};
        var ua = navigator.userAgent.toLowerCase();
        var s;
        var modalHeight = window.outerHeight / 2;
        var modalWidth = window.outerWidth / 2;
        (s = ua.match(/rv:([\d.]+)\) like gecko/))
          ? (Sys.ie = s[1])
          : (s = ua.match(/msie ([\d\.]+)/))
          ? (Sys.ie = s[1])
          : (s = ua.match(/edge\/([\d\.]+)/))
          ? (Sys.edge = s[1])
          : (s = ua.match(/firefox\/([\d\.]+)/))
          ? (Sys.firefox = s[1])
          : (s = ua.match(/(?:opera|opr).([\d\.]+)/))
          ? (Sys.opera = s[1])
          : (s = ua.match(/chrome\/([\d\.]+)/))
          ? (Sys.chrome = s[1])
          : (s = ua.match(/version\/([\d\.]+).*safari/))
          ? (Sys.safari = s[1])
          : 0;
        // 根据关系进行判断
        if (Sys.ie) {
          var link = '点击下载';
          var browser = chromeBrowser;
          var tips =
            '系统检测到您当前的浏览器已过期，存在安全风险，可能导致网站不能正常访问！为了您能正常使用同时获得最佳体验推荐使用现代Web浏览器。';
          modal(tips, browser, link);
        } else if (parseInt(Sys.chrome) < 70) {
          var browser = chromeBrowser;
          modal(tips, browser, link);
        } else if (parseInt(Sys.firefox) < 65) {
          var browser = firefoxBrowser;
          modal(tips, browser, link);
        } else if (parseInt(Sys.safari) <= 11) {
          var browser = safariBrowser;
          modal(tips, browser, link);
        }

        function modal(tips, browser, link) {
          tips = tips
            ? tips
            : '系统检测到您当前的浏览器版本过低，存在安全风险，可能导致网站不能正常访问！为了您能正常使用同时获得最佳体验请先升级浏览器。';
          link = link ? link : '点击升级';
          var el = document.createElement('div');
          el.innerHTML =
            "<div class='browser-dialog'>" +
            "<div class='browser-modal-content'>" +
            "<button class='browser-modal-close'>X</button>" +
            "<div class='browser-modal-header'><div class='browser-modal-title'>提示</div></div>" +
            "<div class='browser-modal-body'>" +
            '<p>' +
            tips +
            '</p>' +
            "<p><a href='" +
            browser +
            "' target='_blank'>" +
            link +
            '</a></p>' +
            '</div>' +
            '</div>' +
            '</div>';
          document.body.appendChild(el);
          var dialog = document.querySelector('.browser-dialog');
          var dialogClose = document.querySelector('.browser-modal-close');
          dialogClose.addEventListener('click', function () {
            dialog.style.display = 'none';
          });
        }
      })();
    </script>
    <app-root></app-root>
  </body>
</html>
