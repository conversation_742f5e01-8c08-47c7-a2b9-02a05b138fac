package com.megvii;

import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DingDingRestClient;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import com.megvii.operator.SyncDingdingIdOperatorService;
import com.megvii.service.DingdingService;
import com.megvii.service.TeamService;
import com.megvii.service.UserService;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DingDingTest {
    @Autowired
    DingDingRestClient client;

    @Autowired
    private DingdingService dingdingService;

    @Autowired
    private UserService userService;

    @Autowired
    private TeamService teamService;

//    @Autowired
//    private DingdingOperatorService dingdingOperatorService;

    @Autowired
    private DingDingRestClient dingDingRestClient;

    @Autowired
    private SyncDingdingIdOperatorService syncDingdingIdOperatorService;


    @Test
    public void testGetToken(){
        try{
            System.out.println(client.getToken());
            return;
        }catch (Exception e){

        }

    }

    @Test
    public void testCreateUser() throws Exception {
//        client.createDingTalkUser("姜宇鸿",new Integer[]{73695179},"15001234726","123456","<EMAIL>");
//        client.updateUserDept("163060284422873716", "姜宇鸿", new int[]{73695179});
        User user = userService.getUserByUserId("jiangyuhong");
        dingdingService.createDingdingUser(user);

    }

    @Test
    public void updateDingdingUser() throws Exception {
//        client.createDingTalkUser("姜宇鸿",new Integer[]{73695179},"15001234726","123456","<EMAIL>");
//        client.updateUserDept("163060284422873716", "姜宇鸿", new int[]{73695179});
        User user = userService.getUserByUserId("fanchao");
        dingdingService.updateDingdingUser(user,600190119);

    }
    @Test
    public void testGetUserDetail() throws Exception {
        client.getUserDetail("163060284422873716");
    }

    @Test
    public void testUpdateUser() {
        User user = userService.getUserByUserId("jiangyuhong");
        user.setUnionId("1111");
        user.setDingdingId("2222");
        userService.updateUserDingdingIdAndUnionid(user);
    }

//    @Test
//    public void testOnce() {
//
//        try {
//            dingdingOperatorService.OperateByUserId("jiakai");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @Test
    public void testAddUserChat() throws Exception {

        List<String> list = new ArrayList<>();
        list.add("163060284422873716");
        client.addUserChat("chatfe88703da8880f231d286a3f54c4e594", list);


    }

    @Test
    public void testCreateDept() throws Exception {
//        String id = client.createDept("测试添加群聊", null, "1");

//        List list = new ArrayList();
////        list.add("73695179");
//        list.add("134771333");

//        dingDingRestClient.updateUserDept("163060284422873716", "姜宇鸿AAA", list);



//        List<String> list = new ArrayList<>();
//        list.add("163060284422873716");
//        dingDingRestClient.delUserToChat("chatfe88703da8880f231d286a3f54c4e594", list);

        User user = userService.getUserByUserId("jiangyuhong");
        List<String> chatIds = new ArrayList<>();

        dingdingService.getUserDingdingChats(user, chatIds);
        System.out.println(1);

    }
    @Test
    public void testUpdateDeptContainSubDept() {
        Team team = new Team();
        team.setDingdingId("663632468");
        team.setName("指纹质量部");

        try {
            dingDingRestClient.updateDeptContainSubDept(team.getDingdingId(),team.getName());
        } catch (Exception e) {
            e.printStackTrace();
        }


    }
    @Test
    public void testUpdateDept() {
        Team team = new Team();
        team.setDingdingId("663632468");
        team.setName("指纹质量部");

        try {
            dingDingRestClient.updateDept(team, null);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Test
    public void testDeleteDept() {
        try {

            dingdingService.notContainSubDept();

//            dingDingRestClient.deleteDept("134771333");
//            String groupId = dingDingRestClient.createDept("测试第一1111级部门", null, "1");
//            String buId = dingDingRestClient.createDept("测试第二2222级部门", null, groupId);
//            String teamId = dingDingRestClient.createDept("测试第三3333级部门", null, buId);

            System.out.println(1);

//            JSONObject data = dingDingRestClient.getDeptDetail(285040734);

            System.out.println(2);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testSendMsg() throws Exception {
//        JSONObject body = new JSONObject();
//        body.put("title", "DHR消息提醒");
//        body.put("markdown", "# DHR消息提醒  \n  ##### DHR消息内容啊啊啊啊啊  \n  [this is a link](https://dhr.megvii-inc.com/home)");
//        body.put("single_title", "查看详情");
//        body.put("single_url", "https://dhr.megvii-inc.com/home");
//
//        JSONObject dhrdata = new JSONObject();
//        dhrdata.put("msgtype", "action_card");
//        dhrdata.put("action_card", body);

        //oa
//
//        JSONObject head = new JSONObject();
//        head.put("bgcolor", "FFBBBBBB");
//        head.put("text", "DHR头部标题");
//
//        JSONObject body = new JSONObject();
//        body.put("title", "正文标题");
//
//
//        JSONObject oa = new JSONObject();
//        oa.put("message_url", "https://dhr.megvii-inc.com/home");
//        oa.put("pc_message_url", "https://dhr.megvii-inc.com/home");
//        oa.put("head", head);
//        oa.put("body", body);
//
//         JSONObject dhrdata = new JSONObject();
//        dhrdata.put("msgtype", "oa");
//        dhrdata.put("oa", oa);


       JSONObject markdown = new JSONObject();
        markdown.put("title", "韩啸的请假流程");
        markdown.put("text", "### DHR消息提醒  \n  ##### " + "韩啸的请假流程需要您审批" + "  \n  [查看详情](" + "https://dhr-uat.megvii-inc.com/process/approval-flow?instanceid=11278&flowid=4351&fromemail=1&flowkey=F002" + ")");

        JSONObject dhrdata = new JSONObject();
        dhrdata.put("msgtype", "markdown");
        dhrdata.put("markdown", markdown);



        dingDingRestClient.sendTopMsg(dhrdata, null);

        System.out.println(2);

    }



    @Test
    public void testTemp() {


         String result = DigestUtils.md5Hex("876da4a266c46af2" + "1598525003");


        System.out.println(1);

         LocalDateTime time = LocalDateTime.ofEpochSecond(Long.valueOf("1598524248"), 0, ZoneOffset.of("+8"));
        Duration duration = Duration.between(time, LocalDateTime.now());

        System.out.println(1);
    }

    @Test
    public void testGetByMobile() {
        try {
            String msg = dingDingRestClient.getDingdingIdByMobile("15001234727");
            System.out.println(1);
        } catch (Exception e) {
            System.out.println(1);
        }
        System.out.println(1);
    }

    @Test
    public void testTemps() throws Exception {

        String token = dingDingRestClient.getTokenV2();

        System.out.println(1);
    }

    @Test
    public void testSyncDingdingId() throws Exception {

        syncDingdingIdOperatorService.operateMain(null, null);

    }


    @Test
    public void testAddData() throws Exception {

//        Team group = teamService.getTeamById(1500);
//        dingdingService.createDingDingDept(group, "1");
//
//        System.out.println(1);
//
//        Team bu = teamService.getTeamById(1501);
//        dingdingService.createDingDingDept(bu, group.getDingdingId());
//
//        System.out.println(1);
//
//        Team team = teamService.getTeamById(1502);
//        dingdingService.createDingDingDept(team, bu.getDingdingId());
//
//        System.out.println(1);

//        Team team = teamService.getTeamById(1503);
//        dingdingService.createDingDingDept(team, bu.getDingdingId());
//
//        System.out.println(1);


        User user = userService.getUserByUserId("sunkui");

        System.out.println(dingdingService.createDingdingUser(user));

        System.out.println(2);

        List<String> list = new ArrayList<>();
        list.add(user.getDingdingId());

        dingDingRestClient.delUserToChat("chataf9b42fbb7acb48d14ecb73c6fc68198", list);


        System.out.println(2);


    }


}
