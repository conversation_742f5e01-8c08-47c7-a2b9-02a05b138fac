package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.SyncSfUserService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/9/24 14:57
 */
@Service
public class SyncSfUserOperatorService extends BaseOperatorService {

    @Autowired
    private SyncSfUserService syncSfUserService;

    @Autowired
    private SyncSfUserOperatorService syncSfUserOperatorService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        Object userId=paraMap.get("userId");
        if (userId != null) {
            return OperateByUserId((String) userId);
        } else {
            return syncSfUserOperatorService.operateOnce(paraMap, params);
        }
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = syncSfUserService.syncSfUser(null);
        return new OperateResult(1,msg);
    }


    public OperateResult OperateByUserId(String userId){
        String msg= syncSfUserService.syncSfUser(userId);
        return new OperateResult(1,msg);
    }


}
