package com.megvii.controller.api;

import com.megvii.entity.opdb.UserRole;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/11 11:01
 */
@Data
public class UserRoleVO {

	private int id;
	private String userId;
	private String userName;
	private List<String> codes;


	public static UserRoleVO toVO(List<UserRole> userRole) {
		UserRoleVO vo = new UserRoleVO();
		vo.setId(userRole.get(0).getId());
		vo.setUserId(userRole.get(0).getUserId());
		vo.setUserName(userRole.get(0).getUserName());

		List<String> codes = new ArrayList<>();
		for (UserRole role : userRole) {
			codes.add(role.getRoleCode());
		}
		vo.setCodes(codes);

		return vo;
	}

}
