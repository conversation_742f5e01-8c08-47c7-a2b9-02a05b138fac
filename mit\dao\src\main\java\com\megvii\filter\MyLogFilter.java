package com.megvii.filter;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;

public class MyLogFilter extends Filter<ILoggingEvent> {
    @Override
    public FilterReply decide(ILoggingEvent iLoggingEvent) {
        if ("com.megvii.aop.LoggingRestClientInterceptor".equals(iLoggingEvent.getLoggerName())){
            return FilterReply.DENY;
        }else{
            return FilterReply.ACCEPT;
        }
    }
}
