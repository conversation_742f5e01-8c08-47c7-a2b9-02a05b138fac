package com.megvii.entity.opdb;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Data
public class UserDimissionContractSnapshotExtendPO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 第几笔
     */
    private Integer num;

    /**
     * 生效日期
     */
    private Date effectDate;

    /**
     * sers合计分数
     */
    private String sersNum;

    /**
     * 离职人员
     */
    private String userId;

    /**
     * 等待期
     */
    private String waitTime;
}
