{"name": "mit", "version": "2.0.0", "scripts": {"ng": "ng", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "serve": "ng serve --hmr --host 0.0.0.0 --port 4701 --disable-host-check --proxy-config proxy.conf.json", "build:test": "ng build --prod --aot --build-optimizer --stats-json --configuration=test", "build:uat": "ng build --prod --aot --build-optimizer --stats-json --configuration=test", "build:prod": "ng build --prod --aot --build-optimizer --stats-json --configuration=production --base-href /mit/", "conan-scan": "npx @megvii/conan scan --eslintType typescript --include ./ ", "conan-fix": "npx @megvii/conan fix --eslintType typescript --include ./ "}, "private": true, "dependencies": {"@angular-devkit/core": "^9.0.1", "@angular/animations": "^9.0.0", "@angular/common": "^9.0.0", "@angular/compiler": "^9.0.0", "@angular/core": "^9.0.0", "@angular/forms": "^9.0.0", "@angular/platform-browser": "^9.0.0", "@angular/platform-browser-dynamic": "^9.0.0", "@angular/router": "^9.0.0", "@antv/data-set": "^0.10.2", "@ckeditor/ckeditor5-angular": "^1.2.1", "@ckeditor/ckeditor5-build-classic": "^17.0.0", "@ckeditor/ckeditor5-ui": "^17.0.0", "@ngx-loading-bar/http": "^4.2.0", "@sentry/browser": "^6.19.7", "@types/echarts": "^4.4.3", "codemirror": "^5.52.0", "core-js": "^3.6.4", "generate-password": "^1.5.1", "highlight.js": "^9.18.1", "lodash": "^4.17.15", "ng-zorro-antd": "^8.5.2", "ng2-animate": "^1.2.1", "ng2-codemirror": "^1.1.3", "node-sass": "^6.0.1", "pdfjs-dist": "^2.2.228", "rxjs": "6.5.4", "rxjs-compat": "6.5.4", "save": "^2.4.0", "tslib": "^1.10.0", "type": "2.0.0", "web-animations-js": "^2.3.2", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.900.1", "@angular/cli": "~9.0.1", "@angular/compiler-cli": "^9.0.0", "@angular/language-service": "^9.0.0", "@angularclass/hmr": "^2.1.3", "@types/jasmine": "~3.5.3", "@types/jasminewd2": "~2.0.8", "@types/lodash": "^4.14.150", "@types/node": "^12.11.1", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.4.1", "karma-chrome-launcher": "~3.1.0", "karma-cli": "~2.0.0", "karma-coverage-istanbul-reporter": "~2.1.1", "karma-jasmine": "~3.1.0", "karma-jasmine-html-reporter": "^1.5.2", "protractor": "~5.4.3", "ts-md5": "^1.2.7", "ts-node": "~8.6.2", "tslint": "~6.0.0", "typescript": "~3.7.5"}}