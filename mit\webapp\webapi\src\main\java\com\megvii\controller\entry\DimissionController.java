package com.megvii.controller.entry;

import com.megvii.common.IPUtils;
import com.megvii.common.ParameterUtil;
import com.megvii.entity.opdb.User;
import com.megvii.service.DimissionService;
import com.megvii.service.IPWhiteListService;
import com.megvii.service.MailSendService;
import com.megvii.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/29 15:28
 */
@RestController
@RequestMapping("/api/V2")
public class DimissionController {

	private Logger logger = LoggerFactory.getLogger(DimissionController.class);

	@Autowired
	private DimissionService dimissionService;

	@Autowired
	private IPWhiteListService ipWhiteListService;

	@Autowired
	private UserService userService;

	@Autowired
	private MailSendService mailSendService;


	/**
	 * OA调用离职接口（已废弃）
	 * @param request
	 * @param par
	 * @return
	 */
	@PostMapping("/staff/dimission")
	@ResponseBody
	public String OA2MIT(HttpServletRequest request, @RequestBody String par) {

		//过滤ip,若用户在白名单内，则放行
		String ipAddress = IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "oa")) {
			return "IP不在白名单内";
		}

		logger.info("OA:" + ipAddress + "*****调用MIT离职接口*****" + par);

		Map map ;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error(e.getMessage());
			return e.getMessage();
		}
		if (map.get("user_id") == null) {
			return "user_id is null";
		}

		User user = userService.getUserByUserId(String.valueOf(map.get("user_id")));
		if (user == null) {
			logger.error("OA调用离职接口查询人员异常:" + map.get("user_id"));
			return "fail";
		}

		String msg = dimissionService.dismissAccount(user);

		mailSendService.sendMail("离职任务提醒", msg, "<EMAIL>");

		return "success";
	}



}
