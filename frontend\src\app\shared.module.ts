import { NgModule, ModuleWithProviders } from '@angular/core'
import { CommonModule } from '@angular/common'
import { HttpClientModule } from '@angular/common/http'
import { RouterModule } from '@angular/router'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { NgZorroAntdModule } from 'ng-zorro-antd'
import { SafeHtmlPipe, KeysPipe } from './core/pipes'

import {
  NoProjectComponent,
  ShortInfoComponent,
  TitleComponent,
  WordCountComponent
} from 'src/app/core/components'

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    NgZorroAntdModule
  ],
  providers: [],
  declarations: [
    SafeHtmlPipe,
    KeysPipe,
    NoProjectComponent,
    ShortInfoComponent,
    WordCountComponent,
    TitleComponent
  ],
  exports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    NgZorroAntdModule,
    SafeHtmlPipe,
    KeysPipe,
    NoProjectComponent,
    ShortInfoComponent,
    WordCountComponent,
    TitleComponent
  ]
})
export class SharedModule {
  static forRoot(): ModuleWithProviders<SharedModule> {
    return {
      ngModule: SharedModule,
      providers: [] // 需要导出的共享服务
    }
  }
}
