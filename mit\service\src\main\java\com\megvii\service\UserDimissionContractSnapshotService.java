package com.megvii.service;

import com.megvii.entity.opdb.UserDimissionContractSnapshotPO;
import com.megvii.mapper.UserDimissionContractSnapshotMapper;
import com.megvii.service.UserDimissionContractSnapshotService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 离职合同快照 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Service
public class UserDimissionContractSnapshotService {

  @Autowired
  private UserDimissionContractSnapshotMapper mapper;

  public Boolean addSnapshot(UserDimissionContractSnapshotPO po){
    return mapper.addSnapshot(po);
  }

  public Boolean deleteSnapshot(String dismissionId){
    return mapper.deleteSnapshot(dismissionId);
  }

  public UserDimissionContractSnapshotPO getSnapshot(String dismissionId){
    return mapper.getSnapshot(dismissionId);
  }

  public Boolean updateSnapshot(UserDimissionContractSnapshotPO po){
     return mapper.updateSnapshot(po);
  }
}
