package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.service.SyncZtUserService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/25 14:21
 */
@Service
public class SyncZtUserOperatorService extends BaseOperatorService {

    @Autowired
    private SyncZtUserService syncZtUserService;

    @Autowired
    private SyncZtUserOperatorService syncZtUserOperatorService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        Object userId = paraMap.get("userId");
        Object teamCode = paraMap.get("team");
        if (userId != null) {
            return OperateByUserId((String) userId);
        } else if (teamCode != null) {
            return OperateByTeam((String) teamCode);
        } else {
            return syncZtUserOperatorService.operateOnce(paraMap, params);
        }
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = syncZtUserService.syncZTUser(null, null);
        return new OperateResult(1,msg);
    }


    /**
     * 通过userId 执行单个人
     * @param userId
     * @return
     */
    public OperateResult OperateByUserId(String userId){
        String msg= syncZtUserService.syncZTUser(userId, null);
        return new OperateResult(1,msg);
    }

    /**
     * 通过teamCode 执行一个部门的人
     * @param team
     * @return
     */
    public OperateResult OperateByTeam(String team){
        String msg= syncZtUserService.syncZTUser(null, team);
        return new OperateResult(1,msg);
    }

}
