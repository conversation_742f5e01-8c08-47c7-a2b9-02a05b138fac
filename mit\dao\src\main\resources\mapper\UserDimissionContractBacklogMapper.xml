<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserDimissionContractBacklogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.UserDimissionContractBacklogPO">
        <id column="id" property="id" />
        <result column="dismission_id" property="dismissionId" />
        <result column="user_name" property="userName" />
        <result column="dismission_date" property="dismissionDate" />
        <result column="work_number" property="workNumber" />
        <result column="account" property="account" />
        <result column="leave_type" property="leaveType" />
        <result column="top_team" property="topTeam" />
        <result column="social_last_time" property="socialLastTime" />
        <result column="accumulation_last_time" property="accumulationLastTime" />
        <result column="compensation" property="compensation" />
        <result column="shares" property="shares" />
        <result column="competition" property="competition" />
        <result column="status" property="status" />
        <result column="flow_status" property="flowStatus" />
        <result column="termination_agreement" property="terminationAgreement" />
        <result column="resignation_certificate" property="resignationCertificate" />
        <result column="salary_statement" property="salaryStatement" />
        <result column="competition_notice" property="competitionNotice" />
        <result column="economic_agreement" property="economicAgreement" />
        <result column="self_email" property="selfEmail" />
        <result column="contract_zip" property="contractZip" />
        <result column="create_time" property="createTime" />
        <result column="deleted_at" property="deletedAt" />
        <result column="deleted_user" property="deletedUser" />
        <result column="company" property="company" />
        <result column="competition_duration" property="competitionDuration" />
        <result column="competition_start" property="competitionStart" />
        <result column="competition_end" property="competitionEnd" />
        <result column="stock_manage" property="stockManage" />
        <result column="special_company" property="specialCompany" />
        <result column="initial_start_time" property="initialStartTime" />
        <result column="contract_create_time" property="contractCreateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dismission_id, user_name, dismission_date, work_number, account, leave_type, top_team, social_last_time, accumulation_last_time, compensation, shares, competition, status, flow_status, termination_agreement, resignation_certificate, salary_statement, competition_notice, economic_agreement, self_email, contract_zip, create_time, deleted_at, deleted_user,company,competition_duration,competition_start,competition_end,stock_manage,special_company,initial_start_time,contract_create_time
    </sql>
    <select id="selectBacklogByFilter" resultMap="BaseResultMap">
        select
        bl.*
        from user_dimission_contract_backlog bl
        <where>
            <if test="filter.dismissionDateFrom != null ">
                AND DATE_FORMAT( bl.dismission_date, '%Y-%m-%d' ) &gt;=  DATE_FORMAT( #{filter.dismissionDateFrom} , '%Y-%m-%d' )
            </if>
            <if test="filter.dismissionDateTo != null ">
                AND DATE_FORMAT( bl.dismission_date, '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{filter.dismissionDateTo}, '%Y-%m-%d' )
            </if>
            <if test="filter.workNumber != null ">
                AND bl.work_number = #{filter.workNumber}
            </if>
            <if test="filter.userId != null ">
                AND bl.account like CONCAT ('%', #{filter.userId}, '%')
            </if>
            <if test="filter.spell != null ">
                AND bl.user_name like CONCAT ('%', #{filter.spell}, '%')
            </if>
            <if test="filter.flowStatus != null ">
                AND bl.flow_status = #{filter.flowStatus}
            </if>
            <if test="filter.topTeam != null ">
                AND bl.top_team like CONCAT ('%', #{filter.topTeam}, '%')
            </if>
            and deleted_at is null
        </where>
        order by dismission_date asc
    </select>
    <select id="selectAllBacklog" resultType="string">
        select
        bl.dismission_id
        from user_dimission_contract_backlog bl
    </select>
    <insert id="addBacklog" parameterType="com.megvii.entity.opdb.UserDimissionContractBacklogPO">
        INSERT INTO `user_dimission_contract_backlog`(
id, dismission_id, user_name, dismission_date, work_number, account, leave_type, top_team, social_last_time, accumulation_last_time, compensation, shares, competition, status, flow_status, termination_agreement, resignation_certificate, salary_statement, competition_notice, economic_agreement, self_email, contract_zip, create_time, deleted_at, deleted_user,company,competition_duration,competition_start,competition_end,stock_manage,special_company,initial_start_time,contract_create_time
        )
        VALUES (
        0,
        #{dismissionId},
        #{userName},
        #{dismissionDate},
        #{workNumber},
        #{account},
        #{leaveType},
        #{topTeam},
        #{socialLastTime},
        #{accumulationLastTime},
        #{compensation},
        #{shares},
        #{competition},
        #{status},
        #{flowStatus},
        #{terminationAgreement},
        #{resignationCertificate},
        #{salaryStatement},
        #{competitionNotice},
        #{economicAgreement},
        #{selfEmail},
        #{contractZip},
        #{createTime},
        #{deletedAt},
        #{deletedUser},
        #{company},
        #{competitionDuration},
        #{competitionStart},
        #{competitionEnd},
        #{stockManage},
        #{specialCompany},
        #{initialStartTime},
        #{contractCreateTime}
        );
    </insert>

    <select id="getBacklogByDismissionId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select *
        from user_dimission_contract_backlog
        where dismission_id = #{dismissionId}
    </select>
    <update id="updateBacklogInfo" parameterType="com.megvii.entity.opdb.UserDimissionContractBacklogPO">
        update user_dimission_contract_backlog set competition = #{competition},self_email = #{selfEmail},stock_manage = #{stockManage},special_company = #{specialCompany},initial_start_time = #{initialStartTime}
        where dismission_id = #{dismissionId}
    </update>
    <update id="updateBacklogflowStatus" parameterType="com.megvii.entity.opdb.UserDimissionContractBacklogPO">
        update user_dimission_contract_backlog set flow_status = #{flowStatus},`status` = #{status},stock_manage = #{stockManage},special_company = #{specialCompany},contract_create_time = #{contractCreateTime}
        where dismission_id = #{dismissionId}
    </update>
    <update id="updateContractFile" parameterType="com.megvii.entity.opdb.UserDimissionContractBacklogPO">
        update user_dimission_contract_backlog
        set
        termination_agreement = #{terminationAgreement},
        resignation_certificate = #{resignationCertificate},
        salary_statement = #{salaryStatement},
        competition_notice = #{competitionNotice},
        economic_agreement = #{economicAgreement},
        `status` = #{status}
        where dismission_id = #{dismissionId}
    </update>

    <update id="deleteOtherBacklog" parameterType="com.megvii.entity.opdb.UserDimissionContractBacklogPO">
        update user_dimission_contract_backlog set flow_status='-1',deleted_at=NOW()
        where dismission_id != #{dismissionId} and account=#{userId} and flow_status='0'
    </update>
</mapper>
