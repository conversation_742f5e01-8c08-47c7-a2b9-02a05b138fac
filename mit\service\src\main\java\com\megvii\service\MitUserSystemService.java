package com.megvii.service;

import com.megvii.entity.opdb.MitUserSystem;
import com.megvii.mapper.MitUserSystemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/6/18 11:21
 */
@Service
public class MitUserSystemService {

	@Autowired
	private MitUserSystemMapper mitUserSystemMapper;

	public boolean addMitUserSystem(MitUserSystem mitUserSystem) {
		MitUserSystem system = mitUserSystemMapper.selectMitUserSystemByUserId(mitUserSystem.getUserId());
		if (system == null) {
			if (mitUserSystemMapper.insertMitUserSystem(mitUserSystem) == 1) {
				return true;
			}
		} else {
			mitUserSystem.setId(system.getId());
			if (mitUserSystemMapper.updateMitUserSystem(mitUserSystem) == 1) {
				return true;
			}
		}
		return false;
	}

	public MitUserSystem selectMitUserSystemByUserId(String userId) {
		return mitUserSystemMapper.selectMitUserSystemByUserId(userId);
	}



}
