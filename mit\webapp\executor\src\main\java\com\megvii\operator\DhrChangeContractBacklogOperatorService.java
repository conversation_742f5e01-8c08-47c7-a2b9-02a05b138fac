package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/13 16:40
 */
@Service
public class DhrChangeContractBacklogOperatorService extends BaseOperatorService {

	@Autowired
	private DhrChangeContractBacklogOperatorService dhrChangeContractBacklogOperatorService;

	@Autowired
	private DhrService dhrService;

	@Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return dhrChangeContractBacklogOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = dhrService.getChangeContractBacklogFromDhr();
        return new OperateResult(1,msg);
    }



}
