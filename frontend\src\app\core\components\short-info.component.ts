import { Component, Input, OnInit, OnChanges } from '@angular/core'

@Component({
  selector: 'app-short-info',
  template: `
    <span *ngIf="!data.showTooltip">{{ data.info }}</span>
    <span *ngIf="data.showTooltip" nz-tooltip [nzTitle]="info" nzPlacement="topCenter">{{
      data.info
    }}</span>
  `
})
export class ShortInfoComponent implements OnInit, OnChanges {
  constructor() {}

  @Input() info = '' // 要处理的信息
  @Input() maxLength = 10 // 大于多少个字时隐藏

  data: any = {}

  ngOnInit() {}

  ngOnChanges() {
    if (this.info) {
      this.data.showTooltip = this.info.length > this.maxLength
      this.data.info = this.data.showTooltip
        ? this.info.substr(0, this.maxLength) + '...'
        : this.info
    }
  }
}
