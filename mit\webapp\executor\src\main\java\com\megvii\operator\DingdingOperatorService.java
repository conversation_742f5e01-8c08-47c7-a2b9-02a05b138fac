package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.DingdingService;
import com.megvii.service.MailSendService;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DingdingOperatorService extends BaseOperatorService {

	@Autowired
	private DingdingOperatorService dingdingOperatorService;

	@Autowired
	private UserService userService;

	@Autowired
	private DingdingService dingdingService;

	@Autowired
	private MailSendService mailSendService;


	@Override
	public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
		Object userId = paraMap.get("userId");
		Object date = paraMap.get("date");
		List<User> users;
		if (userId != null) {
			return OperateByUserId((String) userId);
		} else if (date != null) {
			UserFilter filter = new UserFilter();
			filter.setExpectDate((String) date);
			users = userService.getUsersByFilter(filter);
			if (users.size() > 0) {
				StringBuilder msg = new StringBuilder();
				for (User user : users) {
					OperateResult result = dingdingOperatorService.operateOnce(paraMap, user);
					msg.append(result.getMsg() + "<br/>");
				}
				mailSendService.sendMail("入职添加钉钉log", msg.toString(), "<EMAIL>");
				return new OperateResult(1, msg.toString());
			}
			return new OperateResult(1, "没有要添加的账号");
		} else {
			users = userService.getUserByExpectDate();
			if (users.size() > 0) {
				StringBuilder msg = new StringBuilder();
				for (User user : users) {
					OperateResult result = dingdingOperatorService.operateOnce(paraMap, user);
					msg.append(result.getMsg() + "<br/>");
				}
				mailSendService.sendMail("入职添加钉钉log", msg.toString(), "<EMAIL>");
				return new OperateResult(1, msg.toString());
			}
			return new OperateResult(1, "没有要添加的账号");
		}

	}


	/**
	 * 单次操作
	 *
	 * @param paraMap
	 * @param params  参数
	 * @return
	 * @throws Exception
	 */
	@Override
	protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
		User user = (User) params[0];
		if (user != null) {
			String result = dingdingService.createDingdingUser(user);
			return new OperateResult(1, result);
		} else {
			return new OperateResult(1, user.getUserId() + "信息不存在");
		}
	}

	public OperateResult OperateByUserId(String userId) throws Exception {
		User user = userService.getUserByUserId(userId);
		if (user != null) {
			if(user.getCreateTag() == 9){
				return new OperateResult(1, "ZK的人员不添加钉钉");
			}
			String result = dingdingService.createDingdingUser(user);
			return new OperateResult(1, result);
		} else {
			return new OperateResult(1, userId + "信息不存在");
		}
	}


}
