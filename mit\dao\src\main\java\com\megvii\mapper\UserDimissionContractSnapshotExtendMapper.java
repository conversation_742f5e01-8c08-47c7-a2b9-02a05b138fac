package com.megvii.mapper;

import com.megvii.entity.opdb.UserDimissionContractSnapshotExtendPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
public interface UserDimissionContractSnapshotExtendMapper {

  Boolean addSnapshotExtend(UserDimissionContractSnapshotExtendPO po);

  Boolean deleteSnapshotExtend(@Param("userId")String userId);

  List<UserDimissionContractSnapshotExtendPO> getSnapshotExtend(@Param("userId")String userId);
}
