package com.megvii;

import com.megvii.client.KoalaRestClient;
import com.megvii.service.AliyunService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestKoala {

    @Autowired
    AliyunService aliyunService;

//    @Autowired
//    private AliyunExecutorHandler aliyunExecutorHandler;

    @Autowired
    private KoalaRestClient client;

    @Test
    public void testSyncKoala() {

//        aliyunOperatorService.OperateByUserId("zhaohan");

        return;
    }

//    @Test
//    public void testNewKoala() {
//        try {
//            aliyunExecutorHandler.execute("userId=wangkainuo");
//
//
////            AliyunUserInfo info = aliyunUserInfoService.getUserInfoByUserId("jiangyuhong");
////            info.setSpell("姜宇鸿");
////            ImageFile file = FileUtil.getByteFromBase64(info.getPhotoCert(),info.getUserId());
////            Integer id = client.uploadBase(file, "jiangyuhong");
////            String koalaId = client.importSubject(id, info);
////            client.deleteSubject("9999999999999", "jiangyuhong");
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//
//    }





}
