package com.megvii.common;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 密码生成器
 */
public class PasswordGenerator {
    public final static String[] commonStr=new String[]{"0","1","2","3","4","5","6","7","8","9","a","q","w","e","r","t","y","u","i","o","p","a","s","d","f","g","h","j","k","l","z","x","c","v","b","n","m"};
    public final static String[] specialStr=new String[]{".","?","*","&","@"};

    /**
     *
     * @return 生成10位伪随机密码
     */
    public static String productPassword(){
        ThreadLocalRandom random=ThreadLocalRandom.current();
        int specialSit=random.nextInt(10);
        StringBuilder password=new StringBuilder();
        for (int i=0;i<10;i++){
            if(i==specialSit){
                password.append(specialStr[random.nextInt(specialStr.length-1)]);
            }else {
                password.append(commonStr[random.nextInt(commonStr.length-1)]);
            }
        }
        //密码规则 存在数字
        password.append(specialSit);
        return  password.toString();

    }
}
