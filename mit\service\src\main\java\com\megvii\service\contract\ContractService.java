package com.megvii.service.contract;

import com.alibaba.fastjson.JSONObject;
import com.megvii.client.ContractRestClient;
import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.FileUtil;
import com.megvii.common.ParameterUtil;
import com.megvii.entity.entry.ItUserInfoInternPO;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserDimissionContractBacklogPO;
import com.megvii.entity.opdb.contract.*;
import com.megvii.mapper.UserContractCategoryMapper;
import com.megvii.mapper.UserContractMapper;
import com.megvii.mapper.UserContractSnapshotMapper;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.*;
import net.lingala.zip4j.ZipFile;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

import static java.lang.Thread.sleep;

/**
 * <AUTHOR>
 * @date 2020/4/16 15:47
 */
@Service
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties", encoding = "UTF-8")
public class ContractService {

	private Logger logger = LoggerFactory.getLogger(ContractService.class);

	@Value("${SNAPSHOT_FILE_PATH}")
	private String snapFilePath;

	@Value("${CONTRACT_FILE_PATH}")
	private String contractFilePath;

	@Value("${TEMP_CONTRACT_FILE_PATH}")
	private String tempContractFilePath;

	@Value("${CONTRACT_ZIPFILE_PATH}")
	private String contractZipFilePath;

	@Autowired
	private UserContractMapper userContractMapper;

	@Autowired
	private UserContractSnapshotMapper userContractSnapshotMapper;

	@Autowired
	private UserService userService;

	@Autowired
	private DhrRestClient dhrRestClient;

	@Autowired
	private MMISMegviiConfigService mmisMegviiConfigService;

	@Autowired
	private ContractChangelLogService changelLogService;

	@Autowired
	private ContractRestClient contractRestClient;

	@Autowired
	private ContractRestService contractRestService;

	@Autowired
	private ContractBacklogService contractBacklogService;

	@Autowired
	private ChangeContractBacklogService changeContractBacklogService;

	@Autowired
	private DhrService dhrService;

	@Autowired
	private MailSendService mailSendService;

	@Autowired
	private UserDimissionContractBacklogService userDimissionContractBacklogService;

	@Autowired
	private UserContractCategoryMapper userContractCategoryMapper;

	@Autowired
	private MokaService mokaService;

	@Autowired
	private ItUserInfoInternService itUserInfoInternService;
	/**
	 * 线下签署合同
	 * @param contract
	 * @return
	 */
	public boolean offlineSign(UserContract contract) {
		//更新当前合同状态
		User user = userService.getUserByUserId(contract.getUserId());
		user.setContractStatus(ContractStatus.COMPLETE);
		user.setContractCount(Integer.valueOf(contract.getContractCount()));

		User updater = (User) SecurityUtils.getSubject().getPrincipal();

		contract.setContractType("劳动合同");
		contract.setMemo("线下签署");
		contract.setContractStatus(ContractStatus.COMPLETE);
		contract.setWorkNumber(user.getWorkNumber());
		contract.setSpell(user.getSpell());
		contract.setContractNumber(getContractNumber(user.getUserId(), user.getWorkNumber()));
		contract.setCreatorUserId(updater.getUserId());

		//更新续签状态
		UserContractBacklog backlog = contractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
		if (backlog != null) {
			backlog.setStatus(BacklogStatus.SIGN);
			contractBacklogService.updateContractBacklogStatus(backlog);
		}

		userService.updateUser(user);
		userContractMapper.insertUserContract(contract);
		dhrService.contractToDhr(contract);
		changelLogService.insertChangeLog(contract.getUserId(), contract.getContractNumber(), ContractChangeLogType.OFFLINE);
		return true;
	}
	public boolean dismissionOfflineSign(UserDimissionContractBacklogPO backlogByDismissionId,String contractFile) {
		//更新当前合同状态
		User user = userService.getUserByUserId(backlogByDismissionId.getAccount());
		user.setContractStatus(ContractStatus.COMPLETE);
		user.setDimissionDate(DateTimeUtil.string2DateYMD(DateTimeUtil.dateToString(backlogByDismissionId.getDismissionDate())));
		User updater = (User) SecurityUtils.getSubject().getPrincipal();
		UserContract contract = new UserContract();
		contract.setUserId(user.getUserId());
		contract.setContractCount(String.valueOf(user.getContractCount()));
		contract.setContractAttribute("固定期");
		contract.setCompany(backlogByDismissionId.getCompany());
		contract.setSignStatus("离职合同");
		contract.setMemo("线下签署");
		contract.setContractStatus(ContractStatus.COMPLETE);
		contract.setDismissionId(backlogByDismissionId.getDismissionId());
		contract.setSpell(user.getSpell());
		contract.setWorkNumber(user.getWorkNumber());
		contract.setStartDate(user.getExpectDate());
		contract.setEndDate(DateTimeUtil.string2DateYMD(DateTimeUtil.dateToString(backlogByDismissionId.getDismissionDate())));
		contract.setEffectDate(DateTimeUtil.string2DateYMD(DateTimeUtil.dateToString(backlogByDismissionId.getDismissionDate())));
		String contractNumber = userDimissionContractBacklogService.getContractNumber(user.getWorkNumber());
		contract.setContractNumber(contractNumber);
		contract.setCreatorUserId(updater.getUserId());
		contract.setContractType("劳动合同");
		contract.setUserContractFile(contractFile);
		userService.updateUser(user);
		userContractMapper.insertUserContract(contract);
		dhrService.contractToDhr(contract);
		changelLogService.insertChangeLog(contract.getUserId(), contract.getContractNumber(), ContractChangeLogType.OFFLINE);
		return true;
	}

	@Async
	public void downloadContractZip(String contractId) {
		logger.info(contractId + "**********开始下载zip");
		try {
			contractRestClient.downloadContractZip(contractId, contractFilePath);
		} catch (Exception e) {
			logger.error(contractId + "下载合同异常:" + e);
		}
		logger.info(contractId + "**********保存完成zip");
	}

	public void downloadContractZipSync(String contractId) {
		logger.info(contractId + "**********开始下载zip");
		try {
			contractRestClient.downloadContractZip(contractId, tempContractFilePath);
		} catch (Exception e) {
			logger.error(contractId + "下载合同异常:" + e);
		}
		logger.info(contractId + "**********保存完成zip");
	}

	//发送证明类合同附件的邮件
	public Boolean sendProveEmailAndAttachment( UserProveContractBacklog userProveContractBacklog){
		String contractId = userProveContractBacklog.getContractId();
		try {
			User user = userService.getUserByUserId(userProveContractBacklog.getUserId());
			String source=tempContractFilePath+contractId+".zip";
//			String target=contractZipFilePath+contractId+".zip";
			File sourceFile= new File(source);
			if(!sourceFile.exists()){
				logger.error(contractId + "发送证明类合同附件的邮件失败:文件不存在" );
					return false;
			}
//			File targetFile= new File(source);
//			FileCopyUtils.copy(sourceFile, targetFile);
			ZipFile zipFile  = new ZipFile(sourceFile);
			zipFile.removeFile("签署摘要.pdf");
			// 审批人和发起人均需要收到邮件
			String receiver = userProveContractBacklog.getUserId() + "@megvii.com," + userProveContractBacklog.getApproveUserId() + "@megvii.com";
			mailSendService.sendAttachmentEmail("证明文件查收",user.getSpell() + "您好，您申请的在职/收入证明已开具，请查收附件，如有问题请钉钉咨询" +userProveContractBacklog.getApprove() + "。",
					receiver,source);
			return true;
		}catch (Exception e) {
			logger.error(contractId + "发送证明类合同附件的邮件异常:" + e);
			return false;
		}
	}

	// 删除合同文件
	public String deleteContractFile(String contractId){
		String source=contractFilePath+contractId+".zip";
		String result = "";
		try {
			File file = new File(source);
			if (file.exists()) {
				file.delete();
				result = "文件删除成功，文件路径为："+contractId  + "<br/>";
			}else{
				result = "文件不存在，文件路径为："+contractId  + "<br/>";
			}
		} catch (Exception e) {
			logger.error("删除合同文件异常:" + e);
			result = "删除合同文件异常:" + contractId + "<br/>";
		}
		return result;
	}

	public Boolean sendEmailAndAttachment(String contractId,UserDimissionContractBacklogPO backlog){
		try {
			String source=contractFilePath+contractId+".zip";
			String target=contractZipFilePath+contractId+".zip";
			File sourceFile= new File(source);
			if(!sourceFile.exists()){
				logger.error(backlog.getAccount()+";contractId="+ contractId + "发送离职邮件失败:文件不存在" );
					return false;
			}
			File targetFile= new File(target);
			FileCopyUtils.copy(sourceFile, targetFile);
			ZipFile zipFile  = new ZipFile(targetFile);
			zipFile.removeFile("签署摘要.pdf");
			mailSendService.sendAttachmentEmail("离职相关文件查收","您好，附件为"+backlog.getCompany()+"给您发送的离职相关文件，请妥善保存。",
					backlog.getSelfEmail(),target);
			targetFile.delete();
			return true;
		}catch (Exception e){
			logger.error(backlog.getAccount()+";contractId="+ contractId + "发送离职邮件失败:" + e);
			return false;
		}

	}

	/**
	 * 删除快照并且将当前合同状态变为历史最新状态
	 * @param userId
	 * @return
	 */
	public String removeSnapshotUpdateStatus(String userId) {
		if (!delContractSnapshot(userId)) {
			return null;
		}

		User user = userService.getUserByUserId(userId);
		List<UserContract> contracts = getContractsByUserId(userId);
		//没有历史数据变为无合同
		if (contracts == null || contracts.size() == 0) {
			user.setContractStatus(ContractStatus.NOTHING);
		} else {
			//查询最新历史合同状态
			user.setContractStatus(contracts.get(0).getContractStatus());
		}
		userService.updateUser(user);
		return user.getContractStatus();
	}

	/**
	 * 离职删除快照
	 * @param userId
	 * @return
	 */
	public String removeDimSnapshotUpdateStatus(String userId) {

		User user = userService.getUserByUserId(userId);
		List<UserContract> contracts = getContractsByUserId(userId);
		//没有历史数据变为无合同
		if (contracts == null || contracts.size() == 0) {
			user.setContractStatus(ContractStatus.NOTHING);
		} else {
			//查询最新历史合同状态
			user.setContractStatus(contracts.get(0).getContractStatus());
		}
		userService.updateUser(user);
		return user.getContractStatus();
	}


	/**
	 * 取消作废后的操作
	 * @param contract
	 * @return
	 */
	public boolean finishCancel(UserContract contract) {
		//更新当前合同状态
		User user = userService.getUserByUserId(contract.getUserId());
		if (userContractMapper.selectUserStandardNewestContract(contract.getUserId()).getContractNumber().equals(contract.getContractNumber())){
			user.setContractStatus(ContractStatus.COMPLETE);
			userService.updateUser(user);
		}

		contract.setContractStatus(ContractStatus.COMPLETE);

		mailSendService.sendMail(user.getSpell() + "-" + user.getUserId() + "拒绝《" + contractRestService.getSubject(contract) + "》通知",
				user.getSpell() + "-" + user.getUserId() + "拒绝作废《" + contractRestService.getSubject(contract) + "》，请联系员工及时处理。",
				"<EMAIL>");
		//更新合同状态
		return updateUserContract(contract);
	}


	/**
	 * 签署完成后的操作
	 * 1.更新当前合同状态  已完成更新合同计数   已作废判断是否为变更，变更则-1
	 * 2.异步下载文件
	 * 3.更新合同状态
	 *
	 * @param contract
	 * @return
	 */
	public boolean finishSign(UserContract contract) {
		//更新当前合同状态
		User user = userService.getUserByUserId(contract.getUserId());
		user.setContractStatus(contract.getContractStatus());
		//已完成
		if (contract.getContractStatus().equals(ContractStatus.COMPLETE)) {
			//变更次数
			user.setContractCount(Integer.valueOf(contract.getContractCount()));
			UserContractBacklog backlog = contractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
			//更新续签状态
			if (backlog != null) {
				backlog.setStatus(BacklogStatus.SIGN);
				contractBacklogService.updateContractBacklogStatus(backlog);
			}
			UserChangeContractBacklog userChangeContractBacklog = changeContractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
			if(userChangeContractBacklog!= null){//更新转签状态
				userChangeContractBacklog.setStatus(BacklogStatus.SIGN);
				String newCompanyName = contractRestService.getCompanyNameByCode(userChangeContractBacklog.getNewContract());
				contract.setCompany(newCompanyName);
				user.setCompany(contract.getNewContract());
				user.setEmployBase(contractRestService.getWorkCityByCode(contract.getNewWorkCity()));
				changeContractBacklogService.updateContractBacklogStatus(userChangeContractBacklog);
			}
			UserDimissionContractBacklogPO backlogByDismissionId = userDimissionContractBacklogService.getBacklogByDismissionId(contract.getDismissionId());
			if(backlogByDismissionId!=null){
				contract.setContractStatus(BacklogStatus.EFFECT);
				backlogByDismissionId.setFlowStatus("3");
				backlogByDismissionId.setStatus(BacklogStatus.EFFECT);
				userDimissionContractBacklogService.updateBacklogflowStatus(backlogByDismissionId);
			}
			//异步信息回传DHR
			dhrService.contractToDhr(contract);
		} else if (contract.getContractStatus().equals(ContractStatus.TERMINATED)) {
			//作废 不是变更 合同签约次数-1
			if (!contract.getSignStatus().equals(SignStatus.CH)&&!contract.getSignStatus().equals(SignStatus.CHC)) {
				int count = user.getContractCount();
				user.setContractCount(count - 1);
				UserContractBacklog backlog = contractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
				//更新续签状态
				if (backlog != null) {
					backlog.setStatus(BacklogStatus.PULL);
					contractBacklogService.updateContractBacklogStatus(backlog);
				}
			}
			UserChangeContractBacklog userChangeContractBacklog = changeContractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
				if(userChangeContractBacklog!= null){//更新转签状态
					userChangeContractBacklog.setStatus(BacklogStatus.PULL);
					user.setCompany(contract.getContract());
					user.setEmployBase(contractRestService.getWorkCityByCode(contract.getWorkCity()));
					changeContractBacklogService.updateContractBacklogStatus(userChangeContractBacklog);
				}
			UserDimissionContractBacklogPO backlogByDismissionId = userDimissionContractBacklogService.getBacklogByDismissionId(contract.getDismissionId());
			if(backlogByDismissionId!=null){
				backlogByDismissionId.setFlowStatus("0");
				backlogByDismissionId.setStatus(BacklogStatus.PULL);
				userDimissionContractBacklogService.updateBacklogflowStatus(backlogByDismissionId);
			}
			//异步信息回传DHR
			dhrService.contractToDhr(contract);
		}

		if (userContractMapper.selectUserStandardNewestContract(contract.getUserId()).getContractNumber().equals(contract.getContractNumber())){
			userService.updateUser(user);
		}

		//异步下载合同文件
		downloadContractZip(contract.getContractId());
		//更新合同状态
		return updateUserContract(contract);
	}

	/**
	 * 离职员工签署回调
	 * @param contract
	 * @return
	 */
	public boolean dismissionSign(UserContract contract) {
		userDimissionContractBacklogService.signCallback(contract);
		//更新合同状态
		return updateUserContract(contract);
	}

	/**
	 * 创建合同并且发起签署
	 *
	 * @param snapshot
	 * @return
	 */
	public String createSignContract(UserContractSnapshot snapshot) throws Exception {
		if (snapshot.getFilePath() != null && !snapshot.getFilePath().equals("")) {
			return createContractAndFile(snapshot);
		} else {
			return createContract(snapshot);
		}
	}

	/**
	 * 创建证明类合同并发起签署
	 *
	 * @param proveInfo
	 * @return
	 */

	public String createProveContract(JSONObject proveInfo) throws Exception {
		try {
			JSONObject data = contractRestService.createProveContractData(proveInfo);
			logger.info("创建证明类合同数据："+data);
			return contractRestClient.createContract(data);
		}catch (Exception e){
			throw new Exception("创建证明类合同失败"+e.getMessage());
		}

	}


	/**
	 * 创建新签合同并且发起签署
	 *
	 * @param snapshot
	 * @return
	 */
	public String createNewSignContract(UserContractSnapshot snapshot) throws Exception {

		List<File> fileList = new ArrayList<>();
		if (snapshot.getFilePath() != null && !snapshot.getFilePath().equals("")) {
			for (String s : snapshot.getFilePath().split(",")) {
				fileList.add(new File(s));
			}
		}

		Long documentId = null;
		if (fileList.size() > 0) {
			documentId = contractRestClient.createContractByFile(fileList);
		}

		List<File> offerList = new ArrayList<>();
		if(snapshot.getOfferPath()!= null && !snapshot.getOfferPath().equals("")){
			offerList.add(new File(snapshot.getOfferPath()));
		}
		//判断offerList是否为空，若为空则不再offerDocumentId
		Long offerDocumentId = null;
		if(!offerList.isEmpty()){
			offerDocumentId = contractRestClient.createNewContractByFile(offerList,snapshot);
		}

//		Long offerDocumentId = contractRestClient.createNewContractByFile(offerList,snapshot);
		//合同body
		JSONObject data = contractRestService.createNewSignContractData(snapshot, true, documentId, offerDocumentId);
		//创建合同
		String contractId = contractRestClient.createContract(data);

		return afterSignNew(snapshot, contractId);
	}


	/**
	 * 带附件创建合同
	 * 1.生成body-->通过文件创建合同文档
	 * 2.创建合同-->生成body
	 * 3.添加文件-->创建合同
	 * 4.发起签署
	 *
	 * @param snapshot
	 * @return
	 */
	private String createContractAndFile(UserContractSnapshot snapshot) throws Exception {

		List<File> fileList = new ArrayList<>();
		for (String s : snapshot.getFilePath().split(",")) {
			fileList.add(new File(s));
			//添加附件
//			res = res && contractRestClient.addFileToContract(file, contractId);
		}
		Long documentId = contractRestClient.createContractByFile(fileList);

		//合同body
		JSONObject data = contractRestService.createContractData(snapshot, true, documentId);
		//创建合同
		String contractId = contractRestClient.createContract(data);

		return afterSign(snapshot, contractId);
	}


	/**
	 * 无附件直接创建合同
	 *
	 * @param snapshot
	 * @return
	 */
	private String createContract(UserContractSnapshot snapshot) throws Exception {
		//合同body
		JSONObject data = contractRestService.createContractData(snapshot, true, null);
		//创建并签署合同
		String contractId = contractRestClient.createContract(data);
		return afterSign(snapshot, contractId);

	}


	/**
	 * 合同成功发起签署后的操作
	 *
	 * @param snapshot
	 * @param contractId
	 * @return
	 */
	@Transactional
	public String afterSign(UserContractSnapshot snapshot, String contractId) throws Exception {
		String userId = snapshot.getUserId();
		//合同表插入数据
		UserContract contract = new UserContract();
		ParameterUtil.fatherToChild(snapshot, contract);

		contract.setContractId(contractId);
		contract.setContractStatus(ContractStatus.SIGNING);
		userContractMapper.insertUserContract(contract);
		//删除快照和附件
		delContractSnapshot(userId);
		//当前合同状态变更为签署中
		User user = userService.getUserByUserId(userId);
		user.setContractStatus(ContractStatus.SIGNING);
		userService.updateUser(user);
		//change log
		changelLogService.insertChangeLog(userId, contract.getContractNumber(), ContractChangeLogType.START_SIGN);

		return "发起签署成功";
	}
	/**
	 * 合同成功发起签署后的操作
	 *
	 * @param snapshot
	 * @param contractId
	 * @return
	 */
	@Transactional
	public String afterSignNew(UserContractSnapshot snapshot, String contractId) throws Exception {
		String userId = snapshot.getUserId();
		//合同表插入数据
		UserContract contract = new UserContract();
		ParameterUtil.fatherToChild(snapshot, contract);

		contract.setContractId(contractId);
		contract.setContractStatus(ContractStatus.SIGNING);
		userContractMapper.insertUserContract(contract);
		//删除快照和附件
		delContractSnapshot(userId);
		//删除offer
		if (snapshot.getOfferPath() != null && !snapshot.getOfferPath().equals("")) {
			FileUtil.deleteFile(snapshot.getOfferPath());
		}
		//当前合同状态变更为签署中
		User user = userService.getUserByUserId(userId);
		user.setContractStatus(ContractStatus.SIGNING);
		userService.updateUser(user);
		//change log
		changelLogService.insertChangeLog(userId, contract.getContractNumber(), ContractChangeLogType.START_SIGN);

		return "发起签署成功";
	}


	/**
	 * 作废合同
	 *
	 * @param contractId
	 * @return
	 */
	public boolean cancelContract(String contractId) {
		UserContract contract = getContractById(contractId);
		if (contractRestClient.cancelContract(contractId, null)) {
			contract.setContractStatus(ContractStatus.TERMINATING);
			userContractMapper.updateUserContract(contract);
			UserDimissionContractBacklogPO backlogByDismissionId = userDimissionContractBacklogService.getBacklogByDismissionId(contract.getDismissionId());
			if(backlogByDismissionId!=null){
				backlogByDismissionId.setFlowStatus("0");
				backlogByDismissionId.setStatus(BacklogStatus.PULL);
				userDimissionContractBacklogService.updateBacklogflowStatus(backlogByDismissionId);
			}
			User user = userService.getUserByUserId(contract.getUserId());
			if (userContractMapper.selectUserStandardNewestContract(contract.getUserId()).getContractNumber().equals(contract.getContractNumber())){
				user.setContractStatus(ContractStatus.TERMINATING);
				userService.updateUser(user);
			}

			changelLogService.insertChangeLog(contract.getUserId(), contract.getContractNumber(), ContractChangeLogType.CANCEL);
			return true;
		}
		return false;
	}


	/**
	 * 撤回合同
	 *
	 * @param contractId
	 * @return
	 */
	public boolean recallContract(String contractId) {
		if (contractRestClient.recallContract(contractId)) {
			UserContract contract = getContractById(contractId);
			contract.setContractStatus(ContractStatus.RECALLED);
			userContractMapper.updateUserContract(contract);
			User user = userService.getUserByUserId(contract.getUserId());
			user.setContractStatus(ContractStatus.RECALLED);
			if (userContractMapper.selectUserStandardNewestContract(contract.getUserId()).getContractNumber().equals(contract.getContractNumber())){
				userService.updateUser(user);
			}
			changelLogService.insertChangeLog(contract.getUserId(), contract.getContractNumber(), ContractChangeLogType.RECALL);
			return true;
		}
		return false;
	}

	/**
	 * 离职撤回合同
	 * @param contractId
	 * @return
	 */
	public boolean recallDismissionContract(String contractId) {
		if (contractRestClient.recallContract(contractId)) {
			UserContract contract = getContractById(contractId);
			contract.setContractStatus(ContractStatus.RECALLED);
			userContractMapper.updateUserContract(contract);
			UserDimissionContractBacklogPO backlogByDismissionId = userDimissionContractBacklogService.getBacklogByDismissionId(contract.getDismissionId());
			if(backlogByDismissionId!=null){
				backlogByDismissionId.setFlowStatus("0");
				backlogByDismissionId.setStatus(BacklogStatus.PULL);
				backlogByDismissionId.setContractCreateTime(null);
				userDimissionContractBacklogService.updateBacklogflowStatus(backlogByDismissionId);
			}
			User user = userService.getUserByUserId(contract.getUserId());
			if (userContractMapper.selectUserStandardNewestContract(contract.getUserId()).getContractNumber().equals(contract.getContractNumber())){
				user.setContractStatus(ContractStatus.RECALLED);
				userService.updateUser(user);
			}
			changelLogService.insertChangeLog(contract.getUserId(), contract.getContractNumber(), ContractChangeLogType.RECALL);
			return true;
		}
		return false;
	}

	/**
	 * 获取查看合同的url
	 *
	 * @param contractId
	 * @return
	 */
	public String getContractViewUrl(String contractId) throws Exception {
		return contractRestClient.getViewContractUrl(contractId);
	}


	/**
	 * 保存附件 文件名称为合同编号
	 *
	 * @param inputStream
	 * @param userId
	 * @return
	 */
	public String saveContractFile(FileInputStream inputStream, String userId, String name) {
		//获取快照
		UserContractSnapshot snapshot = userContractSnapshotMapper.selectContractSnapshotByUserId(userId);
		if (snapshot == null) {
			return null;
		}
		//文件名
		String fileName = snapshot.getContractNumber() + "-" + name;

		if (FileUtil.saveFile(inputStream, snapFilePath, fileName)) {
			//已有附件
			if (snapshot.getFilePath() != null) {
				for (String s : snapshot.getFilePath().split(",")) {
					//已有附件和新附件重名 直接返回
					if (s.equals(snapFilePath + "/" + fileName)) {
						return snapFilePath + "/" + fileName;
					}
				}
				String path = snapshot.getFilePath() + snapFilePath + "/" + fileName + ",";
				snapshot.setFilePath(path);
			} else {
				snapshot.setFilePath(snapFilePath + "/" + fileName + ",");
			}
			if (updateContractSnapshot(snapshot)) {
				return snapFilePath + "/" + fileName;
			} else {
				return null;
			}

		} else {
			return null;
		}
	}

	/**
	 * 线下签署上传合同扫描件
	 * @param inputStream
	 * @param userId
	 * @param fileName
	 * @return
	 */
	public String saveOffSignContractFile(FileInputStream inputStream, String userId, String fileName) {
		String path=contractZipFilePath+userId+'/' + DateTime.now().toString("yyyyMMddHHmmss") +'/' + fileName;
		File file=new File(path);
		if(file.exists()){
			file.delete();
		}
		if (FileUtil.saveFile(inputStream, contractZipFilePath+userId+'/'+ DateTime.now().toString("yyyyMMddHHmmss"), fileName)) {
			return path;
		} else {
			logger.info("合同附件上传失败"+userId+":"+fileName);
			return null;
		}
	}

	/**
	 * 离职上传扫描文件，保存在临时位置
	 * @param inputStream
	 * @param fileName
	 * @return
	 */
	public String saveDismissionTempContractFile(FileInputStream inputStream, String fileName) {
		String path=contractZipFilePath+"temp/"+ fileName;
		File file=new File(path);
		if(file.exists()){
			file.delete();
		}
		if (FileUtil.saveFile(inputStream, contractZipFilePath+"temp/", fileName)) {
			return path;
		} else {
			logger.info("离职合同附件上传失败"+":"+fileName);
			return null;
		}
	}

	public String saveDismissionContractFile(UserDimissionContractBacklogPO po){
		String msg="";
		String savePath=contractZipFilePath+po.getAccount()+'/';//实际存放员工离职文件路径
		File saveFile=new File(savePath);
		try{
			copyFile(po.getTerminationAgreement(),saveFile);
			copyFile(po.getResignationCertificate(),saveFile);
			copyFile(po.getSalaryStatement(),saveFile);
			copyFile(po.getCompetitionNotice(),saveFile);
			copyFile(po.getTerminationAgreement(),saveFile);
		}catch (Exception e){
			logger.error("保存员工离职文件失败："+po.getAccount()+e.getMessage());
			return "保存员工离职文件失败："+po.getAccount()+e.getMessage();
		}
		return msg;
	}

	public String getDismissionContractPath(String userId,String name){
		return contractZipFilePath+userId+"/"+name;
	}

	public void copyFile(String srcFile, File destFile) throws IOException {
		String path=contractZipFilePath+"temp/";
		if(StringUtils.isNotEmpty(srcFile)){
			File file=new File(path+srcFile);
			if(!destFile.exists()){
				destFile.mkdir();
			}
			if(file.exists()){
				FileUtils.copyFileToDirectory(file,destFile);
				file.delete();
			}
		}
	}

	/**
	 * 生成合同快照
	 * 根据签订次数带出合同属性
	 * 合同类型默认劳动合同
	 *
	 * @param userId
	 * @return
	 */
	public boolean createContractSnapshot(String userId, String createUserId, String signStatus) {
		User user = userService.getUserByUserId(userId);
		if (user == null) {
			return false;
		}
		int count = user.getContractCount();
		if (signStatus.equals(SignStatus.NEW)||signStatus.equals(SignStatus.RE)) {
			count += 1;
		}

		UserContractSnapshot snapshot = new UserContractSnapshot();
		snapshot.setUserId(user.getUserId());
		snapshot.setWorkNumber(user.getWorkNumber());
		//snapshot.setSpell赋值逻辑：如果用户的certName不为空，则设置为certName，否则设置spell
		snapshot.setSpell(user.getCertName()!=null?user.getCertName():user.getSpell());
		snapshot.setContractNumber(getContractNumber(user.getUserId(), user.getWorkNumber()));
		snapshot.setContractCount(String.valueOf(count));
		if (count == 1) {
			snapshot.setContractAttribute("固定期");
			snapshot.setStartDate(user.getExpectDate());
			snapshot.setEndDate(DateTimeUtil.dateAddInt(DateTimeUtil.dateAddYear(user.getExpectDate(), 3), -1));
			snapshot.setEffectDate(user.getExpectDate());
			snapshot.setProbationStartDate(user.getExpectDate());
			snapshot.setProbationEndDate(DateTimeUtil.dateAddInt(user.getExpectDate(), 180));
		} else if (count == 2) {
			snapshot.setContractAttribute("固定期");
			UserContract lastContract = userContractMapper.selectUserNewestContract(userId);
			if (lastContract != null) {
				snapshot.setStartDate(DateTimeUtil.dateAddInt(lastContract.getEndDate(), 1));
				snapshot.setEndDate(DateTimeUtil.dateAddInt(DateTimeUtil.dateAddYear(snapshot.getStartDate(), 3), -1));
				//如果是已作废，已撤回等，合同复用上一次时间
				if(ContractStatus.RECALLED.equals(lastContract.getContractStatus())
						||ContractStatus.TERMINATED.equals(lastContract.getContractStatus())
						||ContractStatus.TERMINATING.equals(lastContract.getContractStatus())){
					snapshot.setStartDate(lastContract.getStartDate());
					snapshot.setEndDate(lastContract.getEndDate());
				}
				snapshot.setEffectDate(snapshot.getStartDate());
			}
		} else {
			snapshot.setContractAttribute("无固定期");
			UserContract lastContract = userContractMapper.selectUserNewestContract(userId);
			if (lastContract != null) {
				snapshot.setStartDate(lastContract.getEndDate()!=null?DateTimeUtil.dateAddInt(lastContract.getEndDate(), 1):lastContract.getStartDate());
				snapshot.setEffectDate(snapshot.getStartDate());
			}
		}
		if (user.getEmployType().indexOf("实习") != -1) {
			snapshot.setContractType("实习协议");
			//实习津贴
			String allowance = mokaService.getInternshipAllowance(user.getOfferId());
			allowance = allowance == null ? "" : allowance + "元/日";
			snapshot.setInternshipAllowance(allowance);
			ItUserInfoInternPO itUserInfoInternPO = itUserInfoInternService.getInternByUserId(userId);
			if(itUserInfoInternPO != null){
				snapshot.setSchool(itUserInfoInternPO.getSchool());
				snapshot.setStudentNumber(itUserInfoInternPO.getStudentNumber());
				snapshot.setAdmissionTime(itUserInfoInternPO.getAdmissionTime());
				snapshot.setGraduationTime(itUserInfoInternPO.getGraduationTime());
			}
		} else {
			snapshot.setContractType("劳动合同");
		}
		snapshot.setCreatorUserId(createUserId);
		snapshot.setCompany(dhrRestClient.getCompanyValue(user.getCompany()));
		String position = mmisMegviiConfigService.getPositionByCode(user.getPosition());
		if (position == null) {
			return false;
		}
		snapshot.setPosition(position);
		snapshot.setEmployBase(user.getEmployBase());
		snapshot.setContractCell(user.getCell());
		snapshot.setCertType(user.getCertType());
		snapshot.setCertNo(user.getCertNo());
		snapshot.setAddress(user.getAddress());
		snapshot.setSignStatus(signStatus);
		if(signStatus.equals(SignStatus.CHC)){//如果是合同转签，多7个字段
			UserChangeContractBacklog userChangeContractBacklog = changeContractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
			snapshot.setEffectDate(DateTimeUtil.string2DateYMD(userChangeContractBacklog.getEffectDate()));
			snapshot.setContract(userChangeContractBacklog.getContract());
			snapshot.setNewContract(userChangeContractBacklog.getNewContract());
			snapshot.setConBeginDate(userChangeContractBacklog.getConBeginDate());
			snapshot.setConEndDate(userChangeContractBacklog.getConEndDate());
			snapshot.setNewConBeginDate(userChangeContractBacklog.getNewConBeginDate());
			snapshot.setNewConEndDate(userChangeContractBacklog.getNewConEndDate());
			snapshot.setWorkCity(userChangeContractBacklog.getWorkCity());
			snapshot.setNewWorkCity(userChangeContractBacklog.getNewWorkCity());
			snapshot.setStartDate(DateTimeUtil.string2DateYMD(userChangeContractBacklog.getNewConBeginDate()));
			snapshot.setEndDate(StringUtils.isEmpty(userChangeContractBacklog.getNewConEndDate())?null:DateTimeUtil.string2DateYMD(userChangeContractBacklog.getNewConEndDate()));
		if(DateTimeUtil.dateAddInt(user.getExpectDate(), 180).isBefore(DateTimeUtil.string2DateYMD(userChangeContractBacklog.getNewConBeginDate()))){
			snapshot.setProbationStartDate(null);
			snapshot.setProbationEndDate(null);
		}
		}
		if(signStatus.equals(SignStatus.CH)){//如果是合同变更，多2个字段
			UserChangeContractBacklog userChangeContractBacklog1 = changeContractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
			snapshot.setEffectDate(DateTimeUtil.string2DateYMD(userChangeContractBacklog1.getEffectDate()));
			snapshot.setWorkCity(userChangeContractBacklog1.getWorkCity());
			snapshot.setNewWorkCity(userChangeContractBacklog1.getNewWorkCity());
			snapshot.setStartDate(DateTimeUtil.string2DateYMD(userChangeContractBacklog1.getNewConBeginDate()));
			snapshot.setEndDate(StringUtils.isEmpty(userChangeContractBacklog1.getNewConEndDate())?null:DateTimeUtil.string2DateYMD(userChangeContractBacklog1.getNewConEndDate()));
			snapshot.setConBeginDate(userChangeContractBacklog1.getConBeginDate() != null ? userChangeContractBacklog1.getConBeginDate() : null);
			snapshot.setConEndDate(userChangeContractBacklog1.getConEndDate() != null ? userChangeContractBacklog1.getConEndDate() : null);
			snapshot.setNewConBeginDate(userChangeContractBacklog1.getNewConBeginDate() != null ? userChangeContractBacklog1.getNewConBeginDate() : null);
			snapshot.setNewConEndDate(userChangeContractBacklog1.getNewConEndDate() != null ? userChangeContractBacklog1.getNewConEndDate() : null);
		}
		//新签合同 拉取offer附件
		if (signStatus.equals(SignStatus.NEW)) {
			//拉取offer
			String offerPath = mokaService.getMokaOfferFile(userId,user.getOfferId());
			System.out.println("offerPath:"+offerPath);
			if (offerPath != null) {
				snapshot.setOfferPath(offerPath);
			}
		}
		return saveContractSnapshot(snapshot);
	}

	/**
	 * 删除指定用户的合同快照
	 *
	 * @param userId
	 * @return
	 */
	private boolean delContractSnapshot(String userId) {
		UserContractSnapshot oldSnapshot = userContractSnapshotMapper.selectContractSnapshotByUserId(userId);
		if (oldSnapshot == null) {
			return true;
		}

		try {
			//删除附件
			if (oldSnapshot.getFilePath() != null && !oldSnapshot.getFilePath().equals("")) {
				String[] paths = oldSnapshot.getFilePath().split(",");
				for (String path : paths) {
					FileUtil.deleteFile(path);
				}
			}
		} catch (Exception e) {
			logger.error(userId + "删除快照附件异常" + e);
		}

		if (userContractSnapshotMapper.deleteContractSnapshot(oldSnapshot.getId()) < 1) {
			return false;
		}
		return true;
	}

	/**
	 * 保存合同快照
	 * 1.删除旧快照
	 * 2.删除成功后保存新快照
	 *
	 * @param snapshot
	 * @return
	 */
	private boolean saveContractSnapshot(UserContractSnapshot snapshot) {
		if (!delContractSnapshot(snapshot.getUserId())) {
			return false;
		}
		if (userContractSnapshotMapper.insertContractSnapshot(snapshot) > 0) {
			changelLogService.insertChangeLog(snapshot.getUserId(), snapshot.getContractNumber(), ContractChangeLogType.CREATE_SPANSHOT);
			User user = userService.getUserByUserId(snapshot.getUserId());
			user.setContractStatus(ContractStatus.DRAFT);
			userService.updateUser(user);
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 修改合同快照
	 *
	 * @param snapshot
	 * @return
	 */
	public boolean updateContractSnapshot(UserContractSnapshot snapshot) {
		if (userContractSnapshotMapper.updateContractSnapshot(snapshot) > 0) {
			changelLogService.insertChangeLog(snapshot.getUserId(), snapshot.getContractNumber(), ContractChangeLogType.UPDATE_SPANSHOT);
			return true;
		}
		return false;
	}


	/**
	 * 获取指定用户的合同快照
	 *
	 * @param userId
	 * @return
	 */
	public UserContractSnapshot getContractSnapshot(String userId) {
		return userContractSnapshotMapper.selectContractSnapshotByUserId(userId);
	}

	/**
	 * 获取用户的全部合同
	 *
	 * @param userId
	 * @return
	 */
	public List<UserContract> getContractsByUserId(String userId) {
		return userContractMapper.selectUserContractByUserId(userId);
	}
	public List<UserContract> getContracts() {
		return userContractMapper.getContracts();
	}
	public List<String> getAllContractNumber() {
		return userContractMapper.getAllContractNumber();
	}
	/**
	 * 通过id获取快照
	 *
	 * @param id
	 * @return
	 */
	public UserContractSnapshot getSnapshotById(int id) {
		return userContractSnapshotMapper.selectContractSnapshotById(id);
	}

	/**
	 * 通过contract_number获取合同
	 *
	 * @param contractNumber
	 * @return
	 */
	public UserContract getContractByContractNumber(String contractNumber){
		return userContractMapper.selectContractByContractNumber(contractNumber);
	}


	/**
	 * 根据电子签合同id获取合同
	 *
	 * @param contractId
	 * @return
	 */
	public UserContract getContractById(String contractId) {
		return userContractMapper.selectContractByContractId(contractId);
	}
	public List<ContractExportDto> getContractsByFilter(UserFilter filter) {
		return userContractMapper.getContractsByFilter(filter);
	}
	public UserContract getById(String id) {
		return userContractMapper.selectContractById(id);
	}
	public UserContract getContractByDismissionId(String dismissionId) {
		return userContractMapper.selectContractByDismissionId(dismissionId);
	}

	public Boolean removeContractById(String contractId) {
		if(userContractMapper.removeContractByContractId(contractId) > 0){
			return true;
		}else{
			return false;
		}
	}

	/**
	 * 修改合同信息
	 *
	 * @param contract
	 * @return
	 */
	public boolean updateUserContract(UserContract contract) {
		if (userContractMapper.updateUserContract(contract) > 0) {
			return true;
		} else {
			return false;
		}
	}

	public Boolean updateUserCustomizeContract(UserContract contract){
		if (userContractMapper.updateUserCustomizeContract(contract) > 0){
			return true;
		}else{
			return false;
		}
	}

	public Boolean insertUserContract(UserContract contract){
		if (userContractMapper.insertUserContract(contract) > 0) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 获取合同编号
	 * if 存在快照就在快照的基础上+1
	 * else 在最高的合同编号上+1
	 *
	 * @param userId
	 * @param workNumber
	 * @return
	 */
	private String getContractNumber(String userId, String workNumber) {
		UserContractSnapshot snapshot = userContractSnapshotMapper.selectContractSnapshotByUserId(userId);
		if (snapshot != null) {
			String contractNumber = snapshot.getContractNumber();
			Integer number = Integer.valueOf(contractNumber.split("-")[1]);
			if (number < 9) {
				return contractNumber.split("-")[0] + "-0" + (number);
			} else {
				return contractNumber.split("-")[0] + "-" + (number);
			}
		}
		String contractNumber = userContractMapper.selectContractNumberByWorkNumber(workNumber + "-");
		if (contractNumber == null) {
			return workNumber + "-01";
		} else {
			Integer number = Integer.valueOf(contractNumber.split("-")[1]);
			if (number < 9) {
				return contractNumber.split("-")[0] + "-0" + (number + 1);
			} else {
				return contractNumber.split("-")[0] + "-" + (number + 1);
			}
		}
	}

	public String getHighContractNumber(String workNumber){
		return userContractMapper.selectContractNumberByWorkNumber(workNumber + "-");
	}

	public Boolean getContractCategoryByCategoryId(String categoryId){
		List<UserContractCategory> userContractCategory = userContractCategoryMapper.selectCategoryByCategoryId(categoryId);
		if (userContractCategory == null){
			return false;
		}
		return true;
	}


	public String getContractNumberFromUserAndUserContract(String userId){
		String workNumber = userService.getUserByUserId(userId).getWorkNumber();
		String contractNumber = userContractMapper.selectContractNumberByWorkNumber(workNumber + "-");
		if (contractNumber == null) {
			return workNumber + "-01";
		} else {
			Integer number = Integer.valueOf(contractNumber.split("-")[1]);
			if (number < 9) {
				return contractNumber.split("-")[0] + "-0" + (number + 1);
			} else {
				return contractNumber.split("-")[0] + "-" + (number + 1);
			}
		}
	}

	/**
	 * 根据userId获取用户最新合同
	 * @param userId
	 * @return
	 */
	public UserContract selectUserNewestContract(String userId) {
		return userContractMapper.selectUserNewestContract(userId);
	}

	public UserContract selectUserStandardNewestContract(String userId){
		return userContractMapper.selectUserStandardNewestContract(userId);
	}
}