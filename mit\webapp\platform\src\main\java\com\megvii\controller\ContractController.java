package com.megvii.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.megvii.client.ContractRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.FileUtil;
import com.megvii.common.IPUtils;
import com.megvii.common.ParameterUtil;
import com.megvii.controller.api.*;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.entry.ItUserInfoInternPO;
import com.megvii.entity.opdb.OperateTypeEnum;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.*;
import com.megvii.service.*;
import com.megvii.service.contract.ChangeContractBacklogService;
import com.megvii.service.contract.ContractChangelLogService;
import com.megvii.service.contract.ContractService;
import com.megvii.service.contract.ProveContractBacklogService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/4/20 14:35
 */
@RestController
@RequestMapping("/api/contract")
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties",encoding = "UTF-8")
public class ContractController {

	private Logger logger = LoggerFactory.getLogger(ContractController.class);

	@Value("${dhr.redirect}")
	private String dhrRedirect;

	@Value("${SNAPSHOT_FILE_PATH}")
	private String snapshotFilePath;

	/**
	 * 人才档案token
	 */
	@Value("${RCDA_TOKEN}")
	private String rcda_token;
	@Autowired
	private ContractService contractService;

	@Autowired
	private UserService userService;

	@Autowired
	private ContractChangelLogService contractChangelLogService;

	@Autowired
	private ChangeContractBacklogService changeContractBacklogService;
	@Autowired
	private IPWhiteListService ipWhiteListService;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private EnvService service;
	@Autowired
	private UserDimissionContractBacklogService userDimissionContractBacklogService;
	@Autowired
	private ContractRestClient contractRestClient;

	@Autowired
	private ItUserInfoInternService itUserInfoInternService;

	@Autowired
	private ProveContractBacklogService proveContractService;

	@Autowired
	DhrService dhrService;



	@PostMapping("/getContracts")
	@ResponseBody
	public ResponseDataEntity getContracts(HttpServletRequest httpServletRequest, @RequestBody UserRequest request) {
		PagingResponse response = new PagingResponse();
		String token = httpServletRequest.getHeader("token");
		if (!rcda_token.equals(token)) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
			response.setMsg("密钥错误");
			return response;
		}
		String ipAddress = IPUtils.getRealIP(httpServletRequest);
		if (!ipWhiteListService.validateIp(ipAddress, "contract")) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
			response.setMsg("IP不在白名单内");
			return response;
		}
		PageHelper.startPage(request.getPageNumber(), request.getPageSize());
		List<UserContract> userContracts = contractService.getContracts();
		PageInfo<UserContract> pageInfo = new PageInfo<>(userContracts);
		response.setCode(ResponseDataEntity.OK);
		response.setData(ContractRCDAVO.toVOs(userContracts));
		response.setMsg("success");
		response.setPageNumber(pageInfo.getPageNum());
		response.setTotalPage(pageInfo.getPages());
		response.setTotalCount(pageInfo.getTotal());
		return response;
	}

	@PostMapping("/sign/offline")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity signOfflineContract(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();

		UserContract contract = new UserContract();
		contract.setUserId(request.getUserId());
		contract.setContractCount(request.getContractCount());
		contract.setContractAttribute(request.getContractCount().equals("3") ? "无固定期" : "固定期");
		contract.setStartDate(DateTimeUtil.string2DateYMD(request.getStartDate()));
		contract.setEndDate(contract.getContractAttribute().equals("无固定期") ? null : DateTimeUtil.string2DateYMD(request.getEndDate()));
		contract.setCompany(request.getCompany());
		contract.setSignStatus(request.getSignStatus());
		contract.setUserContractFile(request.getUserContractFile());
		if (contractService.offlineSign(contract)) {
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
			return response;
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("线下签署失败");
			return response;
		}

	}


	@PostMapping("/sign")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity signContract(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		UserContractSnapshot snapshot = contractService.getSnapshotById(request.getId());
		try {
			String msg = contractService.createSignContract(snapshot);
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
			response.setData(msg);
			return response;

		} catch (Exception e) {
			logger.error(snapshot.getContractNumber() + "发起签署异常" + e);
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("发起签署失败");
			response.setData(e.getMessage());
			return response;
		}
	}

	@PostMapping("/sign/new")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity signNewContract(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		UserContractSnapshot snapshot = contractService.getSnapshotById(request.getId());
		//如果不在等待入职或者在职节点，由于没有信息收集表，则无法生成合同
		User user = userService.getUserByUserId(snapshot.getUserId());
		if(!(user.getEntryFlow().equals(FlowNodeType.getFlowTypeValue(FlowNodeType.ENTRY.toString()))||user.getEntryFlow().equals(FlowNodeType.getFlowTypeValue(FlowNodeType.WAIT_ENTRY.toString())))){
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("尚未完成信息收集，暂无数据，请完成信息收集流程后，发起签署");
			return response;
		}
		try {
			String msg = contractService.createNewSignContract(snapshot);
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
			response.setData(msg);
			return response;

		} catch (Exception e) {
			logger.error(snapshot.getContractNumber() + "发起签署异常" + e);
			e.printStackTrace();
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg(e.getMessage());
			response.setData(null);
			return response;
		}
	}


	@PostMapping("/recall")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity recallContract(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			if (contractService.recallContract(request.getContractId())) {
				response.setCode(ResponseDataEntity.OK);
				response.setMsg("success");
			} else {
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("撤回失败");
			}
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg(e.getMessage());
		}
		return response;
	}

	@PostMapping("/cancel")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity cancelContract(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			if (contractService.cancelContract(request.getContractId())) {
				response.setCode(ResponseDataEntity.OK);
				response.setMsg("success");
			} else {
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("作废失败");
			}
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg(e.getMessage());
		}
		return response;
	}


	@GetMapping("/info")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getContractInfo(@RequestParam("contractId") String contractId) {
		ResponseDataEntity response = new ResponseDataEntity();

		try {
			String url = contractService.getContractViewUrl(contractId);
			response.setCode(ResponseDataEntity.OK);
			response.setData(url);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg(e.getMessage());
		}
		return response;
	}

	@GetMapping("/view")
	@ResponseBody
	public void getContractView() {
		try {
			Subject subject = SecurityUtils.getSubject();
			User user = (User) subject.getPrincipal();
			//查询全部合同
			List<UserContract> contracts = contractService.getContractsByUserId(user.getUserId());
			if (contracts != null && contracts.size() > 0) {
				for (UserContract contract : contracts) {
					if(SignStatus.CUSTOM.equals(contract.getSignStatus())){
						continue;
					}
					//最新的已完成的合同
					if (contract.getContractStatus().equals(ContractStatus.COMPLETE) || contract.getContractStatus().equals(ContractStatus.TERMINATING)) {
						String url = contractService.getContractViewUrl(contract.getContractId());
						subject.logout();
						service.response.sendRedirect(url);
					}
				}
			}
			subject.logout();
			service.response.sendRedirect(dhrRedirect);
		} catch (Exception e) {
		}

	}


	@PostMapping("/updateInfo")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity updateContractInfo(@RequestBody ContractRequest contractRequest){
		// 更新合同信息
		ResponseDataEntity response = new ResponseDataEntity();
		try{
			String id = String.valueOf(contractRequest.getId());
			UserContract usercontract = contractService.getById(id);
			usercontract.setStartDate(!contractRequest.getStartDate().isEmpty() ? DateTimeUtil.string2DateYMD(contractRequest.getStartDate()) : null);
			usercontract.setEndDate(!contractRequest.getEndDate().isEmpty() ? DateTimeUtil.string2DateYMD(contractRequest.getEndDate()) : null);
			contractService.updateUserCustomizeContract(usercontract);
		}catch (Exception e){
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("更新合同信息异常");
			return response;
		}
		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");
		return response;
	}

	@PostMapping("/updateOfflineInfo")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity updateOfflineInfo(@RequestBody ContractCustomizeVO request){
		ResponseDataEntity response = new ResponseDataEntity();
		try{
			String id = String.valueOf(request.getId());
			UserContract userContract = contractService.getById(id);
			userContract.setContractSubject(request.getSubject());
			userContract.setCompany(request.getCompany());
			userContract.setStartDate(!request.getStartDate().isEmpty() ? DateTimeUtil.string2DateYMD(request.getStartDate()) : null);
			userContract.setEndDate(!request.getEndDate().isEmpty() ? DateTimeUtil.string2DateYMD(request.getEndDate()) : null);
			userContract.setUserContractFile(request.getUserContractFile());
			contractService.updateUserCustomizeContract(userContract);
		}catch (Exception e){
			response.setMsg("更新合同信息异常");
			response.setCode(ResponseDataEntity.ERROR);
			return response;
		}
		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");
		return response;
	}

	@DeleteMapping("/contract/file")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity deleteOfflineFile(@RequestParam("id") String id, @RequestParam("path") String path){
		ResponseDataEntity response = new ResponseDataEntity();
		UserContract contract = contractService.getById(id);
		try {
			FileUtil.deleteFile(path);
			contract.setUserContractFile(null);
			contractService.updateUserCustomizeContract(contract);


		}catch (Exception e){
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("文件删除异常");
			return response;
		}
		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");
		return response;
	}


	@DeleteMapping("/snapshot/file")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity deleteFile(@RequestParam("userId") String userId, @RequestParam("path") String path) {
		ResponseDataEntity response = new ResponseDataEntity();

		UserContractSnapshot snapshot = contractService.getContractSnapshot(userId);
		if (snapshot != null) {
			if (snapshot.getFilePath() != null) {
				String filePath = snapshot.getFilePath();
				for (String s : snapshot.getFilePath().split(",")) {
					if (s.equals(path)) {
						FileUtil.deleteFile(path);
						snapshot.setFilePath(filePath.replaceAll(path + ",", ""));
						contractService.updateContractSnapshot(snapshot);
						response.setCode(ResponseDataEntity.OK);
						response.setMsg("success");
						return response;
					}
				}
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("文件不存在");
				return response;
			}

		}
		response.setCode(ResponseDataEntity.ERROR);
		response.setMsg(userId + "用户没有快照");
		return response;

	}


	@GetMapping("/snapshot/file")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getFile(@RequestParam("userId") String userId, @RequestParam("path") String path, HttpServletResponse res) {
		ResponseDataEntity response = new ResponseDataEntity();

		try {
			//查询快照信息
			UserContractSnapshot snapshot = contractService.getContractSnapshot(userId);
			//遍历文件路径
			for (String s : snapshot.getFilePath().split(",")) {
				//完全匹配才读取
				if (s.equals(path)) {
					try {
						FileInputStream inputStream = new FileInputStream(path);
						int i = inputStream.available();
						byte[] buff = new byte[i];
						inputStream.read(buff);
						inputStream.close();
						//设置发送到客户端的响应内容类型
						res.setContentType("application/pdf");
						OutputStream out = res.getOutputStream();
						out.write(buff);
						//关闭响应输出流
						out.close();

						response.setCode(ResponseDataEntity.OK);
						response.setMsg("success");
					} catch (Exception e) {
						response.setCode(ResponseDataEntity.ERROR);
						response.setMsg("读取文件失败");
					}
				}
			}
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("文件路径不合法");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("文件路径不合法");
			return response;
		}

		return response;
	}

	@GetMapping("/snapshot/offer/file")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getOfferFile(@RequestParam("userId") String userId, HttpServletResponse res) {
		ResponseDataEntity response = new ResponseDataEntity();

		try {
			//查询快照信息
			UserContractSnapshot snapshot = contractService.getContractSnapshot(userId);
			String offerPath = "";
			if(snapshot !=null){
				offerPath = snapshot.getOfferPath();
			}
			try {
				FileInputStream inputStream = new FileInputStream(offerPath);
				int i = inputStream.available();
				byte[] buff = new byte[i];
				inputStream.read(buff);
				inputStream.close();
				//设置发送到客户端的响应内容类型
				res.setContentType("application/pdf");
				OutputStream out = res.getOutputStream();
				out.write(buff);
				//关闭响应输出流
				out.close();

				response.setCode(ResponseDataEntity.OK);
				response.setMsg("success");
			} catch (Exception e) {
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("读取文件失败");
			}
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("读取offer失败");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("读取offer失败");
			return response;
		}

		return response;
	}


	@PostMapping("/snapshot/upload")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity uploadFile(@RequestParam MultipartFile file, @RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();
		if (!file.getOriginalFilename().subSequence(file.getOriginalFilename().length() - 4, file.getOriginalFilename().length()).equals(".pdf")) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("文件格式不合法");
			return response;
		}

		try {
			FileInputStream in = (FileInputStream) file.getInputStream();
			String name = contractService.saveContractFile(in, userId, file.getOriginalFilename());
			if (name != null) {
				response.setCode(ResponseDataEntity.OK);
				response.setData(name);
				response.setMsg("success");
			} else {
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("保存失败");
			}

		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("读取失败");
			return response;
		}
		return response;
	}

	@GetMapping("/contractFile/file")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getContractFile(@RequestParam("userId") String userId, @RequestParam("path") String path, HttpServletResponse res) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			File file = new File(path);
			if (!file.exists()) {
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("文件不存在");
			} else {
				FileInputStream inputStream = new FileInputStream(path);
				int i = inputStream.available();
				byte[] buff = new byte[i];
				inputStream.read(buff);
				inputStream.close();
				//设置发送到客户端的响应内容类型
				res.setContentType("application/octet-stream;charset=utf-8");
				String name = path.substring(path.lastIndexOf("/") + 1);
				res.setHeader("Content-Disposition", "attachment; filename=" + new String(name.getBytes("gb2312"), "ISO8859-1"));
				OutputStream out = res.getOutputStream();
				out.write(buff);
				//关闭响应输出流
				out.close();
				response.setCode(ResponseDataEntity.OK);
				response.setMsg("success");
			}
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("读取文件失败");
		}
		return response;
	}

	/**
	 * 线下签署上传合同扫描件
	 *
	 * @param file
	 * @param userId
	 * @return
	 */
	@PostMapping("/contractFile/upload")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity uploadContractFile(@RequestParam MultipartFile file, @RequestParam("userId") String userId, @RequestParam("fileName") String fileName) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			FileInputStream in = (FileInputStream) file.getInputStream();
			String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
			String name = contractService.saveOffSignContractFile(in, userId, fileName + suffix);
			if (name != null) {
				response.setCode(ResponseDataEntity.OK);
				response.setData(name);
				response.setMsg("success");
			} else {
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("保存失败");
			}

		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("读取失败");
			return response;
		}
		return response;
	}

	@PostMapping("/contractFile/update")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity updateContractFile(@RequestParam("id") String id, @RequestParam("fileName") String fileName) {
		ResponseDataEntity response = new ResponseDataEntity();
		UserContract contract = contractService.getById(id);
		User userByUserId = userService.getUserByUserId(contract.getUserId());
		if (contract == null) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("保存失败，合同不存在");
			return response;
		}
		contract.setUserContractFile(fileName);
		contract.setContractStatus(ContractStatus.COMPLETE);
		contractService.updateUserContract(contract);
		UserContract newestContract = contractService.selectUserNewestContract(contract.getUserId());
		userByUserId.setContractStatus(newestContract.getContractStatus());
		userService.updateUser(userByUserId);
		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");
		return response;
	}

	@GetMapping("/list")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getContractList(@RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();

		List<UserContract> contracts = contractService.getContractsByUserId(userId);
		if (contracts != null && contracts.size() > 0) {
			response.setData(ContractVO.toVOs(contracts));
		}
		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");

		return response;
	}


	@GetMapping("/spanshot")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getSpanshotByUserid(@RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();
		UserContractSnapshot snapshot = contractService.getContractSnapshot(userId);
		if (snapshot != null) response.setData(ContractVO.toVO(snapshot));

		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");

		return response;
	}

	@GetMapping("/spanshot/detailWithIntern")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getSpanshotAndDetailByUserid(@RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();
		JSONObject body = new JSONObject();
		UserContractSnapshot snapshot = contractService.getContractSnapshot(userId);
		if (snapshot != null) {
			body.put("snapshot",ContractVO.toVO(snapshot));
		}else{
			body.put("snapshot",null);
		}
		ItUserInfoInternPO itUserInfoInternPO = itUserInfoInternService.getInternByUserId(userId);
		if(itUserInfoInternPO != null){
			body.put("itUserInfoIntern",itUserInfoInternPO);
		}else{
			body.put("itUserInfoIntern",null);
		}

		response.setData(body);
		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");

		return response;
	}

	@PostMapping("/spanshot/create")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.ADD)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity createSpanshot(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		User user = userService.getUserByUserId(request.getUserId());
		if (user.getContractStatus().equals(ContractStatus.SIGNING) || user.getContractStatus().equals(ContractStatus.TERMINATING)) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("当前合同状态不允许创建快照");
			return response;
		}

		User login = (User) SecurityUtils.getSubject().getPrincipal();
		if (contractService.createContractSnapshot(request.getUserId(), login.getUserId(), request.getSignStatus())) {
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("生成快照失败");
		}
		return response;
	}

	@PostMapping("/spanshot/delete")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.DEL)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity deleteSpanshot(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();
		String contractStatus = contractService.removeSnapshotUpdateStatus(request.getUserId());
		if (contractStatus != null) {
			response.setCode(ResponseDataEntity.OK);
			response.setData(contractStatus);
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("删除失败");
		}
		return response;
	}

	@PostMapping("/spanshot/update")
	@ResponseBody
	@OperateLog(operType = OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity updateSpanshot(@RequestBody ContractRequest request) {
		ResponseDataEntity response = new ResponseDataEntity();

		UserContractSnapshot snapshot = contractService.getContractSnapshot(request.getUserId());
		snapshot.setContractType(request.getContractType());
		snapshot.setContractCount(request.getContractCount());
		snapshot.setContractAttribute(request.getContractAttribute());
		snapshot.setCompany(request.getCompany());
		snapshot.setMemo(request.getMemo());
		snapshot.setStartDate(request.getStartDate() != null ? DateTimeUtil.string2DateYMD(request.getStartDate()) : null);
		snapshot.setEndDate(request.getEndDate() != null ? DateTimeUtil.string2DateYMD(request.getEndDate()) : null);
		snapshot.setEffectDate(request.getEffectDate() != null ? DateTimeUtil.string2DateYMD(request.getEffectDate()) : null);
		snapshot.setProbationStartDate(request.getProbationStartDate() != null ? DateTimeUtil.string2DateYMD(request.getProbationStartDate()) : null);
		snapshot.setProbationEndDate(request.getProbationEndDate() != null ? DateTimeUtil.string2DateYMD(request.getProbationEndDate()) : null);

		//contractType=实习协议
		if (request.getContractType()!=null && "实习协议".equals(request.getContractType())) {
			snapshot.setSchool(request.getSchool());
			snapshot.setStudentNumber(request.getStudentNumber());
			snapshot.setAdmissionTime(request.getAdmissionTime() != null ? DateTimeUtil.stringToDate(request.getAdmissionTime()) : null);
			snapshot.setGraduationTime(request.getGraduationTime() != null ? DateTimeUtil.stringToDate(request.getGraduationTime()) : null);
		}


		User user = (User) SecurityUtils.getSubject().getPrincipal();
		snapshot.setUpdatorUserId(user.getUserId());
		if (SignStatus.CHC.equals(request.getSignStatus())) {//转签合同多6个字段
			snapshot.setContract(request.getContract());
			snapshot.setNewContract(request.getNewContract());
			snapshot.setConBeginDate(request.getConBeginDate());
			snapshot.setConEndDate(request.getConEndDate());
			snapshot.setNewConBeginDate(request.getNewConBeginDate());
			snapshot.setNewConEndDate(request.getNewConEndDate());
			snapshot.setWorkCity(request.getWorkCity());
			snapshot.setNewWorkCity(request.getNewWorkCity());
		}
		if (SignStatus.CH.equals(request.getSignStatus())) {//转签变更多2个字段
			snapshot.setWorkCity(request.getWorkCity());
			snapshot.setNewWorkCity(request.getNewWorkCity());
		}
		if (contractService.updateContractSnapshot(snapshot)) {
			if (SignStatus.CHC.equals(request.getSignStatus())) {//如果是转签，还需要修改
				UserChangeContractBacklog userChangeContractBacklog = changeContractBacklogService.selectNotFinishBacklogByUserid(request.getUserId());
				if (userChangeContractBacklog != null) {
					userChangeContractBacklog.setEffectDate(request.getEffectDate());
					userChangeContractBacklog.setContract(request.getContract());
					userChangeContractBacklog.setNewContract(request.getNewContract());
					userChangeContractBacklog.setConBeginDate(request.getConBeginDate());
					userChangeContractBacklog.setConEndDate(request.getConEndDate());
					userChangeContractBacklog.setNewConBeginDate(request.getNewConBeginDate());
					userChangeContractBacklog.setNewConEndDate(request.getNewConEndDate());
					userChangeContractBacklog.setWorkCity(request.getWorkCity());
					userChangeContractBacklog.setNewWorkCity(request.getNewWorkCity());
					changeContractBacklogService.updateContractBacklogInfo(userChangeContractBacklog);
				}
			}
			if (SignStatus.CH.equals(request.getSignStatus())) {//如果是合同变更，还需要修改
				UserChangeContractBacklog userChangeContractBacklog1 = changeContractBacklogService.selectNotFinishBacklogByUserid(request.getUserId());
				if (userChangeContractBacklog1 != null) {
					userChangeContractBacklog1.setWorkCity(request.getWorkCity());
					userChangeContractBacklog1.setNewWorkCity(request.getNewWorkCity());
					changeContractBacklogService.updateContractBacklogInfo(userChangeContractBacklog1);
				}
			}
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("保存失败");
		}
		return response;
	}

	@GetMapping("/changelog")
	@ResponseBody
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getChangeLog(@RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();

		List<UserContractChangeLog> logs = contractChangelLogService.getLogsByUserId(userId);
		response.setCode(ResponseDataEntity.OK);
		response.setData(ContractChangeLogVO.toVOs(logs));
		response.setMsg("success");
		return response;
	}

	@PostMapping("/callback")
	@ResponseBody
	public ResponseDataEntity signCallback(HttpServletRequest request, @RequestBody String par) {
		logger.info("电子签回调：" + par);
		ResponseDataEntity response = new ResponseDataEntity();

		//过滤ip,若用户在白名单内，则放行
		String ipAddress = IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "contract")) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
			response.setMsg("IP不在白名单内");
			return response;
		}

		Map<String, String> map;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error("电子签回调参数转换异常：" + e.getMessage());
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("电子签回调参数转换异常");
			return response;
		}

		UserContract contract = contractService.getContractById(map.get("contractId"));
		if (contract == null) {
			logger.error("电子签回调异常：" + map.get("contractId") + "不存在");
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("合同编号不存在");
			return response;
		}

		contract.setContractStatus(ContractStatus.map.get(map.get("status")));
		contract.setUpdatorUserId("auto");

		if (contractService.finishSign(contract)) {
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("保存失败");
		}
		return response;
	}

	@PostMapping("/customize/callback")
	@ResponseBody
	public ResponseDataEntity signCustomizeCallback(HttpServletRequest request, @RequestBody String par) throws Exception {
		logger.info("电子签回调：" + par);
		ResponseDataEntity response = new ResponseDataEntity();

		String ipAddress = IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "contract")) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
			response.setMsg("IP不在白名单内");
			return response;
		}

		Map<String, String> map;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error("电子签回调参数转换异常：" + e.getMessage());
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("电子签回调参数转换异常");
			return response;
		}

		String contractId = map.get("contractId");

		UserContract userContract1 = contractService.getContractById(contractId);
		if (userContract1 != null){
			// 如果合同是最新合同则修改用户的合同状态
			if (contractService.getHighContractNumber(userContract1.getWorkNumber()).equals(userContract1.getContractNumber())){
				User user = userService.getUserByUserId(userContract1.getUserId());
				user.setContractStatus(ContractStatus.COMPLETE);
				userService.updateUser(user);
			}
		    userContract1.setContractStatus("已完成");
		    contractService.updateUserContract(userContract1);
			response.setMsg("已存储电子签");
			response.setCode(ResponseDataEntity.OK);
			return response;
		}


		JSONObject jsonObject = contractRestClient.getContractByContractDetails(contractId);

		Boolean sign = contractService.getContractCategoryByCategoryId(jsonObject.getString("categoryId"));

		if (sign && "已完成".equals(jsonObject.get("statusDesc").toString())) {
			UserContract userContract = new UserContract();
			String email = null;
			JSONArray jsonArr = jsonObject.getJSONArray("signatories");
			for(int i = 0;i < jsonArr.size();i++){
				JSONObject jsonObject1 = jsonArr.getJSONObject(i);
				if(jsonObject1.getString("tenantType").equals("PERSONAL")){
					email = jsonObject1.getString("contact");
					break;
				}
			}


			String userId = email.substring(0,email.indexOf("@"));
			userContract.setUserId(userId);

			try{
                User user = userService.getUserByUserId(userId);
                userContract.setContract(user.getCompany());
                userContract.setEmployBase(user.getEmployBase());
                userContract.setCertType(user.getCertType());
                userContract.setCertNo(user.getCertNo());
                userContract.setAddress(user.getAddress());
                userContract.setContractCell(user.getCell());
                userContract.setPosition(user.getPosition());
                userContract.setSpell(user.getSpell());
                userContract.setWorkNumber(user.getWorkNumber());
                userContract.setContractAttribute(user.getContractCount() == 3 ? "无固定期":"固定期");
                userContract.setContractCount(String.valueOf(user.getContractCount()));
            }catch (Exception e){
			    logger.error("获取用户异常" + e.getMessage());
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("获取用户异常");
                return response;
            }

            try {
                userContract.setContractId(jsonObject.get("id").toString());
                userContract.setStartDate(jsonObject.get("createTime") != null ? DateTimeUtil.string2DateYMD2(jsonObject.getString("createTime")) : null);
                userContract.setEndDate(jsonObject.get("endTime") != null ? DateTimeUtil.string2DateYMD2(jsonObject.getString("endTime")) : null);
                userContract.setEffectDate(jsonObject.get("createTime") != null ? DateTimeUtil.string2DateYMD2(jsonObject.getString("createTime")) : null);
                userContract.setContractStatus(jsonObject.get("statusDesc").toString());
                userContract.setCompany(jsonObject.get("tenantName").toString());
                userContract.setContractTitle(jsonObject.get("categoryName").toString());
                userContract.setContractSubject(jsonObject.get("subject").toString());
                userContract.setContractNumber(contractService.getContractNumberFromUserAndUserContract(userId));
                userContract.setContractType("劳动合同");
                userContract.setUpdatorUserId("auto");
                userContract.setSignStatus(SignStatus.CUSTOM);
                userContract.setCreatorUserId("");
            }catch (Exception e){
                logger.error("获取回调异常" + e.getMessage());
                response.setCode(ResponseDataEntity.ERROR);
                response.setMsg("获取回调异常");
                return response;
            }

			if (contractService.insertUserContract(userContract)) {

                try{
                    User user = userService.getUserByUserId(userId);
                    user.setContractStatus(ContractStatus.COMPLETE);
                    userService.updateUser(user);
                }catch (Exception e){
                    logger.error("用户状态设置异常" + e.getMessage());
                    response.setCode(ResponseDataEntity.ERROR);
                    response.setMsg("用户状态设置异常");
                    return response;
                }

				contractService.downloadContractZip(contractId);
				response.setCode(ResponseDataEntity.OK);
				response.setMsg("success");
			} else {
				response.setCode(ResponseDataEntity.ERROR);
				response.setMsg("保存失败");
			}
			return response;
		}else{
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("不属于自定义合同");
		}
		return response;
	}

	@PostMapping("/customize/cancel/callback")
	@ResponseBody
	public ResponseDataEntity cancelCustomizeCallback(HttpServletRequest request, @RequestBody String par) {
		logger.info("电子签回调：" + par);
		ResponseDataEntity response = new ResponseDataEntity();

		//过滤ip,若用户在白名单内，则放行
		String ipAddress = IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "contract")) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
			response.setMsg("IP不在白名单内");
			return response;
		}

		Map<String, String> map;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error("电子签回调参数转换异常：" + e.getMessage());
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("电子签回调参数转换异常");
			return response;
		}

		String contractId = map.get("contractId");
		UserContract userContract1 = contractService.getContractById(contractId);

		try{
            userContract1.setContractStatus(ContractStatus.TERMINATED);
            contractService.updateUserContract(userContract1);
        }catch (Exception e){
		    logger.error("合同状态设置异常" + e.getMessage());
            response.setCode(ResponseDataEntity.ERROR);
            response.setMsg("合同状态设置异常");
            return response;
        }

		if (contractService.getHighContractNumber(userContract1.getWorkNumber()).equals(userContract1.getContractNumber())) {
			User user = userService.getUserByUserId(userContract1.getUserId());
			user.setContractStatus(ContractStatus.TERMINATED);
			userService.updateUser(user);
		}

		response.setCode(ResponseDataEntity.OK);
		response.setMsg("success");
		return response;

	}

	//证明类合同回调
	@PostMapping("/prove/callback")
	@ResponseBody
	public ResponseDataEntity proveSignCallback(HttpServletRequest request, @RequestBody String par){
		logger.info("证明类合同回调：" + par);
		ResponseDataEntity response = new ResponseDataEntity();

		//过滤ip,若用户在白名单内，则放行
		String ipAddress= IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "contract")) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
			response.setMsg("IP不在白名单内");
			return response;
		}

		Map<String,String> map ;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error("证明类合同回调参数转换异常：" + e.getMessage());
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("证明类合同回调参数转换异常");
			return response;
		}
		//休眠5秒，等待合同生成
		try {
			Thread.sleep(5000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		//根据合同id或者合同编号查询合同信息
		String contractId = map.get("contractId");
		UserProveContractBacklog userProveContractBacklog = proveContractService.getProveContractBacklogByContractId(contractId);
		if (userProveContractBacklog == null) {
			logger.error("证明类签署电子签回调异常：" + contractId + "不存在");
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("合同编号不存在");
			return response;
		}
		userProveContractBacklog.setStatus(BacklogStatus.SIGN);
        //拉取合同附件
		contractService.downloadContractZipSync(contractId);

		if (proveContractService.updateProveContractBacklogByDhrId(userProveContractBacklog)) {
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("保存失败");
			logger.error(userProveContractBacklog.getUserId() +"保存失败" );
		}
		return response	;

	}

	@PostMapping("/dismissionSign/callback")
	@ResponseBody
	public ResponseDataEntity dismissionSignCallback(HttpServletRequest request, @RequestBody String par) {
		logger.info("离职员工签署电子签回调：" + par);
		ResponseDataEntity response = new ResponseDataEntity();

		//过滤ip,若用户在白名单内，则放行
		String ipAddress= IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "contract")) {
			response.setCode(ResponseDataEntity.UNAUTHORIZED);
			response.setMsg("IP不在白名单内");
			return response;
		}

		Map<String,String> map ;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error("离职员工签署电子签回调：" + e.getMessage());
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("离职员工签署电子签回调参数转换异常");
			return response;
		}
		UserContract contract = contractService.getContractById(map.get("contractId"));
		if (contract == null) {
			logger.error("离职员工签署电子签回调异常：" + map.get("contractId") + "不存在");
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("合同编号不存在");
			return response;
		}
		contract.setContractStatus(BacklogStatus.WAITSEAL);
		contract.setUpdatorUserId("auto");

		if (contractService.dismissionSign(contract)) {
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("保存失败");
		}
		return response	;
	}


	@PostMapping("/cancel/callback")
	@ResponseBody
	public ResponseDataEntity cancelCallback(HttpServletRequest request, @RequestBody String par) {
		logger.info("电子签回调：" + par);
		ResponseDataEntity response = new ResponseDataEntity();

		//过滤ip,若用户在白名单内，则放行
        String ipAddress= IPUtils.getRealIP(request);
        if (!ipWhiteListService.validateIp(ipAddress, "contract")) {
            response.setCode(ResponseDataEntity.UNAUTHORIZED);
            response.setMsg("IP不在白名单内");
            return response;
        }

        Map<String,String> map ;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error("电子签回调参数转换异常：" + e.getMessage());
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("电子签回调参数转换异常");
			return response;
		}

		UserContract contract = contractService.getContractById(map.get("contractId"));
		if (contract == null) {
			logger.error("电子签回调异常：" + map.get("contractId") + "不存在");
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("合同编号不存在");
			return response;
		}
		contract.setUpdatorUserId("auto");

		if (contractService.finishCancel(contract)) {
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} else {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("保存失败");
		}
		return response	;
	}

	@GetMapping("/getContractInfo")
	@ResponseBody
    @OperateLog(operType= OperateTypeEnum.UPDATE)
	@RequiresPermissions("MANAGE")
	public ResponseDataEntity getContractInfoFromUser(@RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			// 共用UserChangeContractBacklog做VO，里面包含了需要的字段
			UserChangeContractBacklog vo = new UserChangeContractBacklog();
			User user = userService.getUserByUserId(userId);
			int count = user.getContractCount();
			// 签订次数
			if (count < 3) {
				count += 1;
			} else {
				count = 3;
				logger.error("签订次数异常{}", userId);
			}
			vo.setContractCount(count);
			// 签约时间
			if (count == 1) {
				// 新签
				vo.setConBeginDate(DateTimeUtil.date2String(user.getExpectDate()));
				vo.setConEndDate(DateTimeUtil.date2String(DateTimeUtil.dateAddInt(DateTimeUtil.dateAddYear(user.getExpectDate(), 3), -1)));
			} else if (count == 2) {
				// 续签
				UserContract lastContract = contractService.selectUserNewestContract(userId);
				if (lastContract != null) {
					LocalDateTime startDate = DateTimeUtil.dateAddInt(lastContract.getEndDate(), 1);
					vo.setConBeginDate(DateTimeUtil.date2String(startDate));
					vo.setConEndDate(DateTimeUtil.date2String(DateTimeUtil.dateAddInt(DateTimeUtil.dateAddYear(startDate, 3), -1)));
				}
			} else {
				// 无限期
				UserContract lastContract = contractService.selectUserNewestContract(userId);
				if (lastContract != null) {
					LocalDateTime startDate=null;
					if(lastContract.getEndDate()!=null){
						startDate = DateTimeUtil.dateAddInt(lastContract.getEndDate(), 1);
					}else {
						startDate = lastContract.getStartDate();
					}
					vo.setConBeginDate(DateTimeUtil.date2String(startDate));
					vo.setConEndDate("2099-01-01");
				}
			}
			// 公司主体
			vo.setContract(user.getCompany());
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
			response.setData(vo);
			return response;
		} catch (Exception e) {
			logger.error("获取用户新签或转签失败：" + e);
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("获取用户新签或转签失败");
			response.setData(e.getMessage());
			return response;
		}
	}


}
