package com.megvii.service;

import com.megvii.entity.opdb.UserInfoLocale;
import com.megvii.mapper.UserInfoLocaleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserInfoLocaleService {

    @Autowired
    private UserInfoLocaleMapper userInfoLocaleMapper;

    public UserInfoLocale upsertUserInfo(UserInfoLocale userInfoLocale){
        if(userInfoLocaleMapper.updateUserInfo(userInfoLocale)<1){
            userInfoLocaleMapper.insertUserInfo(userInfoLocale);
        }
        return null;
    }

    public UserInfoLocale getUserInfoLocaleByUserId(String userId){
        return userInfoLocaleMapper.selectUserInfoLocaleByUserId(userId);
    }

    /**
     * 人员离职删除userIndoLocale信息
     * @param userId
     * @return
     */
    public boolean deleteUserInfoLocaleByUserId (String userId) {
        int result = userInfoLocaleMapper.deleteUserInfoLocaleByUserId(userId);
        if (result > 0) {
            return true;
        } else {
            return false;
        }
    }




}
