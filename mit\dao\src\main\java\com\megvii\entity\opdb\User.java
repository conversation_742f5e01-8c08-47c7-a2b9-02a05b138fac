package com.megvii.entity.opdb;


import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * IT_user实体类
 */
@Data
public class User implements Serializable {
    private Integer id;
    private String userId;
    private String spell;
    private String certName;
    private String cell;
    private LocalDateTime expectDate;
    private String hireDate;
    private String updater;
    private String team;    //初始插入前通过teamCode查询
    private String computer;
    private String os;
    private String mentor;
    private String buddy;   //extSiteHR_10
    private String seatNumber;
    private String tshirtSize;
    private String employType;
    private String status;
    private String memo;
    private LocalDateTime updateTime;
    private String hrbp;      //初始插入前通过teamCode查询
    private LocalDateTime regularDate;   //初始赋值为expectDate+180天
    private String probationDocUrl;
    private LocalDateTime dimissionDate;
    private String employBase;
    private String fullname;
    private String workNumber;  //redis+1
    private String company;
    private String teamCode;
    private String dingdingId;
    private String fullId;
    private String level;
    private String beisenId;
    private String oriPassword;
    private LocalDateTime birthday;
    private String position;
    private String siteHrg;
    private String didiId;
    private String unionId;
    private Integer teamId;   //初始插入前通过teamCode查询
    private String entryFlow;
    private String duty;
    private String title;
    private String source;
    private String workBase;
    private Integer didiDisabled;
    /**
     * create_tag = 1: 不传DHR；不添加滴滴，美餐
     * create_tag = 2: 不发信息收集
     * create_tag = 3: 不传DHR；不发信息收集，不添加滴滴，美餐
     * create_tag = 4: 标注中心人员（包括但不限于：钉钉不添加大群；不添加滴滴，美餐，差旅，koala，工卡）
     * create_tag = 9: ZK的人员，使用priEmail作为邮箱，不添加滴滴，美餐，差旅，koala，工卡，不添加钉钉
     */
    private Integer createTag;
    private String flowTime;   //时间戳
    private String koalaId;
    private String referTitle;
    private Integer sequence; //序列
    private Integer protect;
    private String certType;//证件类型
    private String certNo;//证件号
    private String address;//现居住地址
    private int contractCount;//合同签订次数
    private String contractStatus;//当前合同状态
    private String sales;
    private String mokaId;
    private String offerId;
    private String oldAddress;//原居住地址

    //此两条数据自定义，数据库没有
    private String priEmail;    //个人邮箱设置redis
    private boolean isDelay;   //是否延期入职，指不在月末入职而推迟入职
    private boolean isNeedSave;
    private String parseMsg;
    private int ldapFlag;    //0 创建失败，1 创建成功, 2 已存在，不重复创建



}
