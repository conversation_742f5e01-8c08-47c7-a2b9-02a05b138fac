import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { AuthComponent } from './auth.component'
// import { RoleComponent } from './role/role.component';
// import { MenuComponent } from './menu/menu.component';
import { UserComponent } from './user/user.component'

const routes: Routes = [
  {
    path: '',
    component: AuthComponent,
    children: [
      // {
      //   path: 'role',
      //   component: RoleComponent,
      //   data: {
      //     title: '角色管理',
      //   }
      // },
      // {
      //   path: 'menu',
      //   component: MenuComponent,
      //   data: {
      //     title: '菜单管理',
      //   }
      // },
      {
        path: 'user',
        component: UserComponent,
        data: {
          title: '人员管理'
        }
      }
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule {}
