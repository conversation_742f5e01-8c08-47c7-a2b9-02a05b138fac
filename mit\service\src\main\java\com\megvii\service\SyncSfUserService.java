package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.SfRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserInfoLocale;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

@PropertySource(value = "classpath:sf.${spring.profiles.active}.properties",encoding = "UTF-8")
@PropertySource(value = "classpath:group.properties",encoding = "UTF-8")
@Service
public class SyncSfUserService {
    Logger logger= LoggerFactory.getLogger(SyncSfUserService.class);

    private static JSONArray sfUserArray;
    private static JSONArray sfRelationArray;
    private static JSONArray sfEmpArray;
    private Map<String, String> employTypeMap = new HashMap<>();
    private Map<String,String> newReferTitleMap = new HashMap<>();

    @Value("#{'${SF_SYS_ADMIN}'.split(',')}")
    private List<String> sfSysAdmin = new ArrayList<>();

    @Value("#{'${SF_SKIP_ID}'.split(',')}")
    private List<String> sfSkipId = new ArrayList<>();

    @Value("#{${SF_EMP_TYPE}}")
    private Map<String,String> empTypeMap;

    @Value("${SF_BUDDY_TYPE}")
    private String SF_BUDDY_TYPE;

    @Value("${SF_HRG_TYPE}")
    private String SF_HRG_TYPE;

    @Value("${SF_SITE_HRG_TYPE}")
    private String SF_SITE_HRG_TYPE;

    @Value("#{${SF_REFER_TITLE}}")
    private Map<String,String> referTitleMap;

    @Autowired
    private SfRestClient sfRestClient;

    @Autowired
    private UserService userService;


    @Autowired
    private UserInfoLocaleService userInfoLocaleService;

    @Autowired
    private TeamService teamService;

    @Autowired
    private MMISMegviiConfigService mmisMegviiConfigService;

    @Autowired
    private SyncUserService syncUserService;

    @Autowired
    private EntryService entryService;


    /**
     * 同步sf user信息
     * @param userId
     * @return
     */
    public String syncSfUser(String userId) {
        return null;
//        StringBuilder message = new StringBuilder();
//        init();
//        List<User> sfUsers = getSfUserList(userId);
//        if (sfUsers == null || sfUsers.size() == 0) {
//            return userId + "在SF不存在";
//        }
//        for (User sfUser : sfUsers) {
//            try {
//                String result = syncUserService.compareUserInfo(sfUser);
//                if (result != null && !result.equals("")) {
//                    message.append(result);
//                }
//            } catch (Exception e) {
//                logger.error(sfUser.getUserId() + "同步SF信息错误:" + e.getMessage());
//                message.append(sfUser.getUserId() + "同步SF信息错误:" + e.getMessage() + "<br/>");
//            }
//        }
//        return message.toString().equals("")?null:message.toString();
    }



    /**
     * 将全部SF人员信息转化为user对象
     * 传入userName则只同步指定人员
     * userName = 姓名拼音
     * @return
     */
    private List<User> getSfUserList(String userName) {
        JSONObject data = null;
        Map<String, JSONObject> sfUserMap = new HashMap<>();
        Map<String, JSONObject> sfBuddy = new HashMap<>();
        Map<String, JSONObject> sfHrbp = new HashMap<>();
        Map<String, JSONObject> sfSiteHrg = new HashMap<>();

        for (Object o : sfUserArray) {
            data = (JSONObject) o;
            sfUserMap.put(data.getString("userId"), data);
        }

        for (Object o : sfRelationArray) {
            data = (JSONObject) o;
            if (data.getString("relationshipType").equals(SF_BUDDY_TYPE)) {
                sfBuddy.put(data.getString("userId"), data);
                continue;
            } else if (data.getString("relationshipType").equals(SF_HRG_TYPE)) {
                sfHrbp.put(data.getString("userId"), data);
                continue;
            } else if (data.getString("relationshipType").equals(SF_SITE_HRG_TYPE)) {
                sfSiteHrg.put(data.getString("userId"), data);
                continue;
            }
        }

        Map<String, JSONObject> sfEmp = new HashMap<>(getMapSize(sfEmpArray.size()));
        for (Object o : sfEmpArray) {
            data = (JSONObject) o;
            sfEmp.put(data.getString("userId"), data);
        }

        List<User> list = new ArrayList<>();
        for (Object o : sfUserArray) {
            data = (JSONObject) o;

            String userId = data.getString("username");
            String workNumber = data.getString("userId");

            if (userName != null && !userId.equals(userName)) {
                continue;
            }

            if (sfSysAdmin.contains(userId) || sfSkipId.contains(userId)) {
                continue;
            }
            if (workNumber.equals("None")) {
                continue;
            }

            JSONObject buddyJson = sfBuddy.get(workNumber);
            JSONObject hrbpJson = sfHrbp.get(workNumber);
            JSONObject siteHrgJson = sfSiteHrg.get(workNumber);
            JSONObject empJson = sfEmp.get(workNumber);

            User sfUser = null;
            try {
                sfUser = getSfUserInfo(data, buddyJson, hrbpJson, siteHrgJson, empJson, sfUserMap);
                list.add(sfUser);
            } catch (Exception e) {
                logger.error(userId + "SF信息转化错误:" + e.getMessage());
                continue;
            }
        }

        return list;
    }

    /**
     * 将SF信息解析放入user对象内
     * @param data SF人员基础信息
     * @param buddyJson SF人员关系信息
     * @param hrbpJson  SF人员关系信息
     * @param siteHrgJson  SF人员关系信息
     * @param empJson   SF人员工作信息
     * @return
     * @throws Exception
     */
    private User getSfUserInfo(JSONObject data, JSONObject buddyJson, JSONObject hrbpJson, JSONObject siteHrgJson, JSONObject empJson, Map<String, JSONObject> sfUserMap) throws Exception {
        User user = new User();
        user.setWorkNumber(data.getString("userId"));
        user.setUserId(data.getString("username"));
        user.setSpell(data.getString("defaultFullName"));
        user.setCell(data.getString("cellPhone")!=null?data.getString("cellPhone").replaceAll(" ", "").replaceAll("-",""):null);
        user.setEmployBase(data.getString("location").equals("N/A")?"":data.getString("location").split(" ")[0]);

        String email = data.getString("email");
        if (email == null) {
            sfRestClient.addEmailInfo(user.getWorkNumber(), user.getUserId());
        }

        if (buddyJson != null) {
            JSONObject buddy = sfUserMap.get(buddyJson.getString("relUserId"));
            user.setBuddy(buddy!=null?buddy.getString("username"):"");
        } else {
            user.setBuddy("");
        }

        if (hrbpJson != null) {
            JSONObject hrbp = sfUserMap.get(hrbpJson.getString("relUserId"));
            user.setHrbp(hrbp!=null?hrbp.getString("username"):"");
        } else {
            user.setHrbp("");
        }

        if (siteHrgJson != null) {
            JSONObject siteHrg = sfUserMap.get(siteHrgJson.getString("relUserId"));
            user.setSiteHrg(siteHrg!=null?siteHrg.getString("username"):"");
        } else {
            user.setSiteHrg("");
        }

        if (empJson != null) {
            user.setExpectDate(empJson.getString("jobEntryDate")!=null?transSfDate(empJson.getString("jobEntryDate")):null);
            user.setRegularDate(empJson.getString("probationPeriodEndDate")!=null?transSfDate(empJson.getString("probationPeriodEndDate")):null);
            user.setEmployType(employTypeMap.get(empJson.getString("employeeType")));

            JSONObject mentor = sfUserMap.get(empJson.getString("managerId"));
            user.setMentor(mentor!=null?mentor.getString("username"):"");

            String teamCode = empJson.getString("customString17")!=null?
                    empJson.getString("customString17"):empJson.getString("department")!=null?
                    empJson.getString("department"):empJson.getString("division")!=null?
                    empJson.getString("division"):empJson.getString("customString16")!=null?
                    empJson.getString("customString16"):empJson.getString("businessUnit")!=null?
                    empJson.getString("businessUnit"):null;

            Team team = teamService.getTeamByCode(teamCode);
            if (team == null) {
                throw new Exception(data.getString("username") + ":team is null");
            }

            user.setTeam(team.getName());
            user.setTeamId(team.getId());
            if (user.getHrbp() == null || user.getHrbp().equals("")) {
                user.setHrbp(team.getHrg());
            }

            user.setBirthday(data.getString("dateOfBirth")!=null?transSfDate(data.getString("dateOfBirth")):null);
            user.setUpdateTime(empJson.getString("lastModifiedDateTime")!=null?transSfDate(empJson.getString("lastModifiedDateTime")):null);

            JSONObject updater = sfUserMap.get(empJson.getString("lastModifiedBy"));
            user.setUpdater(updater!=null?updater.getString("username"):null);
            user.setCompany(empJson.getString("customString14"));
            user.setPosition(empJson.getString("position"));
            user.setTitle(empJson.getString("customString15"));

            String referTitle = empJson.getString("customString20");
            user.setReferTitle((referTitle!=null&&newReferTitleMap.get(referTitle)!=null)?newReferTitleMap.get(referTitle):null);

            Map<String,String> map = mmisMegviiConfigService.getWorkSiteCodeMap();
            user.setWorkBase(map.get(empJson.getString("customString8")));
        }

        return user;
    }

    /**
     * 从sf拉取全部人员数据
     */
    private void init() {
        sfUserArray = sfRestClient.getUsers();
        sfRelationArray = sfRestClient.getSfRelationships();
        sfEmpArray = sfRestClient.getSfEmp();

        //将empTypeMap key-value调换
        for (String key : empTypeMap.keySet()) {
            employTypeMap.put(empTypeMap.get(key), key);
        }

        //referTitleMap
        for (String key : referTitleMap.keySet()) {
            newReferTitleMap.put(referTitleMap.get(key), key);
        }
    }


    /**
     * create_tag = 1: 不传SF
     * create_tag = 2: 不发阿里云
     * create_tag = 3: 不传SF且不发阿里云
     *
     * 新建账号到sf并同步人员信息
     * @param user
     * @return
     */
    public String synUser2Sf(User user){
        if (user.getEntryFlow()!=null && "OA流程".equals(user.getEntryFlow())){
            return user.getUserId()+"处于'OA流程'状态，不创建sf账号";
        }

//        aliyunUserInfoService.deletePsw(user.getUserId());
//        entryService.deletePSW(user.getUserId());

        if (user.getCreateTag()!=null&&(user.getCreateTag()==1 || user.getCreateTag()==3)){
            return user.getUserId()+"人员 create_tag为"+user.getCreateTag()+",不创建sf账号";
        }
//        if (user.getTeam()!=null&&user.getTeam().contains("西雅图")&&user.getEmployType()!=null&&!"正式员工".equals(user.getEmployType())){
//            return user.getUserId()+"西雅图非正式员工,不创建sf账号";
//        }
//        if(sfRestClient.isExistUser(user.getWorkNumber())){
//            return user.getUserId()+"人员已在sf中存在";
//        }
        String mentor=null;
        String hrbp=null;
        String buddy=null;
        String siteHrg=null;
        String groupLeader=null;
        if (user.getMentor()!=null){
            User mentorUser=userService.getUserByUserId(user.getMentor());
            mentor=mentorUser==null?null:mentorUser.getWorkNumber();
        }
        if (user.getHrbp()!=null){
            User hrbpUser=userService.getUserByUserId(user.getHrbp());
            hrbp=hrbpUser==null?null:hrbpUser.getWorkNumber();
        }
        if (user.getBuddy()!=null){
            User buddyUser=userService.getUserByUserId(user.getBuddy());
            buddy=buddyUser==null?null:buddyUser.getWorkNumber();
        }
        if (user.getSiteHrg()!=null){
            User siteHrgUser=userService.getUserByUserId(user.getSiteHrg());
            siteHrg=siteHrgUser==null?null:siteHrgUser.getWorkNumber();
        }
        UserInfoLocale userInfoLocale=userInfoLocaleService.getUserInfoLocaleByUserId(user.getUserId());
        String bankName=null;
        String bankAccount=null;
        String bank=null;
        String gender=null;
        String address=null;
        String householdPlace=null;
        String reproductiveStatus=null;
        String maritalStatus=null;
        String disabilityStatus=null;
        String householdType=null;
        String perNationalId=null;
        LocalDateTime birthDay=null;
        String contactRelation=null;
        String contactRelationPhone=null;
        String contactName=null;
        JSONArray honors=null;
        JSONArray employeds=null;
        JSONArray educationList=null;
        if (userInfoLocale!=null){
            bank=userInfoLocale.getBank();
            try{
                JSONObject info=JSONObject.parseObject(StringEscapeUtils.unescapeJava(userInfoLocale.getInfo()));
                bankAccount=info.getString("employ_bank_account");
                bankName=info.getString("employ_opening_bank");
                gender=info.getString("personal_gender");
                address=info.getString("personal_present_address");
                householdPlace=info.getString("personal_household_place");
                reproductiveStatus=info.getString("personal_reproductive_status");
                maritalStatus=info.getString("personal_marital_status");
                disabilityStatus=info.getString("personal_disability_status");
                householdType=info.getString("personal_household_type");
                perNationalId=info.getString("id_number");
                birthDay= DateTimeUtil.string2DateYMD(info.getString("personal_birthday"));
                contactRelation=info.getString("emergency_contact_relation");
                contactRelationPhone=info.getString("emergency_contact_phone");
                contactName=info.getString("emergency_contact_name");
                honors=info.getJSONArray("honor");
                employeds=info.getJSONArray("employed");
                educationList=info.getJSONArray("education");
            }catch (Exception e){
                logger.error(user.getUserId() + "银行账户获取失败",e);
            }
        }
        if (user.getTeamId()!=null){
            Team team=teamService.getTeamById(user.getTeamId());
            if (team!=null&&team.getGroupLeader()!=null){
                User groupLeaderUser=userService.getUserByUserId(team.getGroupLeader());
                groupLeader=groupLeaderUser==null?null:groupLeaderUser.getWorkNumber();
            }
        }

        //从新计算试用期截止日期
        LocalDateTime expectDate = user.getExpectDate();//入职日期
        LocalDateTime regularDate = null;//试用期截止日期
        if (expectDate != null && user.getEmployType() != null && "正式员工".equals(user.getEmployType())){
            regularDate = DateTimeUtil.dateAddInt(expectDate,180);
            if (!regularDate.equals(user.getRegularDate())) {
                user.setRegularDate(regularDate);
                userService.updateUser(user);
            }
        }

        boolean createUserFlag=sfRestClient.createUser(user.getWorkNumber(),user.getUserId(),user.getSpell(),user.getEmployBase(),mentor,hrbp);
        boolean empEmploymentFlag=sfRestClient.createEmpemployment(user.getWorkNumber(),user.getSource(),user.getExpectDate(),user.getRegularDate(),bankName,bankAccount,bank);
        boolean empJobFlag=sfRestClient.createEmpJob(user.getWorkNumber(),user.getExpectDate(),user.getRegularDate(),user.getPosition(),mentor,user.getEmployBase(),user.getCompany(),
                user.getEmployType(),user.getDuty(),"无".equals(user.getTitle())?null:user.getTitle(),user.getReferTitle(),user.getWorkBase(),getDeptMap(user.getTeamId()==null?-1:user.getTeamId()));
        boolean empJobRelationshipsFlag=sfRestClient.creareEmpJobRelationships(user.getWorkNumber(),hrbp,buddy,siteHrg,groupLeader,user.getExpectDate());
        boolean emailFlag=sfRestClient.createSfMail(user.getWorkNumber(),user.getUserId());
        boolean phoneFlag=sfRestClient.createSfPhone(user.getWorkNumber(),user.getCell());
        boolean personalFlag=sfRestClient.createSfPersonalInfo(user.getWorkNumber(),user.getExpectDate(),user.getSpell(),gender,householdPlace,address,maritalStatus,reproductiveStatus,
                disabilityStatus,householdType);
        boolean perNationalIdFlag=sfRestClient.createPerNationalId(user.getWorkNumber(),perNationalId);
        boolean perPersonFlag=sfRestClient.createSfPerPerson(user.getWorkNumber(),birthDay);
        boolean contactFlag=sfRestClient.createPerEmergencyContacts(user.getWorkNumber(),contactRelation,contactName,contactRelationPhone);
        boolean backgroundAwardsFlag=sfRestClient.createBackgroundAwards(user.getWorkNumber(),honors);
        boolean workExperienceFlag=sfRestClient.createBackgroundOutsideWorkExperience(user.getWorkNumber(),employeds);
        boolean backgroundEducationFlag=sfRestClient.createBackgroundEducation(user.getWorkNumber(),educationList);
        StringBuilder stringBuilder=new StringBuilder();
        stringBuilder.append(user.getUserId()+":");
        stringBuilder.append(createUserFlag?"User创建成功,":"User创建失败,");
        stringBuilder.append(empEmploymentFlag?"EmpEmployment创建成功,":"EmpEmployment创建失败,");
        stringBuilder.append(empJobFlag?"EmpJob创建成功,":"EmpJob创建失败,");
        stringBuilder.append(empJobRelationshipsFlag?"EmpJobRelationships创建成功,":"EmpJobRelationships创建失败,");
        stringBuilder.append(emailFlag?"PerEmail创建成功,":"PerEmail创建失败,");
        stringBuilder.append(phoneFlag?"PerPhone创建成功,":"PerPhone创建失败,");
        stringBuilder.append(personalFlag?"PerPersonal创建成功,":"PerPersonal创建失败,");
        stringBuilder.append(perNationalIdFlag?"PerNationalId创建成功,":"PerNationalId创建失败,");
        stringBuilder.append(perPersonFlag?"PerPerson创建成功,":"PerPerson创建失败,");
        stringBuilder.append(contactFlag?"PerEmergencyContacts创建成功,":"PerEmergencyContacts创建失败,");
        stringBuilder.append(backgroundAwardsFlag?"Background_Awards创建成功,":"Background_Awards创建失败,");
        stringBuilder.append(workExperienceFlag?"Background_OutsideWorkExperience创建成功":"Background_OutsideWorkExperience创建失败");
        stringBuilder.append(backgroundEducationFlag?"BackgroundEducation创建成功,":"BackgroundEducation创建失败,");

//        user.setEntryFlow("在职");
//        userService.updateUserEntryFlow(user);

        return stringBuilder.toString();

    }

    /**
     * 将MIT部门解析为sf部门
     *
     * @param teamId 部门Id
     * @return
     */
    public Map getDeptMap(int teamId){
        Map deptMap=new HashMap();
        try {
            Team team=teamService.getTeamById(teamId);
            if (team!=null){
                String code=team.getSfCode();
                String firstChar=code.substring(0,1);
                String secondChar=code.substring(1,2);
                String[] departments=team.getName().split("-");
                if ("S".equals(firstChar)&&!"C".equals(secondChar)){
                    String groupName=departments[0];
                    String buName=groupName+"-"+departments[1];
                    String divisionName=buName+"-"+departments[2];
                    Team group=teamService.getTeamByName(groupName);
                    Team bu=teamService.getTeamByName(buName);
                    Team division=teamService.getTeamByName(divisionName);
                    deptMap.put("department",code);
                    deptMap.put("division",division==null?null:division.getSfCode());
                    deptMap.put("customString16",bu==null?null:bu.getSfCode());
                    deptMap.put("businessUnit",group==null?null:group.getSfCode());
                }else if ("S".equals(firstChar)&&"C".equals(secondChar)){
                    String groupName=departments[0];
                    String buName=groupName+"-"+departments[1];
                    String divisionName=buName+"-"+departments[2];
                    String subteamName=divisionName+"-"+departments[3];
                    Team group=teamService.getTeamByName(groupName);
                    Team bu=teamService.getTeamByName(buName);
                    Team division=teamService.getTeamByName(divisionName);
                    Team subteam=teamService.getTeamByName(subteamName);
                    deptMap.put("customString17",code);
                    deptMap.put("department",subteam==null?null:subteam.getSfCode());
                    deptMap.put("division",division==null?null:division.getSfCode());
                    deptMap.put("customString16",bu==null?null:bu.getSfCode());
                    deptMap.put("businessUnit",group==null?null:group.getSfCode());
                }else if ("T".equals(firstChar)){
                    String groupName=departments[0];
                    String buName=groupName+"-"+departments[1];
                    Team group=teamService.getTeamByName(groupName);
                    Team bu=teamService.getTeamByName(buName);
                    deptMap.put("division",code);
                    deptMap.put("customString16",bu==null?null:bu.getSfCode());
                    deptMap.put("businessUnit",group==null?null:group.getSfCode());
                }else if ("B".equals(firstChar)) {
                    String groupName=departments[0];
                    Team group=teamService.getTeamByName(groupName);
                    deptMap.put("customString16",code);
                    deptMap.put("businessUnit",group==null?null:group.getSfCode());

                }else if ("G".equals(firstChar)&&"D".equals(secondChar)) {
                    deptMap.put("businessUnit",code);
                }
            }
        }catch (Exception e){
            logger.error("sf同步时解析部门失败",e);
        }

        return deptMap;
    }

    /**
     * 获取hashMap初始长度
     * @param length
     * @return
     */
    private Integer getMapSize(int length) {
        //长度/负载因子
        Double lengthTemp = Math.ceil(length / 0.75);
        int newLength = lengthTemp.intValue();

        //寻找最接近的2^n
        for (int i = 1; i > 0; i ++) {
            Double size = Math.pow(2, i);
            if (newLength > size) {
                continue;
            } else {
                return size.intValue();
            }
        }
        return null;
    }


    /**
     * 将SF格式时间转换为LocalDateTime
     * /Date(1504742400000)/   ->   2017-09-07T00:00
     * @param time
     * @return
     */
    private LocalDateTime transSfDate(String time) {
        String timestamp = time.replaceAll("/Date", "")
                .replaceAll("/", "").replaceAll("\\(", "")
                .replaceAll("\\)", "").replaceAll("\\+0000", "");

        return LocalDateTime.ofEpochSecond(Long.valueOf(timestamp)/1000, 0, ZoneOffset.of("+0"));
    }


}
