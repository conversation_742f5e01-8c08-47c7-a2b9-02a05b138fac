<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.diracMapper.DiracUserMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.dirac.DiracUser">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="spell" property="spell"/>
        <result column="password_hash" property="passwordHash"/>
        <result column="confirmed" property="confirmed"/>
        <result column="common_system" property="commonSystem"/>
    </resultMap>

    <select id="selectUserByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user where user_id = #{userId}
    </select>

    <update id="updateDiracUser" parameterType="com.megvii.entity.dirac.DiracUser">
        update user
        <set>
            <if test="spell != null ">
                spell = #{spell},
            </if>
        </set>
        where id = #{id}
    </update>




</mapper>