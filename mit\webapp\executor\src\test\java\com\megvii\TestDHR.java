package com.megvii;

import com.alibaba.fastjson.JSONArray;
import com.megvii.client.ContractRestClient;
import com.megvii.client.DhrRestClient;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.BacklogStatus;
import com.megvii.entity.opdb.contract.UserProveContractBacklog;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.*;
import com.megvii.service.contract.ContractRestService;
import com.megvii.service.contract.ContractService;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.megvii.service.contract.ProveContractBacklogService;
import com.megvii.service.contract.ProveContractService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/10/12 11:16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestDHR {

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private DhrRestClient dhrRestClient;

	@Autowired
	private DimissionService dimissionService;

	@Autowired
	private UserService userService;

	@Autowired
	private EntryService entryService;

	@Autowired
	private MMISMegviiConfigService mmisMegviiConfigService;

	@Autowired
	private SMSService smsService;

	@Autowired
	private MMISMailTemplateService mmisMailTemplateService;

	@Autowired
	private MailSendService mailSendService;

	@Autowired
	private DhrService dhrService;

	@Autowired
	private ContractRestService contractRestService;

	@Autowired
	private ContractRestClient contractRestClient;

	@Autowired
	private ContractService contractService;

	@Autowired
	private SyncZTDataService syncZTDataService;
	@Autowired
	private SyncZtUserService syncZtUserService;

	@Autowired
	private RemindService remindService;

	@Autowired
	private ProveContractService proveContractService;

	@Autowired
	private ProveContractBacklogService proveContractBacklogService;


	@Test
	public void testDismission(){
		dhrService.getDimissionArray();
	}

	@Test
	public void teatContract(){
		List<String> removeMap = contractService.getAllContractNumber();
		String msg = dhrService.getHistoryContractFromDhr(removeMap);
	}

	@Test
	public void teatDisissonProcess() {
		String msg = dhrService.getDimissionProcessBacklogFromDhr();
		System.out.println(msg);
	}

	//

	@Test
	public void teatProveContractPull() {
	String msg = dhrService.getProveContractBacklogFromDhr();;
	System.out.println(msg);


	}

	@Test
	public void teatProveContract2Dhr() {
		String msg = proveContractService.sendEmailAndPushDHR();
		System.out.println(msg);
	}

	@Test
	public void  testProveContractUpdate(){
		UserProveContractBacklog userProveContractBacklog = proveContractBacklogService.getProveContractBacklogByContractId("3091999078716285547");
		userProveContractBacklog.setStatus(BacklogStatus.SIGN);
		boolean result = proveContractBacklogService.updateProveContractBacklogByDhrId(userProveContractBacklog);
		System.out.println(result);
	}

	@Test
	public void testgetDismissionContractBacklogFromDhr(){
		String msg = dhrService.getDismissionContractBacklogFromDhr();
		System.out.println(msg);
	}

	@Test
	public void testAddDHR() {
		User user = userService.getUserByUserId("liuhaier");


		dhrService.syncUser2Dhr(user);

		System.out.println(1);


	}

	@Test
	public void testGetToken() {
		try {
			dhrRestClient.getToken();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testPutStart() {
		try {
//			dhrRestClient.putStart();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testPutClose() {
		try {
//			dhrRestClient.putClose("jiangyuhong");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testGetData() {
		try {
			JSONArray array = dhrRestClient.getData("9");
			System.out.println(1);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testSendMail() {
		User user = userService.getUserByUserId("zhangliangliang03");
		user.setPriEmail("<EMAIL>");
//		mailSendService.sendEntryInfoMail(user, entryService.getEntryInfoLimitDate(user.getExpectDate()));
		mailSendService.sendOneweekMail(user, "<EMAIL>");
//		remindService.userTodayRemind(user);


		System.out.println(1);

	}

	@Test
	public void testCode() {
//        String code = dhrRestClient.getCompanyCode("北京迈格威科技有限公司");
//        System.out.println(code);

		Map<String, Object> map = mmisMegviiConfigService.getConfigJsonByKey("BANK_DICT");

		System.out.println(1);

	}

	@Test
	public void insertAliyunInfo() {
		User user = userService.getUserByUserId("jiangyuhong");
		String msg = entryService.insertEntryInfo(user);
		System.out.println(1);
	}


	@Test
	public void testSms() {

//        User user = userService.getUserByUserId("jiangyuhong");
//        user.setCell("***********");

//        smsService.sendAliyunEntrySms(user, entryService.getEntryInfoLimitDate(user.getExpectDate()));


//        entryService.sendEntrySmsEmail(user);

	}

	@Test
	public void testPutPhoto() {
//        AliyunUserInfo userInfo = aliyunUserInfoService.getUserCertPhotoByUserId("maminhong");

//        try {
//            ImageFile a = entryService.getPhotoCert("jiangyuhong");
//
//            System.out.println(1);
//
//            byte[] getData = a.getFileBytes();
//
//            File saveDir = new File("/Users/<USER>/Desktop/");
//            if(!saveDir.exists()){
//                saveDir.mkdir();
//            }
//
//            File file = new File(saveDir+File.separator+a.getFileName());
//            FileOutputStream fos = new FileOutputStream(file);
//            fos.write(getData);
//            if(fos!=null){
//                fos.close();
//            }
//        } catch (Exception e) {
//            System.out.println(2);
//        }
//
//
//        System.out.println(2);


		try {
//            dhrRestClient.putPhoto(userInfo.getPhotoCert(), "jiangyuhong", "jiangyuhong_life");
		} catch (Exception e) {
			e.printStackTrace();
		}


	}

	@Test
	public void testPutDhr() {
        User user = userService.getUserByUserId("zhaopang10");
//
        user.setWorkNumber("200001");
//
        dhrService.syncUser2Dhr(user);

//        String code = dhrRestClient.getBankCode("中国工商银行");
//
        System.out.println(1);


//        mailSendService.sendMail("test003", "=.=test-content", "<EMAIL>,<EMAIL>,");


		UserFilter filter = new UserFilter();

//		filter.setStatusCode(0);
//		filter.setExpectDateFrom("2020-02-17");
//		filter.setExpectDateTo("2020-02-23");
		filter.setCreateTagCode("0");

		filter.setEntryFlowStatus(3);
		filter.setStatusCode(0);
		filter.setDingdingIdCode(0);
		List<User> users = userService.getUsersByFilter(filter);


		System.out.println(1);

//		User user = userService.getUserByUserId("jiangyuhong");


		List<User> list = new ArrayList<>();
		list.add(user);
		list.add(user);


		try {
//            mailSendService.sendTomorrowEntryMail(list, "<EMAIL>", "请于本日内与前台蔡晓蕾联系，确认新员工工位位置，并协助腾出工位");
		} catch (Exception e) {
			e.printStackTrace();
		}

		System.out.println(1);
	}


	@Test
	public void testContract() throws Exception {
		contractRestClient.downloadContractZip("2692692298606354731", "/Users/<USER>/Desktop/contract/");
//        UserContractSnapshot snapshot = contractService.getContractSnapshot("jiangyuhong");
//
//        contractService.updateContractSnapshot(snapshot);

//        JSONObject data = contractRestService.createContractData(snapshot, false);
//        String a = contractRestClient.createContract(data);
		System.out.println(1);

//        contractRestClient.sendContract("2692686454254772391");


//        contractRestClient.recallContract("2692274792083267801");
//        contractRestClient.getViewContractUrl("2692318492188516938");
//        contractRestClient.cancelContract("2692318492188516938", "2664718957817237642");

//        JSONObject data = mmisMegviiConfigService.getCompanyConfigByName("旷视科技有限");

//        System.out.println(1);
//        File file = new File("/Users/<USER>/Desktop/合同key值对应关系.xlsx");
//
//        contractRestClient.addFileToContract(file, a);
//
//        contractRestClient.sendContract(a);

//        System.out.println(ContractStatus.valueOf("IMPORT"));


//        contractService.saveContractFile(null, "jiangyuhong", "员工体检-侧重于甲状腺项目方案.pdf");

		System.out.println(1);


	}

	@Test
	public void testTemp() throws Exception {

//		syncZTDataService.syncDhrPositionFromZT();
		syncZtUserService.syncZTUser(null,null);
//		syncZtUserService.syncZTUser("caohailing",null);
//		syncZTDataService.addZTTeam();

//		mailSendService.sendMail("testtitle", "testcontent", "<EMAIL>");

//		UserContract contract = contractService.getContractById("111111");
//
//		System.out.println(1);
//
//		dhrService.contractToDhr(contract);
//
//		System.out.println(1);

//		UserContractSnapshot snapshot = new UserContract();
//		snapshot = contractService.getContractSnapshot("jiangyuhong");
//
//		UserContract contract  = new UserContract();
//
//		ParameterUtil.fatherToChild(snapshot, contract);
//
//
//		System.out.println(1);


	}
	@Test
	public void testGetContractBacklogFromDhr() {
		String contractBacklogFromDhr = dhrService.getContractBacklogFromDhr();
	}

	@Test
	public void testGetChangeContractBacklogFromDhr() {
		String contractBacklogFromDhr = dhrService.getChangeContractBacklogFromDhr();
	}
}
