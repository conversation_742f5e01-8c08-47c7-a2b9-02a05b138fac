import { Component, OnInit } from '@angular/core'
import { NzModalService } from 'ng-zorro-antd/modal'
import { environment } from 'src/environments/environment'
import { MessageService, SendRequest } from '../../core/services'
import { ActivatedRoute, Router } from '@angular/router'
import contractStatus from '../../core/values/contract-status'
import { ContractService } from '../contract.service'
import { Observable, Observer } from 'rxjs'
import { MapService } from '../../core/services/map.service'

// import { SimpleReuseStrategy } from '../../app-routing.cache';

@Component({
  selector: 'app-user-info',
  templateUrl: './user-info.component.html',
  styleUrls: ['./user-info.component.less']
})
export class UserInfoComponent implements OnInit {
  window = window as any
  contractList = []
  oprtList = []

  // 人员基本信息字段
  staffId = ''
  dept = ''
  id = ''
  offerId = ''
  leader = ''
  username = ''
  HRG = ''
  tele = ''
  officeLocation = ''
  type = '1'
  entryLocation = '' // 入职地点
  station = '' // 岗位
  apartment = '' // 居住地
  company = ''
  idTypeList = []
  idType = ''
  idCard = '' // 证件号
  entryDate = ''
  remark = ''
  isShowCreate = true
  isShowStaff = false  // 待签署入职材料
  fileList = [] // 附件内容列表
  curContractStatus = ''
  downLoadUrl // 下载附件链接

  // 下拉选项
  workLocationOfOption: Array<{ value: string; text: string }> = []
  companyOfOption: Array<{ value: string; text: string }> = []
  contractStatusList = this.translateObjToArr(contractStatus)
  positionList = [] // 工作岗位列表
  signing = false
  saving = false

  // 控制转签、变更字段显示
  showEndorse = false
  showChange = false

  nzSelectedIndex = '0'
  uploadFileAction = ''
  // 合同快照对象
  contractSnapshot = {
    company: '',
    contractAttribute: '',
    contractCount: '',
    contractNumber: '',
    contractStatus: '',
    contractType: '',
    effectDate: '',
    employBase: '',
    endDate: '',
    memo: '',
    probationEndDate: '',
    probationStartDate: '',
    startDate: '',
    workNumber: '',
    // 转签合同添加字段
    conBeginDate: '', // 旧合同开始日期
    conEndDate: '',
    newConBeginDate: '', // 新合同开始日期
    newConEndDate: '',
    contract: '', // 新签署公司
    newContract: '',
    // 变更合同添加字段
    workCity: '',
    newWorkCity: ''
  }

  employType = '' // 入职类型
  status = '' // 状态 用于判断是否可以签署
  userId = ''
  backurl = ''

  contractListLoading = true
  oprtListLoading = true

  isBasicLoading = true // 人员基本信息加载中
  isSnapShotLoading = false

  constructor(
    private modalService: NzModalService,
    private sendRequest: SendRequest,
    private messageService: MessageService,
    private activateInfo: ActivatedRoute,
    private contractService: ContractService,
    private router: Router,
    private mapService: MapService // private simpleReuseStrategy: SimpleReuseStrategy
  ) {}

  ngOnInit(): void {
    this.userId = this.activateInfo.snapshot.queryParams['id']
    const urlArr = this.router.url.toLowerCase().split('/user')
    this.backurl = urlArr ? urlArr[0] : ''
    this.uploadFileAction =
      environment.urlPrefix + '/api/contract/snapshot/upload?userId=' + this.userId
    this.downLoadUrl =
      environment.urlPrefix + '/api/contract/snapshot/file?userId=' + this.userId + '&path=' // 下载附件链接
    this.getBaseInfo()
    this.getLocationList()
    this.getEntryCompanyList()
    this.getEmployType()
    this.getPositonList()
    this.getIdType()
    this.getContractSnapshot()
  }

  // 跳转至megin完整信息页面
  linkToDetail() {
    this.window.open(`${environment.linkUrl}/app/user/detail?type=entry&offerId=${this.offerId}&userId=${this.userId}&from=mit`)
  }

  onSave() {
    this.saveBaseInfo(true)
  }

  onSaveAndCreate() {
    this.saveBaseInfo(false, () => this.createSpanshot())
  }

  onSubmit(showSuccess: boolean, callbackFnc = () => {}) {
    const contractSnapshot = {
      ...this.contractSnapshot,
      graduationTime: this.convertDate(this.contractSnapshot['graduationTime']),
      admissionTime: this.convertDate(this.contractSnapshot['admissionTime']),
      probationEndDate: this.convertDate(this.contractSnapshot['probationEndDate']),
      probationStartDate: this.convertDate(this.contractSnapshot['probationStartDate']),
      startDate: this.convertDate(this.contractSnapshot['startDate']),
      endDate: this.convertDate(this.contractSnapshot['endDate']),
      effectDate: this.convertDate(this.contractSnapshot['effectDate']),
      conBeginDate: this.convertDate(this.contractSnapshot['conBeginDate']),
      conEndDate: this.convertDate(this.contractSnapshot['conEndDate']),
      newConBeginDate: this.convertDate(this.contractSnapshot['newConBeginDate']),
      newConEndDate: this.convertDate(this.contractSnapshot['newConEndDate'])
    }
    this.contractService.updateSpanshot(contractSnapshot).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        if (showSuccess) {
          this.messageService.success('保存成功')
          // SimpleReuseStrategy.deleteRouteSnapshot(this.backurl);
        } else {
          callbackFnc()
        }
      } else {
        this.messageService.error(msg)
        if (!showSuccess) {
          this.signing = false
        }
      }
    })
  }

  getOprtHistory() {
    this.oprtListLoading = true
    this.contractService.getOprtHistory({ userId: this.userId }).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.oprtListLoading = false
        this.oprtList = data || []
      } else {
        this.oprtListLoading = false
        this.messageService.error(msg)
      }
    })
  }

  getContractHistory() {
    this.contractListLoading = true
    this.contractService
      .getContractHistoryWithoutSnapshot({ userId: this.userId })
      .subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          this.contractListLoading = false
          this.contractList = data || []
        } else {
          this.contractListLoading = false
          this.messageService.error(msg)
        }
      })
  }

  getBaseInfo() {
    this.isBasicLoading = true
    this.contractService.getBaseInfo({ userId: this.userId }).subscribe((res) => {
      this.isBasicLoading = false
      const { code, msg, data } = res
      if (code === 0) {
        this.staffId = data['workNumber']
        this.dept = data['team']
        this.id = data['id']
        this.offerId = data['offerId']
        this.leader = data['mentor']
        this.username = data['certName']
        this.HRG = data['hrg']
        this.tele = data['cell']
        this.officeLocation = data['employBase']
        this.type = data['employType']
        this.status = data['entryFlow']
        this.entryLocation = data['workBase'] // 入职地点
        this.station = data['position'] // 岗位
        this.apartment = data['address'] // 居住地
        this.company = data['company']
        this.idType = data['certType'] // 证件类型
        this.idCard = data['certNo'] // 身份证
        this.entryDate = data['expectDate']
        this.remark = data['memo']
        this.curContractStatus = data['contractStatus']
      } else {
        this.messageService.error(msg)
      }
    })
  }

  typeSelChange(event) {
    const { index } = event
    this.nzSelectedIndex = index
    switch (index) {
      case 0:
        this.getBaseInfo()
        this.getContractSnapshot()
        break
      case 1:
        this.getContractHistory()
        break
      case 2:
        this.getOprtHistory()
    }
  }

  saveBaseInfo(showSuccess: boolean, callbackFnc = () => {}) {
    this.contractService
      .saveBaseInfo({
        userId: this.userId,
        // workBase: this.entryLocation,
        position: this.station,
        address: this.apartment,
        company: this.company,
        certType: this.idType,
        certNo: this.idCard,
        expectDate: this.convertDate(this.entryDate),
        memo: this.remark,
        employBase: this.officeLocation
      })
      .subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          if (showSuccess) {
            this.messageService.success('人员基本信息保存成功')
            // SimpleReuseStrategy.deleteRouteSnapshot(this.backurl);
          } else {
            callbackFnc()
          }
        } else {
          this.messageService.error(msg)
        }
      })
  }

  getLocationList() {
    this.mapService.getMap('location').then(
      (data) => (this.workLocationOfOption = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  getEmployType() {
    this.mapService.getMap('employType').then(
      (data) => (this.employType = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  getEntryCompanyList() {
    this.mapService.getMap('company').then(
      (data) => (this.companyOfOption = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  translateObjToArr(obj) {
    const arr = []
    Object.keys(obj).forEach((key) => arr.push({ value: key, label: obj[key] }))
    return arr
  }

  onReviewContract(contract) {
    this.contractService
      .getReviewContractUrl({ contractId: contract['contractId'] })
      .subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          if (data) {
            this.window.open(data)
          }
        } else {
          this.messageService.error(msg)
        }
      })
  }

  // 查看offer
  onReviewOffer() {
    this.window.open(`${environment.urlPrefix}/api/contract/snapshot/offer/file?userId=${this.userId}`)
  }

  getPositonList() {
    this.mapService.getMap('position').then(
      (data) => (this.positionList = this.translateObjToArr(data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  getIdType() {
    this.mapService.getMap('idType').then(
      (data) => (this.idTypeList = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }

  getContractSnapshot() {
    if (this.router.url.indexOf('endorse') > -1) {
      this.showChange = false
      this.showEndorse = true
    }
    if (this.router.url.indexOf('change') > -1) {
      this.showChange = true
      this.showEndorse = false
    }

    this.isSnapShotLoading = true
    // 合同新签 调用另外接口
    if (this.router.url.indexOf('new') > -1 || this.router.url.indexOf('mgt') > -1 ) {
      this.contractService.getNewSnapshot({ userId: this.userId }).subscribe((res) => {
        this.isSnapShotLoading = false
        const { code, msg, data } = res
        if (code === 0) {
          if (data && data.snapshot) {
            // 有合同快照
            this.contractSnapshot = data.snapshot
            this.isShowStaff = true
            this.isShowCreate = true
            this.fileList = []
            if (data['paths'] && data['paths'].length > 0) {
              data['paths'].forEach((item, index) => {
                this.fileList.push({
                  uid: index,
                  name: this.convertFileName(item),
                  path: item,
                  status: 'done',
                  url: this.downLoadUrl + item
                })
              })
              this.fileList = [...this.fileList]
            }
          } else {
            this.isShowStaff = false
            this.isShowCreate = false
          }
        } else {
          this.messageService.error(msg)
        }
      })
    } else {
      this.isShowStaff = false
      this.contractService.getSnapshot({ userId: this.userId }).subscribe((res) => {
        this.isSnapShotLoading = false
        const { code, msg, data } = res
        if (code === 0) {
          if (data) {
            // 有合同快照
            this.contractSnapshot = data
            this.isShowCreate = true
            this.fileList = []
            if (data['paths'] && data['paths'].length > 0) {
              data['paths'].forEach((item, index) => {
                this.fileList.push({
                  uid: index,
                  name: this.convertFileName(item),
                  path: item,
                  status: 'done',
                  url: this.downLoadUrl + item
                })
              })
              this.fileList = [...this.fileList]
            }
          } else {
            // 无合同快照
            this.isShowCreate = false
          }
        } else {
          this.messageService.error(msg)
        }
      })
    }
  }

  // 根据业务逻辑，转化文件名称
  convertFileName(filename) {
    const nameArr = filename.split(this.contractSnapshot['contractNumber'])
    if (nameArr.length > 1) {
      return nameArr[1].slice(1, nameArr[1].length)
    }
  }

  onSign() {
    if (this.contractSnapshot['certType'] != this.idType) {
      // 证件类型
      this.messageService.error('合同证件类型与基本信息不符，请重新选择')
      return
    }
    if (this.contractSnapshot['certNo'] != this.idCard) {
      // 证件号
      this.messageService.error('合同证件号与基本信息不符，请重新生成快照')
      return
    }
    const target = this.positionList.find((item) => item['value'] == this.station)
    if (!!target && this.contractSnapshot['position'] != target['label']) {
      this.messageService.error('合同岗位与基本信息不符，请重新生成快照')
      return
    }
    if (this.contractSnapshot['address'] != this.apartment) {
      // 现居住地点
      this.messageService.error('合同现居住地点与基本信息不符，请重新生成快照')
      return
    }
    if (!this.contractSnapshot['probationStartDate'] && this.contractSnapshot['probationEndDate']) {
      this.messageService.error('试用期开始时间不能为空')
      return
    }
    if (this.contractSnapshot['probationStartDate'] && !this.contractSnapshot['probationEndDate']) {
      this.messageService.error('试用期结束时间不能为空')
      return
    }
    if (this.contractSnapshot['graduationTime'] && this.contractSnapshot['admissionTime']) {
      if (new Date(this.contractSnapshot['graduationTime']) <= new Date(this.contractSnapshot['admissionTime'])) {
        this.messageService.error('入学时间必须小于毕业时间')
        return
      }
    }
    this.signing = true
    this.onSubmit(false, () => this.signSnapshot())
  }

  signSnapshot() {
    if (this.router.url.indexOf('endorse') > -1 || this.router.url.indexOf('change') > -1) {
      console.log(1)
      this.contractService
        .signContractChange({ id: this.contractSnapshot['id'] })
        .subscribe((res) => {
          const { code, msg, data } = res
          this.signing = false
          if (code === 0) {
            this.messageService.success('签署成功')
            // todo 跳转
            // this.simpleReuseStrategy.
            // SimpleReuseStrategy.deleteRouteSnapshot(this.backurl);
            this.router.navigate([this.backurl], {}) // 跳转管理员
          } else {
            this.signing = false
            this.messageService.error(data)
          }
        })
    } else if (this.router.url.indexOf('new') > -1 || this.router.url.indexOf('mgt') > -1) {
      this.contractService
        .signContractNew({ id: this.contractSnapshot['id'] })
        .subscribe((res) => {
          const { code, msg, data } = res
          this.signing = false
          if (code === 0) {
            this.messageService.success('签署成功')
            // todo 跳转
            // this.simpleReuseStrategy.
            // SimpleReuseStrategy.deleteRouteSnapshot(this.backurl);
            this.router.navigate([this.backurl], {}) // 跳转管理员
          } else {
            this.signing = false
            this.messageService.error(msg)
          }
        })
    } else {
      console.log(2)
      this.contractService.signContract({ id: this.contractSnapshot['id'] }).subscribe((res) => {
        const { code, msg, data } = res
        this.signing = false
        if (code === 0) {
          this.messageService.success('签署成功')
          // todo 跳转
          // this.simpleReuseStrategy.
          // SimpleReuseStrategy.deleteRouteSnapshot(this.backurl);
          this.router.navigate([this.backurl], {}) // 跳转管理员
        } else {
          this.signing = false
          this.messageService.error(data)
        }
      })
    }
  }

  beforeUpload = (file: File) => {
    return new Observable((observer: Observer<boolean>) => {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.messageService.error('上传文件不能超过10兆。')
        observer.complete()
        return
      }
      observer.next(isLt10M)
      observer.complete()
    })
  }

  handleRemove = (file) => {
    return new Observable((observer: Observer<boolean>) => {
      this.contractService
        .deleteFile({ userId: this.userId, path: file['path'] })
        .subscribe((res) => {
          const { data, code, msg } = res
          if (code === 0) {
            const index = this.fileList.findIndex((item) => item['name'] == file['name'])
            this.fileList.splice(index, 1)
            this.fileList = [...this.fileList]
            observer.complete()
            return
          } else {
            this.messageService.error(msg)
            observer.complete()
          }
        })
    })
  }

  handleUploadChange({ file, fileList, event }) {
    const status = file.status
    if (status === 'done') {
      const { data, code, msg } = file.response
      if (code === 0) {
        const index = this.fileList.findIndex((item) => item['name'] == this.convertFileName(data))
        if (index > -1) {
          this.fileList.splice(index, 1)
        }
        this.fileList.push({
          uid: this.fileList.length,
          name: this.convertFileName(data),
          path: data,
          status: 'done',
          url: this.downLoadUrl + data
        })
        this.fileList = [...this.fileList]
        this.messageService.success(`${file.name} 上传成功`)
      } else {
        fileList.splice(-1)
        this.messageService.error(`${file.name} 上传失败` + msg)
      }
    } else if (status === 'error') {
      this.messageService.error(`${file.name} 上传时报错`)
    }
  }

  createSpanshot() {
    this.saving = true
    let signStatus = ''
    if (this.router.url.indexOf('new') > -1) {
      signStatus = '合同新签'
    }
    if (this.router.url.indexOf('extend') > -1) {
      signStatus = '合同续签'
    }
    if (this.router.url.indexOf('mgt') > -1) {
      signStatus = '合同变更'
    }
    if (this.router.url.indexOf('endorse') > -1) {
      signStatus = '合同转签'
    }
    if (this.router.url.indexOf('change') > -1) {
      signStatus = '合同变更'
    }
    this.contractService.createSpanshot({ userId: this.userId, signStatus }).subscribe((res) => {
      const { data, code, msg } = res
      this.saving = false
      if (code === 0) {
        this.messageService.success('生成合同成功')
        this.getContractSnapshot()
        // SimpleReuseStrategy.deleteRouteSnapshot(this.backurl);
      } else {
        this.messageService.error(msg)
      }
    })
  }

  convertDate(date) {
    try {
      const Y = date.getFullYear()
      const M = date.getMonth() + 1
      const D = date.getDate()
      return Y + (M < 10 ? '-0' : '-') + M + (D < 10 ? '-0' : '-') + D
    } catch (e) {
      return date
    }
  }

  add3Years(date) {
    try {
      date.getFullYear()
    } catch (e) {
      date = new Date(date)
    }
    const newDate = new Date(date.getTime())
    newDate.setFullYear(newDate.getFullYear() + 3)
    newDate.setDate(newDate.getDate() - 1)
    return newDate as any
  }

  // 合同属性变更
  contractAttributeChange(event) {
    if (event == '无固定期') {
      this.contractSnapshot['endDate'] = null
    } else {
      this.contractSnapshot['endDate'] = this.add3Years(this.contractSnapshot['startDate'])
    }
  }

  // 合同开始日期变更
  startDateChange(event) {
    if (
      this.contractSnapshot['signStatus'] == '合同新签' ||
      this.contractSnapshot['signStatus'] == '合同变更'
    ) {
      this.contractSnapshot['probationStartDate'] = event
    }
    if (
      this.contractSnapshot['contractAttribute'] == '固定期' &&
      (this.contractSnapshot['signStatus'] == '合同新签' ||
        this.contractSnapshot['signStatus'] == '合同续签')
    ) {
      this.contractSnapshot['endDate'] = this.add3Years(this.contractSnapshot['startDate'])
    }
    // 合同生效日期始终和合同开始日期相同
    this.contractSnapshot['effectDate'] = event
  }

  contractTypeChange(event) {
    if (event == '实习协议') {
      this.contractSnapshot['endDate'] = null
      this.contractSnapshot['probationStartDate'] = null
      this.contractSnapshot['probationEndDate'] = null
    }
  }

  disabledProbationEndDate = (endValue: Date): boolean => {
    let startDate = this.contractSnapshot['probationStartDate'] as any
    try {
      startDate.getFullYear()
    } catch (e) {
      startDate = new Date(startDate)
    }
    if (!endValue || !startDate) {
      return false
    }
    return endValue.getTime() <= startDate.getTime()
  }

  disabledEndDate = (endValue: Date): boolean => {
    let startDate = this.contractSnapshot['startDate'] as any
    try {
      startDate.getFullYear()
    } catch (e) {
      startDate = new Date(startDate)
    }

    if (!endValue || !startDate) {
      return false
    }
    return endValue.getTime() <= startDate.getTime()
  }
}
