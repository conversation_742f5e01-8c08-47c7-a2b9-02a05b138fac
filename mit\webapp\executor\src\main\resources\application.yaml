spring:
  profiles:
    active: test

---
#web端口、sessiontime
#数据源:测试
spring:
  profiles: test
  jackson:
    date-format: yyyy/MM/dd
    time-zone: GMT+8
  datasource:
    opdb:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: *************************************************************************************************************************
      username: jiangyuhong
      password: jiangyuhong
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
      filters: slf4j,stat
      connectionProperties:  druid.stat.mergeSql=true;druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3000
      useGlobalDataSourceStat: true
      # Druid 监控 Servlet 配置参数
      druidRegistrationUrl: /druid/*
      resetEnable: true
      loginUsername: jBV0NRU82w23Ejfu
      loginPassword: gFfidzzwx9d0bdoQ
      # Druid 监控过滤相关配置参数
      filtersUrlPatterns: /*
      exclusions: '*.js,*.gif,*.jpg,*.jpeg,*.png,*.css,*.ico,*.jsp,/druid/*'
      sessionStatMaxCount: 2000
      sessionStatEnable: true
      principalSessionName: session_user_key
      profileEnable: true
    luna:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: *************************************************************************************************************************
      username: jiangyuhong
      password: jiangyuhong
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
    dirac:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: **************************************************************************************************************************
      username: jiangyuhong
      password: jiangyuhong
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
    entry:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: ****************************************************************************************************************
      username: opdb
      password: oprulesall
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
  aop:
    proxy-target-class: true
    #统一编码处理
    http:
    encoding:
      force: true
      charset: UTF-8
      enabled: true
  redis:
    password: UbVr3MYdgMsAGL7nQZi
    cluster:
      nodes: 10.117.53.50:6381,10.117.53.50:6382,10.117.53.50:6383,10.117.53.39:6381,10.117.52:6381,10.117.53.39:6382,10.117.52:6382,10.117.53.39:6383,10.117.52:6383
      max-redirects: 3
      timeout: 6000
    pool:
      max-active: 50
      max-idle: 50
      max-wait: 0
      min-idle: 20
  ldap:
    urls: ldap://************:636
    base: dc=megvii-demo,dc=com
    username: <EMAIL>
    password: "%m7w2ljudCrzvKhL"
    ou: ou=Users,ou=Company
    domain: "@megvii-demo.com"
    base-environment: {"java.naming.ldap.factory.socket":"com.megvii.config.LTSSSLSocketFactory"}
mybatis:
  type-aliases-package: com.megvii.entity
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    lazy-loading-enabled: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#xxl-job相关配置
xxl:
  job:
    admin:
      addresses: http://*************:8088
    executor:
      appname: mitExecuterTest
      ip: *************
      port: 9999
      logpath: /data/log/job
      logretentiondays: -1
    accessToken:
logging:
  level: info
server:
  port: 8090


---
#web端口、sessiontime
#数据源:正式
spring:
  profiles: prod
  jackson:
    date-format: yyyy/MM/dd
    time-zone: GMT+8
  datasource:
    opdb:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: *******************************************************************************************************************************
      username: mitexecuter
      password: TOw01ZGz6bTZ
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
      filters: slf4j,stat
      connectionProperties:  druid.stat.mergeSql=true;druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3000
      useGlobalDataSourceStat: true
      # Druid 监控 Servlet 配置参数
      druidRegistrationUrl: /druid/*
      resetEnable: true
      loginUsername: jBV0NRU82w23Ejfu
      loginPassword: gFfidzzwx9d0bdoQ
      # Druid 监控过滤相关配置参数
      filtersUrlPatterns: /*
      exclusions: '*.js,*.gif,*.jpg,*.jpeg,*.png,*.css,*.ico,*.jsp,/druid/*'
      sessionStatMaxCount: 2000
      sessionStatEnable: true
      principalSessionName: session_user_key
      profileEnable: true
    luna:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: *******************************************************************************************************************************
      username: diracrw
      password: x2t5yLOSJLQN
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
    dirac:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: ********************************************************************************************************************************
      username: diracrw
      password: x2t5yLOSJLQN
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
    entry:
      driver-class-name: org.gjt.mm.mysql.Driver
      url: *************************************************************************************************************************************
      username: mit
      password: U+vVNeMHanX0
      type: com.alibaba.druid.pool.DruidDataSource
      initialSize: 20
      minIdle: 20
      maxActive: 100
      maxWait: 15000
      timeBetweenEvictionRunsMillis: 120000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: -1
      removeAbandoned: true
      removeAbandonedTimeoutMillis: 20000
      logAbandoned: true
      logDifferentThread: true
  aop:
    proxy-target-class: true
    #统一编码处理
    http:
    encoding:
      force: true
      charset: UTF-8
      enabled: true
#  redis:
#    password: <8KBKsz8V
#    #cluster:
#    #  nodes: **************:6379 ,**************:6379 ,**************:6379,**************:6380 ,**************:6380 ,**************:6380
#    #  max-redirects: 3
#    #  timeout: 6000
#    pool:
#      max-active: 50
#      max-idle: 50
#      max-wait: 0
#      min-idle: 20
#    database: 0
#    host: ***********
#    port: 6379
  redis:
    cluster:
      nodes: *************:6379 ,*************:6379 ,*************:6379,*************:6380 ,*************:6380 ,*************:6380
    jedis:
      pool:
        ## 连接池最大连接数（使用负值表示没有限制）
        max-active: 300
        ## 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 5000
        ## 连接池中的最大空闲连接
        max-idle: 100
        ## 连接池中的最小空闲连接
        min-idle: 20
    ## 连接超时时间（毫秒）
    timeout: 10000
    password: lswDr2EAnqba
  ldap:
    urls: ldaps://ldap.megvii-inc.com:636
    base: dc=megvii-inc,dc=com
    username: <EMAIL>
    password: "%m7w2ljudCrzvKhL"
    ou: "ou=Users,ou=Company"
    domain: "@megvii-inc.com"
    base-environment: {"java.naming.ldap.factory.socket":"com.megvii.config.LTSSSLSocketFactory"}
mybatis:
  type-aliases-package: com.megvii.entity
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    lazy-loading-enabled: true
  #xxl-job相关配置
xxl:
  job:
    admin:
      addresses: http://*************:8081/
    executor:
      appname: mitExecuter
      ip: **********
      port: 9999
      logpath: /data/log/job
      logretentiondays: -1
    accessToken:
logging:
  level: info
server:
  port: 8090