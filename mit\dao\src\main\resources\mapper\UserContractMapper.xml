<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserContractMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.contract.UserContract">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="work_number" property="workNumber"/>
        <result column="contract_id" property="contractId"/>
        <result column="spell" property="spell"/>
        <result column="contract_number" property="contractNumber"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_count" property="contractCount"/>
        <result column="contract_status" property="contractStatus"/>
        <result column="contract_attribute" property="contractAttribute"/>
        <result column="contract_doc_ids" property="contractDocIds"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="effect_date" property="effectDate"/>
        <result column="probation_start_date" property="probationStartDate"/>
        <result column="probation_end_date" property="probationEndDate"/>
        <result column="creator_user_id" property="creatorUserId"/>
        <result column="updator_user_id" property="updatorUserId"/>
        <result column="company" property="company"/>
        <result column="position" property="position"/>
        <result column="employ_base" property="employBase"/>
        <result column="contract_cell" property="contractCell"/>
        <result column="cert_type" property="certType"/>
        <result column="cert_no" property="certNo"/>
        <result column="address" property="address"/>
        <result column="memo" property="memo"/>
        <result column="update_time" property="updateTime"/>
        <result column="sign_status" property="signStatus"/>
        <result column="contract" property="contract"/>
        <result column="new_contract" property="newContract"/>
        <result column="con_begin_date" property="conBeginDate"/>
        <result column="con_end_date" property="conEndDate"/>
        <result column="new_con_begin_date" property="newConBeginDate"/>
        <result column="new_con_end_date" property="newConEndDate"/>
        <result column="work_city" property="workCity"/>
        <result column="new_work_city" property="newWorkCity"/>
        <result column="user_contract_file" property="userContractFile"/>
        <result column="dismission_id" property="dismissionId"/>
        <result column="contract_title" property="contractTitle"/>
        <result column="contract_subject" property="contractSubject"/>
        <result column="self_email" property="selfEmail"/>
    </resultMap>
    <resultMap id="ExportResultMap" type="com.megvii.entity.opdb.contract.ContractExportDto">
        <result column="user_id" property="userId"/>
        <result column="work_number" property="workNumber"/>
        <result column="spell" property="spell"/>
        <result column="contract_number" property="contractNumber"/>
        <result column="contract_status" property="contractStatus"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="expect_date" property="expectDate"/>
        <result column="company" property="company"/>
        <result column="sign_status" property="signStatus"/>
    </resultMap>

    <insert id="insertUserContract" parameterType="com.megvii.entity.opdb.contract.UserContract" >
        INSERT INTO `user_contract` (
	        `id`,
  	        `user_id` ,
  	        `work_number` ,
            `contract_id` ,
 	        `spell` ,
	        `contract_number` ,
  	        `contract_type` ,
  	        `contract_count` ,
            `contract_status` ,
  	        `contract_attribute` ,
            `contract_doc_ids` ,
  	        `start_date` ,
  	        `end_date` ,
  	        `effect_date` ,
  	        `probation_start_date` ,
  	        `probation_end_date` ,
  	        `creator_user_id` ,
  	        `updator_user_id` ,
  	        `company` ,
  	        `position` ,
  	        `employ_base` ,
  	        `contract_cell` ,
  	        `cert_type` ,
  	        `cert_no` ,
  	        `address` ,
  	        `memo` ,
  	        `update_time` ,
  	        `sign_status`,
  	        `contract`,
  	        `new_contract`,
  	        `con_begin_date`,
  	        `con_end_date`,
  	        `new_con_begin_date`,
  	        `new_con_end_date`,
  	        `work_city`,
  	        `new_work_city`,
  	        `user_contract_file`,
  	        `dismission_id`,
  	        `contract_title`,
  	        `contract_subject`,
  	        `self_email`
        )
        VALUES
	    (
		    0,
		    #{userId},
		    #{workNumber},
		    #{contractId},
	    	#{spell},
		    #{contractNumber},
		    #{contractType},
		    #{contractCount},
		    #{contractStatus},
		    #{contractAttribute},
		    #{contractDocIds},
		    #{startDate},
		    #{endDate},
		    #{effectDate},
		    #{probationStartDate},
		    #{probationEndDate},
		    #{creatorUserId},
		    #{updatorUserId},
		    #{company},
		    #{position},
		    #{employBase},
		    #{contractCell},
		    #{certType},
		    #{certNo},
		    #{address},
		    #{memo},
		    NOW(),
		    #{signStatus},
		    #{contract},
		    #{newContract},
		    #{conBeginDate},
		    #{conEndDate},
		    #{newConBeginDate},
		    #{newConEndDate},
		    #{workCity},
	    	#{newWorkCity},
	    	#{userContractFile},
	    	#{dismissionId},
	    	#{contractTitle},
	    	#{contractSubject},
	    	#{selfEmail}
	    );
    </insert>

    <update id="updateUserContract" parameterType="com.megvii.entity.opdb.contract.UserContract">
        update user_contract
        <set>
            update_time = NOW(),
            <if test="contractStatus != null ">
                contract_status = #{contractStatus},
            </if>
            <if test="updatorUserId != null ">
                updator_user_id = #{updatorUserId},
            </if>
            <if test="signStatus != null ">
                sign_status = #{signStatus},
            </if>
            <if test="userContractFile != null ">
                user_contract_file = #{userContractFile},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateUserCustomizeContract" parameterType="com.megvii.entity.opdb.contract.UserContract">
        update user_contract
        <set>
            update_time = NOW(),
            user_contract_file = #{userContractFile},
            contract_subject = #{contractSubject},
            company = #{company},
            start_date = #{startDate},
            end_date = #{endDate},
            user_contract_file = #{userContractFile}
        </set>
        where id = #{id}

    </update>

	<select id="selectUserContractByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract where user_id = #{userId} ORDER BY REPLACE(contract_number, work_number, '') + 0
    </select>
    <select id="getContracts" resultMap="BaseResultMap" >
        select id,user_id,sign_status,company,start_date,end_date,user_contract_file,contract_id,contract_status from user_contract
    </select>
    <select id="selectContractNumberByWorkNumber" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT contract_number FROM user_contract WHERE contract_number REGEXP concat('^',#{workNumber},'([0-9])*$') ORDER BY REPLACE(contract_number, #{workNumber}, '')+0 DESC limit 1
    </select>

    <select id="selectContractByContractNumber" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract where contract_number=#{contractNumber} and sign_status="合同解除"
    </select>

    <select id="selectUserNewestContract" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract where user_id = #{userId} and contract_title is null ORDER BY contract_number desc,start_date desc LIMIT 1
    </select>

    <select id="selectUserStandardNewestContract" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract where user_id = #{userId} ORDER BY contract_number desc,start_date desc LIMIT 1
    </select>

    <select id="selectContractByContractId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract where contract_id = #{contractId}
    </select>
	<select id="selectContractByDismissionId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract where dismission_id = #{dismissionId}
        and contract_status='已生效' order by id desc limit 1
    </select>
    <delete id="removeContractByContractId" parameterType="java.lang.String" >
        delete from user_contract where contract_id = #{contractId}
    </delete>
    <select id="selectContractById" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract where id = #{id}
    </select>
    <select id="getAllContractNumber" resultType="java.lang.String">
        SELECT distinct concat(work_number,date_format(effect_date,'%Y-%m-%d')) as num FROM user_contract
    </select>
    <select id="getContractNoSignStatus" resultMap="BaseResultMap">
       SELECT * FROM user_contract where sign_status is null
    </select>
    <select id="getContractsByFilter" resultMap="ExportResultMap">
        SELECT b.work_number,b.user_id,b.spell,DATE_FORMAT(b.expect_date, '%Y-%m-%d' ) as expect_date,a.sign_status,a.contract_number,a.company,DATE_FORMAT(a.start_date, '%Y-%m-%d' ) as start_date,DATE_FORMAT(a.end_date, '%Y-%m-%d' ) as end_date,a.contract_status FROM user_contract a left join IT_user b
        on a.user_id=b.user_id
        <where>
            <if test="filter.expectDate != null ">
                AND DATE_FORMAT(b.expect_date, '%Y-%m-%d' ) = DATE_FORMAT(#{filter.expectDate}, '%Y-%m-%d' )
            </if>
            <if test="filter.entryFlowStatus != null ">
                <if test="filter.entryFlowStatus == 0 ">
                    AND b.entry_flow != 'OA流程'
                </if>
                <if test="filter.entryFlowStatus == 1 ">
                    AND b.entry_flow = '信息收集'
                </if>
                <if test="filter.entryFlowStatus == 2 ">
                    AND b.entry_flow = 'OA流程'
                </if>
                <if test="filter.entryFlowStatus == 3 ">
                    AND b.entry_flow = '在职'
                </if>
            </if>
            <if test="filter.teamId != null ">
                AND b.team_id = #{filter.teamId}
            </if>
            <if test="filter.statusCode != null ">
                <if test="filter.statusCode == 0 ">
                    AND b.status not in ('已保留','已禁用')
                </if>
                <if test="filter.statusCode == 1 ">
                    AND b.status in ('已保留','已禁用')
                </if>
                <if test="filter.statusCode == 2 ">
                    AND b.status = '已禁用'
                </if>
            </if>
            <if test="filter.expectDateFrom != null ">
                AND DATE_FORMAT( b.expect_date, '%Y-%m-%d' ) &gt;=  DATE_FORMAT( #{filter.expectDateFrom} , '%Y-%m-%d' )
            </if>
            <if test="filter.expectDateTo != null ">
                AND DATE_FORMAT( b.expect_date, '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{filter.expectDateTo}, '%Y-%m-%d' )
            </if>
            <if test="filter.workNumber != null ">
                AND b.work_number = #{filter.workNumber}
            </if>
            <if test="filter.userId != null ">
                AND b.user_id = #{filter.userId}
            </if>
            <if test="filter.spell != null ">
                AND b.spell = #{filter.spell}
            </if>
            <if test="filter.workBase != null ">
                AND b.work_base = #{filter.workBase}
            </if>
            <if test="filter.employType != null ">
                AND b.employ_type = #{filter.employType}
            </if>
            <if test="filter.entryFlow != null ">
                AND b.entry_flow = #{filter.entryFlow}
            </if>
            <if test="filter.contractStatus != null ">
                AND b.contract_status = #{filter.contractStatus}
            </if>
            <if test="filter.contractStatusCode != null ">
                <if test="filter.contractStatusCode == 0 ">
                    AND b.contract_status not in ('已完成', '已导入') AND b.contract_count = 0
                </if>
            </if>
            <if test="filter.employTypeCode != null ">
                <if test="filter.employTypeCode == 0 ">
                    AND b.employ_type != '劳务派遣'
                </if>
            </if>
            and b.work_number is not null
        </where>
        ORDER BY expect_date
    </select>
</mapper>
