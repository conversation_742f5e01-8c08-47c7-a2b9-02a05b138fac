package com.megvii.mapper;

import com.megvii.entity.opdb.contract.UserContractSnapshot;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2020/4/15 15:25
 */
@Repository
public interface UserContractSnapshotMapper {

	int insertContractSnapshot(UserContractSnapshot snapshot);

	int deleteContractSnapshot(int id);

	int updateContractSnapshot(UserContractSnapshot snapshot);

	UserContractSnapshot selectContractSnapshotByUserId(String userId);

	UserContractSnapshot selectContractSnapshotById(int id);


}
