package com.megvii;

import com.megvii.entity.opdb.User;
import com.megvii.service.DimissionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2020/12/22 10:21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestDismission {

	@Autowired
	private DimissionService dimissionService;

	@Test
	public void testExecutor() {
		User user = new User();
		user.setStatus("");
		user.setUserId("xujiayan");
		user.setMentor("weiyichen");

		dimissionService.dismissAccount(user);



	}


}
