package com.megvii.service;

import com.megvii.client.SmsRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.User;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/11/19 15:38
 */
@Service
public class SMSService {

    @Autowired
    private SmsRestClient smsRestClient;

    /**
     * 发送信息收集短信
     * @param user
     * @param limitDate
     */
//    @Async
    public void sendAliyunEntrySms(User user, LocalDateTime limitDate) {
        String date = DateTimeUtil.date2String4(limitDate);

        String content = "【旷视科技】欢迎加入旷视大家庭，为了确保您顺利的完成入职，入职信息收集邮件已发送至您的邮箱，请于 " + date + "（入职前一个工作日）前完成填写。入职前To Do List:" +
                "\n1.工资卡办理" +
                "\n2.个人信息填写补充" +
                "\n网址：https://onboard-epif.megvii-inc.com\n账号：" + user.getUserId() + "\n密码：" + user.getOriPassword() +
//                "\n3．准备入职当天所需材料" +
                "\n详细信息请查看邮件~如有任何问题，请与您的HR联系。";

        smsRestClient.sendSMS(user.getCell(), content);
    }

    /**
     * 入职当天9点13 发送内部账号密码短信
     * @param user
     */
    public void sendEntryPwdSms(User user) {

        String content = "【旷视科技】您的内部账户用户名为：" + user.getUserId() + "，初始密码为：" + user.getOriPassword() +
                "，请妥善保管密码，并尽快修改。修改方式及更多操作请查看电脑桌面上的《旷厂新人手册》-办公指引篇。[Megvii-IT]";

        smsRestClient.sendSMS(user.getCell(), content);

    }


    /**
     * 发送欢迎短信
     * @param user
     */
//    @Async
    public String sendWelcomeSms(User user) {
        String workBase = user.getWorkBase();
        String content;

        if (workBase.equals("大恒")) {
            content = "【旷视科技】新同事你好：欢迎加入旷视科技，请按照offer中的要求准备好入职材料，明天上午8点50分到苏州街3号大恒科技大厦南座16层前台，我们会在那里等候并为您办理入职手续。";
        } else if (workBase.equals("海龙")) {
            content = "【旷视科技】新同事你好：欢迎加入旷视科技，请按照offer中的要求准备好入职材料，明天上午8点50分到海龙大厦H座17层前台，我们会在那里等候并为您办理入职手续。";
        } else if (workBase.equals("金隅")) {
            content = "【旷视科技】新同事你好：欢迎加入旷视科技，请按照offer中的要求准备好入职材料，明天上午8点50分到金隅智造S1-1层前台，我们会在那里等候并为您办理入职手续。地址：北京市海淀区建材城中路27号-金隅智造S1-1层。";
        } else {
            return user.getUserId() + "不发送欢迎信息。";
        }

        smsRestClient.sendSMS(user.getCell(), content);
        return user.getUserId() + "发送成功";
    }

    /**
     * 未完成信息收集短信提醒
     * @param user
     */
    public void sendUserInfoRemindSms(User user, String pwd) {
        String content = "【旷视科技】亲爱的" + user.getSpell() + "，欢迎加入旷视大家庭！在入职前，我们需要您在旷视入职系统里补全您的个人信息：\n网址：https://onboard-epif.megvii-inc.com\n账号："
                + user.getUserId() + "\n密码：" + pwd + "\n您也可以通过查收邮件快速访问。如未填写将影响入职办理，请尽快填写。";

        smsRestClient.sendSMS(user.getCell(), content);
    }



}
