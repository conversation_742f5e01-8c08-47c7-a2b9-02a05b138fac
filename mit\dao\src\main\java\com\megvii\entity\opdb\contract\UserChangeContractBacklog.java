package com.megvii.entity.opdb.contract;

import com.megvii.entity.opdb.User;
import lombok.Data;
import org.apache.http.annotation.Contract;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/4/10 17:08
 */
@Data
public class UserChangeContractBacklog {

	private int id;
	private String userId;
	private String workNumber;
	/**
	 * 变更类型:1:合同主题变更，2:base地变更
	 */
	private String xtype;
	/**
	 *原签署单位
	 */
	private String contract;
	/**
	 *新签署单位
	 */
	private String newContract;
	/**
	 *原工作地
	 */
	private String workCity;
	/**
	 *新工作地
	 */
	private String newWorkCity;
	/**
	 *原签署开始日期
	 */
	private String conBeginDate;
	/**
	 *原签署截止日期
	 */
	private String conEndDate;
	/**
	 *新签署开始日期
	 */
	private String newConBeginDate;
	/**
	 *新签署截止日期
	 */
	private String newConEndDate;
	/**
	 *生效日期
	 */
	private String effectDate;
	/**
	 * 已拉取 从DHR拉取
	 * 已签署 已签署完成
	 * 已完成 DHR已结束操作
	 */
	private String status;
	private LocalDateTime createTime;
	private String updateTime;

	/**
	 * 签约次数 从IT_user拿，对于转签/变更合同直接用，新签/续签签约次数加1再回显
	 */
	private int contractCount;

	private User user;


}
