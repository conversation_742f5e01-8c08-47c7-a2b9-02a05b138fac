package com.megvii.cas;
import com.megvii.cas.prod.CasCustomProperties;
import io.buji.pac4j.profile.ShiroProfileManager;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.pac4j.core.context.Pac4jConstants;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.engine.DefaultCallbackLogic;
import org.pac4j.core.exception.HttpAction;

/**
 * @创建人 cl
 * @创建日期 2019/10/16
 * @描述  回调之后进行重定向处理
 */
public class MyCallbackLogic<R, C extends WebContext> extends DefaultCallbackLogic <R, C> {

    private static Pattern URL_REGEX = Pattern.compile("(?<=next=).*");

    private String directAddress ;

    public MyCallbackLogic(CasCustomProperties casCustomProperties) {
        directAddress = casCustomProperties.getSecondaryDirectory();
        this.setProfileManagerFactory(ShiroProfileManager::new);
    }

    @Override
    protected HttpAction redirectToOriginallyRequestedUrl(C context, String defaultUrl) {

        //处理重定向地址
        String url = directAddress;
        //获取shiro的主题
        Subject subject = SecurityUtils.getSubject();
        boolean loginFlag = subject.isAuthenticated();
        final String requestedUrl = (String) context.getSessionStore().get(context, Pac4jConstants.REQUESTED_URL);
        if(loginFlag && StringUtils.isNotBlank(requestedUrl) && requestedUrl.contains("next=")){
            Matcher matcher = URL_REGEX.matcher(requestedUrl);
            while(matcher.find()){
                url =  matcher.group();
            }
        }
        return HttpAction.redirect(context,url);
    }
}
