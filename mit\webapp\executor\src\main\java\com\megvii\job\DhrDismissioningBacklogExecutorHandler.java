package com.megvii.job;

import com.megvii.common.OperateResult;
import com.megvii.common.ParameterUtil;
import com.megvii.operator.DhrDismissioningBacklogOperatorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/3/23 18:04
 * @desc 拉取dhr离职流程数据
 */
@JobHandler(value="DhrDismissioningBacklogExecutorHandler")
@Component
public class DhrDismissioningBacklogExecutorHandler extends IJobHandler {

    @Autowired
    private DhrDismissioningBacklogOperatorService dhrDismissioningBacklogOperatorService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        OperateResult result = dhrDismissioningBacklogOperatorService.operateMain(ParameterUtil.getParaMap(param));
        if (result.getStatus() == 2) {
            return new ReturnT(500, "<span style=\"color:#e83c4c;\"> 主操作执行失败</span> " + result.getMsg());
        } else if (result.getStatus() == 0) {
            return new ReturnT(200, "操作完成，但存在失败操作--->" + result.getMsg());
        } else {
            return new ReturnT(200, result.getMsg());
        }
    }
}
