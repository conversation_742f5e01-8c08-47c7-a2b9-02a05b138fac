package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/13 16:40
 */
@Service
public class DhrContractBacklogOperatorService extends BaseOperatorService {

	@Autowired
	private DhrContractBacklogOperatorService dhrContractBacklogOperatorService;

	@Autowired
	private DhrService dhrService;

	@Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return dhrContractBacklogOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = dhrService.getContractBacklogFromDhr();
        return new OperateResult(1,msg);
    }



}
