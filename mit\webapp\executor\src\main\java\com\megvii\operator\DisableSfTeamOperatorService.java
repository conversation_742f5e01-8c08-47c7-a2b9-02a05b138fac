package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.SyncSfTeamService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/10/15 11:53
 */
@Service
public class DisableSfTeamOperatorService extends BaseOperatorService {

    @Autowired
    private DisableSfTeamOperatorService disableSfTeamOperatorService;

    @Autowired
    private SyncSfTeamService syncSfTeamService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return disableSfTeamOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
//        String msg = syncSfTeamService.syncDisableSfTeam();
//        return new OperateResult(1,msg);
        return new OperateResult(1,"");
    }





}
