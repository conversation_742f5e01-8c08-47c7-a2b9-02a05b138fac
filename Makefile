HOST_DIR := $(PWD)
MVN_CACHE := $(PWD)/.mvn-cache

ifeq (${ATP_CI}, 1)
	HOST_DIR = ${HOST_JOB_DATADIR}/code/
	MVN_CACHE = ${HOST_APP_DATADIR}/mvn-cache

endif

DOCKER_ENV := docker run --rm --workdir=/opt/workspace -v ${MVN_CACHE}:/root/.m2 --net=host -v ${HOST_DIR}:/opt/workspace mcd.io/mcd/java-env:1.0


build-jar:
	cd mit;mvn clean package -Dskip.test=true

dockerfile-prepare:
	echo "build jar"
	$(DOCKER_ENV) make build-jar

build-dep-image:
	docker build -f Dockerfile.dep -t mcd.io/mcd/java-env:1.0 .
	docker push mcd.io/mcd/java-env:1.0

build-image-for-test: dockerfile-prepare
	docker build -f Dockerfile.dep -t mcd.io/mcd/mit-webapi:latest .


local-backend-test:
	docker run -it -p 26663:80 mcd.io/mcd/mit-webapi:latest java -jar app.jar

