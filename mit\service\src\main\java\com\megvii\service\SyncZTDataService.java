package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DataRestClient;
import com.megvii.client.DingDingRestClient;
import com.megvii.entity.opdb.MMISMegviiConfig;
import com.megvii.entity.opdb.Team;
import java.util.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/10/26 11:49
 */
@PropertySource(value = "classpath:mail.properties", encoding = "UTF-8")
@Service
public class SyncZTDataService {

	private Logger logger = LoggerFactory.getLogger(SyncZTDataService.class);


	//cluster在钉钉显示的规则不同 使用时从map中获取是否创建钉钉部门
	private static Map<String, Boolean> clusterMap = new HashMap<>();

	static {
		clusterMap.put("CL01", false);
		clusterMap.put("CL02", false);
		clusterMap.put("CL03", false);
		clusterMap.put("CL05", false);
		clusterMap.put("CL06", false);
		clusterMap.put("CL07", true);
		clusterMap.put("CL08", true);
		clusterMap.put("CL09", true);
		clusterMap.put("CL10", true);
		clusterMap.put("CL11", true);
		clusterMap.put("CL12", true);
	}

	private JSONArray clusterArray;
	private JSONArray groupArray;
	private JSONArray buArray;
	private JSONArray teamArray;
	private JSONArray subTeamArray;
	private JSONArray secitonArray;
	private JSONArray subSectionArray;

	@Value("${SYNC_SF_TEAM_MAIL}")
	private String mailAddress;

	@Autowired
	private DataRestClient dataRestClient;

	@Autowired
	private TeamService teamService;

	@Autowired
	private MMISMegviiConfigService mmisMegviiConfigService;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private DingdingService dingdingService;

	@Autowired
	private LdapService ldapService;

	@Autowired
	private DingDingRestClient dingDingRestClient;

	@Autowired
	private MailSendService mailSendService;

	@Autowired
	private SyncTeamService syncTeamService;


	/**
	 * 同步历史组织架构的shortname 和 parentcode
	 * 使用1次  刷数据
	 */
//    public void syncTemp() {
//        JSONArray array = dataRestClient.getAllTeam();
//        for (Object o : array) {
//            JSONObject data = (JSONObject) o;
//
//            if (data.getString("name").equals("旷视集团")) {
//                continue;
//            }
//
//            if (data.getString("name").indexOf("-") != -1) {
//                continue;
//            }
//
//            if (!data.getString("status").equals("A")) {
//                continue;
//            }
//            if (data.getString("deptLevel").equals("006") || data.getString("deptLevel").equals("007")) {
//                continue;
//            }
//
//            Team team = teamService.getTeamByCode(data.getString("externalCode"));
//            if (team.getShortName() != null && team.getParentCode() != null) {
//                continue;
//            }
//
//            if (data.getString("deptLevel").equals("001")) {
//                team.setShortName(data.getString("name"));
//                teamService.updateTeam(team);
//            } else {
//                team.setShortName(data.getString("name"));
//                team.setParentCode(data.getString("parent"));
//                teamService.updateTeam(team);
//            }
//        }
//    }


	/**
	 * 禁用部门主方法
	 *
	 * @return
	 */
	public String disableTeam() {
		StringBuilder message = new StringBuilder();
		//获取激活的MIT部门
		List<Team> teams = teamService.getActiviteTeam();
		List<String> teamsCode = new ArrayList<>();
		for (Team team : teams) {
			if (team.getSfCode() == null) {
				logger.error(team.getName() + ":code is null");
				continue;
			}
			teamsCode.add(team.getSfCode());
		}
		//获取激活的部门code
		List<String> codes = getActivityTeamCode();

		//从MIT激活部门中移除激活的DHR部门
		teamsCode.removeAll(codes);

		if (teamsCode.size() == 0) {
			message.append("没有要禁用的MIT部门");
			return message.toString();
		}

		for (String code : teamsCode) {
			message.append(syncTeamService.disableTeam(code) + "<br/>");
		}

		mailSendService.sendMail("MIT禁用部门提醒", message.toString(), mailAddress);

		return message.toString();

	}


	/**
	 * 同步中台部门 除了禁用的其他操作
	 *
	 * @return
	 */
	public String addZTTeam() {
		teamInit();
		StringBuilder message = new StringBuilder();

		String clusterMsg = syncTeam(clusterArray);
		if (clusterMsg != null && !clusterMsg.equals("")) {
			message.append(clusterMsg + "<br/>");
		}

		String groupMsg = syncTeam(groupArray);
		if (groupMsg != null && !groupMsg.equals("")) {
			message.append(groupMsg + "<br/>");
		}

		String buMsg = syncTeam(buArray);
		if (buMsg != null && !buMsg.equals("")) {
			message.append(buMsg + "<br/>");
		}

		String teamMsg = syncTeam(teamArray);
		if (teamMsg != null && !teamMsg.equals("")) {
			message.append(teamMsg + "<br/>");
		}

		String subTeamMsg = syncTeam(subTeamArray);
		if (subTeamMsg != null && !subTeamMsg.equals("")) {
			message.append(subTeamMsg + "<br/>");
		}

		String sectionMsg = syncTeam(secitonArray);
		if (sectionMsg != null && !sectionMsg.equals("")) {
			message.append(sectionMsg + "<br/>");
		}

		String subSectionMsg = syncTeam(subSectionArray);
		if (subSectionMsg != null && !subSectionMsg.equals("")) {
			message.append(subSectionMsg + "<br/>");
		}

		if (!message.toString().equals("")) {
			mailSendService.sendMail("MIT新增部门", message.toString(), mailAddress);
		} else {
			message.append("没有部门变化");
		}

		return message.toString();
	}


	/**
	 * 同步部门通用方法 传入层级array 循环处理
	 *
	 * @param array
	 * @return
	 */
	private String syncTeam(JSONArray array) {
		StringBuilder msg = new StringBuilder();
		for (int i = 0; i < array.size(); i++) {
			JSONObject data = (JSONObject) array.get(i);
			try {
				//校验拉取的部门信息
				checkTeamInfo(data);
				//比较部门信息
				String result = compareTeamInfo(data);
				if (result == null) {
					continue;
				}
				msg.append(result + "<br/>");
			} catch (Exception e) {
				msg.append(e.getMessage() + "<br/>");
				continue;
			}
		}
		return msg.toString();
	}

//    /**
//     * 同步group主方法
//     * @return
//     */
//    private String syncGroup() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < groupArray.size(); i ++) {
//            JSONObject data = (JSONObject) groupArray.get(i);
//            //处理激活的SF_GROUP
//            if (data.get("status").equals("A")) {
////                String result = checkAddGroup(data);
//                String result = syncTemp(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步BU主方法
//     * @return
//     */
//    private String syncBu() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < buArray.size(); i ++) {
//            JSONObject data = (JSONObject) buArray.get(i);
//            //处理激活的SF_BU
//            if (data.get("status").equals("A")) {
////                String result = checkAddTeam(data);
//                String result = syncTemp(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步TEAM主方法
//     * @return
//     */
//    private String syncTeam() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < teamArray.size(); i ++) {
//            JSONObject data = (JSONObject) teamArray.get(i);
//            //处理激活的SF_team
//            if (data.get("status").equals("A")) {
////                String result = checkAddTeam(data);
//                String result = syncTemp(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步subTeam主方法
//     * @return
//     */
//    private String syncSubTeam() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < subTeamArray.size(); i ++) {
//            JSONObject data = (JSONObject) subTeamArray.get(i);
//            //处理激活的SF_subTeam
//            if (data.get("status").equals("A")) {
////                String result = checkAddTeam(data);
//                String result = syncTemp(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }
//
//    /**
//     * 同步section主方法
//     * @return
//     */
//    private String syncSection() {
//        StringBuilder msg = new StringBuilder();
//        for (int i = 0; i < secitonArray.size(); i ++) {
//            JSONObject data = (JSONObject) secitonArray.get(i);
//            //处理激活的SF_Section
//            if (data.get("status").equals("A")) {
////                String result = checkAddTeam(data);
//                String result = syncTemp(data);
//                if (result == null) {
//                    continue;
//                }
//                msg.append(result + "<br/>");
//            }
//        }
//        return msg.toString();
//    }

//    /**
//     * 处理激活状态的group
//     * 1.不存在->添加->返回处理结果
//     * 2.存在，钉钉ID不存在->false
//     * 3.其他->名称是否修改->返回处理结果
//     * @param data
//     * @return
//     */
//    private String checkAddGroup(JSONObject data) {
//        StringBuilder msg = new StringBuilder();
//
//        String groupName = data.get("name")!=null?data.get("name").toString():null;
//        String code = data.get("externalCode")!=null?data.get("externalCode").toString():null;
//        String hrg = data.getString("hrg")!=null?data.getString("hrg"):"";
//        String teamLeader = data.getString("headOfUnit")!=null?data.getString("headOfUnit"):"";
//
//        if (groupName == null) {
//            msg.append("group:" + code + "name为null");
//            logger.error(msg.toString());
//            return msg.toString();
//        }
//
//        //查询group是否存在
//        Team group = teamService.getTeamByCode(code);
//        if (group == null) {
//            group = new Team();
//            group.setHrg(hrg);
//            group.setGroupLeader(teamLeader);
//            group.setTeamLeader(teamLeader);
//            group.setName(groupName);
//            group.setMailGroup("");
//            group.setSfCode(code);
//            group.setShortName(groupName);
//            //创建MIT部门
//            if (teamService.addTeam(group)) {
//                //创建钉钉部门
//                if(dingdingService.createDingDingDept(group, "1")) {
//                    msg.append("MIT新增DHR部门" + groupName + "[成功]");
//                    return msg.toString();
//                } else {
//                    msg.append("MIT新增DHR部门" + groupName + "<span style=\"color:#EE4000;\">[失败]</span>");
//                    return msg.toString();
//                }
//            } else {
//                msg.append("添加MIT部门" + groupName + "错误");
//                logger.error(msg.toString());
//                return msg.toString();
//            }
//        }  else if (group.getDingdingId() == null) {
//            dingdingService.createDingDingDept(group, "1");
//            msg.append(group.getName() + "更新钉钉ID");
//            return msg.toString();
//        } else {
//            //没有群聊ID
//            if (group.getDingdingId() != null && group.getChatId() == null) {
//                try {
//                    dingdingService.saveChatId(group);
//                } catch (Exception e) {
//                    msg.append(group.getName() + "保存添加群聊错误");
//                    logger.error(msg.toString());
//                    return msg.toString();
//                }
//            }
//            //不需要修改
//            if (group.getName().equals(groupName)) {
//                return null;
//            }
//            //group名称变化 邮件组不是all
//            if (!group.getName().equals(groupName) && !group.getMailGroup().equals("all")) {
//                //修改ldap
//                ldapService.modifyGroup(group.getMailGroup(), "displayname", groupName);
//                ldapService.modifyGroup(group.getMailGroup(), "description", groupName);
//            }
//            msg.append("MIT部门修改名称:" + group.getName() + "->" + groupName);
//            logger.info(msg.toString());
//            group.setName(groupName);
//            group.setShortName(groupName);
//
//            try {
//                if (dingDingRestClient.updateDept(group, null)) {
//                    msg.append("----修改钉钉部门名称[成功]");
//                } else {
//                    msg.append("----修改钉钉部门名称" + "<span style=\"color:#EE4000;\">[失败]</span>");
//                }
//            } catch (Exception e) {
//                msg.append(groupName + "修改钉钉部门名称错误");
//                logger.error(msg.toString());
//            }
//            teamService.updateTeam(group);
//            return msg.toString();
//        }
//    }
//
//    /**
//     * 处理激活状态的BU以下部门
//     * @param data
//     * @return
//     */
//    private String checkAddTeam(JSONObject data) {
//        String name = data.get("name")!=null?data.get("name").toString():null;
//        String code = data.get("externalCode")!=null?data.get("externalCode").toString():null;
//        String belongCode = data.get("parent")!=null?data.get("parent").toString():null;
//        String hrg = data.getString("hrg")!=null?data.getString("hrg"):"";
//        String teamLeader = data.getString("headOfUnit")!=null?data.getString("headOfUnit"):"";
//
//        if (name == null) {
//            logger.error("team:" + code + "name is null");
//            return "team:" + code + "name is null";
//        }
//        if (belongCode == null) {
//            logger.error("team:" + code + "上级部门sfCode is null");
//            return "team:" + code + "上级部门sfCode is null";
//        }
//        return checkZtTeam(name, code, belongCode, hrg, teamLeader);
//    }
//
//
//    /**
//     * 处理group以下的部门方法
//     * @param name
//     * @param sfCode
//     * @param belongSfCode
//     * @return
//     */
//    private String checkZtTeam(String name, String sfCode, String belongSfCode, String hrg, String teamLeader) {
//        StringBuilder msg = new StringBuilder();
//        //检查上级部门
//        Team belong = teamService.getTeamByCode(belongSfCode);
//        if (belong == null) {
//            msg.append(name + sfCode + "上级部门不存在");
//            logger.error(msg.toString());
//            return msg.toString();
//        }
//        String fullName;
//        //上级是cluster 不拼接名称
//        if (clusterCodeList.contains(belongSfCode)) {
//            fullName = name;
//        } else {
//            fullName = belong.getName() + "-" + name;
//        }
//
//        Team team = teamService.getTeamByCode(sfCode);
//        if (team == null) {
//            team = new Team();
//            team.setName(fullName);
//            team.setMailGroup("");
//            team.setSfCode(sfCode);
//            team.setHrg(hrg);
//            team.setTeamLeader(teamLeader);
//            team.setShortName(name);
//            team.setParentCode(belongSfCode);
//            //创建MIT部门
//            if (teamService.addTeam(team)) {
//                //创建钉钉部门
//                if(dingdingService.createDingDingDept(team, belong.getDingdingId())) {
//                    msg.append("MIT新增DHR部门" + name + "[成功]");
//                    return msg.toString();
//                } else {
//                    msg.append("MIT新增DHR部门" + name + "<span style=\"color:#EE4000;\">[失败]</span>");
//                    return msg.toString();
//                }
//            } else {
//                msg.append("添加MIT部门" + fullName + "错误");
//                logger.error(msg.toString());
//                return msg.toString();
//            }
//        } else if (team.getDingdingId() == null) {
//            dingdingService.createDingDingDept(team, belong.getDingdingId());
//            msg.append(team.getName() + "更新钉钉ID");
//            return msg.toString();
//        } else {
//            //没有群聊ID
//            if (team.getDingdingId() != null && team.getChatId() == null) {
//                try {
//                    dingdingService.saveChatId(team);
//                } catch (Exception e) {
//                    msg.append(team.getName() + "保存添加群聊错误");
//                    logger.error(msg.toString());
//                    return msg.toString();
//                }
//            }
//
//            //不需要修改
//            if (team.getName().equals(fullName) && team.getParentCode().equals(belongSfCode)) {
//                return null;
//            }
//
//            //换上级
//            if (team.getParentCode() != belongSfCode) {
//                team.setHrg(hrg);
//                team.setGroupLeader(belong.getGroupLeader()!=null?belong.getGroupLeader():"");
//                msg.append(dingdingService.updateDingdingParent(team.getSfCode(), team.getDingdingId(), belong.getDingdingId(), name) + "<br/>");
//            }
//
//            //名称变化 邮件组不是all
//            if (!team.getName().equals(fullName) && !team.getMailGroup().equals("all")) {
//                //修改ldap
//                ldapService.modifyGroup(team.getMailGroup(), "displayname", name);
//                ldapService.modifyGroup(team.getMailGroup(), "description", fullName);
//            }
//            msg.append("MIT部门修改名称:" + team.getName() + "->" + fullName);
//            team.setName(fullName);
//            team.setShortName(name);
//            team.setParentCode(belongSfCode);
//
//            try {
//                if (dingDingRestClient.updateDept(team, name)) {
//                    msg.append("----修改钉钉部门名称[成功]");
//                } else {
//                    msg.append("----修改钉钉部门名称" + "<span style=\"color:#EE4000;\">[失败]</span>");
//                }
//            } catch (Exception e) {
//                msg.append(team + "修改钉钉部门名称:" + name + "错误");
//                logger.error(msg.toString());
//            }
//            teamService.updateTeam(team);
//
//            return msg.toString();
//        }
//    }


	/**
	 * 从中台获取激活的部门code list
	 *
	 * @return
	 */
	private List<String> getActivityTeamCode() {
		JSONArray array = dataRestClient.getAllTeam();
		List<String> codes = new ArrayList<>();

		for (Object o : array) {
			JSONObject team = (JSONObject) o;
			//只获取激活部门
			if (!team.getString("status").equals("A")) {
				continue;
			}

			codes.add(team.getString("externalCode"));
		}

		return codes;
	}


	/**
	 * 拆分team array 分层级
	 */
	private void teamInit() {
		JSONArray array = dataRestClient.getAllTeam();

		clusterArray = new JSONArray();
		groupArray = new JSONArray();
		buArray = new JSONArray();
		teamArray = new JSONArray();
		subTeamArray = new JSONArray();
		secitonArray = new JSONArray();
		subSectionArray = new JSONArray();

		for (Object o : array) {
			JSONObject team = (JSONObject) o;

			if (team.getString("name").equals("旷视集团")) {
				continue;
			}

			if (team.getString("name").indexOf("-") != -1) {
				continue;
			}

			if (!team.getString("status").equals("A")) {
				continue;
			}

			String level = team.getString("deptLevel");
			if (level == null) {
				logger.error("中台组织架构转化错误:" + team.toJSONString());
			}
			if (level.equals("001")) {
				groupArray.add(team);
				continue;
			} else if (level.equals("002")) {
				buArray.add(team);
				continue;
			} else if (level.equals("003")) {
				teamArray.add(team);
				continue;
			} else if (level.equals("004")) {
				subTeamArray.add(team);
				continue;
			} else if (level.equals("005")) {
				secitonArray.add(team);
				continue;
			} else if (level.equals("006")) {
				clusterArray.add(team);
				continue;
			} else if (level.equals("008")) {
				subSectionArray.add(team);
				continue;
			} else if (level.equals("007")) {
				continue;
			} else {
				logger.error("中台组织架构转化错误:" + team.toJSONString());
				continue;
			}
		}

	}


	/**
	 * 校验拉取的部门信息
	 *
	 * @param data
	 * @return
	 * @throws Exception
	 */
	private boolean checkTeamInfo(JSONObject data) throws Exception {
		String name = data.get("name") != null ? data.get("name").toString() : null;
		String code = data.get("externalCode") != null ? data.get("externalCode").toString() : null;
		String belongCode = data.get("parent") != null ? data.get("parent").toString() : null;

		if (code == null) {
			throw new Exception("同步部门拉取信息校验<span style=\"color:#EE4000;\">[异常]</span>:" + data.toJSONString() + "****code is null");
		}
		if (name == null) {
			throw new Exception("同步部门拉取信息校验<span style=\"color:#EE4000;\">[异常]</span>:team:" + code + "****name is null");
		}
		if (belongCode == null) {
			throw new Exception("同步部门拉取信息校验<span style=\"color:#EE4000;\">[异常]</span>:team:" + code + "****上级部门sfCode is null");
		}

		return true;
	}

	/**
	 * 比较部门信息决定相应的操作
	 * 1.新增部门
	 * 2.更换上级
	 * 3.本部门更换名称
	 * 4.上级部门变更名称，更换hrg 负责人
	 *
	 * @param data
	 * @return
	 */
	private String compareTeamInfo(JSONObject data) {
		StringBuilder msg = new StringBuilder();

		//检查上级部门
		Team belong = teamService.getTeamByCode(data.getString("parent"));
		if (belong == null) {
			msg.append(data.getString("externalCode") + "****<span style=\"color:#EE4000;\">[异常]</span>上级部门不存在");
			logger.error(msg.toString());
			return msg.toString();
		}

		//fullname逻辑 不带cluster
		String fullName;
		String shortName = data.getString("name");
		//支持行政拆分 cluster的下级部门认定为group
		String groupLeader;
		//上级是 旷视集团 or cluster 不拼接名称
		if (clusterMap.get(belong.getSfCode()) != null || "G01".equals(belong.getSfCode())) {
			fullName = shortName;
			groupLeader = data.getString("headOfUnit")!=null?data.getString("headOfUnit"):"";
		} else {
			fullName = belong.getName() + "-" + shortName;
			groupLeader = belong.getGroupLeader()!=null?belong.getGroupLeader():"";
		}

		//根据code查询旧部门
		Team team = teamService.getTeamByCode(data.getString("externalCode"));
		// 获取部门层级
		String level = data.getString("deptLevel");

		//1.旧部门不存在 新增
		if (team == null) {
			return addTeam(fullName, data, belong, groupLeader);
		}


		//7.部门全码变更
		if (team.getFullCode() ==null || !team.getFullCode().equals(data.getString("deptFullCode"))) {
			team.setFullCode(data.getString("deptFullCode"));
			teamService.updateTeam(team);
		}

		//未创建钉钉部门，zk的部门不创建钉钉部门
		if (!teamService.isZKTeam(data.getString("externalCode")) && team.getDingdingId() == null) {
			team.setLevel(data.getString("deptLevel"));
			Team parentTeam = teamService.getTeamByCode(team.getParentCode());
			msg.append(addDingdingDept(team, parentTeam));
		}

		//2.上级部门变更
		if (!team.getParentCode().equals(data.getString("parent"))) {
			//zk部门不进行钉钉操作
			if (!teamService.isZKTeam(data.getString("externalCode"))) {
				msg.append(updateParentTeam(team, belong, shortName, fullName, groupLeader));
			} else {
				// zk部门只更新MIT数据库信息，不涉及钉钉操作
				team.setGroupLeader(groupLeader);
				team.setParentCode(belong.getSfCode());
				updateTeamName(team, fullName, shortName);
				msg.append("***ZK部门修改上级：" + team.getName() + "（跳过钉钉操作）");
			}
		}

		//3.部门自身改名(进入2的不会进入3和4，因为已经修改完成)
		if (!team.getShortName().equals(shortName)) {
			//zk部门不进行钉钉操作
			if (!teamService.isZKTeam(data.getString("externalCode"))) {
				try {
	                if (dingDingRestClient.updateDept(team, shortName)) {
	                	msg.append("***修改部门名称:" + team.getShortName() + "--->" + shortName);
	                	updateTeamName(team, fullName, shortName);
	                    msg.append("***修改钉钉部门名称<span style=\"color:#3eda67;\">[成功]</span>");
	                } else {
	                    msg.append("***修改钉钉部门名称" + "<span style=\"color:#EE4000;\">[失败]</span>");
	                }
	            } catch (Exception e) {
	                msg.append(team + "***修改钉钉部门名称:" + shortName + "<span style=\"color:#EE4000;\">[错误]</span>");
	                logger.error(msg.toString());
	            }
			} else {
				// zk部门只更新MIT数据库信息，不涉及钉钉操作
				msg.append("***ZK部门修改名称:" + team.getShortName() + "--->" + shortName + "（跳过钉钉操作）");
				updateTeamName(team, fullName, shortName);
			}
		}

		//4.部门全路径变更(进入2或3的不会进入4，因为已经修改完成)
		if (!team.getName().equals(fullName)) {
			msg.append("****修改部门全路径:" + team.getName() + "--->" + fullName);
			updateTeamName(team, fullName, shortName);
		}

		//5.更换hrg or teamLeader or groupLeader
		if (!Objects.equals(team.getHrg(), data.getString("hrg")!=null?data.getString("hrg"):"") ||
				!Objects.equals(team.getTeamLeader(), data.getString("headOfUnit")!=null?data.getString("headOfUnit"):"") ||
				!Objects.equals(team.getGroupLeader(),groupLeader)
		) {
			//zk部门不进行钉钉操作
			if (!teamService.isZKTeam(data.getString("externalCode"))) {
				try{
					//如果是更换hrg，则替换hrg部门，原来的删除，新的增加

					//HRG自动加入组织架构限制最低层级到BU
					Boolean sign = "001".equals(level) || "002".equals(level) || "006".equals(level);
					if(!Objects.equals(team.getHrg(), data.getString("hrg")!=null?data.getString("hrg"):"") & sign){
						if(StringUtils.isNotEmpty(team.getHrg())&&StringUtils.isNotEmpty(team.getDingdingId())){
							msg.append(dingdingService.updateUserDingdingDept(team.getHrg(),Integer.valueOf(team.getDingdingId()),false));
						}
						if(StringUtils.isNotEmpty(data.getString("hrg"))&&StringUtils.isNotEmpty(team.getDingdingId())){
							msg.append(dingdingService.updateUserDingdingDept(data.getString("hrg"),Integer.valueOf(team.getDingdingId()),true));
						}
					}
					//如果是更换leader，则替换leader部门，原来的删除，新的增加
					if(!Objects.equals(team.getTeamLeader(), data.getString("headOfUnit")!=null?data.getString("headOfUnit"):"")){
						if(StringUtils.isNotEmpty(team.getTeamLeader())&&StringUtils.isNotEmpty(team.getDingdingId())){
							msg.append(dingdingService.updateUserDingdingDept(team.getTeamLeader(),Integer.valueOf(team.getDingdingId()),false));
						}
						if(StringUtils.isNotEmpty(data.getString("headOfUnit"))&&StringUtils.isNotEmpty(team.getDingdingId())){
							msg.append(dingdingService.updateUserDingdingDept(data.getString("headOfUnit"),Integer.valueOf(team.getDingdingId()),true));
						}
					}
				}catch (Exception e){
					msg.append(team + "*****[" + team.getName() + "]修改teamleader错误<span style=\"color:#EE4000;\">[错误]</span>" + e.getMessage());
				}
			} else {
				// zk部门跳过钉钉相关操作
				msg.append("*****[" + team.getName() + "]ZK部门修改hrg和teamleader（跳过钉钉操作）");
			}

			// 更新数据库信息（无论是否为zk部门都需要更新）
			team.setHrg(data.getString("hrg"));
			team.setTeamLeader(data.getString("headOfUnit"));
			team.setGroupLeader(groupLeader);
			teamService.updateTeam(team);
			
			if (!teamService.isZKTeam(data.getString("externalCode"))) {
				msg.append("*****[" + team.getName() + "]修改hrg和teamleader<span style=\"color:#3eda67;\">[成功]</span>");
			}
		}

		//8.补充群聊id chatId is null && 上级不是G01，zk的部门不补充群聊
		if (team.getChatId() == null && !"G01".equals(data.getString("parent"))&&!teamService.isZKTeam(data.getString("externalCode"))) {
			try {
				dingdingService.saveChatId(team);
				msg.append("******" + fullName + "--补充群聊<span style=\"color:#3eda67;\">[成功]</span>");
			} catch (Exception e) {
				msg.append("******" + fullName + "--补充群聊<span style=\"color:#EE4000;\">[失败]</span>");
			}
		}

		//没有发生变化
		if (!"".equals(msg.toString())) {
			return msg.toString();
		}


		return null;
	}

	private void updateTeamName(Team team, String fullName, String shortName) {
		team.setName(fullName);
		team.setShortName(shortName);
		//修改ldap
		ldapService.modifyGroup(team.getMailGroup(), "displayname", shortName);
		ldapService.modifyGroup(team.getMailGroup(), "description", fullName);
		teamService.updateTeam(team);
	}


	/**
	 * 修改上级部门
	 *
	 *
	 * @param team
	 * @param parentTeam
	 * @param shortName
	 * @return
	 */
	private String updateParentTeam(Team team, Team parentTeam, String shortName, String fullName, String groupLeader) {
		StringBuilder msg = new StringBuilder();
		
		//zk部门不进行钉钉操作
		if (teamService.isZKTeam(team.getSfCode())) {
			team.setGroupLeader(groupLeader);
			team.setParentCode(parentTeam.getSfCode());
			updateTeamName(team, fullName, shortName);
			msg.append(team.getName() + "**ZK部门修改上级部门（跳过钉钉操作）<span style=\"color:#3eda67;\">[成功]</span>");
			return msg.toString();
		}

		String parentDingdingId;

		//上级是不创建钉钉的cluster
		if (clusterMap.get(parentTeam.getSfCode()) != null && !clusterMap.get(parentTeam.getSfCode())) {
			parentDingdingId = "1";
		} else {  //上级是创建钉钉的cluster
			if (parentTeam.getDingdingId() == null) {
				msg.append(team.getName() + "**修改上级部门异常:上级部门dingdingId<span style=\"color:#EE4000;\">[异常]</span>");
				return msg.toString();
			}
			parentDingdingId = parentTeam.getDingdingId();
		}

		dingdingService.updateDingdingParent(team.getSfCode(), team.getDingdingId(), parentDingdingId, shortName);
		team.setGroupLeader(groupLeader);
		team.setParentCode(parentTeam.getSfCode());
		updateTeamName(team, fullName, shortName);

		msg.append(team.getName() + "**钉钉修改上级部门<span style=\"color:#3eda67;\">[成功]</span>");
		return msg.toString();
	}


	/**
	 * 新增部门
	 * 1.创建mit部门
	 * 2.创建钉钉部门
	 * 3.添加群聊
	 * 4.添加实习生群聊
	 *
	 * @param fullName
	 * @param data
	 * @param parentTeam
	 * @return
	 */
	private String addTeam(String fullName, JSONObject data, Team parentTeam, String groupLeader) {
		StringBuilder msg = new StringBuilder();

		Team team = new Team();
		team.setName(fullName);
		team.setSfCode(data.getString("externalCode"));
		team.setHrg(data.getString("hrg") != null ? data.getString("hrg") : "");
		team.setTeamLeader(data.getString("headOfUnit") != null ? data.getString("headOfUnit") : "");
		team.setShortName(data.getString("name"));
		team.setParentCode(data.getString("parent"));
		team.setMailGroup("");
		team.setGroupLeader(groupLeader);
		team.setLevel(data.getString("deptLevel"));
		team.setFullCode(data.getString("deptFullCode"));

		//创建MIT部门
		if (teamService.addTeam(team)) {
			msg.append("MIT新增DHR部门:" + team.getSfCode() + "*****" + fullName + "<span style=\"color:#3eda67;\">[成功]</span>");
		} else {
			msg.append("MIT新增DHR部门:" + team.getSfCode() + "*****" + fullName + "<span style=\"color:#EE4000;\">[失败]</span>");
			return msg.toString();
		}

		//zk部门不创建钉钉部门
		if (!teamService.isZKTeam(data.getString("externalCode"))) {
			msg.append(addDingdingDept(team, parentTeam));
		} else {
			msg.append("***ZK部门跳过创建钉钉部门和群聊操作");
		}
		return msg.toString();
	}


	/**
	 * 向上查找cluster下的最高层级
	 * @param team
	 * @return
	 */
	public Team getTopParentTeam(Team team) {
		//如果上级部门不是cluster，递归向上查找 将mc强制设置为cluster
		if (clusterMap.get(team.getParentCode()) == null && !team.getSfCode().equals("G50")) {
			Team parentTeam = teamService.getTeamByCode(team.getParentCode());
			team = getTopParentTeam(parentTeam);
		}
		return team;
	}


	/**
	 * 从中台同步DHR码表信息
	 *
	 * @return
	 */
	public String syncDhrDataFromZT(String key) {
		StringBuilder msg = new StringBuilder();
		try {
			JSONArray result = dataRestClient.getData(key);

			MMISMegviiConfig config = new MMISMegviiConfig();
			config.setKey(key);
			config.setValue(result.toJSONString());

			if (mmisMegviiConfigService.updateMegviiConfig(config)) {
				msg.append("更新config[成功]:key=" + key);
				redisTemplate.opsForValue().set("mitexecutor_dhr_" + key, result);
			} else {
				msg.append("更新config[失败]:key=" + key);
			}
		} catch (Exception e) {
			msg.append("同步中台" + key + "异常:" + e.getMessage());
			logger.error(msg.toString());
		}
		return msg.toString();
	}

	/**
	 * 同步DHR岗位码表 放入redis中
	 *
	 * @return
	 */
	public String syncDhrPositionFromZT() {
		StringBuilder msg = new StringBuilder();
		try {
			JSONArray result = dataRestClient.getAllPosition();
			Map<String, String> map = new HashMap<>();
			for (Object o : result) {
				JSONObject data = (JSONObject) o;
				map.put(data.getString("jobCode"), data.getString("jobName"));
			}
			redisTemplate.opsForHash().putAll("mitexecutor_dhr_position", map);
		} catch (Exception e) {
			msg.append("同步position异常:" + e.getMessage());
			logger.error(msg.toString());
		}
		return msg.toString();

	}

	/**
	 * 新增钉钉部门规则
	 * @param team
	 * @param parentTeam
	 * @return
	 */
	private String addDingdingDept(Team team, Team parentTeam) {
		StringBuilder msg = new StringBuilder();

		//zk部门不创建钉钉部门
		if (teamService.isZKTeam(team.getSfCode())) {
			msg.append("***ZK部门跳过钉钉部门创建操作");
			return msg.toString();
		}

		/**
		 * 控制是否创建钉钉部门
		 * cluster通过map判断自身是否创建
		 * 其他层级通过parent 判断上级是根还是其他部门
		 * createChat 判断是否创建部门群聊
		 */
		String parentDingdingId;
		boolean createChat;
		//上级是G01
		if ("G01".equals(team.getParentCode())) {
			//不创建钉钉部门的cluster
			if (clusterMap.get(team.getSfCode()) != null && !clusterMap.get(team.getSfCode())) {
//				msg.append("***不创建钉钉部门");
				return "";
			}
			//创建钉钉部门的cluster
			parentDingdingId = "1";
			createChat = false;
		} else {
			//上级是不创建钉钉的cluster
			if (clusterMap.get(team.getParentCode()) != null && !clusterMap.get(team.getParentCode())) {
				parentDingdingId = "1";
				createChat = true;
			} else {  //上级是创建钉钉的cluster
				//特殊处理数智产业研究院
				if ("G37".equals(team.getSfCode())) {
					parentDingdingId = "1";
					createChat = true;
				} else {
					parentDingdingId = parentTeam.getDingdingId();
					createChat = true;
				}
			}
		}

		//创建钉钉部门
		if (dingdingService.createDingDingDept(team, parentDingdingId, createChat)) {
			msg.append("***创建钉钉部门:" + team.getSfCode() + "*****" + team.getName() + "<span style=\"color:#3eda67;\">[成功]</span>");
		} else {
			msg.append("***创建钉钉部门:" + team.getSfCode() + "*****" + team.getName() + "<span style=\"color:#EE4000;\">[失败]</span>");
			return msg.toString();
		}

		//cluster不创建群聊
		if ("G01".equals(team.getParentCode())) {
			msg.append("***cluster不创建群聊");
			return msg.toString();
		} else {
			try {
				dingdingService.saveChatId(team);
				msg.append("***创建群聊<span style=\"color:#3eda67;\">[成功]</span>");
			} catch (Exception e) {
				msg.append("***创建群聊<span style=\"color:#EE4000;\">[失败]</span>");
			}
		}

		/**
		 * 实习生群聊逻辑:
		 * 1.支撑体系实习生放在本部门内
		 * 2.业务部实习生放在特殊实习生部门内，向上查找到cluster的下一级进行继承
		 */
		Team topTeam = getTopParentTeam(team);
		//上级在钉钉内显示，实习生放入本部门内
		if (clusterMap.get(topTeam.getParentCode())) {
			team.setInternId(team.getDingdingId());
			teamService.updateTeam(team);
		} else {
			team.setInternId(topTeam.getInternId() != null ? topTeam.getInternId() : "");
			teamService.updateTeam(team);
		}

		return msg.toString();
	}


}
