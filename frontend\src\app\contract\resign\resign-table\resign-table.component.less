:host {
  .search-container {
    overflow: hidden;

    .ant-input,
    .ant-select {
      width: 180px;
    }
    & > span {
      display: inline-block;
      margin-bottom: 10px;

      & > label {
        display: inline-block;
        width: 100px;
        text-align: right;
      }
    }

    .pick-up-label {
      width: 40px !important;
    }
  }
  .invalid-contract {
    td > span {
      color: #d9d9d9;
    }
  }
  .ant-btn {
    border-radius: 0px;
  }
  .signStatus {
    display: inline-block;
    min-width: 80px;
  }
}

:host::ng-deep {
  .user-item {
    background: rgb(255, 255, 255) !important;
  }

  .ant-table table th {
    padding: 11px 5px;
  }

  .ant-table-tbody > tr > td {
    padding: 16px 5px;
  }
}
.actionA {
  margin-right: 15px;
}
.alert-text {
  width: 550px;
  margin-left: 110px;
}

.upload-span {
  margin-left: 15px;
  border: none;
  background-color: #fff;
}

.returnWord {
  color: gray;
  border: none;
}
.returnGreenWord {
  color: green;
}
.stampUl {
  margin-top: 20px;
  background-color: rgb(242, 242, 242);
  li:before {
    content: '';
    display: inline-block;
    width: 7px;
    height: 7px;
    background-color: blue;
    border-radius: 50%;
    margin-right: 5px;
  }
  li {
    height: 40px;
    line-height: 20px;
    padding: 10px;
    border: 1px solid rgb(217, 217, 217);
  }
}
.tag-span {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ff0000;
}
.tag-span1 {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #f9ab4a;
}

