package com.megvii.job;

import com.megvii.common.OperateResult;
import com.megvii.common.ParameterUtil;
import com.megvii.operator.DhrContractBacklogOperatorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/4/13 18:04
 * 拉取dhr续签数据
 */
@JobHandler(value="DhrContractBacklogExecutorHandler")
@Component
public class DhrContractBacklogExecutorHandler extends IJobHandler {

	@Autowired
	private DhrContractBacklogOperatorService dhrContractBacklogOperatorService;

	@Override
	public ReturnT<String> execute(String s) throws Exception {
		OperateResult result = dhrContractBacklogOperatorService.operateMain(ParameterUtil.getParaMap(s));
		if (result.getStatus() == 2) {
			return new ReturnT(500, "<span style=\"color:#e83c4c;\"> 主操作执行失败</span> " + result.getMsg());
		} else if (result.getStatus() == 0) {
			return new ReturnT(200, "操作完成，但存在失败操作--->" + result.getMsg());
		} else {
			return new ReturnT(200, result.getMsg());
		}
	}

}
