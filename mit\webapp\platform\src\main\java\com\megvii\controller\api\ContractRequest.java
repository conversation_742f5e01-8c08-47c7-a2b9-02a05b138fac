package com.megvii.controller.api;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/20 15:12
 */
@Data
public class ContractRequest {

	private Integer id;
	private String userId;
	private String contractId;
	private String contractType;
	private String contractCount;
	private String contractAttribute;
	private String startDate;
	private String endDate;
	private String effectDate;
	private String probationStartDate;
	private String probationEndDate;
	private String company;
	private String memo;
	private String signStatus;

	/**
	 *原签署单位
	 */
	private String contract;
	/**
	 *新签署单位
	 */
	private String newContract;
	/**
	 *原签署开始日期
	 */
	private String conBeginDate;
	/**
	 *原签署截止日期
	 */
	private String conEndDate;
	/**
	 *新签署开始日期
	 */
	private String newConBeginDate;
	/**
	 *新签署结束日期
	 */
	private String newConEndDate;
	/**
	 *原工作地
	 */
	private String workCity;
	/**
	 *新工作地
	 */
	private String newWorkCity;

	/**
	 * 合同文件包地址
	 */
	private String userContractFile;

	/**
	 * 在读学校
	 */
	private String school;

	/**
	 * 学号
	 */
	private String studentNumber;

	/**
	 * 入学时间
	 */
	private String admissionTime;

	/**
	 * 毕业时间
	 */
	private String graduationTime;


}
