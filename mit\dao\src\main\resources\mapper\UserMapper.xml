<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.User">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="spell" property="spell"/>
        <result column="cell" property="cell"/>
        <result column="expect_date" property="expectDate"/>
        <result column="hire_date" property="hireDate"/>
        <result column="updater" property="updater"/>
        <result column="team" property="team"/>
        <result column="computer" property="computer"/>
        <result column="os" property="os"/>
        <result column="mentor" property="mentor"/>
        <result column="buddy" property="buddy"/>
        <result column="seat_number" property="seatNumber"/>
        <result column="tshirt_size" property="tshirtSize"/>
        <result column="employ_type" property="employType"/>
        <result column="status" property="status"/>
        <result column="memo" property="memo"/>
        <result column="update_time" property="updateTime"/>
        <result column="hrbp" property="hrbp"/>
        <result column="regular_date" property="regularDate"/>
        <result column="probation_doc_url" property="probationDocUrl"/>
        <result column="dimission_date" property="dimissionDate"/>
        <result column="employ_base" property="employBase"/>
        <result column="fullname" property="fullname"/>
        <result column="work_number" property="workNumber"/>
        <result column="company" property="company"/>
        <result column="team_code" property="teamCode"/>
        <result column="dingding_id" property="dingdingId"/>
        <result column="full_id" property="fullId"/>
        <result column="level" property="level"/>
        <result column="beisen_id" property="beisenId"/>
        <result column="ori_password" property="oriPassword"/>
        <result column="birthday" property="birthday"/>
        <result column="position" property="position"/>
        <result column="site_hrg" property="siteHrg"/>
        <result column="didi_id" property="didiId"/>
        <result column="unionid" property="unionId"/>
        <result column="team_id" property="teamId"/>
        <result column="entry_flow" property="entryFlow"/>
        <result column="duty" property="duty"/>
        <result column="title" property="title"/>
        <result column="source" property="source"/>
        <result column="work_base" property="workBase"/>
        <result column="didi_disabled" property="didiDisabled"/>
        <result column="create_tag" property="createTag"/>
        <result column="flow_time" property="flowTime"/>
        <result column="koala_id" property="koalaId"/>
        <result column="refer_title" property="referTitle"/>
        <result column="sequence" property="sequence"/>
        <result column="protect" property="protect"/>
        <result column="cert_type" property="certType"/>
        <result column="cert_no" property="certNo"/>
        <result column="address" property="address"/>
        <result column="contract_count" property="contractCount"/>
        <result column="contract_status" property="contractStatus"/>
        <result column="sales" property="sales"/>
        <result column="moka_id" property="mokaId"/>
        <result column="offer_id" property="offerId"/>
        <result column="old_address" property="oldAddress"/>
        <result column="pri_email" property="priEmail"/>
        <result column="cert_name" property="certName"/>
    </resultMap>

    <insert id="insert" parameterType="com.megvii.entity.opdb.User" >
        INSERT INTO `IT_user` (
	        `id`,
	        `user_id`,
	        `spell`,
	        `cell`,
        	`expect_date`,
        	`updater`,
        	`team`,
        	`mentor`,
        	`buddy`,
        	`employ_type`,
        	`status`,
        	`update_time`,
	        `hrbp`,
	        `regular_date`,
	        `employ_base`,
	        `work_number`,
	        `team_code`,
        	`beisen_id`,
        	`position`,
        	`site_hrg`,
        	`team_id`,
        	`entry_flow`,
	        `duty`,
        	`title`,
	        `source`,
          	`work_base`,
          	`create_tag`,
	        `flow_time`,
	        `refer_title`,
	        `sequence`,
            `moka_id`,
            `offer_id`,
            `cert_name`
        )
        VALUES
	    (
		    0,
		    #{userId},
		    #{spell},
	    	#{cell},
		    #{expectDate},
		    #{updater},
		    #{team},
		    #{mentor},
		    #{buddy},
		    #{employType},
		    #{status},
		    now(),
		    #{hrbp},
	    	#{regularDate},
    		#{employBase},
    		#{workNumber},
      		#{teamCode},
            #{beisenId},
    		#{position},
		    #{siteHrg},
		    #{teamId},
		    #{entryFlow},
		    #{duty},
		    #{title},
		    #{source},
		    #{workBase},
		    #{createTag},
		    #{flowTime},
		    #{referTitle},
		    #{sequence},
	        #{mokaId},
	        #{offerId},
	        #{certName}
	    );
    </insert>


    <select id="selectUserByBeiSenId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_user where beisen_id=#{beiSenId}
    </select>

    <select id="selectUserByMokaId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_user where moka_id=#{mokaId}
    </select>

    <select id="selectUserByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_user where user_id=#{userId}
    </select>

    <select id="selectUserByWorkNumber" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from IT_user where work_number = #{workNumber}
    </select>

    <select id="selectMaxUserIdByRegexp" resultType="java.lang.String" parameterType="java.lang.String">
        select user_id from IT_user where user_id REGEXP concat('^',#{userPinyin},'([0-9])*$') ORDER BY REPLACE(user_id, #{userPinyin}, '')+0 DESC limit 1
    </select>

    <select id="selectMaxFullIdByRegexp" resultType="java.lang.String" parameterType="java.lang.String">
        select full_id from IT_user where full_id = #{userPinyin}
    </select>

    <select id="selectUsersNeedToLdap" resultMap="BaseResultMap">
        select * from IT_user where status != '已禁用' and entry_flow != 'OA流程'  and datediff(now(),expect_date) &lt;=0 and datediff(now(),expect_date) &gt;=-6
    </select>

    <select id="selectUsersNeedToOffice365" resultMap="BaseResultMap">
        SELECT
	    *
        FROM
	    IT_user
        WHERE
	    STATUS != '已禁用'
	    AND entry_flow != 'OA流程'
	    AND
        DATE_FORMAT( expect_date, '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( now( ), INTERVAL 0 DAY ), '%Y-%m-%d' )
    </select>

    <select id="selectUserListNeedToSf" resultMap="BaseResultMap">
        select * from IT_user where status != '已禁用' and entry_flow != 'OA流程'  and DATE_FORMAT(expect_date,'%Y-%m-%d') =DATE_FORMAT(now(),'%Y-%m-%d') AND DATE_FORMAT( dimission_date, '%Y-%m-%d' ) IS NULL
    </select>
    <update id="updateUser4Aliyun" parameterType="com.megvii.entity.opdb.User">
        update IT_user set cert_type = #{certType}, cert_no = #{certNo},  address = #{address}, entry_flow=#{entryFlow},tshirt_size=#{tshirtSize},koala_id=#{koalaId},flow_time=#{flowTime}  where id=#{id}
    </update>

    <update id="updateUserPassword" parameterType="com.megvii.entity.opdb.User">
        update IT_user set ori_password=#{oriPassword} where id=#{id}
    </update>

    <update id="updateUserDidiId" parameterType="com.megvii.entity.opdb.User">
        update IT_user set didi_id = #{didiId} where id=#{id}
    </update>

    <select id="selectUserByExpectDate" resultMap="BaseResultMap">
        SELECT * FROM IT_user
        WHERE
        DATE_FORMAT(expect_date,'%Y-%m-%d') = DATE_FORMAT(NOW(),'%Y-%m-%d')
        AND
        status not in ('已保留', '已禁用')
        AND entry_flow != 'OA流程'
    </select>

    <update id="updateUserDingdingIdAndUnionid" parameterType="com.megvii.entity.opdb.User">
        update IT_user set dingding_id = #{dingdingId}, unionid = #{unionId} where id = #{id}
    </update>

    <update id="updateUserEntryFlow" parameterType="com.megvii.entity.opdb.User">
        update IT_user set entry_flow = #{entryFlow} where id = #{id}
    </update>

    <select id="selectUserByDimissionDate" resultMap="BaseResultMap">
        SELECT * FROM IT_user
        WHERE
        DATE_FORMAT(dimission_date,'%Y-%m-%d') &lt;= DATE_FORMAT(NOW(),'%Y-%m-%d')
        AND
        status not in ('已保留', '已禁用')
    </select>

    <update id="updateUser" parameterType="com.megvii.entity.opdb.User">
        update IT_user
        <set>
            <if test="userId != null ">
                user_id = #{userId},
            </if>
            <if test="spell != null ">
                spell = #{spell},
            </if>
            <if test="cell != null ">
                cell = #{cell},
            </if>
            <if test="memo != null ">
                memo = #{memo},
            </if>
            <if test="computer != null ">
                computer = #{computer},
            </if>
            <if test="os != null ">
                os = #{os},
            </if>
            <if test="duty != null ">
                duty = #{duty},
            </if>
            <if test="seatNumber != null ">
                seat_number = #{seatNumber},
            </if>
            <if test="employBase != null ">
                employ_base = #{employBase},
            </if>
            <if test="buddy != null ">
                buddy = #{buddy},
            </if>
            <if test="hrbp != null ">
                hrbp = #{hrbp},
            </if>
            <if test="siteHrg != null ">
                site_hrg = #{siteHrg},
            </if>
            <if test="siteHrg == null ">
                site_hrg = null,
            </if>
            <if test="expectDate != null ">
                expect_date = #{expectDate},
            </if>
            <if test="regularDate != null ">
                regular_date = #{regularDate},
            </if>
            <if test="employType != null ">
                employ_type = #{employType},
            </if>
            <if test="mentor != null ">
                mentor = #{mentor},
            </if>
            <if test="team != null ">
                team = #{team},
            </if>
            <if test="teamId != null ">
                team_id = #{teamId},
            </if>
            <if test="teamId == null ">
                team_id = null,
            </if>
            <if test="teamCode != null ">
                team_code = #{teamCode},
            </if>
            <if test="birthday != null ">
                birthday = #{birthday},
            </if>
            <if test="updater != null ">
                updater = #{updater},
            </if>
            <if test="company != null ">
                company = #{company},
            </if>
            <if test="position != null ">
                position = #{position},
            </if>
            <if test="title != null ">
                title = #{title},
            </if>
            <if test="status != null ">
                status = #{status},
            </if>
            <if test="referTitle != null ">
                refer_title = #{referTitle},
            </if>
            <if test="workBase != null ">
                work_base = #{workBase},
            </if>
            <if test="entryFlow != null ">
                entry_flow = #{entryFlow},
            </if>
            <if test="flowTime != null ">
                flow_time = #{flowTime},
            </if>
            <if test="source != null ">
                source = #{source},
            </if>
            <if test="dimissionDate != null ">
                dimission_date = #{dimissionDate},
            </if>
            <if test="dimissionDate == null ">
                dimission_date = null,
            </if>
            <if test="sequence != null ">
                sequence = #{sequence},
            </if>
            <if test="oriPassword != null ">
                ori_password = #{oriPassword},
            </if>
            <if test="certType != null ">
                cert_type = #{certType},
            </if>
            <if test="certNo != null ">
                cert_no = #{certNo},
            </if>
            <if test="address != null ">
                address = #{address},
            </if>
            <if test="contractCount != null ">
                contract_count = #{contractCount},
            </if>
            <if test="contractStatus != null ">
                contract_status = #{contractStatus},
            </if>
            <if test="dingdingId != null ">
                dingding_id = #{dingdingId},
            </if>
            <if test="sales != null ">
                sales = #{sales},
            </if>
            <if test="createTag != null ">
                create_tag = #{createTag},
            </if>
            <if test="createTag == null ">
                create_tag = null,
            </if>
            <if test="offerId != null ">
                offer_id = #{offerId},
            </if>
            <if test="oldAddress != null ">
                old_address = #{oldAddress},
            </if>
            <if test="priEmail != null ">
                pri_email = #{priEmail},
            </if>
            <if test="certName != null ">
                cert_name = #{certName},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectAllUser" resultMap="BaseResultMap">
        select * from IT_user
    </select>

    <select id="selectUsersByUpdateTime" resultMap="BaseResultMap">
        SELECT * FROM IT_user WHERE datediff(now(),update_time) &lt;=#{day} and datediff(now(),update_time) &gt;=0
    </select>

    <select id="selectUsersByFilter" resultMap="BaseResultMap">
        SELECT * FROM IT_user
        <where>
            <if test="filter.expectDate != null ">
                AND DATE_FORMAT(expect_date, '%Y-%m-%d' ) = DATE_FORMAT(#{filter.expectDate}, '%Y-%m-%d' )
            </if>
            <if test="filter.entryFlowStatus != null ">
                <if test="filter.entryFlowStatus == 0 ">
                    AND entry_flow != 'OA流程'
                </if>
                <if test="filter.entryFlowStatus == 1 ">
                    AND entry_flow = '信息收集'
                </if>
                <if test="filter.entryFlowStatus == 2 ">
                    AND entry_flow = 'OA流程'
                </if>
                <if test="filter.entryFlowStatus == 3 ">
                    AND entry_flow = '在职'
                </if>
            </if>
            <if test="filter.teamId != null ">
                AND team_id = #{filter.teamId}
            </if>
            <if test="filter.statusCode != null ">
                <if test="filter.statusCode == 0 ">
                    AND status not in ('已保留','已禁用')
                </if>
                <if test="filter.statusCode == 1 ">
                    AND status in ('已保留','已禁用') AND DATE_FORMAT(dimission_date, '%Y-%m-%d' ) &gt;= DATE_FORMAT(expect_date, '%Y-%m-%d' )
                </if>
                <if test="filter.statusCode == 2 ">
                    AND status = '已禁用' AND DATE_FORMAT(dimission_date, '%Y-%m-%d' ) &gt;= DATE_FORMAT(expect_date, '%Y-%m-%d' )
                </if>
            </if>
            <if test="filter.expectDateFrom != null ">
                AND DATE_FORMAT( expect_date, '%Y-%m-%d' ) &gt;=  DATE_FORMAT( #{filter.expectDateFrom} , '%Y-%m-%d' )
            </if>
            <if test="filter.expectDateTo != null ">
                AND DATE_FORMAT( expect_date, '%Y-%m-%d' ) &lt;= DATE_FORMAT( #{filter.expectDateTo}, '%Y-%m-%d' )
            </if>
            <if test="filter.dimissionDateFrom != null">
                AND DATE_FORMAT( dimission_date, '%Y-%m-%d' ) &gt;=  DATE_FORMAT( #{filter.dimissionDateFrom} , '%Y-%m-%d' )
            </if>
            <if test="filter.dimissionDateTo != null">
                AND DATE_FORMAT( dimission_date, '%Y-%m-%d' ) &lt;=  DATE_FORMAT( #{filter.dimissionDateTo} , '%Y-%m-%d' )
            </if>
            <if test="filter.workNumber != null ">
                AND work_number = #{filter.workNumber}
            </if>
            <if test="filter.userId != null ">
                AND user_id = #{filter.userId}
            </if>
            <if test="filter.spell != null ">
                AND spell = #{filter.spell}
            </if>
            <if test="filter.workBase != null ">
                AND work_base = #{filter.workBase}
            </if>
            <if test="filter.employType != null ">
                AND employ_type = #{filter.employType}
            </if>
            <if test="filter.entryFlow != null ">
                AND entry_flow = #{filter.entryFlow}
            </if>
            <if test="filter.contractStatus != null ">
                AND contract_status = #{filter.contractStatus}
            </if>
            <if test="filter.contractStatusCode != null ">
                <if test="filter.contractStatusCode == 0 ">
                    AND contract_status not in ('已完成', '已导入') AND contract_count = 0
                </if>
            </if>
            <if test="filter.employTypeCode != null ">
                <if test="filter.employTypeCode == 0 ">
                    AND employ_type != '劳务派遣'
                </if>
            </if>
            <if test="filter.createTagCode != null ">
                <if test="filter.createTagCode == 0 ">
                    AND (create_tag != 4 OR create_tag IS NULL)
                </if>
            </if>
            <if test="filter.dingdingIdCode != null ">
                <if test="filter.dingdingIdCode == 0 ">
                    AND dingding_id is NULL
                </if>
            </if>
        </where>
        <if test="filter.statusCode == 0">
            ORDER BY expect_date
        </if>
        <if test="filter.statusCode == 1">
            ORDER BY dimission_date desc
        </if>
    </select>

    <select id="selectUsersByDiffExpectDimissDate" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        IT_user
        WHERE
        (datediff( DATE_FORMAT( dimission_date, '%Y-%m-%d' ), DATE_FORMAT( NOW( ), '%Y-%m-%d' ) ) &gt;= #{diff}
        AND datediff( DATE_FORMAT( dimission_date, '%Y-%m-%d' ), DATE_FORMAT( NOW( ), '%Y-%m-%d' ) ) &lt;= 0
        AND entry_flow = '在职')
        OR
        (datediff( DATE_FORMAT( expect_date, '%Y-%m-%d' ), DATE_FORMAT( NOW( ), '%Y-%m-%d' ) ) &gt;= #{diff}
        AND datediff( DATE_FORMAT( expect_date, '%Y-%m-%d' ), DATE_FORMAT( NOW( ), '%Y-%m-%d' ) ) &lt;= 0
        AND STATUS != '已禁用' AND entry_flow = '在职')
        ORDER BY expect_date DESC
    </select>

</mapper>