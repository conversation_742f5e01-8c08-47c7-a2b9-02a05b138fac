package com.megvii.service;

import com.megvii.entity.opdb.UserDimissionContractSnapshotExtendPO;
import com.megvii.mapper.UserDimissionContractSnapshotExtendMapper;
import com.megvii.service.UserDimissionContractSnapshotExtendService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Service
public class UserDimissionContractSnapshotExtendService {

  @Autowired
  private UserDimissionContractSnapshotExtendMapper mapper;

  public Boolean addSnapshotExtend(UserDimissionContractSnapshotExtendPO po){
    return mapper.addSnapshotExtend(po);
  }
  public Boolean deleteSnapshotExtend(String userId){
    return mapper.deleteSnapshotExtend(userId);
  }

  public List<UserDimissionContractSnapshotExtendPO> getSnapshotExtend(String userId){
    return mapper.getSnapshotExtend(userId);
  }

  public Boolean updateSnapshotExtend(List<UserDimissionContractSnapshotExtendPO> SnapshotExtends,String userId){
    mapper.deleteSnapshotExtend(userId);
    for (UserDimissionContractSnapshotExtendPO extendPO : SnapshotExtends) {
      addSnapshotExtend(extendPO);
    }
    return true;
  }

}
