package com.megvii.operator;

import com.megvii.common.DateTimeUtil;
import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.RemindService;
import com.megvii.service.UserService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/2/20 16:59
 */
@Service
public class SendUserInfoSmsOperatorService extends BaseOperatorService {

    @Autowired
    private SendUserInfoSmsOperatorService sendUserInfoSmsOperatorService;

    @Autowired
    private RemindService remindService;

    @Autowired
    private UserService userService;


    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId = paraMap.get("userId");
        if(userId != null){
            return OperateByUserId((String) userId);
        } else {
            UserFilter filter = new UserFilter();
            filter.setExpectDate(DateTimeUtil.date2String(DateTimeUtil.dateAddInt(LocalDateTime.now(), 1)));
            filter.setStatusCode(0);
            filter.setEntryFlowStatus(1);

            List<User> users = userService.getUsersByFilter(filter);

            if (users.size() > 0) {
                StringBuilder msg = new StringBuilder();
                for (User user : users) {
                    OperateResult result = sendUserInfoSmsOperatorService.operateOnce(paraMap, user);
                    msg.append(result.getMsg() + "<br/>");
                }
                return new OperateResult(1,msg.toString());
            } else {
                return new OperateResult(1, "没有要发送提醒账号");
            }
        }
    }


    /**
     *
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        User user = (User) params[0];
        if (user!=null){
            String result = remindService.sendUserInfoSmsRemind(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1, user.getUserId() + "信息不存在");
        }
    }


    /**
     *
     * 根据userId添加单个人的美餐信息
     * @param userId
     * @return
     */
    public OperateResult OperateByUserId(String userId) throws Exception {
        User user = userService.getUserByUserId(userId);

        if (user != null){
            String result = remindService.sendUserInfoSmsRemind(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1,userId + "信息不存在");
        }
    }



}
