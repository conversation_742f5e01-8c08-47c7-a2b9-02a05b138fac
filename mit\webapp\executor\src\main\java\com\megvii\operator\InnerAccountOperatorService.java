package com.megvii.operator;

import com.megvii.common.OperateResult;
import java.util.Map;

public class InnerAccountOperatorService extends BaseOperatorService {
    @Override
    OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        return null;
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        return null;
    }
}
