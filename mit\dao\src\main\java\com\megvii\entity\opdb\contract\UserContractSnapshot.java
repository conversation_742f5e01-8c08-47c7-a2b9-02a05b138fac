package com.megvii.entity.opdb.contract;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/4/15 14:34
 */
@Data
public class UserContractSnapshot {

	private int id;
	private String userId;
	private String workNumber;
	private String spell;
	private String contractNumber;
	private String contractType;
	private String contractCount;
	private String contractAttribute;
	private LocalDateTime startDate;
	private LocalDateTime endDate;
	private LocalDateTime effectDate;
	private LocalDateTime probationStartDate;
	private LocalDateTime probationEndDate;
	private String creatorUserId;
	private String updatorUserId;
	private String company;
	private String position;
	private String employBase;
	private String contractCell;
	private String certType;
	private String certNo;
	private String address;
	private String memo;
	private String filePath;
	private String signStatus;

	/**
	 *原签署单位
	 */
	private String contract;
	/**
	 *新签署单位
	 */
	private String newContract;
	/**
	 *原签署开始日期
	 */
	private String conBeginDate;
	/**
	 *原签署截止日期
	 */
	private String conEndDate;
	/**
	 *新签署开始日期
	 */
	private String newConBeginDate;
	/**
	 *新签署结束日期
	 */
	private String newConEndDate;
	/**
	 *原工作地
	 */
	private String workCity;
	/**
	 *新工作地
	 */
	private String newWorkCity;
	/**
	 * 合同文件包地址
	 */
	private String userContractFile;

	/**
	 * offer存储路径
	 */
	private String offerPath;

	/**
	 * 实习津贴
	 */
	private String internshipAllowance;


	/**
	 * 在读学校
	 */
	private String school;

	/**
	 * 学号
	 */
	private String studentNumber;

	/**
	 * 入学时间
	 */
	private Date admissionTime;

	/**
	 * 毕业时间
	 */
	private Date graduationTime;

}
