package com.megvii;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DidiRestClient;
import com.megvii.entity.opdb.User;
import com.megvii.service.DidiService;
import com.megvii.service.UserService;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestDidi {

    @Autowired
    private DidiRestClient didiRestClient;

    @Autowired
    private UserService userService;

    @Autowired
    private DidiService didiService;

    @Test
    public void testToken() {
//        didiRestClient.getMemberFromDidi(null,"104656", null, null);
        String token = didiRestClient.getToken();
        return;
    }

    @Test
    public void testAddMember() {
//        String token = didiRestClient.getToken();
//        List<Map<String, String>> warningUserList = new ArrayList<>();
        User user = userService.getUserByUserId("wangxiaoer03");

//        didiRestClient.addMember2Didi(token, user.getTeamId().toString(), user.getWorkNumber(), user.getCell(), warningUserList);
//        didiService.addDidiInformation(user, false);

    }

    @Test
    public  void testAddUser() throws Exception {
        User user = userService.getUserByUserId("wangxiaoer03");
//        String result = didiService.addDidiInformation(user,false);
//        System.out.println(result);
    }


    @Test
    public void testGetMember() {
        String token = didiRestClient.getToken();

        JSONObject data = didiRestClient.getMemberFromDidi(token, null, "15001234726", null, 70);
        JSONObject records = (JSONObject)((JSONArray) data.get("records")).get(0);

//        didiRestClient.editMember(token, "101123");

//        int totalUser = 100;
//        int lenUser = 0;
//        List<Object> list = new ArrayList();
//        while (totalUser > lenUser) {
//            JSONObject data = didiRestClient.getMemberFromDidi(token, null, lenUser, 70);
//            totalUser = data.getInteger("total");
//
//            JSONArray didiUsers=((JSONArray) data.get("records"));
//            list.addAll(didiUsers);
//            lenUser = lenUser + didiUsers.size();
//        }
    }



    @Test
    public void deleteDidi() {
        String token = didiRestClient.getToken();
        List<String> workNumber = new ArrayList<>();
        workNumber.add("104656");

        boolean result = didiRestClient.delMember(token, workNumber);
        if (result) {
            return;
        }
    }

//    @Test
//    public void editDidi() {
//        String token = didiRestClient.getToken();
//        didiRestClient.editMember(token, "104124");
//
//    }

    @Test
    public void getCompany() {
        String token = didiRestClient.getToken();
//        JSONObject data = didiRestClient.getCompany(token);
//        JSONObject data = didiRestClient.getRegulation(token);
//        didiRestClient.getBudgetItem(token);
        didiRestClient.getOrderDetail(token, "1");
    }


    @Test
    public void updateDidi() throws Exception {
        User user = userService.getUserByUserId("jiangyuhong");

        System.out.println(user.getDidiId());

        String token = didiRestClient.getAuthToken();
        didiRestClient.editMember(token, user.getDidiId(), user.getCell(), user.getWorkNumber(), user.getTeamId().toString(), "1125900605012934_1125900605006608");


        JSONObject data = didiRestClient.getMemberFromDidi(token, null, user.getCell(), null, null);
        if (data != null) {
            if (!data.getString("total").equals("0")) {
                JSONObject records = (JSONObject) ((JSONArray) data.get("records")).get(0);
                String didiId = records.getString("id");

                System.out.println(didiId);
            }
        }



        System.out.println(1);



    }

}
