package com.megvii.service;

import com.megvii.entity.opdb.UserDimissionContractBacklogPO;
import com.megvii.entity.opdb.UserDimissionProcessBacklogPO;
import com.megvii.mapper.UserDimissionContractBacklogMapper;
import com.megvii.mapper.UserDimissionProcessBacklogMapper;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * dhr离职流程处理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */

@Service
public class UserDimissionProcessBacklogService {
    private org.slf4j.Logger logger = LoggerFactory.getLogger(UserDimissionProcessBacklogService.class);

    @Autowired
    private UserDimissionProcessBacklogMapper userDimissionProcessBacklogMapper;

    public Boolean addProcess(UserDimissionProcessBacklogPO po){
        return userDimissionProcessBacklogMapper.addProcess(po);
    }

    public UserDimissionProcessBacklogPO getProcessByDismissionId(String dimissionId) {
        return userDimissionProcessBacklogMapper.getProcess(dimissionId);
    }

    public Boolean updateProcessInfo(UserDimissionProcessBacklogPO po){
        return userDimissionProcessBacklogMapper.updateProcess(po);
    }
}
