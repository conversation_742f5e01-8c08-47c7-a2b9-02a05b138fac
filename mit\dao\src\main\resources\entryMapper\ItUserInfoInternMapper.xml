<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.entryMapper.ItUserInfoInternMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.megvii.entity.entry.ItUserInfoInternPO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="school" property="school" />
        <result column="student_number" property="studentNumber" />
        <result column="admission_time" property="admissionTime" />
        <result column="graduation_time" property="graduationTime" />
        <result column="check_input" property="checkInput" />
        <result column="create_at" property="createAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, school, student_number, admission_time, graduation_time, check_input, create_at
    </sql>

    <select id="selectInternInfoByUserId" resultMap="BaseResultMap" >
        SELECT * FROM IT_user_info_intern WHERE user_id = #{userId} LIMIT 1;
    </select>
    
</mapper>
