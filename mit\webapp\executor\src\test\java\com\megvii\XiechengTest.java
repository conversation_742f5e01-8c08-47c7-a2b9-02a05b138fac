package com.megvii;

import com.megvii.service.XiechengService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/8/28 16:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class XiechengTest {

    @Autowired
    private XiechengService xiechengService;

    @Test
    public void testGetToken() throws Exception {
//        String result = xiechengService.syncXiechengInfo(null, true);

//        System.out.println(result);


    }



}
