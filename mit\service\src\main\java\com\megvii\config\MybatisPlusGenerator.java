package com.megvii.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

/***
 * @Author: sunkui
 * @Date: 2020/4/1
 * @Time: 19:49
 * @Description:
 ***/
public class MybatisPlusGenerator {
    public static void main(String[] args) {
        generateByTables("user_dimission_contract_snapshot_extend");
    }

    private static void generateByTables(String... tableNames) {
        new AutoGenerator()
                .setGlobalConfig(
                        new GlobalConfig()
                                // 是否开启 activeRecord 模式
                                .setActiveRecord(false)
                                // 设置作者
                                .setAuthor("maziwei")
                                // 输出目录
                                .setOutputDir("./service/src/main/java/")
                                // 是否覆盖文件
                                .setFileOverride(true)
                                // xml resultMap
                                .setBaseResultMap(true)
                                // xml columnList
                                .setBaseColumnList(true)
                                // 自定义实体命名, %s 自动填充表实体属性
                                .setEntityName("%sPO")
                                // 自定义dao层命名
                                .setMapperName("%sMapper")
                                // 自定义xml命名
                                .setXmlName("%sMapper")
                                // 自定义service命名
                                .setServiceName("%sService")
                                // 自定义impl命名
                                .setServiceImplName("%sServiceImpl")
                                // 自定义controller命名
                                .setControllerName("%sController")
                        // 自定义 mapper 父类
                        // .setSuperMapperClass("")
                        // 自定义 service 父类
                        // .setSuperServiceClass("")
                        // 自定义 service 实现类父类
                        // .setSuperServiceImplClass("")
                        // Boolean类型字段是否移除is前缀处理
                        // .setEntityBooleanColumnRemoveIsPrefix(true)
                )
                // 数据源配置
                .setDataSource(
                        new DataSourceConfig()
                                // 设置数据库类型
                                .setDbType(DbType.MYSQL)
                                // 连接url
                                .setUrl("*********************************************")
                                // 用户名todo
                                .setUsername("jiangyuhong")
                                // 密码
                                .setPassword("jiangyuhong")
                                // 驱动名
                                .setDriverName("com.mysql.jdbc.Driver")
                )
                // 策略配置
                .setStrategy(
                        new StrategyConfig()
                                // 全局大写命名
                                .setCapitalMode(true)
                                // 实体是否使用lombok插件
                                .setEntityLombokModel(true)
                                // 表名生成策略
                                .setNaming(NamingStrategy.underline_to_camel)
                                //
                                .setEntityBooleanColumnRemoveIsPrefix(true)
                                // 设置需要生成的表
                                .setInclude(tableNames)
                                // 设置需要排除的表
                                .setExclude()
                                // 设置需要过滤掉的表名前缀
                                .setTablePrefix("sys")
                )
                // 包配置
                .setPackageInfo(
                        new PackageConfig()
                                // 自定义包路径
                                .setParent("com.megvii")
                                // 控制器包名
                                .setController("controller")
                                // 实体包名
                                .setEntity("entity")
                                // service包名
                                .setService("service")
                                // impl 包名
                                .setServiceImpl("service.impl")
                                // mapper 包名
                                .setMapper("mapper")
                                // xml 包名
                                .setXml("mapper.xml")
                ).execute();
    }
}
