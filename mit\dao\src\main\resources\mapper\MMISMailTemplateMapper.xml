<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.MMISMailTemplateMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.MMISMailTemplate">
        <id column="id" property="id"/>
        <result column="mail_type" property="mailType"/>
        <result column="content" property="content"/>
        <result column="updater" property="updater"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
    </resultMap>

    <select id="selectMailTemplateByType" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from MMIS_mail_template where mail_type = #{type} AND deleted_at IS NULL
    </select>






</mapper>