package com.megvii.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.FileUtil;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.ImageFile;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserInfoLocale;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AliyunService {

    @Autowired
    private UserService userService;

    @Autowired
    private UserInfoLocaleService userInfoLocaleService;

    @Autowired
    private KoalaService koalaService;

    @Autowired
    private EntryService entryService;

    @Autowired
    private DhrRestClient dhrRestClient;

    @Autowired
    private PanguService panguService;

    /**
     * 同步阿里云数据的数据，单条
     *      生活照上传到ftp
     *      证件照上传至考拉
     *      MIT更新入职流程状态到'等待入职'（如果是非强制更新）
     *      MIT保存sTshirtSize跟koalaId
     * @param userInfo
     * @return
     */
    public String synData(EntryUserInfo userInfo, boolean isForced){
        StringBuilder msg = new StringBuilder();
        User user = userService.getUserByUserId(userInfo.getUserId());
        if (user == null){
            return userInfo.getUserId()+"用户未找到";
        }

        //如果是强制更新，不判断入职状态，不更改入职状态相关信息
        if (!isForced){
            if (!"信息收集".equals(user.getEntryFlow())){
                return userInfo.getUserId()+"用户未处于'信息收集'状态";
            }

            if (!(userInfo.getSaved() != null && userInfo.getSaved() == 1)) {
                return userInfo.getUserId() + "用户未提交";
            }

            user.setEntryFlow("等待入职");
            String flowTimes=user.getFlowTime();
            if (flowTimes==null || "".equals(flowTimes)){
                user.setFlowTime(",,"+ DateTimeUtil.date2String4(LocalDateTime.now()));
            }else {
                user.setFlowTime(flowTimes+","+ DateTimeUtil.date2String4(LocalDateTime.now()));
            }
        }else {
            msg.append("通过userId:" + user.getUserId()+"强制同步阿里云数据,");
        }
        if (userInfo.getPhotoCert() == null || "".equals(userInfo.getPhotoCert()) || userInfo.getPhotoLife() == null || "".equals(userInfo.getPhotoLife())){
            return userInfo.getUserId()+"生活照跟证件照不能为空";
        }
        userInfo.setSpell(user.getSpell());
        JSONObject jsonInfo= JSON.parseObject(userInfo.getInfo());
        String gender=getGender(jsonInfo);
        int genderNumber="女".equals(gender)?2:1;
        userInfo.setGenderNumber(genderNumber);
        user.setTshirtSize(gender+jsonInfo.getString("tshirt_size"));

        String certType = jsonInfo.getString("CERTTYPE");
        user.setCertType(certType!=null?dhrRestClient.getCertTypeValue(certType):"居民身份证");
        user.setCertNo(jsonInfo.getString("CERTNO"));
        user.setAddress(jsonInfo.getString("ADDRESS"));

        //标注人员不进koala
        if (user.getCreateTag() != null && (user.getCreateTag() == 4||user.getCreateTag() == 9)) {
            msg.append(user.getUserId() + "为data标注人员，不上传koala***");
        } else {
            ImageFile photoCertFile = FileUtil.getByteFromBase64(userInfo.getPhotoCert(),userInfo.getUserId());
            msg.append(user.getUserId() + "****");
            //保存到本地
            if (koalaService.saveOnLocal(user.getUserId(), photoCertFile)) {
                msg.append("保存本地<span style=\"color:#3eda67;\">【成功】</span>****");
            } else {
                msg.append("保存本地<span style=\"color:#dc143c;\">【失败】</span>****");
            }
            //添加盘古
            msg.append(panguService.addPanguUser(user, photoCertFile));
            //添加旧koala
            try {
                String koalaId = koalaService.addKoalaUserV1(photoCertFile, userInfo);
                user.setKoalaId(koalaId);
                msg.append("上传koala<span style=\"color:#3eda67;\">【成功】</span>****");
            } catch (Exception e) {
                msg.append("上传koala<span style=\"color:#dc143c;\">【失败】</span>****");
            }
            //添加新koala
            try {
                String koalaId = koalaService.addKoalaUserV2(photoCertFile, userInfo);
                msg.append("灰度上传koala<span style=\"color:#3eda67;\">【成功】</span>****");
            } catch (Exception e) {
                msg.append("灰度上传koala<span style=\"color:#dc143c;\">【失败】</span>****");
            }
        }

        userService.updateUser4Aliyun(user);
        userInfoLocaleService.upsertUserInfo(new UserInfoLocale(user.getUserId(),userInfo.getInfo(),userInfo.getEmail(),userInfo.getBank()));

//        aliyunUserInfoService.updateSavedStatus(userInfo.getId());
        entryService.updateSavedStatus(userInfo.getId());

        return msg.toString();
    }

    /**
     *
     * 从info信息中获取性别
     * @param jsonInfo
     * @return
     */
    private String getGender(JSONObject jsonInfo){
        String gender=jsonInfo.getString("personal_gender");
        return gender==null?"男":(gender.equals("F")?"女":"男");
    }

}
