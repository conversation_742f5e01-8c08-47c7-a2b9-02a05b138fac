package com.megvii.service.contract;

import com.alibaba.fastjson.JSONObject;
import com.megvii.client.ContractRestClient;
import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.FileUtil;
import com.megvii.common.ParameterUtil;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.*;
import com.megvii.mapper.UserContractMapper;
import com.megvii.mapper.UserContractSnapshotMapper;
import com.megvii.service.DhrService;
import com.megvii.service.MMISMegviiConfigService;
import com.megvii.service.MailSendService;
import com.megvii.service.UserService;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/16 15:47
 */
@Service
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties", encoding = "UTF-8")
public class ChangeContractService {

	private Logger logger = LoggerFactory.getLogger(ChangeContractService.class);

	@Value("${SNAPSHOT_FILE_PATH}")
	private String snapFilePath;

	@Value("${CONTRACT_FILE_PATH}")
	private String contractFilePath;

	@Autowired
	private UserContractMapper userContractMapper;

	@Autowired
	private UserContractSnapshotMapper userContractSnapshotMapper;

	@Autowired
	private UserService userService;

	@Autowired
	private DhrRestClient dhrRestClient;

	@Autowired
	private MMISMegviiConfigService mmisMegviiConfigService;

	@Autowired
	private ContractChangelLogService changelLogService;

	@Autowired
	private ContractRestClient contractRestClient;

	@Autowired
	private ContractRestService contractRestService;

	@Autowired
	private ChangeContractBacklogService changeContractBacklogService;

	@Autowired
	private DhrService dhrService;

	@Autowired
	private MailSendService mailSendService;

	/**
	 * 创建合同并且发起签署
	 *
	 * @param snapshot
	 * @return
	 */
	public String createSignContract(UserContractSnapshot snapshot) throws Exception {
		if (snapshot.getFilePath() != null && !snapshot.getFilePath().equals("")) {
			return createContractAndFile(snapshot);
		} else {
			return createContract(snapshot,null);
		}
	}

	/**
	 * 线下签署合同
	 * @param contract
	 * @return
	 */
	public boolean offlineSign(UserContract contract) {
		//更新当前合同状态
		User user = userService.getUserByUserId(contract.getUserId());
		user.setContractStatus(ContractStatus.COMPLETE);
		user.setContractCount(Integer.valueOf(contract.getContractCount()));

		User updater = (User) SecurityUtils.getSubject().getPrincipal();

		contract.setContractType("劳动合同");
		contract.setMemo("线下签署");
		contract.setContractStatus(ContractStatus.COMPLETE);
		contract.setWorkNumber(user.getWorkNumber());
		contract.setSpell(user.getSpell());
		contract.setContractNumber(getContractNumber(user.getUserId(), user.getWorkNumber()));
		contract.setCreatorUserId(updater.getUserId());

		//更新续签状态
		UserChangeContractBacklog backlog = changeContractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
		if (backlog != null) {
			backlog.setStatus(BacklogStatus.SIGN);
			backlog.setNewWorkCity(contract.getNewWorkCity());
			backlog.setNewContract(contract.getNewContract());
			user.setCompany(contract.getNewContract());
			user.setEmployBase(contractRestService.getWorkCityByCode(contract.getNewWorkCity()));
			changeContractBacklogService.updateContractBacklogStatus(backlog);
		}
		userService.updateUser(user);
		userContractMapper.insertUserContract(contract);
		dhrService.contractToDhr(contract);
		changelLogService.insertChangeLog(contract.getUserId(), contract.getContractNumber(), ContractChangeLogType.OFFLINE);
		return true;
	}



	/**
	 * 带附件创建合同
	 * 1.生成body-->通过文件创建合同文档
	 * 2.创建合同-->生成body
	 * 3.添加文件-->创建合同
	 * 4.发起签署
	 *
	 * @param snapshot
	 * @return
	 */
	private String createContractAndFile(UserContractSnapshot snapshot) throws Exception {

		List<File> fileList = new ArrayList<>();
		for (String s : snapshot.getFilePath().split(",")) {
			fileList.add(new File(s));
			//添加附件
//			res = res && contractRestClient.addFileToContract(file, contractId);
		}
		Long documentId = contractRestClient.createContractByFile(fileList);
		return	createContract(snapshot,documentId);
//		//合同body
//		JSONObject data = contractRestService.createChangeContractData(snapshot, true, documentId);
//		//创建合同
//		String contractId = contractRestClient.createContract(data);
//		String documentId1 = contractRestClient.getContractDetail(contractId);
//		JSONObject contractCompony=contractRestService.signByCompany(snapshot,documentId1,contractId);
//		Boolean sign = contractRestClient.signByCompany(contractCompony);
//		return afterSign(snapshot, contractId);
	}


	/**
	 * 无附件直接创建合同
	 *
	 * @param snapshot
	 * @return
	 */
	private String createContract(UserContractSnapshot snapshot,Long document) throws Exception {
		//合同body
		JSONObject data = contractRestService.createChangeContractData(snapshot, true, document);
		//创建并签署合同
		String contractId = contractRestClient.createContract(data);
		if (SignStatus.CHC.equals(snapshot.getSignStatus())) {//如果是合同转签
			String documentId = contractRestClient.getContractDetail(contractId);
			JSONObject contractCompony=contractRestService.signByCompany(snapshot,documentId,contractId);
			Boolean sign = contractRestClient.signByCompany(contractCompony);
		}
		return afterSign(snapshot, contractId);

	}


	/**
	 * 合同成功发起签署后的操作
	 *
	 * @param snapshot
	 * @param contractId
	 * @return
	 */
	@Transactional
	public String afterSign(UserContractSnapshot snapshot, String contractId) throws Exception {
		String userId = snapshot.getUserId();
		//合同表插入数据
		UserContract contract = new UserContract();
		ParameterUtil.fatherToChild(snapshot, contract);
		if(SignStatus.CHC.equals(snapshot.getSignStatus())){
			contract.setCompany(contractRestService.getCompanyNameByCode(snapshot.getNewContract()));
		}
		contract.setContractId(contractId);
		contract.setContractStatus(ContractStatus.SIGNING);
		userContractMapper.insertUserContract(contract);
		//删除快照和附件
		delContractSnapshot(userId);
		//当前合同状态变更为签署中
		User user = userService.getUserByUserId(userId);
		user.setContractStatus(ContractStatus.SIGNING);
		userService.updateUser(user);
		//change log
		changelLogService.insertChangeLog(userId, contract.getContractNumber(), ContractChangeLogType.START_SIGN);

		return "发起签署成功";
	}


	/**
	 * 删除指定用户的合同快照
	 *
	 * @param userId
	 * @return
	 */
	private boolean delContractSnapshot(String userId) {
		UserContractSnapshot oldSnapshot = userContractSnapshotMapper.selectContractSnapshotByUserId(userId);
		if (oldSnapshot == null) {
			return true;
		}

		try {
			//删除附件
			if (oldSnapshot.getFilePath() != null && !oldSnapshot.getFilePath().equals("")) {
				String[] paths = oldSnapshot.getFilePath().split(",");
				for (String path : paths) {
					FileUtil.deleteFile(path);
				}
			}
		} catch (Exception e) {
			logger.error(userId + "删除快照附件异常" + e);
		}

		if (userContractSnapshotMapper.deleteContractSnapshot(oldSnapshot.getId()) < 1) {
			return false;
		}
		return true;
	}

	/**
	 * 获取合同编号
	 * if 存在快照就在快照的基础上+1
	 * else 在最高的合同编号上+1
	 *
	 * @param userId
	 * @param workNumber
	 * @return
	 */
	private String getContractNumber(String userId, String workNumber) {
		UserContractSnapshot snapshot = userContractSnapshotMapper.selectContractSnapshotByUserId(userId);
		if (snapshot != null) {
			String contractNumber = snapshot.getContractNumber();
			Integer number = Integer.valueOf(contractNumber.split("-")[1]);
			if (number < 9) {
				return contractNumber.split("-")[0] + "-0" + (number);
			} else {
				return contractNumber.split("-")[0] + "-" + (number);
			}
		}
		String contractNumber = userContractMapper.selectContractNumberByWorkNumber(workNumber + "-");
		if (contractNumber == null) {
			return workNumber + "-01";
		} else {
			Integer number = Integer.valueOf(contractNumber.split("-")[1]);
			if (number < 9) {
				return contractNumber.split("-")[0] + "-0" + (number + 1);
			} else {
				return contractNumber.split("-")[0] + "-" + (number + 1);
			}
		}
	}


}