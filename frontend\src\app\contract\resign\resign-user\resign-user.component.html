<nz-breadcrumb>
  <nz-breadcrumb-item> 合同管理 </nz-breadcrumb-item>
  <nz-breadcrumb-item>
    <a [routerLink]="[backurl]">离职合同</a>
  </nz-breadcrumb-item>
  <nz-breadcrumb-item> 人员详情 </nz-breadcrumb-item>
</nz-breadcrumb>

<div class="content-block content-item">
  <app-title title="人员详情"></app-title>
  <nz-tabset
    nzSize="large"
    (nzSelectChange)="typeSelChange($event)"
    [nzSelectedIndex]="nzSelectedIndex"
  >
    <nz-tab nzTitle="基本信息">
      <nz-spin [nzSpinning]="isBasicLoading" [nzDelay]="500">
        <form nz-form class="top-20" #basicInfoForm="ngForm">
          <h3 class="left-20"><span class="title-divider"></span>人员基本信息</h3>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">姓名</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="姓名必填">
                <input nz-input disabled [(ngModel)]="spell" name="spell" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">部门</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="部门必填">
                <input nz-input disabled [(ngModel)]="team" name="team" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">工号</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="Input is required">
                <input nz-input disabled [(ngModel)]="workNumber" name="workNumber" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">上级主管</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="上级领导为必填">
                <input nz-input disabled [(ngModel)]="mentor" name="mentor" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">ID</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="ID为必填">
                <input disabled nz-input [(ngModel)]="userId" name="userId" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">HRG</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="hrg为必填">
                <input nz-input disabled [(ngModel)]="hrg" name="hrg" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">离职日期</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <nz-date-picker
                  disabled
                  [(ngModel)]="dismissionDate"
                  name="dismissionDate"
                ></nz-date-picker>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">联系地址</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="联系地址 is required">
                <input nz-input [(ngModel)]="address" name="address" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">入职类型</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="入职类型必填">
                <input nz-input disabled [(ngModel)]="employType" name="employType" required />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">证件编号</nz-form-label>
              <nz-form-control
                [nzValidateStatus]="basicInfoForm.controls['certNo']"
                nzHasFeedback
                [nzSpan]="18"
              >
                <nz-input-group nzCompact>
                  <nz-select
                    [(ngModel)]="certType"
                    style="width: 40%"
                    name="certType"
                    nzPlaceHolder="请选择证件类型"
                  >
                    <nz-option
                      *ngFor="let item of certTypeList"
                      [nzLabel]="item['name']"
                      [nzValue]="item['name']"
                    ></nz-option>
                  </nz-select>
                  <input
                    type="text"
                    required
                    nz-input
                    name="certNo"
                    [(ngModel)]="certNo"
                    style="width: 60%"
                  />
                </nz-input-group>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">岗位名称</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="岗位名称必填">
                <nz-select
                  required
                  nzShowSearch
                  nzAllowClear
                  disabled
                  name="position"
                  [(ngModel)]="position"
                  nzPlaceHolder="请选择岗位"
                >
                  <nz-option
                    *ngFor="let item of positionList"
                    [nzValue]="item['value']"
                    [nzLabel]="item['label']"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">手机号</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="现居住地址为必填">
                <input nz-input [(ngModel)]="cell" name="cell" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">签约公司主体</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="签约公司主体必填">
                <nz-select
                  required
                  disabled
                  name="company"
                  [(ngModel)]="company"
                  nzPlaceHolder="请选择签约公司"
                >
                  <nz-option
                    *ngFor="let item of companyOfOption"
                    [nzValue]="item['code']"
                    [nzLabel]="item['name']"
                  ></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label nzRequired [nzSpan]="6">员工私人邮箱</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="岗位名称必填">
                <input nz-input [(ngModel)]="selfEmail" name="selfEmail" required />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-row [nzGutter]="24" nzType="flex" nzJustify="center">
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">竞业协议</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18" nzErrorTip="最多填写200个字">
                <nz-select
                  required
                  disabled
                  name="competition"
                  [(ngModel)]="competition"
                  nzPlaceHolder="有无竞业协议"
                >
                  <nz-option [nzValue]="0" nzLabel="无"></nz-option>
                  <nz-option [nzValue]="1" nzLabel="有"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col [nzSpan]="11">
              <nz-form-label [nzSpan]="6">备注</nz-form-label>
              <nz-form-control nzHasFeedback [nzSpan]="18">
                <input name="memo" [(ngModel)]="memo" nz-input />
              </nz-form-control>
            </nz-form-item>
          </div>
          <div class="save-create">
            <button
              nz-button
              class="right-20"
              [disabled]="!basicInfoForm.form.valid"
              (click)="editUserIn()"
            >
              保存
            </button>
            <button
              type="submit"
              [disabled]="!basicInfoForm.form.valid"
              (click)="editUserIn()"
              nz-button
              nzType="primary"
            >
              保存并生成合同
            </button>
          </div>
        </form>
      </nz-spin>
      <!-- 结算单 -->
      <nz-spin [nzSpinning]="isBasicInfoLoading">
        <div class="top-20 info-header">
          <app-title title="结算单信息"></app-title>
          <div class="info-open">
            <a *ngIf="isOpen" (click)="openInfo('off')">收起</a>
            <a *ngIf="!isOpen" (click)="openInfo('open')">展开</a>
          </div>
        </div>
        <div class="top-30 info-box">
          <div>
            <span class="info-font">补偿金金额：</span>
            <span *ngIf="lzbc !== ''" class="info-font1">{{ lzbc }}</span>
          </div>
          <div>
            <span class="info-font">备注：</span>
            <span *ngIf="remarks !== ''" class="info-font1">{{ remarks }}</span>
          </div>
        </div>
      </nz-spin>
      <!-- 两个表单的分界处 -->
      <nz-spin *ngIf="isShowDetail" [nzSpinning]="isBasicTwoLoading">
        <form nz-form class="top-20 create-contract" #newContractForm="ngForm">
          <h1 class="top-20 left-20">待签署合同详情</h1>
          <h2 class="top-20 left-20">合同补充</h2>
          <div *ngIf="competition === 1">
            <span class="top-20 create-book">竞业发起通知书</span>
            <div nz-row [nzGutter]="24">
              <nz-form-item nz-col [nzSpan]="10">
                <nz-form-label nzRequired [nzSpan]="8">是否含有特定公司</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="特定公司必填">
                  <nz-select
                    [(ngModel)]="specialCompany"
                    required
                    name="specialCompany"
                    nzPlaceHolder="是否含有特定公司"
                    (ngModelChange)="companyChange($event)"
                  >
                    <nz-option nzLabel="含有特定公司" [nzValue]="1"></nz-option>
                    <nz-option nzLabel="无特定公司" [nzValue]="0"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-row [nzGutter]="24" *ngIf="specialCompany">
              <nz-form-item nz-col [nzSpan]="10">
                <nz-form-label nzRequired [nzSpan]="8">特定公司</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="特定公司必填">
                  <textarea
                    nz-input
                    placeholder="输入特定公司"
                    [(ngModel)]="specialCompanyTxt"
                    name="specialCompanyTxt"
                    [nzAutosize]="{ minRows: 2 }"
                  ></textarea>
                  <!-- <input [(ngModel)]="specialCompanyTxt" name="specialCompanyTxt" nz-input required /> -->
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
          <div *ngIf="shares === 1">
            <span class="top-20 create-book">股份经济受益权协议</span>
            <div nz-row [nzGutter]="24">
              <nz-form-item nz-col [nzSpan]="10">
                <nz-form-label nzRequired [nzSpan]="8">处理方式</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="处理方式必填">
                  <nz-select
                    required
                    name="stockManage"
                    [(ngModel)]="stockManage"
                    nzPlaceHolder="请选择处理方式"
                    (ngModelChange)="stockManageChange($event)"
                  >
                    <nz-option [nzValue]="0" nzLabel="放弃"></nz-option>
                    <nz-option [nzValue]="1" nzLabel="保留"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-row [nzGutter]="24">
              <nz-form-item nz-col [nzSpan]="10">
                <nz-form-label nzRequired [nzSpan]="8">SERS协议签署日期</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="签署日期必填">
                  <nz-date-picker
                    [(ngModel)]="sersSignDate"
                    name="sersSignDate"
                    required
                  ></nz-date-picker>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-row [nzGutter]="24" *ngIf="stockManage">
              <nz-form-item nz-col [nzSpan]="10">
                <nz-form-label nzRequired [nzSpan]="8">笔数选择</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="笔数必填">
                  <nz-select
                    required
                    name="stockNumber"
                    [(ngModel)]="stockNumber"
                    nzPlaceHolder="请选择笔数"
                    (ngModelChange)="stockNumberChange($event)"
                  >
                    <nz-option [nzValue]="1" nzLabel="1"></nz-option>
                    <nz-option [nzValue]="2" nzLabel="2"></nz-option>
                    <nz-option [nzValue]="3" nzLabel="3"></nz-option>
                    <nz-option [nzValue]="4" nzLabel="4"></nz-option>
                    <nz-option [nzValue]="5" nzLabel="5"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
            <div *ngIf="stockManage">
              <div *ngFor="let item of extendVOS; let in = index">
                <div nz-row [nzGutter]="24">
                  <nz-form-item nz-col [nzSpan]="10">
                    <nz-form-label nzRequired [nzSpan]="8"
                      >第{{ item.num }}笔生效日期</nz-form-label
                    >
                    <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="生效日期必填">
                      <nz-date-picker
                        name="effectDate-{{ in }}"
                        [(ngModel)]="item.effectDate"
                        required
                      ></nz-date-picker>
                    </nz-form-control>
                  </nz-form-item>
                </div>
                <div nz-row [nzGutter]="24">
                  <nz-form-item nz-col [nzSpan]="10">
                    <nz-form-label nzRequired [nzSpan]="8"
                      >第{{ item.num }}笔SERS合计份数</nz-form-label
                    >
                    <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="SERS合计份数必填">
                      <input [(ngModel)]="item.sersNum" name="sersNum-{{ in }}" nz-input required />
                    </nz-form-control>
                  </nz-form-item>
                </div>
                <div nz-row [nzGutter]="24">
                  <nz-form-item nz-col [nzSpan]="10">
                    <nz-form-label nzRequired [nzSpan]="8">第{{ item.num }}笔等待期</nz-form-label>
                    <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="等待期必填">
                      <input
                        [(ngModel)]="item.waitTime"
                        name="waitTime-{{ in }}"
                        nz-input
                        required
                      />
                    </nz-form-control>
                  </nz-form-item>
                </div>
              </div>
            </div>
            <div nz-row [nzGutter]="24" *ngIf="stockManage">
              <nz-form-item nz-col [nzSpan]="10">
                <nz-form-label nzRequired [nzSpan]="8">SERS保留总数</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="SERS保留总数必填">
                  <input [(ngModel)]="sersKeepNumber" name="sersKeepNumber" nz-input required />
                </nz-form-control>
              </nz-form-item>
            </div>
            <div nz-row [nzGutter]="24">
              <nz-form-item nz-col [nzSpan]="10">
                <nz-form-label nzRequired [nzSpan]="8">SERS作废总数</nz-form-label>
                <nz-form-control nzHasFeedback [nzSpan]="16" nzErrorTip="SERS作废总数必填">
                  <input
                    [(ngModel)]="sersInvalidNumber"
                    name="sersInvalidNumber"
                    nz-input
                    required
                  />
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>

          <h2 class="top-20 left-20">待签署合同</h2>
          <div>
            <ul class="stampUl">
              <li>离职结算单</li>
              <li>
                解除劳动关系协议书（<span>{{ compensation === 0 ? '无补偿' : '有补偿' }}</span>
                <span>{{ competition === 0 ? '无竞业' : '有竞业' }}</span>
                <span>{{ shareAndStock === 0 ? '无股权' : '有股权' }}</span
                >）
              </li>
              <li>
                离职证明 （<span>{{ competition === 0 ? '无竞业' : '有竞业' }}</span
                >）
              </li>
              <li *ngIf="competition === 1">
                竞业发起通知书（<span>{{ specialCompany === 0 ? '无特定公司' : '有特定公司' }}</span
                >）
              </li>
              <li *ngIf="shares === 1">
                股份经济受益权协议（<span>{{ stockManage === 1 ? '保留' : '放弃' }}</span
                >）
              </li>
            </ul>
          </div>
          <nz-form-item>
            <div class="save-create">
              <button
                type="submit"
                [disabled]="!newContractForm.form.valid"
                nz-button
                class="right-20"
                (click)="saveNoContract()"
              >
                保存
              </button>
              <button
                nz-button
                [nzLoading]="loading"
                nzType="primary"
                [disabled]="!newContractForm.form.valid"
                (click)="isShowConfirm()"
              >
                签署
              </button>
            </div>
          </nz-form-item>
        </form>
      </nz-spin>
    </nz-tab>
    <nz-tab nzTitle="合同历史"></nz-tab>
    <nz-tab nzTitle="操作历史"></nz-tab>
  </nz-tabset>
</div>
