package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.Office365Service;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/11/5 15:50
 */
@Service
public class Office365SyncGroupVisibilityService extends BaseOperatorService {

    @Autowired
    private Office365Service office365Service;

    @Autowired
    private Office365SyncGroupVisibilityService office365SyncGroupVisibilityService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return office365SyncGroupVisibilityService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = office365Service.syncOfficeVisibility();
        return new OperateResult(1,msg);
    }



}
