package com.megvii.entity.opdb;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-22
 */
@Data
public class UserDimissionContractBacklogPO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 离职结算单id
     */
    private String dismissionId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 离职日期
     */
    private Date dismissionDate;

    /**
     * 工号
     */
    private String workNumber;

    /**
     * 账号
     */
    private String account;

    /**
     * 离职类型
     */
    private String leaveType;

    /**
     * 一级部门
     */
    private String topTeam;

    /**
     * 社保截止月
     */
    private String socialLastTime;

    /**
     * 公积金截止月
     */
    private String accumulationLastTime;

    /**
     * 是否有补偿金
     */
    private Integer compensation;

    /**
     * 是否有股份
     */
    private Integer shares;

    /**
     * 是否有竞业协议
     */
    private Integer competition;

    /**
     * 状态
     */
    private String status;

    /**
     * 流程节点 已完成：-1 0,1,2,3 对应tab
     */
    private String flowStatus;

    /**
     * 解除劳动关系协议书
     */
    private String terminationAgreement;

    /**
     * 离职证明
     */
    private String resignationCertificate;

    /**
     * 薪酬结算单
     */
    private String salaryStatement;

    /**
     * 履行竞业限制义务通知书
     */
    private String competitionNotice;

    /**
     * 经济受益权保留协议
     */
    private String economicAgreement;

    /**
     * 私人邮箱
     */
    private String selfEmail;

    /**
     * 合同文件包
     */
    private String contractZip;

    private Date createTime;

    private Date deletedAt;

    /**
     * 删除操作人
     */
    private String deletedUser;
    /**
     * 离职人公司
     */
    private String company;
    /**
     * 竞业时长：月
     */
    private String competitionDuration;
    /**
     * 竞业开始日期
     */
    private Date competitionStart;
    /**
     * 竞业结束日期
     */
    private Date competitionEnd;

    /**
     * 股权管理方式
     */
    private Integer stockManage;
    /**
     * 是否含有特定公司
     */
    private Integer specialCompany;
    /*
    * 初始合同开始时间
    * */
    private Date initialStartTime;

    /*
     * 最新合同创建日期 contract_create_time
     */
    private Date contractCreateTime;

    /*
     * 标签标识字段（ “即将过期”、“已过期”）
     */
    @TableField(exist=false)
    private String dimissionTag;

}
