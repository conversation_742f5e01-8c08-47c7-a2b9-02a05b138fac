package com.megvii;


import com.megvii.client.GitRestClient;
import com.megvii.service.GitService;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/10/30 11:09
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestGit {

    @Autowired
    private GitRestClient gitRestClient;

    @Autowired
    private GitService gitService;

    @Test
    public void testGit() {

        try {
            gitService.syncGroupMember();
//            String id = gitService.getActiveGitUserId("liuyang08", "https://git-core.megvii-inc.com/api/v4", "dJhsw_zh8MhE7W7RhB2P");
//            gitRestClient.disableGit(id, "https://git-core.megvii-inc.com/api/v4", "dJhsw_zh8MhE7W7RhB2P");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testGet() {
        String gitUrl = "https://git-v.megvii-inc.com/api/v4";
        String gitToken = "yDdmvBDB_TMP4_1dXX8h";

//        String gitUrl = "https://git-pd.megvii-inc.com/api/v4";
//        String gitToken = "19_Df6TwnXBNw1nZa_HC";




        Map map = gitRestClient.getGitUserInfo("jiangyuhong", gitUrl, gitToken);

        if (map == null) {

        } else {
            gitRestClient.addGitUser(gitUrl, gitToken, "767", map.get("id").toString(), "20");
             gitRestClient.delGitUser(gitUrl, gitToken, "767", map.get("id").toString());
        }








//        List<String> users = gitRestClient.getGitUsers("https://git-pd.megvii-inc.com/api/v4", "1444", "19_Df6TwnXBNw1nZa_HC");
//
//        List<String> coreUsers = gitRestClient.getGitUsers("https://git-core.megvii-inc.com/api/v4", "1164", "dJhsw_zh8MhE7W7RhB2P");


        System.out.println(1);

    }



}
