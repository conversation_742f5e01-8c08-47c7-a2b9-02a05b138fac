package com.megvii.mapper;

import com.megvii.entity.opdb.contract.UserProveContractBacklog;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public interface UserProveContactBacklogMapper {

    int insertProveLog(UserProveContractBacklog proveLog);

    int updateUserProveContractBacklog(UserProveContractBacklog proveLog);

    UserProveContractBacklog selectProveLogByContractId(String contractId);

    List<UserProveContractBacklog> selectProveLogByStatus(String status);

    UserProveContractBacklog selectProveLogByDhrId(String dhrId);
}
