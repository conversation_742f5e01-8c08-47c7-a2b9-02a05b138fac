<nz-breadcrumb>
  <nz-breadcrumb-item>
    权限管理
  </nz-breadcrumb-item>
  <nz-breadcrumb-item>
    人员管理
  </nz-breadcrumb-item>
</nz-breadcrumb>
<div class="content-block content-item">
  <app-title title="用户管理"></app-title>
  <div class="search-container bottom-30">
    <button class="pull-right" nzType="primary" nz-button (click)="onAdd()">
      <i nz-icon nzType="plus" nzTheme="outline"></i>
      新建
    </button>
  </div>
  <nz-table
    #listTable
    [nzData]="userList"
    [nzShowPagination]="false"
    [nzFrontPagination]="false"
    class="dept-table"
    [nzLoading]="loading"
    [nzShowTotal]="totalTemplate"
    [nzTotal]="total"
  >
    <thead>
      <tr>
        <th nzWidth="10%">工号</th>
        <th nzWidth="10%">userId</th>
        <th nzWidth="10%">姓名</th>
        <th nzWidth="50%">所属部门</th>
        <th nzWidth="20%">操作</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of listTable.data">
        <td>
          {{ data['workNumber'] }}
        </td>
        <td>
          {{ data['userId'] }}
        </td>
        <td>
          {{ data['spell'] }}
        </td>
        <td>
          {{ data['team'] }}
        </td>
        <td class="table-oprt">
          <a href="javascript:void(0);" (click)="onUpdate(data)">分配角色</a>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <ng-template #totalTemplate let-total> 总共 {{ total }} 条</ng-template>
</div>

<nz-modal
  [(nzVisible)]="isAddModalVisible"
  [nzOkLoading]="isAdding"
  [nzTitle]="isAdd ? '新增人员' : '修改人员权限'"
  (nzOnCancel)="addCancel()"
  nzClassName="form-modal"
  (nzOnOk)="addCommit()"
>
  <div class="modal-item">
    <span class="ant-form-item-required right-20">姓名:</span>
    <input
      *ngIf="!isAdd"
      type="text"
      [disabled]="true"
      nz-input
      placeholder="请输入姓名"
      [(ngModel)]="this.selUser['userId']"
    />
    <nz-select
      *ngIf="isAdd"
      nzShowSearch
      nzServerSearch
      nzPlaceHolder="搜索账号"
      [(ngModel)]="selUserId"
      nzAutoClearSearchValue
      [nzShowArrow]="false"
      [nzFilterOption]="nzFilterOption"
      (nzOnSearch)="searchUser($event)"
    >
      <nz-option
        *ngFor="let o of listOfUserOption"
        [nzLabel]="o.userId"
        [nzValue]="o.userId"
      >
      </nz-option>
    </nz-select>
  </div>
  <div class="modal-item">
    <span class="ant-form-item-required right-20">角色:</span>
    <nz-select
      [(ngModel)]="selRoleList"
      nzMode="tags"
      nzPlaceHolder="Please select"
    >
      <nz-option
        *ngFor="let option of roleList"
        [nzLabel]="option.roleName"
        [nzValue]="option.roleCode"
      ></nz-option>
    </nz-select>
  </div>
</nz-modal>
