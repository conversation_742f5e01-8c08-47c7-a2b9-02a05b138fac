package com.megvii.service;

import com.alibaba.fastjson.JSONObject;
import com.megvii.client.AliMailClient;
import com.megvii.client.Office365Client;
import com.megvii.mapper.OfficeGroupMemberWhiteListMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/2/28 17:10
 */
@Service
public class SyncOfficeService {

    Logger logger= LoggerFactory.getLogger(SyncOfficeService.class);

    @Autowired
    private GroupService groupService;

    @Autowired
    private AliGroupService  aliGroupService;

    @Autowired
    private LdapService ldapService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private Office365Client office365Client;

    @Autowired
    private AliMailClient aliMailClient;

    @Autowired
    private OfficeGroupMemberWhiteListMapper officeGroupMemberWhiteListMapper;


    /**
     * 同步全量office365组
     * 1.获取全部office组的map
     * 2.获取全部ldap组的list
     * 3.获取全部安全组的list
     *   获取跳过的组
     * 4.从map中去除ldap存在的组
     * 5.从map中去除安全组
     *   去除跳过的组
     * 6.移除wzn组
     * 7.创建ldap组
     * @return
     */
    public String syncAllOfficeGroup() {
        StringBuilder msg = new StringBuilder();

        Map<String, JSONObject> officeMap = null;
        try {
            officeMap = aliGroupService.getAllOfficeMap();
        } catch (Exception e) {
            logger.error("获取office group map异常:" + e.getMessage());
            return "获取office group map异常";
        }

        List<String> ldapGroupList = groupService.getLdapGroupList();
        List<String> secureGroupList = groupService.selectAllSecureGroup();
        List<String> skipGroupList = groupService.selectSkipSyncGroup();

        //过滤不需要同步的组
        Map<String, JSONObject> newMap = officeMap.entrySet().stream().filter(e->!ldapGroupList.contains(e.getKey())).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));
        Map<String, JSONObject> newMap2 = newMap.entrySet().stream().filter(e->!secureGroupList.contains(e.getKey())).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));
        Map<String, JSONObject> newMap3 = newMap2.entrySet().stream().filter(e->!skipGroupList.contains(e.getKey())).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));

        if (newMap3.size() == 0) {
            return "没有需要增加的group";
        }

        for (Map.Entry<String, JSONObject> entry : newMap3.entrySet()) {
            JSONObject data = entry.getValue();
            msg.append(ldapService.createLdapGroup(data.getString("address").split("@")[0].toLowerCase(), data.getString("name"), data.getString("description")));
            msg.append("</br>");
        }

//        mailSendService.sendMail("同步office365组", msg.toString(), "<EMAIL>,<EMAIL>");
        return msg.toString();

    }





    /**
     * 全量同步office组人员
     * @return
     */
    public String syncAllOfficeGroupMembers() {
        StringBuffer msg = new StringBuffer();

        Map<String, JSONObject> officeMap = null;
        try {
            officeMap = aliGroupService.getAllOfficeMap();
        } catch (Exception e) {
            logger.error("获取office group map异常:" + e.getMessage());
            return "获取office group map异常";
        }

        List<String> secureGroupList = groupService.selectAllSecureGroup();
        List<String> skipGroupList = groupService.selectSkipSyncGroup();

        //移除安全组和特殊组
        Map<String, JSONObject> newMap = officeMap.entrySet().stream().filter(e->!secureGroupList.contains(e.getKey())).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));
        Map<String, JSONObject> newMap2 = newMap.entrySet().stream().filter(e->!skipGroupList.contains(e.getKey())).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));

        try {
            //开启子线程个数
            Integer threadCount = 4;
            CountDownLatch countDownLatch = new CountDownLatch(threadCount);
            for (int i = 0; i < threadCount; i++) {
                Integer temp = i;
                //将大map拆分为线程个数的小map
                Map<String, JSONObject> threadMap = newMap2.entrySet().stream().filter(e -> (Math.abs(e.getKey().hashCode() % threadCount) == temp)).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));

                SyncOfficeGroupMembers syncOfficeGroupMembers = new SyncOfficeGroupMembers();
                syncOfficeGroupMembers.setMap(threadMap);
                syncOfficeGroupMembers.setMsg(msg);
                syncOfficeGroupMembers.setCountDownLatch(countDownLatch);

                Thread thread = new Thread(syncOfficeGroupMembers, "SyncOfficeMemberThread");
                thread.start();
            }
            //阻塞主线程
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("同步office group members异常:" + e.getMessage());
            return "同步office group members异常";
        }

//        for (Map.Entry<String, JSONObject> entry : newMap2.entrySet()) {
//            //获取office组成员
//           List<String> officeUsers = office365Client.getGroupMemberById(entry.getValue().getString("id"));
//           if (officeUsers == null) {
//               continue;
//           }
//
//           officeUsers.removeAll(officeGroupMemberWhiteListMapper.selectAllMember());
//
//           List<String> ldapUsers = ldapService.getGroupMembers(entry.getKey());
//           if (ldapUsers == null ) {
//               logger.error("查询ldap组" + entry.getKey() + "成员异常：");
//               continue;
//           }
//
//           msg.append(addLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers));
//           msg.append(deleteLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers));
//
//        }

        if (msg.toString().equals("")) {
            return "没有发生变化";
        }

//        mailSendService.sendMail("同步office人员提醒", msg.toString(), "<EMAIL>");
        return msg.toString();
    }


    /**
     * 根据组名同步office组人员
     * @return
     */
    public String syncGroupMembersByName(String groupName) {
        StringBuffer msg = new StringBuffer();

        Map<String, JSONObject> officeMap = null;
        try {
            officeMap = aliGroupService.getAllOfficeMap();
        } catch (Exception e) {
            logger.error("获取office group map异常:" + e.getMessage());
            return "获取office group map异常";
        }

        Map<String, JSONObject> newMap2 = officeMap.entrySet().stream().filter(e->e.getKey().equals(groupName)).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));
        System.out.println("================");
        System.out.println(newMap2.size());
        System.out.println(newMap2);

        try {
            //开启子线程个数
            Integer threadCount = 4;
            CountDownLatch countDownLatch = new CountDownLatch(threadCount);
            for (int i = 0; i < threadCount; i++) {
                Integer temp = i;
                //将大map拆分为线程个数的小map
                Map<String, JSONObject> threadMap = newMap2.entrySet().stream().filter(e -> (Math.abs(e.getKey().hashCode() % threadCount) == temp)).collect(Collectors.toMap((e) -> (String) e.getKey(), (e) -> e.getValue()));

                SyncOfficeGroupMembers syncOfficeGroupMembers = new SyncOfficeGroupMembers();
                syncOfficeGroupMembers.setMap(threadMap);
                syncOfficeGroupMembers.setMsg(msg);
                syncOfficeGroupMembers.setCountDownLatch(countDownLatch);

                Thread thread = new Thread(syncOfficeGroupMembers, "SyncOfficeMemberThread");
                thread.start();
            }
            //阻塞主线程
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("同步office group members异常:" + e.getMessage());
            return "同步office group members异常";
        }

//        for (Map.Entry<String, JSONObject> entry : newMap2.entrySet()) {
//            //获取office组成员
//           List<String> officeUsers = office365Client.getGroupMemberById(entry.getValue().getString("id"));
//           if (officeUsers == null) {
//               continue;
//           }
//
//           officeUsers.removeAll(officeGroupMemberWhiteListMapper.selectAllMember());
//
//           List<String> ldapUsers = ldapService.getGroupMembers(entry.getKey());
//           if (ldapUsers == null ) {
//               logger.error("查询ldap组" + entry.getKey() + "成员异常：");
//               continue;
//           }
//
//           msg.append(addLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers));
//           msg.append(deleteLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers));
//
//        }

        if (msg.toString().equals("")) {
            return "没有发生变化";
        }

//        mailSendService.sendMail("同步office人员提醒", msg.toString(), "<EMAIL>");
        return msg.toString();
    }


    /**
     * office人员中去掉ldap已存在的，剩余的为需要添加的人员
     * @param group        组名称
     * @param officeUsers  office人员list
     * @param ldapUsers    ldap人员list
     * @return
     */
    private String addLdapGroupUsers(String group, List<String> officeUsers, List<String> ldapUsers) {
        StringBuilder msg = new StringBuilder();
        List<String> addUsers = new ArrayList<>();
        addUsers.addAll(officeUsers);
        //需要增加的人员
        addUsers.removeAll(ldapUsers);

        if (addUsers.size() == 0) {
            return "";
        }

        for (String officeUser : addUsers) {
            try {
                String userDn = ldapService.getRealUserDn(officeUser);

                ldapService.addMemberToGroupByUserDn(group, userDn);
//                msg.append(group + "添加" + officeUser + "<span style=\"color:#3eda67;\">【成功】</span></br>");
            } catch (Exception e) {
//                logger.error(group + "添加" + officeUser + "<span style=\"color:#dc143c;\">【异常】</span>" + e);
//                msg.append(group + "添加" + officeUser + "<span style=\"color:#dc143c;\">【异常】</span></br>");
                continue;
            }

        }
        return msg.toString();

    }

    /**
     * 从ldap中去掉office已存在的，剩余的为需要删除的
     * 若需要删除的大于10人，return  听说office接口偶发返回人员为空
     * @param group
     * @param officeUsers
     * @param ldapUsers
     * @return
     */
    private String deleteLdapGroupUsers(String group, List<String> officeUsers, List<String> ldapUsers) {
        StringBuilder msg = new StringBuilder();
        List<String> delUsers = new ArrayList<>();
        delUsers.addAll(ldapUsers);
        //需要移除的人员
        delUsers.removeAll(officeUsers);

        if (delUsers.size() > 10) {
            return group + "删除人数过多:" + delUsers.toString() + "</br>";
        }

        if (delUsers.size() == 0) {
            return "";
        }

        for (String ldapUser : delUsers) {
            msg.append(ldapService.deleteGroupUser(ldapUser, group) + "</br>");
        }
        return msg.toString();

    }


    /**
     *
     * 多个线程执行同步任务
     */
    class SyncOfficeGroupMembers implements Runnable {

        //需要执行的group
        private Map<String, JSONObject> map;
        //返回msg
        private StringBuffer msg;
        //线程完成倒数
        private CountDownLatch countDownLatch;

        public void setMap(Map<String, JSONObject> map) {
            this.map = map;
        }

        public void setMsg(StringBuffer msg) {
            this.msg = msg;
        }

        public void setCountDownLatch(CountDownLatch countDownLatch) {
            this.countDownLatch = countDownLatch;
        }

        @Override
        public void run() {

            try {
                for (Map.Entry<String, JSONObject> entry : map.entrySet()) {
                    //获取office组成员
//                    List<String> officeUsers = office365Client.getGroupMemberById(entry.getValue().getString("id"));
                    List<String> officeUsers = aliMailClient.getMailGroupMembers(entry.getValue().getString("address"));
                    if (officeUsers == null) {
                        continue;
                    }

                    officeUsers.removeAll(officeGroupMemberWhiteListMapper.selectAllMember());
                    List<String> ldapUsers = ldapService.getGroupMembers(entry.getKey());
                    if (ldapUsers == null) {
                        logger.error("查询ldap组" + entry.getKey() + "成员异常：");
                        continue;
                    }

//                    msg.append(addLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers));
//                    msg.append(deleteLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers));
                    addLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers);
                    deleteLdapGroupUsers(entry.getKey(), officeUsers, ldapUsers);
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            } finally {
                if (null != countDownLatch) {
                    //计数
                    countDownLatch.countDown();
                }
            }
        }

    }




}
