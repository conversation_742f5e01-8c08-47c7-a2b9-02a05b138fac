<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserContractSnapshotMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.contract.UserContractSnapshot">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="work_number" property="workNumber"/>
        <result column="spell" property="spell"/>
        <result column="contract_number" property="contractNumber"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_count" property="contractCount"/>
        <result column="contract_attribute" property="contractAttribute"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="effect_date" property="effectDate"/>
        <result column="probation_start_date" property="probationStartDate"/>
        <result column="probation_end_date" property="probationEndDate"/>
        <result column="creator_user_id" property="creatorUserId"/>
        <result column="updator_user_id" property="updatorUserId"/>
        <result column="company" property="company"/>
        <result column="position" property="position"/>
        <result column="employ_base" property="employBase"/>
        <result column="contract_cell" property="contractCell"/>
        <result column="cert_type" property="certType"/>
        <result column="cert_no" property="certNo"/>
        <result column="address" property="address"/>
        <result column="memo" property="memo"/>
        <result column="file_path" property="filePath"/>
        <result column="sign_status" property="signStatus"/>
        <result column="contract" property="contract"/>
        <result column="new_contract" property="newContract"/>
        <result column="con_begin_date" property="conBeginDate"/>
        <result column="con_end_date" property="conEndDate"/>
        <result column="new_con_begin_date" property="newConBeginDate"/>
        <result column="new_con_end_date" property="newConEndDate"/>
        <result column="work_city" property="workCity"/>
        <result column="new_work_city" property="newWorkCity"/>
        <result column="offer_path" property="offerPath"/>
        <result column="internship_allowance" property="internshipAllowance"/>
        <result column="school" property="school" />
        <result column="student_number" property="studentNumber" />
        <result column="admission_time" property="admissionTime" />
        <result column="graduation_time" property="graduationTime" />
    </resultMap>

    <insert id="insertContractSnapshot" parameterType="com.megvii.entity.opdb.contract.UserContractSnapshot" >
        INSERT INTO `user_contract_snapshot` (
	        `id`,
  	        `user_id` ,
  	        `work_number` ,
 	        `spell` ,
	        `contract_number` ,
  	        `contract_type` ,
  	        `contract_count` ,
  	        `contract_attribute` ,
  	        `start_date`  ,
  	        `end_date`  ,
  	        `effect_date`  ,
  	        `probation_start_date` ,
  	        `probation_end_date` ,
  	        `creator_user_id` ,
  	        `updator_user_id` ,
  	        `company` ,
  	        `position` ,
  	        `employ_base` ,
  	        `contract_cell` ,
  	        `cert_type` ,
  	        `cert_no` ,
  	        `address` ,
  	        `memo` ,
  	        `sign_status`,
  	        `contract`,
  	        `new_contract`,
  	        `con_begin_date`,
  	        `con_end_date`,
  	        `new_con_begin_date`,
  	        `new_con_end_date`,
  	        `work_city`,
  	        `new_work_city`,
  	        `offer_path`,
  	        `internship_allowance`,
            `school`,
            `student_number`,
            `admission_time`,
            `graduation_time`
        )
        VALUES
	    (
		    0,
		    #{userId},
		    #{workNumber},
	    	#{spell},
		    #{contractNumber},
		    #{contractType},
		    #{contractCount},
		    #{contractAttribute},
		    #{startDate},
		    #{endDate},
		    #{effectDate},
		    #{probationStartDate},
		    #{probationEndDate},
		    #{creatorUserId},
		    #{updatorUserId},
		    #{company},
		    #{position},
		    #{employBase},
		    #{contractCell},
		    #{certType},
		    #{certNo},
		    #{address},
		    #{memo},
		    #{signStatus},
		    #{contract},
		    #{newContract},
		    #{conBeginDate},
		    #{conEndDate},
		    #{newConBeginDate},
		    #{newConEndDate},
		    #{workCity},
	    	#{newWorkCity},
	    	#{offerPath},
	    	#{internshipAllowance},
	    	#{school},
	    	#{studentNumber},
	    	#{admissionTime},
	    	#{graduationTime}
	    );
    </insert>

    <update id="updateContractSnapshot" parameterType="com.megvii.entity.opdb.contract.UserContractSnapshot">
        update user_contract_snapshot
        <set>
             <if test="spell != null ">
                spell = #{spell},
            </if>
            <if test="contractType != null ">
                contract_type = #{contractType},
            </if>
            <if test="contractCount != null ">
                contract_count = #{contractCount},
            </if>
            <if test="contractAttribute != null ">
                contract_attribute = #{contractAttribute},
            </if>
            <if test="startDate != null ">
                start_date = #{startDate},
            </if>
            <if test="endDate != null ">
                end_date = #{endDate},
            </if>
            <if test="endDate == null ">
                end_date = null,
            </if>
            <if test="effectDate != null ">
                effect_date = #{effectDate},
            </if>
            <if test="probationStartDate != null ">
                probation_start_date = #{probationStartDate},
            </if>
            <if test="probationStartDate == null ">
                probation_start_date = null,
            </if>
            <if test="probationEndDate != null ">
                probation_end_date = #{probationEndDate},
            </if>
            <if test="probationEndDate == null ">
                probation_end_date = null,
            </if>
            <if test="updatorUserId != null ">
                updator_user_id = #{updatorUserId},
            </if>
            <if test="company != null ">
                company = #{company},
            </if>
            <if test="position != null ">
                position = #{position},
            </if>
            <if test="employBase != null ">
                employ_base = #{employBase},
            </if>
            <if test="contractCell != null ">
                contract_cell = #{contractCell},
            </if>
            <if test="certType != null ">
                cert_type = #{certType},
            </if>
            <if test="certNo != null ">
                cert_no = #{certNo},
            </if>
            <if test="address != null ">
                address = #{address},
            </if>
            <if test="memo != null ">
                memo = #{memo},
            </if>
            <if test="filePath != null ">
                file_path = #{filePath},
            </if>
            <if test="contract != null ">
                contract = #{contract},
            </if>
            <if test="newContract != null ">
                new_contract = #{newContract},
            </if>
            <if test="conBeginDate != null ">
                con_begin_date = #{conBeginDate},
            </if>
            <if test="conEndDate != null ">
                con_end_date = #{conEndDate},
            </if>
            <if test="newConBeginDate != null ">
                new_con_begin_date = #{newConBeginDate},
            </if>
            <if test="newConEndDate != null ">
                new_con_end_date = #{newConEndDate},
            </if>
            <if test="workCity != null ">
                work_city = #{workCity},
            </if>
            <if test="newWorkCity != null ">
                new_work_city = #{newWorkCity},
            </if>
            <if test="school != null ">
                school = #{school},
            </if>
            <if test="studentNumber != null ">
                student_number = #{studentNumber},
            </if>
            <if test="admissionTime != null ">
                admission_time = #{admissionTime},
            </if>
            <if test="graduationTime != null ">
                graduation_time = #{graduationTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteContractSnapshot" parameterType="int" >
        DELETE FROM user_contract_snapshot WHERE id = #{id}
    </delete>

    <select id="selectContractSnapshotByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract_snapshot WHERE user_id = #{userId}
    </select>

    <select id="selectContractSnapshotById" resultMap="BaseResultMap" >
        select * from user_contract_snapshot WHERE id = #{id}
    </select>


</mapper>
