package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.mapper.DataKeyConfigMapper;
import com.megvii.service.SyncZTDataService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/10/26 15:41
 */
@Service
public class SyncZTDataOperatorService extends BaseOperatorService  {

    @Autowired
    private DataKeyConfigMapper dataKeyConfigMapper;

    @Autowired
    private SyncZTDataService syncZTDataService;

    @Autowired
    private SyncZTDataOperatorService syncZTDataOperatorService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        List<String> keyList = dataKeyConfigMapper.selectAllKey();
        redisTemplate.opsForValue().set("mitexecutor_dhr_CN_KEY", keyList);

        StringBuilder msg=new StringBuilder();
        for (String key : keyList) {
            OperateResult result = syncZTDataOperatorService.operateOnce(paraMap, key);
            msg.append(result.getMsg()+"<br/>");
        }
        msg.append(syncZTDataService.syncDhrPositionFromZT());
        return new OperateResult(1,msg.toString());
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String key = (String) params[0];
        if (key != null) {
            String result = syncZTDataService.syncDhrDataFromZT(key);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1,  "key信息不存在");
        }
    }

}
