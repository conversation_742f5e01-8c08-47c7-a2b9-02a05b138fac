package com.megvii.aop;

import com.alibaba.fastjson.JSONArray;
import java.util.Arrays;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 对主操参数、执行时间进行统计
 */
@Component
@Aspect
@Order(1)
public class TimeRecordInterceptor {
    private Logger logger= LoggerFactory.getLogger(TimeRecordInterceptor.class);


    /**
     * 申明主操作为aop切入点
     */
    @Pointcut("execution(* com.megvii.*.operateMain(..))")
    public void logPointcut(){}


    //将执行时间进行日志输出
    @Around("logPointcut()" )
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable{
        long start = System.currentTimeMillis();
        try{
            Object result = joinPoint.proceed();
            long time = System.currentTimeMillis()-start;
            //请求时间进行日志记录
            logger.info("method:"+joinPoint.getSignature().getName()+"   args:"+new JSONArray(Arrays.asList(joinPoint.getArgs())) +",time:"+time);
            return result;
        }catch (Throwable e){
            long end = System.currentTimeMillis();
            logger.info("method:"+joinPoint.getSignature().getName()+"   args:"+new JSONArray(Arrays.asList(joinPoint.getArgs()))+",time:"+(end-start));
            throw  e;
        }
    }
}
