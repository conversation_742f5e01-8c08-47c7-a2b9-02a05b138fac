package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2021/7/20 18:35
 */
@Component
@PropertySource(value = "classpath:megin.${spring.profiles.active}.properties",encoding = "UTF-8")
public class MeginRestClient {

	Logger logger = LoggerFactory.getLogger(MeginRestClient.class);

	@Value("${MEGIN_HOST}")
    private String host;

	@Value("${MEGIN_DIMISSION_URL}")
    private String dimissionUrl;

	@Autowired
	private RestTemplate restTemplate;

	/**
	 * 禁用megin账号
	 * @param userId
	 * @return
	 */
	public boolean disableMeginUser(String userId) {
		try {
			String url = host + dimissionUrl + userId;

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);

			HttpEntity httpEntity = new HttpEntity(null, headers);

			ResponseEntity<JSONObject> result = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
			if (result.getStatusCode().equals(HttpStatus.OK) && "success".equals(result.getBody().getString("msg"))) {
				return true;
			}
		} catch (Exception e) {
			logger.error(userId + "禁用megin异常");
		}
		return false;
	}



}
