package com.megvii.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;


@Component
@PropertySource(value = "classpath:erp.${spring.profiles.active}.properties",encoding = "UTF-8")
public class ErpRestClient {
    Logger logger =  LoggerFactory.getLogger(ErpRestClient.class);

    @Value("${ERP_HOST_URL}")
    private String hostURL;

    @Value("${ERP_ACCTID}")
    private String acctId;

    @Value("${ERP_USERNAME}")
    private String username;

    @Value("${ERP_PASSWORD}")
    private String password;

    @Value("${ERP_LCID}")
    private String lcid;

    @Value("${ERP_COOKIE_URL}")
    private String CookieURL;

    @Value("${ERP_DISABLE_USER}")
    private String disableUserUrl;

    @Autowired
    private RestTemplate restTemplate;


    /**
     * 组装Erp包体
     */
    private JSONObject getErpJson(){
        JSONObject erp = new JSONObject();
        erp.put("acctID", acctId);
        erp.put("username", username);
        erp.put("password", password);
        erp.put("lcid", lcid);

        return erp;
    }

    /**
     * Erp 鉴权
     */
    private String getCookie() throws Exception {
        String url =  hostURL + CookieURL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject data = getErpJson();
        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);
        if(responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
            JSONObject result = JSON.parseObject(responseEntity.getBody());
            String cookie = result.getJSONObject("Context").getString("SessionId");
            return cookie;
        }
        throw new Exception("获取erp cookie异常");
    }

    /**
     * Erp 禁用用户
     * @param workNumber
     * @return boolean
     */
    public boolean erpUserDisable(String workNumber){
        try {
            String url = hostURL + disableUserUrl;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("Cookie", getCookie());

            JSONObject data = new JSONObject();
            JSONObject data2 = new JSONObject();
            data2.put("userid", workNumber);
            data.put("data", data2);
            HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString(), headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);

            if (responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
                JSONObject result = JSON.parseObject(responseEntity.getBody());
                if (result.getString("IsSuccess") != null) {
                    return true;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return false;
    }

}


