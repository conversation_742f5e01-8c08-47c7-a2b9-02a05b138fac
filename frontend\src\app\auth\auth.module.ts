import { NgModule } from '@angular/core'
import { CommonModule } from '@angular/common'
import { AuthComponent } from './auth.component'
import { UserComponent } from './user/user.component'
import { AuthRoutingModule } from './auth-routing.module'
import { SharedModule } from '../shared.module'
import { SendRequest } from '../core/services'
import { AuthService } from './auth.service'

@NgModule({
  declarations: [AuthComponent, UserComponent],
  imports: [CommonModule, SharedModule, AuthRoutingModule],
  providers: [SendRequest, AuthService]
})
export class AuthModule {}
