package com.megvii.controller.api;

import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.contract.ContractStatus;
import com.megvii.entity.opdb.contract.UserContract;
import com.megvii.entity.opdb.contract.UserContractSnapshot;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 19:05
 */
@Data
public class ContractRCDAVO {

	private int id;
	private String userId;
	private String startDate;
	private String endDate;
	private String company;
	private String signStatus;
	private String userContractFile;
	private String contractId;
	private String contractStatus;
	public static List<ContractRCDAVO> toVOs(List<UserContract> contracts) {
		List<ContractRCDAVO> vos = new ArrayList<>();
		for (UserContract contract : contracts) {
			vos.add(contractToVO(contract));
		}
		return vos;
	}

	private static ContractRCDAVO contractToVO(UserContract contract) {
		ContractRCDAVO vo = new ContractRCDAVO();
		vo.setId(contract.getId());
		vo.setUserId(contract.getUserId());
		vo.setStartDate(contract.getStartDate()!=null?DateTimeUtil.date2String(contract.getStartDate()):null);
		vo.setEndDate(contract.getEndDate()!=null?DateTimeUtil.date2String(contract.getEndDate()):null);
		vo.setCompany(contract.getCompany());
		vo.setSignStatus(contract.getSignStatus());
		vo.setUserContractFile(contract.getUserContractFile());
		vo.setContractId(contract.getContractId());
		vo.setContractStatus(contract.getContractStatus());
		return vo;
	}

}
