<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.EntryDateMapper">


    <select id="selectEntryDate" resultType="java.time.LocalDateTime" parameterType="java.time.LocalDateTime">
        select date from MMIS_entry_date where date>#{date} and type = 'white'
    </select>

    <select id="selectBlackEntryDate" resultType="java.time.LocalDateTime" parameterType="java.time.LocalDateTime">
        select date from MMIS_entry_date where date>#{date} and type = 'black'
    </select>

</mapper>