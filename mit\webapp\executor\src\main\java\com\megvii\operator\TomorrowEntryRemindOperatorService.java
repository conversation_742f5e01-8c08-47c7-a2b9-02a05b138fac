package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.RemindService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/2/7 16:45
 */
@Service
public class TomorrowEntryRemindOperatorService extends BaseOperatorService {

    @Autowired
    private TomorrowEntryRemindOperatorService tomorrowEntryRemindOperatorService;

    @Autowired
    private RemindService remindService;


    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object date = paraMap.get("date");

        OperateResult result = tomorrowEntryRemindOperatorService.operateOnce(paraMap, date);

        return new OperateResult(1, result.getMsg());
    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        String date = (String) params[0];
        String result = remindService.sendTomorrowEntryRemind(date);
        return new OperateResult(1, result);

    }


}
