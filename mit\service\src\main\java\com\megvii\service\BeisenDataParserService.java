package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.common.StringUtil;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;
import java.lang.reflect.Method;

@Service
//@PropertySource(value = "classpath:beisen.${spring.profiles.active}.properties",encoding = "UTF-8")
@PropertySource(value = "classpath:group.properties",encoding = "UTF-8")
public class BeisenDataParserService {

    Logger logger= LoggerFactory.getLogger(BeisenDataParserService.class);

    private static Map<String, String> beisenKeyMap = new HashMap<>();

    static {
        beisenKeyMap.put("extBuddy_107102_94322460", "buddy");
        beisenKeyMap.put("extSiteHR_107102_1797468263", "siteHrg");
        beisenKeyMap.put("extckzydj_107102_21907213", "referTitle");
        beisenKeyMap.put("extzhiji_107102_2006004533", "title");
        beisenKeyMap.put("extruzhileix_107102_631149364", "employType");
        beisenKeyMap.put("extruzhidizhi_107102_1979277340", "employBase");
        beisenKeyMap.put("extzhaopinqud_107102_734713344", "source");
        beisenKeyMap.put("extzhinen_107102_582432184", "duty");
        beisenKeyMap.put("extxinggzdd_107102_1423821484", "workBase");
        beisenKeyMap.put("Name", "spell");
        beisenKeyMap.put("email", "priEmail");
        beisenKeyMap.put("Mobile", "cell");

        //test环境
        beisenKeyMap.put("extnull_430214_1029867296", "buddy");
        beisenKeyMap.put("extruzhileix_430214_2018523964", "employType");
        beisenKeyMap.put("exthgj_430214_1897406725", "employBase");
    }


    @Value("${DATA_BIAOZHU_TEAM_CODE}")
    private String biaozhuTeamCode;

    @Autowired
    private TeamService teamService;

    @Autowired
    private DhrRestClient dhrRestClient;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private UserService userService;

    @Autowired
    private CheckService checkService;

    @Autowired
    private DingdingService dingdingService;

    /**
     * 将beisen信息转化为user对象
     * @param userInfoMap
     * @return
     */
    public User getUser(Map userInfoMap) {
        JSONObject applicantInfo = (JSONObject)((JSONArray) userInfoMap.get("applicantInfo")).get(0);
        JSONObject offerInfo = getEffectOffer((JSONArray) userInfoMap.get("offerInfo"));

        String beiSenID=applicantInfo.getString("ApplicantId");
        User user = userService.getUserByBeiSenId(beiSenID);
        if (user!=null){
            logger.info("beiSenId:"+beiSenID+"人员已经存在");
            user.setNeedSave(false);
            user.setParseMsg("beiSenId:"+beiSenID+"人员已经存在");
            return user;
        }

        //校招跳过拉取
        String recuitCategoryId = ((JSONObject)applicantInfo.getJSONArray("ApplyJobSummaries").get(0)).getJSONObject("RecuitCategory").getString("Id");
        if (recuitCategoryId != null && recuitCategoryId.equals("2")) {
            logger.info("beiSenId:"+beiSenID+"为校招，跳过拉取");
            user = new User();
            user.setNeedSave(false);
            user.setParseMsg("beiSenId:"+beiSenID+"为校招，跳过拉取");
            return user;
        }

        user=new User();
        user.setNeedSave(true);
        user.setBeisenId(beiSenID);
        JSONArray offerCustomInfos=offerInfo.getJSONArray("OfferCustomInfos");
        try {
            setBeisenUserParam(user, offerCustomInfos);
        } catch (Exception e) {
            logger.error("****写入北森数据异常:" + e);
        }
//        for (int i=0;i<offerCustomInfos.size();i++){
//            JSONObject offerCustomInfo=(JSONObject)offerCustomInfos.get(i);
//            String key=offerCustomInfo.getString("PropertyName").trim();
//            switch (key){
//                case "extBuddy_107102_94322460": {
//                    User buddy = userService.getUserByUserId(offerCustomInfo.getString("Value").trim().toLowerCase());
//                    if (buddy!=null)
//                        user.setBuddy(buddy.getUserId());
//                    else
//                        user.setBuddy("");
//                } break;
//                case "extSiteHR_107102_1797468263":{
//                    User siteHrg = userService.getUserByUserId(offerCustomInfo.getString("Value").trim().toLowerCase());
//                    if (siteHrg!=null)
//                        user.setSiteHrg(siteHrg.getUserId());
//                    else
//                        user.setSiteHrg("");
//                } break;
//                case "extckzydj_107102_21907213":{
//                    user.setReferTitle(offerCustomInfo.getString("Value").trim());
//                }break;
//                case "extzhiji_107102_2006004533":{
//                    user.setTitle(offerCustomInfo.getString("Value").trim());
//                }break;
//                case "extruzhileix_107102_631149364":{
//                    user.setEmployType(offerCustomInfo.getString("Value").trim().toLowerCase());
//                }break;
//                case "exth012_107102_1888350092":{
//                    User hrg=userMapper.selectUserByUserId(offerCustomInfo.getString("Value").trim().toLowerCase());
//                    if (hrg!=null)
//                        user.setHrbp(hrg.getUserId());
//                    else
//                        user.setHrbp("");
//                }break;
//                case "extxulie_107102_2071160025":{
//                    String sequence = dhrRestClient.getSequenceCode(offerCustomInfo.getString("Value"));
//                    user.setSequence(sequence != null ? Integer.valueOf(sequence) : null);
//                }break;
//                case "extruzhidizhi_107102_1979277340":{
//                    user.setEmployBase(offerCustomInfo.getString("Value").trim().toLowerCase());
//                }break;
//                case "extxmqb_107102_429650772":{
//                    String userId = userService.getUserId(offerCustomInfo.getString("Value").trim().toLowerCase());
//                    user.setUserId(userId);
//                }break;
//                case "extzhaopinqud_107102_734713344":{
//                    user.setSource(offerCustomInfo.getString("Value").trim().toLowerCase());
//                }break;
//                case "extzhinen_107102_582432184":{
//                    user.setDuty(offerCustomInfo.getString("Value").trim().toLowerCase());
//                }break;
//                case "extxinggzdd_107102_1423821484":{
//                    user.setWorkBase(offerCustomInfo.getString("Value").trim().toLowerCase());
//                }break;
                /**
                 * 测试环境code
                 */
//                case "extnull_430214_1029867296": {
//                    User buddy = userService.getUserByUserId(offerCustomInfo.getString("Value").trim().toLowerCase());
//                    if (buddy!=null)
//                        user.setBuddy(buddy.getUserId());
//                    else
//                        user.setBuddy("");
//                } break;
//                case "exthgj_430214_1897406725":{
//                    user.setEmployBase(offerCustomInfo.getString("Value").trim().toLowerCase());
//                }break;
//                case "extghsdhcb_430214_290532932":{
//                    String userId = userService.getUserId(offerCustomInfo.getString("Value").trim().toLowerCase());
//                    user.setUserId(userId);
//                }break;
//                case "extruzhileix_430214_2018523964":{
//                    user.setEmployType(offerCustomInfo.getString("Value").trim().toLowerCase());
//                }break;
//            }
//        }

        JSONObject offerBasicInfo=offerInfo.getJSONObject("OfferBasicInfo");
        String mentorId=offerBasicInfo.getString("ImmediateSuperior").trim().toLowerCase();
        if (!"".equals(mentorId)){
            User mentor = userService.getUserByUserId(mentorId);
            if (mentor == null || mentor.getStatus().equals("已禁用")) {
                mailSendService.sendMail("入职员工直接上级异常提醒", user.getUserId() + "直接上级异常,请在OA中修改", "<EMAIL>");
                user.setMentor("");
            } else {
                user.setMentor(mentor.getUserId());
            }
        }else {
            user.setMentor("");
        }

        //获取部门相关
        String teamCode = offerBasicInfo.getString("DepartmentCode").trim();
        if (teamCode != null && !"".equals(teamCode)){
            Team team = teamService.getTeamByCode(teamCode);
            if (team != null){
                user.setTeam(team.getName());
                user.setTeamId(team.getId());
                user.setTeamCode(teamCode);
                if (user.getHrbp()==null||"".equals(user.getHrbp())){
                    user.setHrbp(team.getHrg());
                }
                //data++执行中心处理方法
                Team biaozhuTeam = teamService.getTeamByCode(biaozhuTeamCode);
                if (team.getName().contains(biaozhuTeam.getShortName())) {
                    user.setCreateTag(4);
                }
            }
        }

        //获取个人信息
        JSONObject jsonObject=applicantInfo.getJSONObject("Profile");
        if (jsonObject!=null){
            JSONArray items=jsonObject.getJSONArray("Items");
            if (items != null){
//                for (int i=0;i<items.size();i++){
//                    JSONObject item=items.getJSONObject(i);
//                    if ("Name".equals(item.getString("PropertyName"))){
//                        user.setSpell(item.getString("Value").trim());
//                    }else if ("email".equals(item.getString("PropertyName"))){
//                        user.setPriEmail(item.getString("Value").trim());
//                    } else if ("Mobile".equals(item.getString("PropertyName"))){
//                        user.setCell(item.getString("Value").replaceAll(" ",""));
//                    }
//                }
                try {
                    setBeisenUserParam(user, items);
                } catch (Exception e) {
                    logger.error("****写入北森数据异常:" + e);
                }
            }
        }

        //获取日期相关
        String expectDate = offerBasicInfo.getString("StartDate");
        if (!"".equals(expectDate)){
            Map<String,Object> dateMap = checkService.getHireDate(expectDate,user.getEmployType());
            user.setExpectDate((LocalDateTime) dateMap.get("expectDate"));
            user.setRegularDate((LocalDateTime) dateMap.get("regularDate"));
            user.setDelay((boolean) dateMap.get("isDelay"));
            if ((boolean) dateMap.get("isDelay")) {
                mailSendService.sendDelayEntryMail(user);
                dingdingService.sendEntryDelayHook(user.getSpell(), user.getUserId(), DateTimeUtil.date2String(DateTimeUtil.string2DateTime3(expectDate)), DateTimeUtil.date2String((LocalDateTime) dateMap.get("expectDate")));
            }
        }

        //人员信息解析结束
        logger.info("北森人员解析信息"+user);
        return user;
    }

    /**
     * 重构北森写入array的方法
     * 数据格式要求：
     *      {
                "PropertyName":"XXXXX",
                "Value":"XXXXX"
            }
     * PropertyName 为key  通过beisenKeyMap 对应到user字段 进行写入
     * Value 为写入数据
     * @param user
     * @param jsonArray
     * @return
     * @throws Exception
     */
    public User setBeisenUserParam(User user, JSONArray jsonArray) throws Exception {
        for (Object o : jsonArray) {
            JSONObject data = (JSONObject) o;
            String key = data.getString("PropertyName").trim();
            //序列需要特殊处理
            if ("extxulie_107102_2071160025".equals(key)) {
                String sequence = dhrRestClient.getSequenceCode(data.getString("Value"));
                user.setSequence(sequence != null ? Integer.valueOf(sequence) : null);
                continue;
            }

            //user_id需要特殊处理
            if ("extxmqb_107102_429650772".equals(key) || "extghsdhcb_430214_290532932".equals(key)) {
                String userId = userService.getUserId(data.getString("Value").trim().toLowerCase());
                user.setUserId(userId);
                continue;
            }
            //不是特殊处理的key 也不在map中 需要跳过
            if (beisenKeyMap.get(key) == null) {
                continue;
            }

            Class[] parameterTypes = new Class[1];
            Field field = user.getClass().getDeclaredField(beisenKeyMap.get(key));
            parameterTypes[0] = field.getType();

            StringBuffer sb = new StringBuffer();
            sb.append("set");
            sb.append(StringUtil.upperHeadChar(beisenKeyMap.get(key)));
            Method method = user.getClass().getMethod(sb.toString(), parameterTypes);

            //title 和 refertitle 不需要小写
            if ("extzhiji_107102_2006004533".equals(key) || "extckzydj_107102_21907213".equals(key)) {
                method.invoke(user, data.getString("Value").trim());
            } else {
                method.invoke(user, data.getString("Value").trim().toLowerCase());
            }

            continue;

        }
        return user;
    }




    /**
     * 根据 OfferSubmitTime 取最新offer
     * @param offers
     * @return
     */
    private JSONObject getEffectOffer(JSONArray offers) {
        TreeMap<LocalDateTime, JSONObject> map = new TreeMap();
        for (Object offer : offers) {
            JSONObject data = (JSONObject) offer;
            LocalDateTime time = DateTimeUtil.string2DateTime3(data.getString("OfferSubmitTime"));
            map.put(time, data);
        }

        return map.lastEntry().getValue();
    }



}
