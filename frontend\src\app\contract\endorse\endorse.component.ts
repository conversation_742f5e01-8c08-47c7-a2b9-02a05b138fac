import { Component, OnInit, ViewChild } from '@angular/core'
import { ContractTableComponent } from '../component/contract-table/contract-table.component'
import { ParamStoreService } from '../../core/services/paramStore.service'

@Component({
  selector: 'app-endorse',
  templateUrl: './endorse.component.html',
  styleUrls: ['./endorse.component.less']
})
export class EndorseComponent implements OnInit {
  @ViewChild('endorse', { static: true }) endorse: ContractTableComponent
  isShowSelf = true
  selectedIndex = 0

  constructor(private paramStoreService: ParamStoreService) {}

  ngOnInit(): void {
    this.selectedIndex = this.paramStoreService.getMap('endorse')
  }
  onActivate(event) {
    this.isShowSelf = false
  }
  onDeactivate(event) {
    this.isShowSelf = true
  }

  selectedIndexChange(event) {
    this.selectedIndex = event
    this.paramStoreService.saveMap('endorse', event)
  }
}
