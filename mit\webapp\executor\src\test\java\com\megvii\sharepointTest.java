package com.megvii;

import com.megvii.client.SharePointClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class sharepointTest {

    @Autowired
    SharePointClient sharePointClient;

    @Test
    public void testIsExist(){
        String token = sharePointClient.getToken();
        System.out.println("token: " + token);
        return;
    }

    @Test
    public void testDownloadFilesFromSharePoint(){
        sharePointClient.downloadFilesFromSharePoint();
        return;
    }
}
