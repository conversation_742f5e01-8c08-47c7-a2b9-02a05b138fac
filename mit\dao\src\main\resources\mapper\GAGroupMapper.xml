<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.GAGroupMapper">

    <resultMap id="GAgroupResult" type="com.megvii.entity.opdb.GAGroup" >
        <id column="id" property="id"/>
        <result column="gid" property="gid"/>
        <result column="email" property="email"/>
        <result column="name" property="name"/>
        <result column="number" property="number"/>
        <result column="appid" property="appid"/>
        <result column="addid" property="addid"/>
        <result column="description" property="description"/>
        <result column="owner" property="owner"/>
        <result column="department" property="department"/>
        <result column="tag" property="tag"/>
        <result column="read_authority" property="readAuthority"/>
        <result column="leader" property="leader"/>
    </resultMap>

    <select id="selectGAGroupByEmail" resultMap="GAgroupResult" >
        SELECT *
        FROM GA_group
        WHERE email = #{email}
    </select>






</mapper>