import { Component, OnInit } from '@angular/core'
import { NzFormatEmitEvent, NzModalService } from 'ng-zorro-antd'
import { AuthService } from '../auth.service'
import { MessageService, SendRequest } from '../../core/services'
import set = Reflect.set

@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.less']
})
export class UserComponent implements OnInit {
  userList = []
  page = 1
  total = 0
  isAdd = false
  isAdding = false // 是否在添加或者修改提交中
  loading = false
  selRoleList = []
  roleList = []
  selUser = {}
  selUserId = null
  listOfUserOption: Array<{ userId: string; spell: string }> = []
  timer = null

  isAddModalVisible = false
  nzFilterOption = () => true

  constructor(
    private modalService: NzModalService,
    private authService: AuthService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.getUserList()
    this.getRoleList()
  }

  searchUser(key) {
    if (key.length === 0) {
      return
    }
    clearTimeout(this.timer)
    let me = this
    this.timer = setTimeout(() => {
      me.authService.searchUser({ userId: key, pageNumber: 1, pageSize: 10 }).subscribe((res) => {
        const { code, msg, data } = res
        if (code === 0) {
          me.listOfUserOption = data || []
        } else {
          me.messageService.error(msg)
        }
      })
    }, 500)
  }

  getUserList() {
    this.loading = true
    this.authService.getAllUser().subscribe((res) => {
      const { code, msg, data } = res
      this.loading = false
      if (code === 0) {
        this.userList = data || []
      } else {
        this.messageService.error(msg)
      }
    })
  }

  onAdd() {
    this.isAdd = true
    this.isAddModalVisible = true
  }

  getRoleList() {
    this.authService.getAllRole().subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.roleList = data || []
      } else {
        this.messageService.error(msg)
      }
    })
  }

  getRoleByUser() {
    this.authService.getRoleByUser({ userId: this.selUser['userId'] }).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.selRoleList = data['codes'] || []
      } else {
        this.messageService.error(msg)
      }
    })
  }

  onUpdate(user) {
    this.selUser = user
    this.isAddModalVisible = true
    this.isAdd = false
    this.getRoleByUser()
  }

  addCancel() {
    this.isAddModalVisible = false
    this.selRoleList = []
    this.selUser = {}
    this.selUserId = null
    this.listOfUserOption = []
  }

  addCommit() {
    this.isAdding = true
    this.authService
      .postRoleByUser({
        userId: this.isAdd ? this.selUserId : this.selUser['userId'],
        roleCodes: this.selRoleList
      })
      .subscribe((res) => {
        const { code, msg, data } = res
        this.isAdding = false
        if (code === 0) {
          this.getUserList()
          this.selRoleList = []
          this.listOfUserOption = []
          this.selUserId = null
          this.selUser = {}
          this.messageService.success('提交成功')
          this.isAddModalVisible = false
        } else {
          this.messageService.error(msg)
        }
      })
  }
}
