package com.megvii;

import com.megvii.entity.opdb.User;
import com.megvii.mapper.UserMapper;
import com.megvii.service.KoalaService;
import com.megvii.service.LdapService;
import java.io.UnsupportedEncodingException;

import com.megvii.service.RemindService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest
public class MitexecutorApplicationTests {
    @Autowired
    LdapService ldapService;

    @Autowired
    UserMapper userMapper;

    @Autowired
    KoalaService koalaService;
    @Autowired
    private RemindService remindService;

    @Test
    public void contextLoads() {
        User user=userMapper.selectUserByBeiSenId("566765592");

        return;
    }
    @Test
    public void sendMail(){
        User user = userMapper.selectUserByUserId("lsw");
        remindService.userTodayRemind(user);
    }

//    @Test
//    public void testAliyunData(){
//        AliyunResultSet resultSet=new AliyunResultSet();
//        resultSet.executeSql("select info,expect_date from IT_user_info where user_id='lijunhao'");
//        if (resultSet.next()){
//            String info=resultSet.getString(1);
//            LocalDateTime dateTime=resultSet.getDateTime(2);
//            System.out.println(info);
//            System.out.println(dateTime);
//        }
//    }

    @Test
    public void testUserInfo() throws UnsupportedEncodingException {
       /* List<AliyunUserInfo> userInfoList = userInfoService.getUserInfoList();
        for (AliyunUserInfo info:userInfoList){
            System.out.println(StringEscapeUtils.unescapeJava(info.getInfo()) );
        }*/
        return;
    }

//    @Test
//    public void testAliyun(){
//        aliyunOperatorService.OperateByUserId("qiqiang");
//    }


    @Test
    public void testLdap(){
        boolean b=ldapService.isExist("mamin");
        return;

    }


}
