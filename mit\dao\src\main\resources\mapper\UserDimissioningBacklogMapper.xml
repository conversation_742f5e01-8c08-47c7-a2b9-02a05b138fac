<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserDimissionProcessBacklogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.UserDimissionProcessBacklogPO">
        <id column="id" property="id" />
        <result column="dimission_id" property="dimissionId" />
        <result column="user_id" property="userId" />
        <result column="dimission_date" property="dimissionDate" />
        <result column="x_type" property="xType" />
        <result column="monitor_status" property="monitorStatus" />
        <result column="create_time" property="createTime" />
        <result column="deleted_time" property="deletedTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dimission_id, user_id,dimission_date,x_type,monitor_status,create_time,deleted_time,update_time
    </sql>

    <insert id="addProcess" parameterType="com.megvii.entity.opdb.UserDimissionProcessBacklogPO">
        insert into  user_dimission_process_backlog(
             dimission_id, user_id,dimission_date,x_type,monitor_status,create_time
        )
        values (
                   #{dimissionId},
                   #{userId},
                   #{dimissionDate},
                   #{xType},
                   #{monitorStatus},
                   NOW()
               )
    </insert>
    <select id="getProcess" parameterType="string" resultMap="BaseResultMap">
        select * from user_dimission_process_backlog where dimission_id=#{dimissionId};
    </select>
    <delete id="deleteProcess" parameterType="string">
        delete from user_dimission_process_backlog where dimission_id=#{dimissionId};
    </delete>
    <update id="updateProcess" parameterType="com.megvii.entity.opdb.UserDimissionProcessBacklogPO">
        update user_dimission_process_backlog
        set
             dimission_date = #{dimissionDate},
             x_type = #{xType},
             monitor_status = #{monitorStatus},
             update_time = #{updateTime}
        where id = #{id}
    </update>
</mapper>
