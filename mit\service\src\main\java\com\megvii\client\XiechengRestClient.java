package com.megvii.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/8/28 16:18
 */
@Component
@PropertySource(value = "classpath:xiecheng.${spring.profiles.active}.properties",encoding = "UTF-8")
public class XiechengRestClient {

    private Logger logger= LoggerFactory.getLogger(XiechengRestClient.class);

    @Value("${XIECHENG_APPKEY}")
    private String appKey;

    @Value("${XIECHENG_APPSECURITY}")
    private String appSecurity;

    @Autowired
    private RedisTemplate redisTemplate;


    /**
     * 同步携程人员信息
     * @param userId
     * @param requst
     * @return
     */
//    public String multipleEmployeeSync(String userId, AuthenticationListRequst requst) {
//        AuthenticationInfoListResponse response;
//
//        MultipleEmployeeSyncService empoyeeSyncService=new MultipleEmployeeSyncService();
//        response = empoyeeSyncService.MultipleEmployeeSync(requst);
//
//        if (response == null) {
//            logger.error(userId + "没有返回结果");
//            return userId + "没有返回结果";
//        }
//        //请求失败
//        if (response.getResult().equals("Failed")) {
//            List<ErrorMessage> errorMessages = response.getErrorMessageList();
//            if(errorMessages != null && errorMessages.size()>0) {
//                //可能存在多个错误
//                StringBuilder msg = new StringBuilder();
//                for (ErrorMessage errorMessage : errorMessages) {
//                    msg.append( userId + "更新携程信息错误:" + JSONObject.toJSONString(errorMessage));
//                }
//                logger.error(msg.toString());
//                return msg.toString();
//            }
//        }
//        return userId + "更新携程信息成功";
//    }

    /**
     * 优先从redis中获取 2小时失效
     * @return
     * @throws Exception
     */
//    public String getXichengToken() throws Exception {
//        Object token=redisTemplate.opsForValue().get("mitexecutor_xiecheng_token");
//
//        if (token==null){
//            token = getEmployeeSyncTicket();
//            redisTemplate.opsForValue().set("mitexecutor_xiecheng_token",token);
//            redisTemplate.expire("mitexecutor_xiecheng_token",7200, TimeUnit.SECONDS);
//        }
//        return (String) token;
//    }

    /**
     * 获取携程ticket
     * @return
     */
//    private String getEmployeeSyncTicket() throws Exception {
//        OrderSearchTicketResponse response = CorpTicketService.getEmployeeSyncTicket(appKey, appSecurity, null);
//
//        if (response.getStatus().getSuccess()) {
//            return response.getTicket();
//        } else {
//            logger.error("调用携程ticket错误:" + response.getStatus().getMessage());
//            throw new Exception("调用携程ticket错误:" + response.getStatus().getMessage());
//        }
//    }


}
