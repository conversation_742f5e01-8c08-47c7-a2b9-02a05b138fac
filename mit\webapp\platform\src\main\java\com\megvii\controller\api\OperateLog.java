package com.megvii.controller.api;


import com.megvii.entity.opdb.OperateTypeEnum;

import java.lang.annotation.*;

/**
 * 记录操作日志注解
 *
 * <AUTHOR>
 * @date 2020/04/17
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperateLog {

    /****
     * 备注
     * @return
     */
    String remark() default "";

    /****
     * 操作类型
     * @return
     */
    OperateTypeEnum operType();

}
