package com.megvii.controller.entry;

import com.megvii.common.IPUtils;
import com.megvii.common.ParameterUtil;
import com.megvii.entity.opdb.User;
import com.megvii.service.*;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019/11/18 20:15
 */
@RestController
@RequestMapping("/backend")
public class EntryController {

	private Logger logger = LoggerFactory.getLogger(EntryController.class);

	@Autowired
	private OaService oaService;

	@Autowired
	private EntryService entryService;

	@Autowired
	private IPWhiteListService ipWhiteListService;

	@Autowired
	private RemindService remindService;

	@Autowired
	private CheckService checkService;

	@Autowired
	private DingdingService dingdingService;

	@PostMapping("/staff/oa_end")
	@ResponseBody
	public String OA2MIT(HttpServletRequest request, @RequestBody String par) throws UnsupportedEncodingException {


		//过滤ip,若用户在白名单内，则放行
		String ipAddress = IPUtils.getRealIP(request);
		if (!ipWhiteListService.validateIp(ipAddress, "oa")) {
			return "IP不在白名单内";
		}

		logger.info("OA回传MIT*****" + par);

		Map map ;
		try {
			map = ParameterUtil.getParaMapV2(par);
		} catch (Exception e) {
			logger.error(e.getMessage());
			return e.getMessage();
		}

		//检查必传字段
		String check = checkService.checkKey(map);
        if (check != null) {
            logger.error(check);
            dingdingService.sendOa2MitErrorHook( "OA回传" + check +  " \n  ##### " + map.get("user_id") + "  " + map.get("spell"));
            return check;
        }

        //检查部门和tag
        if (!checkService.checkDataTeamAndTag(map)) {
        	dingdingService.sendOa2MitErrorHook( "OA回传部门与tag不匹配,请撤回OA流程修改后从新审批。  \n  ##### " + map.get("user_id") + "  " + map.get("spell"));
		}

		User user;
		try {
			//更新user信息
			user = oaService.updateUserInfoFromOA(map);
		} catch (Exception e) {
			logger.error(e.getMessage());
			return e.getMessage();
		}

		//跳过标记1和3的人
		if (!(user.getCreateTag() != null && (user.getCreateTag() == 2 || user.getCreateTag() == 3))) {
			//插入entry数据
			String msg = entryService.insertEntryInfo(user);
			//发送信息收集邮件和短信
			entryService.sendEntrySmsEmail(user);
		}
		String lccsry = (String) map.get("lccsry");
		if (lccsry != null) {
			remindService.sendAddUserMail(user, URLDecoder.decode(lccsry, "utf-8").split(","));
		} else {
			remindService.sendAddUserMail(user, null);
		}

		return "success";
	}





}
