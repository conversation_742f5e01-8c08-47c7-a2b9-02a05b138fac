package com.megvii.service;


import com.megvii.client.*;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.MMISMailTemplate;
import com.megvii.entity.opdb.TempUser;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserInfoLocale;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/22 18:04
 */
@Service
public class DimissionService {

    Logger logger= LoggerFactory.getLogger(DimissionService.class);

    private final static String MAIL_SUFFIX_MEGVII="@megvii.com";

    @Value("${DISMISSION_RECV_MAIL}")
    private String mailAddress;

    @Value("${ADMIN_MAIL}")
    private String adminMailAddress;

    @Autowired
    private MMISMailTemplateService mmisMailTemplateService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private OaService oaService;

    @Autowired
    private Office365Service office365Service;

    @Autowired
    private LdapService ldapService;

    @Autowired
    private GitService gitService;

    @Autowired
    private DingdingService dingdingService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserInfoLocaleService userInfoLocaleService;

    @Autowired
    private DidiService didiService;

    @Autowired
    private MeicanService meicanService;

    @Autowired
    private KoalaService koalaService;

    @Autowired
    private TempUserService tempUserService;

    @Autowired
    private BrainRestClient brainRestClient;

    @Autowired
    private BrainRestClientExpel brainRestClientExpel;

    @Autowired
    private PanguService panguService;

    @Autowired
    private ErpRestClient erpRestClient;

    @Autowired
    private MeginRestClient meginRestClient;

    @Autowired
    private MokaService mokaService;

    @Autowired
    AliMailClient aliMailClient;
    /**
     * 外包人员离职任务
     * 1.删除office365组 ldap组  (技术型才有ldap)
     * 2.删除ldap账号
     * 3.禁用git-V
     * 4.修改status
     * @param tempUser
     * @return
     */
    public String tempDismissAccount(TempUser tempUser) {
        if (tempUser.getStatus().equals("inactive")) {
            return tempUser.getUserId() + "status = " + tempUser.getStatus() + ", 不处理";
        }

        StringBuilder msg = new StringBuilder();

        msg.append(brainRestClient.expelBrain(tempUser.getUserId(), tempUser.getMentor()));
        if (tempUser.getTechType() != null && tempUser.getTechType().equals("技术型")) {
            // 删除office365 改为ali-mail
            msg.append(office365Service.disableInternalAccounts(tempUser.getUserId()) + "<br/>");
            msg.append(aliMailClient.removeAccounts(tempUser.getUserId() + MAIL_SUFFIX_MEGVII) + "<br/>");
            msg.append(ldapService.disableTempLdap(tempUser.getUserId()) + "<br/>");
        }
        msg.append(gitService.disableGitV(tempUser.getUserId()) + "<br/>");
        msg.append(tempUserService.updateTempUserStatus(tempUser.getUserId()) + "<br/>");

        return msg.toString();
    }


    /**
     * 离职相关任务
     * 1.禁用brain++
     * 2.删除ali-mail组 ldap组
     * 3.修改ldap密码
     * 4.禁用ldap账号
     * 5.修改邮箱状态 取消邮箱授权
     * 6.禁用git-pd
     * 7.禁用git-core
     * 8.删除钉钉
     * 9.改MIT状态，删除info表信息
     * 10.删除滴滴
     * 11.删除美餐
     * 12.删除koala
     * 13.删除盘古
     * @param user
     * @return
     */
    public String dismissAccount(User user) {
        if (user.getStatus() != null && (user.getStatus().equals("已禁用") || user.getStatus().equals("已保留"))) {
            return user.getUserId() + "status = " + user.getStatus() + ",不处理";
        }

        // VP账号保护
        if (user.getProtect() != null) {
            mailSendService.sendMail("禁用账号任务账号保护提醒", user.getUserId() + "处于账号保护状态: protect = " + user.getProtect(), adminMailAddress );
            return user.getUserId() + "处于账号保护状态,请修改后执行!";
        }


        StringBuilder msg = new StringBuilder();

        msg.append(user.getUserId() + "离职任务<br/>");
        msg.append(brainRestClientExpel.expelBrainSec(user.getUserId()) + "<br/>");
        msg.append(brainRestClient.expelBrain(user.getUserId(), user.getMentor()) + "<br/>");
        msg.append(user.getUserId() + (erpRestClient.erpUserDisable(user.getWorkNumber())?"禁用ERP【成功】<br/>":"禁用ERP【失败】<br/>"));
        msg.append(user.getUserId() + (meginRestClient.disableMeginUser(user.getUserId())?"禁用megin【成功】<br/>":"禁用megin【失败】<br/>"));
        msg.append(office365Service.disableInternalAccounts(user.getUserId()) + "<br/>");
        msg.append(ldapService.updatePassword(user.getUserId(), "Megvii@58cdf5b6") + "<br/>");
        msg.append(ldapService.disableLdap(user.getUserId()) + "<br/>");
//        msg.append(office365Service.disableAccount(user.getUserId()) + "<br/>");
        msg.append(gitService.disableGitPdCore(user.getUserId()) + "<br/>");
        msg.append(dingdingService.deleteDingdingUser(user) + "<br/>");
        msg.append(updateDimissionUserInfo(user) + "<br/>");
//        msg.append(koalaService.deleteOldKoalaInfo(user) + "<br/>");
        msg.append(panguService.deletePanguUser(user.getUserId()) + "<br/>");
        msg.append(didiService.removeDidi(user) + "<br/>");
        msg.append(meicanService.deleteMeicanUser(user) + "<br/>");
        msg.append(mokaService.disable(user.getCell()) + "<br/>");
        // 删除ali-mail
        msg.append(aliMailClient.removeAccounts(user.getUserId() + MAIL_SUFFIX_MEGVII) + "<br/>");

        return msg.toString();
    }


    /**
     * 修改it_user 状态
     * 删除user_info_local数据
     * @param user
     * @return
     */
    private String updateDimissionUserInfo(User user) {
        StringBuilder msg = new StringBuilder();

        user.setStatus("已禁用");
        user.setTeamId(null);
        userService.updateUser(user);
        msg.append(user.getUserId() + "修改状态成功;");

        UserInfoLocale info = userInfoLocaleService.getUserInfoLocaleByUserId(user.getUserId());
        if (info != null) {
            if (userInfoLocaleService.deleteUserInfoLocaleByUserId(user.getUserId())) {
                msg.append("删除userInfo成功");
            } else {
                msg.append("删除userInfo失败");
            }
        }

        return msg.toString();
    }



    /**
     * 离职流程 发邮件 推OA
     * @param user
     * @return
     */
    public String dimissionFlow(User user, Map<String, String> map) {
        StringBuilder msg = new StringBuilder();

        if (sendDimissionMail(user)) {
            msg.append("离职邮件发送成功!");
        } else {
            msg.append("离职邮件发送失败!");
        }

        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
            msg.append("***执行中心人员不创建OA流程");
            return msg.toString();
        }

//        boolean isIntern = false;
//        if (user.getEmployType() != null && user.getEmployType().indexOf("实习生")!=-1) {
//            isIntern = true;
//        }
//        msg.append(oaService.createDismissFlow(user.getWorkNumber(), user.getDimissionDate(), isIntern)?"OA流程推送成功":"OA流程推送失败");

        map.put("worknumber", user.getWorkNumber());
        map.put("spell", user.getSpell());
        map.put("employType", user.getEmployType());
        if (oaService.sendE9DimissionFlow(map)) {
            msg.append("OA离职流程推送<span style=\"color:#3eda67;\">【成功】</span>");
        } else {
            msg.append("OA离职流程推送<span style=\"color:#dc143c;\">【失败】</span>");
        }

        return msg.toString();
    }


    /**
     * 发送离职邮件
     * @param user
     * @return
     */
    private boolean sendDimissionMail(User user) {
        MMISMailTemplate template = mmisMailTemplateService.getMMISMailTemplateByType("dismission");
        if (template == null) {
            logger.error(user.getUserId() + "查不到离职邮件模板");
            return false;
        }

        String memo = user.getMemo()!=null?user.getMemo():"";

        StringBuilder body = new StringBuilder();
        body.append("<!DOCTYPE html><html><head></head><body>");
        body.append(template.getContent());
        body.append("<p>员工信息</p>\n" +
                "        <table class=\"table table-striped table-hover table-condensed\" border=\"1\" cellpadding=\"2\" cellspacing=\"0\">\n" +
                "        <tr>\n" +
                "        <td>员工ID</td>\n" +
                "        <td>" + user.getUserId() + "</td>\n" +
                "        </tr>\n" +
                "        <tr>\n" +
                "        <td>类型</td>\n" +
                "        <td>" + user.getEmployType() + "</td>\n" +
                "        </tr>\n" +
                "        <tr>\n" +
                "        <td>姓名</td>\n" +
                "        <td>" + user.getSpell() + "</td>\n" +
                "        </tr>\n" +
                "        <tr>\n" +
                "        <td>小组</td>\n" +
                "        <td>" + user.getTeam() + "</td>\n" +
                "        </tr>\n" +
                "        <tr>\n" +
                "        <td>入职时间</td>\n" +
                "        <td>" + DateTimeUtil.date2String(user.getExpectDate()) + "</td>\n" +
                "        </tr>\n" +
                "        <tr>\n" +
                "        <td>离职时间</td>\n" +
                "        <td><strong>" + DateTimeUtil.date2String(user.getDimissionDate()) + "</strong></td>\n" +
                "        </tr>\n" +
                "        <tr>\n" +
                "        <td>备注</td>\n" +
                "        <td>" + memo + "</td>\n" +
                "        </tr>\n" +
                "        </table>");

        String recvs = mailAddress;
        if (user.getMentor() != null && !user.getMentor().equals("")) {
            recvs += "," + user.getMentor() + MAIL_SUFFIX_MEGVII;
        }
        if (user.getHrbp() != null && !user.getHrbp().equals("")) {
            recvs += "," + user.getHrbp() + MAIL_SUFFIX_MEGVII;
        }
        if (user.getSiteHrg() != null && !user.getSiteHrg().equals("")) {
            recvs += "," + user.getSiteHrg() + MAIL_SUFFIX_MEGVII;
        }
        recvs += "," + user.getUserId() + MAIL_SUFFIX_MEGVII;

        mailSendService.sendMail("离职通知", body.toString(), recvs);
        return true;
    }











}
