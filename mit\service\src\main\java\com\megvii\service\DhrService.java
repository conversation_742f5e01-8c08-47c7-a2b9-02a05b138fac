package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.*;
import com.megvii.entity.opdb.contract.*;
import com.megvii.mapper.UserContractMapper;
import com.megvii.mapper.filter.UserBacklogFilter;
import com.megvii.service.contract.ChangeContractBacklogService;
import com.megvii.service.contract.ContractBacklogService;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.megvii.service.contract.ContractService;
import com.megvii.service.contract.ProveContractBacklogService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2019/10/17 11:57
 */
@Service
@PropertySource(value = "classpath:mail.properties", encoding = "UTF-8")
@PropertySource(value = "classpath:dhr.${spring.profiles.active}.properties", encoding = "UTF-8")
public class DhrService {

	Logger logger = LoggerFactory.getLogger(DhrService.class);

	@Value("${DHR_GET_DIMISSION_DATA_FUNC_ID}")
	private String dhrGetDimissionFuncId;

	@Value("${DHR_GET_CONTRACT_BACKLOG_FUNC_ID}")
	private String dhrGetContractBacklogFuncId;

	@Value("${DHR_GET_CHANGE_CONTRACT_BACKLOG_FUNC_ID}")
	private String dhrGetChangeContractBacklogFuncId;

	@Value("${DHR_GET_Dismission_CONTRACT_BACKLOG_FUNC_ID}")
	private String dhrGetDismissionContractBacklogFuncId;

	@Value("${DHR_GET_Dismissioning_BACKLOG_FUNC_ID}")
	private String dhrGetDismissioningBacklogFuncId;

	@Value("${DHR_GET_Dismission_HISTORY_CONTRACT_FUNC_ID}")
	private String dhrGetDismissionHistoryContractFuncId;

	@Value("${DHR_GET_Dismission_CONTRACT_STAFF_FUNC_ID}")
	private String dhrGetDismissionContractStaffFuncId;

	@Value("${DHR_PUT_CONTRACT_FUNC_ID}")
	private String dhrPutContractFuncId;

	@Value("${DHR_POST_CONTRACT_PROVE_STATUS_FUNC_ID}")
	private String dhrPostContractProveStatusFuncId;

	@Value("${DHR_PUT_DIMISSION_CONTRACT_FUNC_ID}")
	private String dhrPutDimissionContractFuncId;

	@Value("${DHR_PUT_USER_FUNC_ID}")
    private String dhrPutUserFuncId;

	@Value("${DHR_GET_CONTRACT_PROVE_FUNC_ID}")
	private String dhrGetProveContractFuncId ;

//	@Value("${DHR_POST_PROVE_CONTRACT_FUNC_ID}")
//	private String dhrPostProveStatusFuncId;


	@Autowired
	private MokaService mokaService;

	@Autowired
	private TeamService teamService;

	@Autowired
	private UserService userService;

	@Autowired
	private DhrRestClient dhrRestClient;

	@Autowired
	private UserInfoLocaleService userInfoLocaleService;

	@Autowired
	private MailSendService mailSendService;

	@Autowired
	private DimissionService dimissionService;

	@Autowired
	private EntryService entryService;

	@Autowired
	private ContractBacklogService contractBacklogService;
	@Autowired
	private ChangeContractBacklogService changeContractBacklogService;
	@Autowired
	private UserDimissionContractBacklogService userDimissionContractBacklogService;

	@Autowired
	private UserDimissionProcessBacklogService userDimissionProcessBacklogService;

	@Autowired
	private UserContractMapper userContractMapper;

	@Autowired
	private MeginUserDimissionFlowService meginUserDimissionFlowService;

	@Autowired
	private LdapService ldapService;

	@Autowired
	private ProveContractBacklogService proveContractBacklogService;

	@Autowired
	@Lazy
	private ContractService contractService;

	/**
	 * 获取DHR离职流程
	 * @return
	 * @throws Exception
	 */
	public String getDimissionArray()  {
		StringBuilder msg = new StringBuilder();
		try {
			JSONArray array = dhrRestClient.getData(dhrGetDimissionFuncId);
			if (array == null) {
				msg.append("DHR离职信息没有返回数据!请联系DHR管理员");
				mailSendService.sendMail("DHR离职信息没有返回数据", "时间:" + LocalDateTime.now(), "<EMAIL>");
				throw new Exception(msg.toString());
			}
			for (Object o : array) {
				try {
					JSONObject data = (JSONObject) o;

					//历史数据判断与今天的相差天数，超过60天的跳过不判断，
					LocalDateTime dimissionDate = DateTimeUtil.string2DateYMD(data.getString("LeaveDate"));
					if (dimissionDate.isBefore(LocalDateTime.now()) && DateTimeUtil.diffDateByDay(dimissionDate, LocalDateTime.now()) < -60) {
						continue;
					}

					msg.append(compareUserDimissionData(data));
				} catch (Exception e) {
					msg.append(e.getMessage() + "</br>");
					continue;
				}
			}
		}catch (Exception e){

		}
		return msg.toString();

	}
	private String compareUserDimissionData(JSONObject data) {
		String workNumber = data.getString("Badge");
		try {
			LocalDateTime now = LocalDateTime.now();

			LocalDateTime dimissionDate = DateTimeUtil.string2DateYMD(data.getString("LeaveDate"));
			String jobHandever = data.getString("Job_handever");
			String leaWorkcity = data.getString("Lea_WorkCity");
			String leaveType = data.getString("leavetype");
			// 从DHR获取在职时长
			Integer employLength = Integer.parseInt(data.getString("cday"));

			if (jobHandever == null || leaWorkcity == null) {
				return workNumber + "不处理!</br>";
			}
			//有离职记录并且信息未发生变化
			MeginUserDimissionFlowPO userDimissionFlowPO = meginUserDimissionFlowService.getDimissionFlowByUid(data.getString("ID"));
//			if (userDimissionFlowPO != null &&
//					!needUpdateDimissionFlow(userDimissionFlowPO, dimissionDate)) {
//				return "";
//			}
			User user = userService.getUserByWorkNumber(workNumber);
      if(userDimissionFlowPO==null){
        userDimissionFlowPO = new MeginUserDimissionFlowPO();
      }
			userDimissionFlowPO.setUid(data.getString("ID"));
			userDimissionFlowPO.setUserId(user.getUserId());
			userDimissionFlowPO.setSpell(user.getSpell());
			userDimissionFlowPO.setWorkNumber(user.getWorkNumber());
			userDimissionFlowPO.setMeginUserId(0L);
			userDimissionFlowPO.setDimissionDate(DateTimeUtil.getDate(dimissionDate));
			userDimissionFlowPO.setHandover(jobHandever);
			userDimissionFlowPO.setLeaWorkcity(leaWorkcity);
			userDimissionFlowPO.setCreateTime(new Date());
			// 保存时增加离职类型和在职时长2个字段
			userDimissionFlowPO.setLeaveType(leaveType);
			userDimissionFlowPO.setEmployLength(employLength);
			userDimissionFlowPO.setFlowStatus("0");
			Boolean res = false;
			if(userDimissionFlowPO.getId()==null){
        res=meginUserDimissionFlowService.insert(userDimissionFlowPO);
      }else{
        res=meginUserDimissionFlowService.update(userDimissionFlowPO);
      }
			if(res){
				UserBacklogFilter filter = new UserBacklogFilter();
				filter.setUserId(user.getUserId());
				//查询当前人所有转签合同
				List<UserContractBacklog> backlogByFilter = contractBacklogService.getBacklogByFilter(filter);
				for (UserContractBacklog userContractBacklog : backlogByFilter) {
					if(!BacklogStatus.FINISH.equals(userContractBacklog.getStatus())&&!BacklogStatus.SIGN.equals(userContractBacklog.getStatus())){//待续签或者续签中
						//当前合同结束日期<离职日期时就打标签标识“加急续签”
						if(DateTimeUtil.string2DateYMD(userContractBacklog.getEndDate()).isBefore(dimissionDate)){
							userContractBacklog.setDimissionTag(BacklogTag.URGENT);
						}else{//续签合同开始日期>=离职日期时，需打标签标识“无需续签”
							userContractBacklog.setDimissionTag(BacklogTag.NOTNEED);
						}
						contractBacklogService.updateUserContractBacklogTag(userContractBacklog);
					}else{//已完成续签的
						//状态为“已完成”，当续签合同开始日期>=离职日期时，合同进入续签管理-待作废列表
            UserContract userNewestContract = userContractMapper.selectUserNewestContract(user.getUserId());
						LocalDateTime conDate=userContractBacklog.getEndDate()==null?userNewestContract.getStartDate():DateTimeUtil.string2DateYMD(userContractBacklog.getEndDate());
            if(!conDate.isBefore(dimissionDate)&&!BacklogTag.TERMINATED.equals(userContractBacklog.getDimissionTag())){
							userContractBacklog.setDimissionTag(BacklogTag.WAITTERMINATE);
							contractBacklogService.updateUserContractBacklogTag(userContractBacklog);
						}
					}
				}
				return "";
			}else {
				return "保存失败："+workNumber;
			}

		}catch (Exception e){
			return "保存失败："+workNumber+e.getMessage();
		}
	}

	/**
	 * 比较是否需要更新
	 *
	 * @param po
	 * @param dimissionDate
	 * @return
	 */
	private boolean needUpdateDimissionFlow(MeginUserDimissionFlowPO po, LocalDateTime dimissionDate) {
		if (DateTimeUtil.diffDateByDay(DateTimeUtil.getLocalDateTime(po.getDimissionDate()), dimissionDate) == 0) {
			return false;
		}
		return true;
	}

	/**
	 * 证明类合同签署完成信息回传DHR
	 * @param userProveContractBacklog
	 */
	@Async
	public void proveContractToDhr(UserProveContractBacklog userProveContractBacklog){
		JSONArray bizData = getProveContractJson(userProveContractBacklog);
		try {
			dhrRestClient.putStart(dhrPostContractProveStatusFuncId);
			dhrRestClient.putData(userProveContractBacklog.getUserId(), bizData, dhrPostContractProveStatusFuncId);
			dhrRestClient.putClose(userProveContractBacklog.getUserId(), dhrPostContractProveStatusFuncId);
		} catch (Exception e) {
			logger.error(userProveContractBacklog.getContractId() + "回传DHR异常：" + e);
			mailSendService.sendMail(userProveContractBacklog.getContractId() + "回传DHR异常", e.toString(), "<EMAIL>");
		}
	}

	/**
	 * 合同签署完成信息回传DHR
	 * @param contract
	 */
	@Async
	public void contractToDhr(UserContract contract) {
		JSONArray bizData = getContractJson(contract);

		try {
			dhrRestClient.putStart(dhrPutContractFuncId);
			dhrRestClient.putData(contract.getUserId(), bizData, dhrPutContractFuncId);
			dhrRestClient.putClose(contract.getUserId(), dhrPutContractFuncId);
		} catch (Exception e) {
			logger.error(contract.getContractId() + "回传DHR异常：" + e);
			mailSendService.sendMail(contract.getContractId() + "回传DHR异常", e.toString(), "<EMAIL>");
		}
	}

	@Async
	public void dimissionContractToDhr(String userId,String dimissionId) {
		JSONArray bizData = getDimissionContractJson(dimissionId);

		try {
			dhrRestClient.putStart(dhrPutDimissionContractFuncId);
			dhrRestClient.putData(userId, bizData, dhrPutDimissionContractFuncId);
			dhrRestClient.putClose(userId, dhrPutDimissionContractFuncId);
		} catch (Exception e) {
			logger.error(dimissionId + "离职合同回传DHR异常：" + e);
			mailSendService.sendMail(dimissionId + "离职合同回传DHR异常", e.toString(), "<EMAIL>");
		}
	}

	/**
	 * 组装证明类合同回传DHR需要的json
	 * @param
	 * @return
	 */
	private JSONArray getProveContractJson(UserProveContractBacklog userProveContractBacklog) {
		User user = userService.getUserByUserId(userProveContractBacklog.getUserId());

		JSONObject data = new JSONObject();
		data.put("TYPE", userProveContractBacklog.getXtype());
		data.put("BADGE", user.getWorkNumber());
		data.put("CONTRACTID", userProveContractBacklog.getContractId());
		data.put("WFINSTANCEID",userProveContractBacklog.getDhrId());
		// 数字1表示已完成，数字2 表示取消
		data.put("STATUS", "1");

		JSONArray contractData = new JSONArray();
		contractData.add(data);

		JSONObject contractDataObj = new JSONObject();
		contractDataObj.put("eProve", contractData.toString());

		JSONArray bizData = new JSONArray();
		bizData.add(contractDataObj);

		return bizData;
	}



	/**
	 * 组装回传DHR需要的json
	 * @param contract
	 * @return
	 */
	private JSONArray getContractJson(UserContract contract) {
		User user = userService.getUserByUserId(contract.getUserId());
		Team team = teamService.getTeamById(user.getTeamId());

		JSONObject data = new JSONObject();
		data.put("TYPE", contract.getSignStatus());
		data.put("BADGE", contract.getWorkNumber());
		data.put("NAME", contract.getSpell());
		data.put("COMPID", "44");
		data.put("DEPID", team.getSfCode());
		data.put("JOBID", contract.getPosition());
		data.put("NEW_CONCOUNT", contract.getContractCount());
		data.put("NEW_CONTRACT", dhrRestClient.getCompanyCode(contract.getCompany()));
		data.put("NEW_CONTYPE", contract.getContractType());
		data.put("NEW_CONPROPERTY", contract.getContractAttribute());
		data.put("NEW_CONNO", contract.getContractNumber());
		data.put("NEW_CONBEGINDATE", contract.getStartDate());
		data.put("OPERATE_STATUS", contract.getContractStatus());

		if (contract.getContractAttribute().equals("固定期")) {
			data.put("NEW_CONTERM", "1");
		} else {
			data.put("NEW_CONTERM", null);
		}

		data.put("NEW_CONENDDATE", contract.getEndDate());
		data.put("EFFECTDATE", contract.getEffectDate());
		data.put("REMARK", contract.getMemo());
		data.put("OPERATE_BY", contract.getUpdatorUserId());
		data.put("OPERATE_DATE", contract.getUpdateTime());

		JSONArray contractData = new JSONArray();
		contractData.add(data);

		JSONObject contractDataObj = new JSONObject();
		contractDataObj.put("EBG_CONTRACT", contractData.toString());

		JSONArray bizData = new JSONArray();
		bizData.add(contractDataObj);

		return bizData;
	}

	private JSONArray getDimissionContractJson(String dimissionId) {
		JSONObject data = new JSONObject();
		data.put("ID", dimissionId);
		JSONArray contractData = new JSONArray();
		contractData.add(data);
		JSONObject contractDataObj = new JSONObject();
		contractDataObj.put("EBG_CONTRACT", contractData.toString());
		JSONArray bizData = new JSONArray();
		bizData.add(contractDataObj);
		return bizData;
	}



    /**
     * 拉取DHR待续签人员 修改DHR操作完成人员在backlog中的状态
     * 1.拉取人员
     * 2.查看backlog状态  已完成 or null  则新增
     * 3.从已签署的map中移除拉取到的人员
     * 4.map中为 已签署 DHR操作完成 修改状态为已完成
     * @return
     */
	public String getContractBacklogFromDhr() {
		StringBuilder msg = new StringBuilder();
		JSONArray array;
		try {
			array = dhrRestClient.getData(dhrGetContractBacklogFuncId);
		} catch (Exception e) {
			msg.append("拉取DHR待续签员错误!" + e.getMessage());
			logger.error(msg.toString());
			return msg.toString();
		}

		if (array == null || array.size() == 0) {
			return "没有待续签的人员";
		}

		Map<String, UserContractBacklog> removeMap = contractBacklogService.selectBacklogByStatus(BacklogStatus.SIGN);
		for (Object o : array) {
			JSONObject data = (JSONObject) o;
			User user = userService.getUserByWorkNumber(data.getString("badge"));
			if (user == null || user.getStatus().equals("已禁用")) {
				msg.append(data.getString("name") + "账号不存在或已禁用，拉取续签失败。<br>");
				continue;
			}

			/*
			* 标注中心人员使用续签合同
			* */
//			if (user.getCreateTag() != null && user.getCreateTag() == 4) {
//				msg.append(data.getString("name") + "data++执行中心不拉取。<br>");
//				continue;
//			}

			// 从DHR拉取续签用户的现住址信息，存入IT_user表address字段，原地址存入old_address字段
			if (!StringUtils.isEmpty(data.getString("address"))) {
				user.setOldAddress(user.getAddress());
				user.setAddress(data.getString("address"));
				userService.updateUser(user);
			}

			removeMap.remove(user.getUserId());

			UserContractBacklog backlog = contractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
			//需要新增续签记录的人
			if (backlog == null || backlog.getStatus().equals(BacklogStatus.FINISH)) {
				backlog = new UserContractBacklog();
				backlog.setUserId(user.getUserId());
				backlog.setWorkNumber(user.getWorkNumber());
				backlog.setStatus(BacklogStatus.PULL);
				backlog.setEndDate(data.getString("ConEndDate"));
				if (contractBacklogService.addContractBacklog(backlog)) {
					//续签流程创建的时候：用账号去离职流程表（离职时间在某时间段）是否存在，存在：当前合同结束日期<离职日期时就打标签标识“加急续签”；续签合同开始日期>=离职日期时，需打标签标识“无需续签”。不存在就不处理
//					MeginUserDimissionFlowPO meginUserDimissionFlowPO = meginUserDimissionFlowService.selectByUserId(user.getUserId());
//					if(meginUserDimissionFlowPO!=null){
//						LocalDateTime dimissionDate = DateTimeUtil.getLocalDateTime(meginUserDimissionFlowPO.getDimissionDate());
//						UserBacklogFilter filter = new UserBacklogFilter();
//						filter.setUserId(user.getUserId());
//						filter.setBacklogStatus(BacklogStatus.PULL);
//						//查询当前人所有转签合同
//						List<UserContractBacklog> backlogByFilter = contractBacklogService.getBacklogByFilter(filter);
//						for (UserContractBacklog userContractBacklog : backlogByFilter) {
//							if(DateTimeUtil.string2DateYMD(userContractBacklog.getEndDate().split(" ")[0]).isBefore(dimissionDate)){
//								userContractBacklog.setDimissionTag(BacklogTag.URGENT);
//							}else{//续签合同开始日期>=离职日期时，需打标签标识“无需续签”
//								userContractBacklog.setDimissionTag(BacklogTag.NOTNEED);
//							}
//							contractBacklogService.updateUserContractBacklogTag(userContractBacklog);
//						}
//					}
					msg.append(user.getUserId() + "续签状态:已拉取");
				} else {
					msg.append(user.getUserId() + "续签状态:修改已拉取失败");
				}
				continue;
			}else{
				backlog.setEndDate(data.getString("ConEndDate"));
				contractBacklogService.updateUserContractBacklogTag(backlog);
			}
		}

		if (removeMap.size() == 0) {
		    return msg.toString();
        }

		for (Map.Entry<String, UserContractBacklog> entry : removeMap.entrySet()) {
			entry.getValue().setStatus(BacklogStatus.FINISH);
			if (contractBacklogService.updateContractBacklogStatus(entry.getValue())) {
			    msg.append(entry.getValue().getUserId() + "续签状态:已完成");
            } else {
			    msg.append(entry.getValue().getUserId() + "续签状态:修改已完成失败");
            }
		}
		return msg.toString();
	}
	/**
	 * 获取dhr证明类合同数据
	 * @return String
	 */
	public String getProveContractBacklogFromDhr(){
		StringBuilder msg = new StringBuilder();
		JSONArray array;
		try {
			array = dhrRestClient.getData(dhrGetProveContractFuncId);
		} catch (Exception e) {
			msg.append("拉取DHR证明类合同员错误!" + e.getMessage());
			logger.error(msg.toString());
			return msg.toString();
		}
		if(array == null || array.size() == 0){
			return "没有证明类合同的人员";
		}

		for (Object o : array) {
			JSONObject data = (JSONObject) o;
			User user = userService.getUserByWorkNumber(data.getString("badge"));
			if (user == null || user.getStatus().equals("已禁用")) {
				msg.append(user.getUserId() + "证明类合同:未找到对应的员工信息。<br>");
				continue;
			}
			UserProveContractBacklog backlog = proveContractBacklogService.getProveContractBacklogByDhrId(data.getString("WFINSTANCEID"));
			//需要新增证明类合同记录的人
			if (backlog == null) {
				backlog = new UserProveContractBacklog();
				backlog.setUserId(user.getUserId());
				backlog.setWorkNumber(user.getWorkNumber());
				backlog.setDhrId(data.getString("WFINSTANCEID"));
				backlog.setStatus(BacklogStatus.WAITSEAL);
				backlog.setApprove(data.getString("approve"));
				backlog.setXtype(data.getString("xtype"));
				backlog.setApproveUserId(data.getString("appuserID"));
				//发起合同流程
				try {
					String contractId = contractService.createProveContract(data);
					backlog.setContractId(contractId);
					proveContractBacklogService.addProveContractBacklog(backlog);
					msg.append(user.getUserId() + "证明类合同:发起成功<br>");
				}catch (Exception e){
					msg.append(user.getUserId() + "证明类合同发起失败:" + e.getMessage() + "<br>");
				}
			}
		}
		return msg.toString();
	}

	/**
	 * 获取dhr转签人员数据
	 * @return
	 */
	public String getChangeContractBacklogFromDhr() {
		StringBuilder msg = new StringBuilder();
		JSONArray array;
		try {
 			array = dhrRestClient.getData(dhrGetChangeContractBacklogFuncId);
		} catch (Exception e) {
			msg.append("拉取DHR待转签员错误!" + e.getMessage());
			logger.error(msg.toString());
			return msg.toString();
		}

		if (array == null || array.size() == 0) {
			return "没有待转签的人员";
		}

		Map<String, UserChangeContractBacklog> removeMap = changeContractBacklogService.selectBacklogByStatus(BacklogStatus.SIGN);
		for (Object o : array) {
			JSONObject data = (JSONObject) o;
			User user = userService.getUserByWorkNumber(data.getString("Badge"));
			if (user == null || user.getStatus().equals("已禁用")) {
				msg.append(data.getString("Name") + "账号不存在或已禁用，拉取转签失败。<br>");
				continue;
			}
			/*标注中心人员需要使用合同管理*/
//			if (user.getCreateTag() != null && user.getCreateTag() == 4) {
//				msg.append(data.getString("Name") + "data++执行中心不拉取。<br>");
//				continue;
//			}

			removeMap.remove(user.getUserId());

			UserChangeContractBacklog backlog = changeContractBacklogService.selectNotFinishBacklogByUserid(user.getUserId());
			//需要新增续签记录的人
			if (backlog == null || backlog.getStatus().equals(BacklogStatus.FINISH)) {
				backlog = new UserChangeContractBacklog();
				backlog.setUserId(user.getUserId());
				backlog.setWorkNumber(user.getWorkNumber());
				backlog.setStatus(BacklogStatus.PULL);
				backlog.setXtype(data.getString("xtype"));
				backlog.setContract(data.getString("Contract"));
				backlog.setNewContract(data.getString("New_Contract"));
				backlog.setWorkCity(data.getString("WorkCity"));
				backlog.setNewWorkCity(data.getString("New_WorkCity"));
				backlog.setConBeginDate(data.getString("ConBeginDate"));
				backlog.setConEndDate(data.getString("ConEndDate"));
				backlog.setNewConBeginDate(data.getString("NEW_CONBEGINDATE"));
				backlog.setNewConEndDate(data.getString("NEW_CONENDDATE"));
				backlog.setEffectDate(data.getString("EffectDate"));

				if (changeContractBacklogService.addContractBacklog(backlog)) {
					msg.append(user.getUserId() + "转签状态:已拉取");
				} else {
					msg.append(user.getUserId() + "转签状态:修改已拉取失败");
				}
				continue;
			}
		}

		if (removeMap.size() == 0) {
			return msg.toString();
		}

		for (Map.Entry<String, UserChangeContractBacklog> entry : removeMap.entrySet()) {
			entry.getValue().setStatus(BacklogStatus.FINISH);
			if (changeContractBacklogService.updateContractBacklogStatus(entry.getValue())) {
				msg.append(entry.getValue().getUserId() + "转签状态:已完成");
			} else {
				msg.append(entry.getValue().getUserId() + "转签状态:修改已完成失败");
			}
		}
		return msg.toString();
	}

	/**
	 * 定时拉取DHR离职人员列表
	 * @return
	 */
	public String getDismissionContractBacklogFromDhr() {
		StringBuilder msg = new StringBuilder();
		JSONArray array;
		try {
			array = dhrRestClient.getData(dhrGetDismissionContractBacklogFuncId);
			if (array == null || array.size() == 0) {
				return "没有待离职的人员";
			}
		} catch (Exception e) {
			msg.append("拉取DHR待离职人员错误!" + e.getMessage());
			logger.error(msg.toString());
			return msg.toString();
		}
		List<String> removeMap = userDimissionContractBacklogService.selectAllBacklog();
		for (Object o : array) {
			JSONObject data = (JSONObject) o;
			if(!removeMap.contains(data.getString("ID"))){
				UserDimissionContractBacklogPO backlog = new UserDimissionContractBacklogPO();
				backlog.setStatus(BacklogStatus.PULL);
				backlog.setFlowStatus("0");
				backlog.setDismissionId(data.getString("ID"));
				backlog.setWorkNumber(data.getString("Badge"));
				backlog.setUserName(data.getString("Name"));
				backlog.setAccount(data.getString("UserID"));
				backlog.setTopTeam(data.getString("Deptitle"));
				backlog.setDismissionDate(DateTimeUtil.stringToDate(data.getString("LeaveDate")));
				backlog.setSocialLastTime(data.getString("sbjzy"));
				backlog.setAccumulationLastTime(data.getString("gjjjzy"));
				backlog.setCompensation(data.getInteger("sfbcj"));
				backlog.setShares(data.getInteger("sfgf"));
				backlog.setCompetition(data.getInteger("sfjyxx"));
				backlog.setSelfEmail(data.getString("selfemail"));
				backlog.setCreateTime(new Date());
				backlog.setCompany(data.getString("comp"));
				backlog.setCompetitionDuration(data.getString("competitionDuration"));
				backlog.setCompetitionStart(DateTimeUtil.stringToDate(data.getString("competitionStart")));
				backlog.setCompetitionEnd(DateTimeUtil.stringToDate(data.getString("competitionEnd")));
				backlog.setInitialStartTime(DateTimeUtil.stringToDate(data.getString("Firstconbegindate")));
				if (userDimissionContractBacklogService.addBacklog(backlog)) {
					msg.append(data.getString("UserID") + "离职状态:已拉取");
				} else {
					msg.append(data.getString("UserID") + "离职状态:拉取失败");
				}

				continue;
			}
			else {
                UserDimissionContractBacklogPO backlog = userDimissionContractBacklogService.getBacklogByDismissionId(data.getString("ID"));
                backlog.setInitialStartTime(DateTimeUtil.stringToDate(data.getString("Firstconbegindate")));

				if (userDimissionContractBacklogService.updateBacklogInfo(backlog)){
					msg.append(data.getString("UserID") + "已更新");
				}else {
					msg.append(data.getString("UserID") + "更新失败");
				}

				continue;
			}


		}
		return msg.toString();
	}

	/**
	 * 定时拉取DHR离职人员列表，包括离职中，取消离职，离职流程完成的
	 * @return
	 */
	@Transactional
	public String getDimissionProcessBacklogFromDhr(){
		StringBuilder msg = new StringBuilder();
		JSONArray array;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("E MMM dd hh:mm:ss Z yyyy", Locale.ENGLISH);
		try {
			array = dhrRestClient.getData(dhrGetDismissioningBacklogFuncId);
			if (array == null || array.size() == 0) {
				return "没有离职流程数据变更";
			}
		} catch (Exception e) {
			msg.append("拉取DHR离职离职流程错误!" + e.getMessage());
			logger.error(msg.toString());
			return msg.toString();
		}
		for (Object o : array) {
			JSONObject data = (JSONObject) o;
			UserDimissionProcessBacklogPO processBacklogPO=userDimissionProcessBacklogService.getProcessByDismissionId(data.getString("ID"));
			User user = userService.getUserByWorkNumber(data.getString("Badge"));
			if(user ==null){
				msg.append(data.getString("Badge") + "工号不存在");
				continue;
			}
			if(processBacklogPO == null){
				UserDimissionProcessBacklogPO backlog = new UserDimissionProcessBacklogPO();
				backlog.setXType(Integer.parseInt(data.getString("xtype")));
				backlog.setDimissionId(data.getString("ID"));
				backlog.setMonitorStatus(1);
				backlog.setUserId(user.getUserId());
				try {
					backlog.setDimissionDate(simpleDateFormat.parse(data.getString("LeaveDate")));
//					String userDn = ldapService.getRealUserDn(backlog.getUserId());
//					ldapService.addMemberToGroupByUserDn("dimissioning", userDn);
					//提了dhr离职申请的人员，ldap中进行特殊标记，飞连上会筛选该标记，进行监控操作
					ldapService.modifyUser(user.getUserId(), "extensionAttribute10", "6666");
					userDimissionProcessBacklogService.addProcess(backlog);
					//添加监控群组
					msg.append(user.getUserId() + "离职流程:已拉取");
				}catch (Exception e){
					msg.append(user.getUserId() + "离职流程:拉取失败" + e.getMessage());
					continue;
				}
			}
			else {
				Integer xtype = Integer.parseInt(data.getString("xtype"));
				try {
					processBacklogPO.setDimissionDate(simpleDateFormat.parse(data.getString("LeaveDate")));
					//只有当取消离职时，才进行更新
					if( xtype.equals(9) && !xtype.equals(processBacklogPO.getXType()) && processBacklogPO.getMonitorStatus()==1){
						//移除监控群组
						String result = ldapService.removeUserFromDimissioning(user.getUserId());
						try  {
							ldapService.modifyUser(user.getUserId(), "extensionAttribute10", "7777");
							msg.append(user.getUserId()).append("移除监控组成功");
							processBacklogPO.setMonitorStatus(0);
							processBacklogPO.setXType(xtype);
							processBacklogPO.setUpdateTime(new Date());
							userDimissionProcessBacklogService.updateProcessInfo(processBacklogPO);
							msg.append(result);
							msg.append(user.getUserId()).append("更新成功");
						} catch (Exception e1) {
							msg.append(e1.getMessage());
							e1.printStackTrace();
						}
					}
				}catch (Exception e){
					msg.append(user.getUserId()).append("更新失败").append(e.getMessage());
				}
			}
		}
		return msg.toString();
	}

	/**
	 * 获取合同台账
	 * @return
	 */
	public String getHistoryContractFromDhr(List<String> removeMap) {
		StringBuilder msg = new StringBuilder();
		JSONArray array;
		try {
			array = dhrRestClient.getData(dhrGetDismissionHistoryContractFuncId);
			if (array == null || array.size() == 0) {
				return "没有合同台账";
			}
		} catch (Exception e) {
			msg.append("拉取DHR合同台账错误!" + e.getMessage());
			logger.error(msg.toString());
			return msg.toString();
		}
		List<UserContract> contractNoSignStatus = userContractMapper.getContractNoSignStatus();//获取所有没有合同类型的
		for (Object o : array) {
			JSONObject data = (JSONObject) o;
			try {
				String num_effect=data.getString("Badge")+data.getString("effect_date");
				if(!removeMap.contains(num_effect)){//mit没有的合同
					UserContract contract = new UserContract();
					contract.setUserId(data.getString("UserID"));
					contract.setWorkNumber(data.getString("Badge"));
					contract.setSpell(data.getString("Name"));
					contract.setContractNumber(data.getString("Contract_number"));
					contract.setContractType(data.getString("Contract_type"));
					contract.setContractCount(StringUtils.isEmpty(data.getString("Contract_count"))?"0":data.getString("Contract_count"));
					contract.setContractStatus(data.getString("Contract_status"));
					contract.setContractAttribute(data.getString("Contract_attribute"));
					contract.setStartDate(DateTimeUtil.string2DateYMD(data.getString("start_date")));
					contract.setEndDate(StringUtils.isEmpty(data.getString("end_date"))?null:DateTimeUtil.string2DateYMD(data.getString("end_date")));
					contract.setEffectDate(DateTimeUtil.string2DateYMD(data.getString("effect_date")));
					contract.setCompany(data.getString("company"));
					contract.setPosition(data.getString("position"));
					contract.setEmployBase(data.getString("employ_base"));
					contract.setCertType(data.getString("Cert_type"));
					contract.setCertNo(data.getString("Cert_no"));
					contract.setAddress(data.getString("Address"));
					contract.setMemo(data.getString("memo"));
					contract.setSignStatus(data.getString("sign_status"));
					contract.setUpdateTime(LocalDateTime.now());
					contract.setCreatorUserId("dhr");
					userContractMapper.insertUserContract(contract);
					continue;
				}else {//mit有的合同
					try {
						if ("合同解除".equals(data.getString("sign_status"))){
							UserContract contract = userContractMapper.selectContractByContractNumber(data.getString("Contract_number"));
							contract.setStartDate(DateTimeUtil.string2DateYMD(data.getString("start_date")));
							contract.setEndDate(StringUtils.isEmpty(data.getString("end_date"))?null:DateTimeUtil.string2DateYMD(data.getString("end_date")));
							if (userContractMapper.updateUserCustomizeContract(contract) > 0){
								logger.info(contract.getSpell() + "更新成功");
							}
						}
					}catch (Exception e){
						logger.info(data.getString("Contract_number") + "合同异常");
					}
					List<UserContract> userContracts = contractNoSignStatus.stream().filter(f -> f.getWorkNumber().equals(data.getString("Badge")) & DateTimeUtil.date2String(f.getEffectDate()).equals(data.getString("effect_date"))).collect(Collectors.toList());
					for (UserContract userContract : userContracts) {
						userContract.setSignStatus(data.getString("sign_status"));
						userContractMapper.updateUserContract(userContract);
					}
				}
			}catch (Exception e){
				msg.append(data.getString("Badge")+"失败；");
				logger.info("插入DHR台账失败，合同编号："+data.getString("Badge")+"；异常消息："+e.getMessage());
			}
		}
		return msg.toString();
	}
	/**
	 * 根据离职结算单id获取离职人员薪资数据
	 * @param dismissionId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getDismissionStaff(String dismissionId) throws Exception{
		JSONObject paras = new JSONObject();
		paras.put("ID", dismissionId);
		try {
			JSONArray dataObject = dhrRestClient.getDataObject(dhrGetDismissionContractStaffFuncId, paras);
			return dataObject.getJSONObject(0);
		}catch (Exception e){
			logger.error("获取DHR离职人员薪资异常"+e.getMessage());
			throw new Exception("获取DHR离职人员薪资异常"+e.getMessage());
		}
	}

	/**
	 * 从DHR同步离职时间，触发OA流程
	 *
	 * @return
	 */
	public String getDimssionDateFromDhr() {
		StringBuilder msg = new StringBuilder();
		JSONArray array;
		try {
			array = dhrRestClient.getData(dhrGetDimissionFuncId);
		} catch (Exception e) {
			msg.append("拉取DHR离职人员错误!" + e.getMessage());
			logger.error(msg.toString());
			return msg.toString();
		}

		if (array == null) {
			msg.append("DHR离职信息没有返回数据!请联系DHR管理员");
			mailSendService.sendMail("DHR离职信息没有返回数据", "时间:" + LocalDateTime.now(), "<EMAIL>");
			return msg.toString();
		}

		for (Object o : array) {
			JSONObject data = (JSONObject) o;

			String workNumber = data.getString("Badge");
			LocalDateTime dimissionDate = DateTimeUtil.string2DateYMD(data.getString("LeaveDate"));

			//历史数据判断与今天的相差天数，超过60天的跳过不判断，
			if (dimissionDate.isBefore(LocalDateTime.now()) && DateTimeUtil.diffDateByDay(dimissionDate, LocalDateTime.now()) < -60) {
				continue;
			}

			LocalDateTime now = LocalDateTime.now();
			User user = userService.getUserByWorkNumber(workNumber);

			if (user == null) {
                msg.append(workNumber + "不存在<br>");
                continue;
            }

            //离职日期未发生变化 跳过
            if (user.getDimissionDate() != null && user.getDimissionDate().equals(dimissionDate)) {
                logger.info(user.getUserId() + "离职日期未发生变化<br>");
                continue;
            }

            if (!user.getEntryFlow().equals("在职")) {
                msg.append(workNumber + "回流员工不修改离职日期<br>");
                continue;
            }

            //已禁用的人如果没有离职日期 依然推送OA
            if (user.getStatus() != null && user.getStatus().indexOf("已禁用")!=-1) {
                if (user.getDimissionDate() != null) {
                    continue;
                }
            }

            Map<String, String> map = new HashMap<>();
			if (data.getString("Job_handever") != null) {
				User handever = userService.getUserByUserId(data.getString("Job_handever"));
				if (handever != null) {
					map.put("handoverWorknumber", handever.getWorkNumber());
				} else {
					logger.error(workNumber + "离职交接人:" + data.getString("Job_handever") + "在mit中不存在<br>");
					msg.append(workNumber + "离职交接人:" + data.getString("Job_handever") + "在mit中不存在<br>");
					continue;
				}
			} else {
				map.put("handoverWorknumber", "");
			}
			map.put("workBase", data.getString("Lea_WorkCity"));
			map.put("dimissionDate", data.getString("LeaveDate"));


			if (now.isAfter(dimissionDate)) {
				logger.info(user.getUserId() + "离职日期异常");
				mailSendService.sendMail(user.getUserId() + "离职日期异常", "离职时间:" + dimissionDate +
						"<br>姓名:" + user.getSpell() + "<br>工号:" + user.getWorkNumber(), "<EMAIL>");
			}

			msg.append(user.getUserId() + "更新离职日期:" + dimissionDate + "------");
			user.setDimissionDate(dimissionDate);
			userService.updateUser(user);

			msg.append(dimissionService.dimissionFlow(user, map));
			msg.append("<br>");

		}
		return msg.toString();

	}


	/**
	 * 推送人员数据到DHR
	 *
	 * @param user
	 * @return
	 */
	public String syncUser2Dhr(User user) {
		StringBuilder msg = new StringBuilder();

		if (user.getEntryFlow() != null && "OA流程".equals(user.getEntryFlow())) {
			msg.append(user.getUserId() + "处于'OA流程'状态，不创建DHR账号");
			return msg.toString();
		}

//        aliyunUserInfoService.deletePsw(user.getUserId());
		//DHR与SF并行期间关闭
		entryService.deletePSW(user.getUserId());

		if (user.getCreateTag() != null && (user.getCreateTag() == 1 || user.getCreateTag() == 3)) {
			msg.append(user.getUserId() + "人员 create_tag为" + user.getCreateTag() + ",不创建DHR账号");
			return msg.toString();
		}

		//转化DHR
		JSONArray bizData = new JSONArray();
		try {
			JSONObject data = getDhrJson(user);
			if (data == null) {
				msg.append(user.getUserId() + ":dhr信息转换错误!");
				return msg.toString();
			}
			bizData.add(data);
			msg.append(user.getUserId() + ":dhr信息转换成功---");
		} catch (Exception e) {
			msg.append(user.getUserId() + ":dhr信息转换错误!" + e);
			logger.error(e.getMessage());
			return msg.toString();
		}

		//执行推送步骤
		try {
			msg.append(dhrRestClient.putStart(dhrPutUserFuncId) ? "putStart成功---" : "putStart失败---");
			msg.append(dhrRestClient.putData(user.getUserId(), bizData, dhrPutUserFuncId) ? "putData成功---" : "putData失败---");
			msg.append(dhrRestClient.putClose(user.getUserId(), dhrPutUserFuncId));

			user.setEntryFlow("在职");
			userService.updateUserEntryFlow(user);

			return msg.toString();
		} catch (Exception e) {
			msg.append("DHR调用异常:" + e.getMessage());
			logger.error("DHR调用异常:" + e.getMessage());
			return msg.toString();
		}
	}

	/**
	 * 封装DHR需要的数据格式
	 *
	 * @param user
	 * @return
	 * @throws Exception
	 */
	public JSONObject getDhrJson(User user) throws Exception {
		String workNumber = user.getWorkNumber();

		UserInfoLocale userInfoLocale = userInfoLocaleService.getUserInfoLocaleByUserId(user.getUserId());
		if (userInfoLocale == null) {
			logger.error(user.getUserId() + "local信息不存在!");
			throw new Exception(user.getUserId() + "local信息不存在!");
		}

		JSONObject info = JSONObject.parseObject(StringEscapeUtils.unescapeJava(userInfoLocale.getInfo().replace(" ", "")));
		Team team = teamService.getTeamById(user.getTeamId());

		if (info == null) {
			logger.error(user.getUserId() + "local信息不存在!");
			throw new Exception(user.getUserId() + "local信息不存在!");
		}

		if (team == null || team.getName().indexOf("old") != -1) {
			logger.error(user.getUserId() + "部门不存在!");
			throw new Exception(user.getUserId() + "部门不存在!");
		}

		String photoCert = entryService.getPhotoCert(user.getUserId());
		String certId = photoCert != null ? dhrRestClient.putPhoto(photoCert, user.getUserId(), user.getSpell() + "cert") : "";

		String photoLife = entryService.getPhotoLife(user.getUserId());
		String lifeId = photoLife != null ? dhrRestClient.putPhoto(photoLife, user.getUserId(), user.getSpell() + "life") : "";

		JSONObject users = getDhrUserEntity(user, team.getName(), team.getSfCode(), info, (userInfoLocale.getBank() != null && !userInfoLocale.getBank().equals("")) ? dhrRestClient.getBankCode(userInfoLocale.getBank()) : info.getString("BANKNAME"), userInfoLocale.getEmail(), certId, lifeId);
		JSONArray edus = getDhrArray(workNumber, info.getJSONArray("edu"));
		JSONArray workings = getDhrArray(workNumber, info.getJSONArray("working"));
		JSONArray titles = getDhrArray(workNumber, info.getJSONArray("title"));
		JSONArray trainings = getDhrArray(workNumber, info.getJSONArray("train"));
		JSONArray projects = getDhrArray(workNumber, info.getJSONArray("project"));
		JSONArray hortations = getDhrArray(workNumber, info.getJSONArray("hortation"));
		JSONArray familys = getDhrArray(workNumber, info.getJSONArray("family"));
		JSONArray languages = getDhrArray(workNumber, info.getJSONArray("language"));

		JSONObject data = new JSONObject();
		data.put("staff", users.toString());
		data.put("eBG_Edu", edus.toString());
		data.put("eBG_Working", workings.toString());
		data.put("eBG_Title", titles.toString());
		data.put("ebg_training", trainings.toString());
		data.put("eBG_Project", projects.toString());
		data.put("eBG_Hortation", hortations.toString());
		data.put("ebg_family", familys.toString());
		data.put("eBG_Language", languages.toString());

		if (user.getOfferId() != null) {
			Map<String, String> map;
			try {
				map = mokaService.getMokaOfferMap(user.getOfferId());
			} catch (Exception e) {
				logger.error("获取moka_offer异常:" + e.getMessage());
				throw new Exception(user.getUserId() + "获取moka_offer异常！offer_id=" + user.getOfferId());
			}
			JSONArray salary = getDhrArray(workNumber, getDhrSalaryEntity(map));
			data.put("cstaff", salary.toString());
		}

		return data;
	}


	/**
	 * 拼装DHR 薪酬字段
	 *
	 * @param map
	 * @return
	 */
	public JSONArray getDhrSalaryEntity(Map<String, String> map) {
		JSONArray array = new JSONArray();
	 	JSONObject data = new JSONObject();

	 	data.put("Homeunit_Remark", map.get("上家公司离职原因"));
	 	data.put("Homeunit_MonSlary", map.get("原平均月薪（元/月）"));
	 	data.put("Homeunit_YearSlary", map.get("原年薪（元/年）"));
	 	data.put("ExpectMonSlary", map.get("期望薪资（元/月）"));
	 	data.put("SALARYTYPE", map.get("薪资类型")!=null?dhrRestClient.getSalaryTypeCode(map.get("薪资类型")):null);
	 	data.put("MONSALARY", map.get("建议薪资（元/月）"));
	 	data.put("YEARSALARY", map.get("建议薪资（元/年）"));
	 	data.put("DaySlary", map.get("建议薪资（元/日）"));
	 	data.put("Slary_Gains", map.get("薪酬涨幅（%）"));
	 	data.put("SignOnType", map.get("Sign on类型")!=null?dhrRestClient.getSignOnTypeCode(map.get("Sign on类型")):null);
	 	data.put("SIGNONSUM", map.get("Sign on总金额（元）"));
	 	data.put("HAVEOPTIONS", map.get("是否有期权"));
	 	data.put("Haveoptions_Value", map.get("授予期权价值（元）"));
	 	data.put("MEALSUPPLEMENT", map.get("是否有餐费补贴"));
	 	data.put("IsHWBT", map.get("是否有海外补贴"));
	 	data.put("isswzsbt", map.get("双外住宿补贴"));

	 	array.add(data);
	 	return array;
	}


	/**
	 * 拼装DHR staff json
	 *
	 * @param data
	 * @param teamName
	 * @param info
	 * @param bank
	 * @param email
	 * @param certId
	 * @param lifeId
	 * @return
	 * @throws Exception
	 */
	private JSONObject getDhrUserEntity(User data, String teamName, String teamCode, JSONObject info, String bank, String email, String certId, String lifeId) throws Exception {
		JSONObject user = new JSONObject();

		user.put("Badge", data.getWorkNumber());
		user.put("Depid", teamName);
		user.put("Depcode", teamCode);
		user.put("PYNAME", data.getUserId());
		user.put("Name", data.getSpell());
		user.put("EmpCustom2", data.getSales()!=null?data.getSales():"");
		if (data.getCreateTag() != null && data.getCreateTag() == 4) {
			user.put("EZID", 101);
		} else {
			user.put("EZID", 100);
		}

		if (data.getBuddy() != null) {
			User buddy = userService.getUserByUserId(data.getBuddy());
			user.put("BUDDY", buddy != null && buddy.getWorkNumber() != null ? buddy.getWorkNumber() : "");
		} else {
			user.put("BUDDY", "");
		}

		if (data.getSiteHrg() != null) {
			User siteHr = userService.getUserByUserId(data.getSiteHrg());
			user.put("SITEHR", siteHr != null && siteHr.getWorkNumber() != null ? siteHr.getWorkNumber() : "");
		} else {
			user.put("SITEHR", "");
		}

		user.put("EMPTYPE", dhrRestClient.getEmpTypeCode(data.getEmployType()));//员工类型
		String workbase = data.getWorkBase();
		user.put("OFFICESPACE", dhrRestClient.getWorkBaseCode(workbase));//入职地点
		user.put("WORKCITY", dhrRestClient.getEmployBaseCode(data.getEmployBase()));//工作地点

		user.put("JOBGRADE", (data.getTitle().indexOf("无") != -1 || data.getTitle().equals("")) ? "WU" : data.getTitle());
		user.put("JOBCUSTOM1", dhrRestClient.getDutyCode(data.getDuty()));//职能
		user.put("JOBCUSTOM2", data.getSequence() != null ? data.getSequence() : "");//序列

		if (data.getReferTitle() != null && data.getReferTitle().indexOf("无") == -1) {
			user.put("TECHGRADING", dhrRestClient.getReferTitleCode(data.getReferTitle()));//参考技术定级
		} else {
			user.put("TECHGRADING", "A01");
		}
		user.put("CONTRACT", data.getCompany());//合同签约单位

		//从新计算试用期截止日期
		LocalDateTime expectDate = data.getExpectDate();//入职日期
		LocalDateTime regularDate = null;//试用期截止日期
		if (expectDate != null && data.getEmployType() != null && "正式员工".equals(data.getEmployType())) {
			regularDate = DateTimeUtil.dateAddInt(expectDate, 180);
			if (!regularDate.equals(data.getRegularDate())) {
				data.setRegularDate(regularDate);
				userService.updateUser(data);
			}
			user.put("PROBENDDATE", regularDate);
		} else {
			user.put("PROBENDDATE", "");
		}

		user.put("JOINDATE", expectDate);

		user.put("RECRUCHANNELS", dhrRestClient.getSourceCode(data.getSource()));//招聘渠道

		user.put("BANKNAME", bank != null ? bank : "99");//开户行
		user.put("OPENINGBANK", info.getString("OPENINGBANK"));
		user.put("BANKACCOUNT", info.getString("BANKACCOUNT"));
		user.put("IFJEXZXY", info.getString("IFJEXZXY"));//是否竞业限制
		user.put("COUNTRY", info.getString("COUNTRY"));//国籍
		user.put("NATION", info.getString("NATION"));//民族
		user.put("CERTTYPE", info.getString("CERTTYPE"));//证件类型
		user.put("CERTNO", info.getString("CERTNO"));
		user.put("CERTVALIDITYTERM", info.getString("CERTVALIDITYTERM").split("T")[0]);//证件有效期
		user.put("GENDER", info.getString("GENDER"));
		user.put("BIRTHDAY", info.getString("BIRTHDAY").split("T")[0]);
		user.put("HIGHLEVEL", info.getString("HIGHLEVEL"));//最高学历
		user.put("HIGHDEGREE", info.getString("HIGHDEGREE"));//最高学位
		user.put("MAJOR", info.getString("MAJOR"));//专业
		user.put("HIGHTITLE", info.getString("HIGHTITLE"));//最高职称
		user.put("PARTY", info.getString("PARTY"));//政治面貌
		user.put("RESIDENT", info.getString("RESIDENT"));//籍贯
		user.put("RESIDENTTYPE", info.getString("RESIDENTTYPE"));//户口性质
		user.put("RESIDENTADDRESS", info.getString("RESIDENTADDRESS"));
		user.put("RESIDENTCITY", info.getString("RESIDENTCITY"));//户口所在城市
		user.put("ADDRESS", info.getString("ADDRESS"));
		user.put("MARRIAGE", info.getString("MARRIAGE"));//婚姻状况

		user.put("FERTILITYSTATE", info.getString("FERTILITYSTATE"));//生育状况
		user.put("EMAIL_PERS", email);
		user.put("MOBILE", data.getCell());
		user.put("TSSIZE", info.getString("TSSIZE"));//T恤尺码
		user.put("HOBBY", info.getString("HOBBY"));
		user.put("PERSONALINTRO", info.getString("PERSONALINTRO"));
		user.put("YWFZJL", info.getString("YWFZJL"));//有无犯罪记录
		user.put("LINKNAME", info.getString("LINKNAME"));
		user.put("LINKRELATION", info.getString("LINKRELATION"));//紧急联系人关系
		user.put("LINKPHONE", info.getString("LINKPHONE"));
		user.put("HEALTH", info.getString("HEALTH"));//健康状况
		user.put("IDPHOTO", certId);
		user.put("LIFEPHOTO", lifeId);
		user.put("REMARK", data.getMemo() != null ? data.getMemo().replace(" ", "") : "");//备注
		user.put("BSID", data.getBeisenId()!=null?data.getBeisenId():null);
		user.put("WORKBEGINDATE", info.getString("WORKBEGINDATE").split("T")[0]);//开始工作日期

		//判断使用moka薪酬还是北森
		user.put("ISXC", data.getOfferId()!=null?"1":"0");

		return user;
	}


	/**
	 * 预入职系统填写的array 转化DHR需要的ARRAY 只添加工号
	 *
	 * @param workNumber
	 * @param info
	 * @return
	 */
	private JSONArray getDhrArray(String workNumber, JSONArray info) {
		for (Object o : info) {
			JSONObject data = (JSONObject) o;
			data.put("Badge", workNumber);

		}

		return info;
	}


}
