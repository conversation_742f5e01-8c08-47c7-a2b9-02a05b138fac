package com.megvii.entity.opdb;


import java.lang.annotation.*;

/**
 * 标识字段在excel中列索引的注解
 *
 * <AUTHOR>
 * @date 2019-11-06
 */
@Documented
@Retention(value = RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Column {
    /**
     * 返回字段的index值
     *
     * @return 索引值
     */
    int index();

    /**
     * 是否作为导出的字段
     * @return 是否导出
     */
    boolean export() default true;

    /**
     * 是否作为读取的字段
     * @return 是否读取
     */
    boolean read() default true;

    /**
     * 导出时的列名
     * @return 导出时的列名
     */
    String name() default "";
}
