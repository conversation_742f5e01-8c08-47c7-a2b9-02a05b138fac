package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.entity.opdb.MitUserSystem;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import com.megvii.client.PanguRestClient;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.ImageFile;
import com.megvii.entity.opdb.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/6/17 14:22
 */
@Service
public class PanguService {

	Logger logger= LoggerFactory.getLogger(PanguService.class);

	@Autowired
	private PanguRestClient panguRestClient;

	@Autowired
	private MitUserSystemService mitUserSystemService;


	/**
	 * 删除盘古用户
	 * @param userId
	 * @return
	 */
	public String deletePanguUser(String userId) {
		MitUserSystem mitUserSystem = mitUserSystemService.selectMitUserSystemByUserId(userId);
		if (mitUserSystem == null) {
			return userId + "没有盘古数据!";
		}

		ThreadLocalRandom random = ThreadLocalRandom.current();
		String appkey = "appkey" + random.nextInt(1, 3);
		String nonce = String.valueOf(random.nextInt(10000, 999999));

		List list = new ArrayList();
		list.add(mitUserSystem.getPanguId());

		JSONObject data = new JSONObject();
		data.put("uuidList", list);

		try {
			panguRestClient.deleteUser(data, userId, appkey, nonce);
			return userId + "删除盘古成功！";
		} catch (Exception e) {
			logger.error(e.getMessage());
			return userId + "删除盘古失败！";
		}
	}


	/**
	 * 添加盘古用户
	 * @param user
	 * @param photoCert
	 * @return
	 */
	public String addPanguUser(User user, ImageFile photoCert) {
		try {
			ThreadLocalRandom random = ThreadLocalRandom.current();
			String appkey = "appkey" + random.nextInt(1,3);
			String nonce = String.valueOf(random.nextInt(10000,999999));
			String uri = panguRestClient.uploadImage(photoCert, user.getUserId(), appkey, nonce);
			if (panguRestClient.addUser(getAddUserBody(user, uri), user.getUserId(), appkey, nonce)) {
				MitUserSystem mitUserSystem = new MitUserSystem();
				mitUserSystem.setWorkNumber(user.getWorkNumber());
				mitUserSystem.setUserId(user.getUserId());
				mitUserSystem.setPanguUri(uri);
				mitUserSystem.setPanguId(user.getUserId() + "_" + user.getWorkNumber());

				mitUserSystemService.addMitUserSystem(mitUserSystem);
				return user.getUserId() + "添加盘古<span style=\"color:#3eda67;\">【成功】</span>";
			}
		} catch (Exception e) {
			logger.error("入职新增盘古用户异常：" + e.getMessage());
		}

		return user.getUserId() + "添加盘古<span style=\"color:#dc143c;\">【失败】</span>";
	}

	/**
	 * 获取盘古添加用户body
	 * @param user
	 * @param uri
	 * @return
	 */
	private JSONObject getAddUserBody(User user, String uri) {
		JSONObject panguUser = new JSONObject();
		panguUser.put("name", user.getSpell());
		panguUser.put("type", 1);
		panguUser.put("email", user.getUserId() + "@megvii.com");
		panguUser.put("uuid", user.getUserId() + "_" + user.getWorkNumber());
		if (uri != null) {
			panguUser.put("imageUri", uri);
		}
		panguUser.put("code", user.getUserId());
		panguUser.put("groupList", new String[]{"976055aae31445c5a583ebc1a53c7514"});

		JSONArray array = new JSONArray();
		array.add(panguUser);

		JSONObject data = new JSONObject();
		data.put("personList", array);

		return data;
	}


}
