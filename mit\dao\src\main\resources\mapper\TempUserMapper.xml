<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.TempUserMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.TempUser">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="spell" property="spell"/>
        <result column="cell" property="cell"/>
        <result column="mentor" property="mentor"/>
        <result column="expire_date" property="expireDate"/>
        <result column="status" property="status"/>
        <result column="updater" property="updater"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="permission" property="permission"/>
        <result column="entry_time" property="entryTime"/>
        <result column="dismiss_time" property="dismissTime"/>
        <result column="tech_type" property="techType"/>
        <result column="company" property="company"/>
        <result column="work_base" property="workBase"/>
        <result column="team" property="team"/>
        <result column="project" property="project"/>
        <result column="position" property="position"/>
        <result column="door_card" property="doorCard"/>
        <result column="hrg" property="hrg"/>
        <result column="oa_id" property="oaId"/>
        <result column="other_perm" property="otherPerm"/>
        <result column="dingding_id" property="dingdingId"/>
    </resultMap>


    <select id="selectNeedDisTempUserList" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT * FROM IT_temp_user WHERE dismiss_time &lt;= NOW() AND `status` = 'active'
    </select>

    <update id="updateTempUserStatus" >
        update IT_temp_user set status = 'inactive' where user_id = #{userId}
    </update>

    <select id="selectTempUserByEntryTime" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT * FROM IT_temp_user WHERE DATE_FORMAT(entry_time, '%Y-%m-%d' ) = DATE_FORMAT(#{entryTime}, '%Y-%m-%d' )
    </select>

    <select id="selectTempByPhone" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT * FROM IT_temp_user WHERE cell = #{phone}
    </select>
    
    <select id="selectTempByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT * FROM IT_temp_user WHERE user_id = #{userId}
    </select>

    <update id="updateHrgByMentor">
        UPDATE  IT_temp_user
        <set>
            <if test="hrg != null">
                hrg = #{hrg},
            </if>
                update_time = NOW()
        </set>
        where mentor = #{userId}
    </update>

    <select id="selectTempByMentor" resultMap="BaseResultMap">
        <if test="userId != null">
            select * from IT_temp_user where mentor = #{userId}
        </if>
    </select>
</mapper>