package com.megvii.controller;

import com.alibaba.druid.util.StringUtils;
import com.megvii.controller.api.LoginVO;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.Menu;
import com.megvii.entity.opdb.User;
import com.megvii.service.EnvService;
import com.megvii.service.UserMenuService;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/8 11:51
 */
@RestController
@RequestMapping("/api/V1")
public class LoginController {

	@Value("${casServerUrlPrefix}")
	private String casServer;

	@Value("${cas.serviceUrl}")
	private String front_url;

	@Autowired
	private EnvService service;

	@Autowired
	private UserMenuService userMenuService;


    @GetMapping("/login")
	@ResponseBody
    public void getLogin(String next) throws IOException {

		if (!StringUtils.isEmpty(next)) {
			System.out.println("next:--------------------->" + next);
//			logger.info("next:--------------------->" + next);
			service.response.sendRedirect(next);
		} else {
			System.out.println("front_url:--------------------->" + front_url);
//			logger.info("front_url:--------------------->" + front_url);
			service.response.sendRedirect(front_url);
		}


	}


	@GetMapping("/logout")
	public void logout(HttpServletResponse response) {
		try {
			String casLogoutUrl = casServer + "/logout";
			Subject subject = SecurityUtils.getSubject();
			subject.logout();
			response.setStatus(HttpStatus.FOUND.value());
			response.setHeader("location", casLogoutUrl == null ? "" : casLogoutUrl + "?service=" + URLEncoder.encode(front_url, "utf-8"));
		} catch (Exception e) {
			System.out.println(1);
		}
	}


    @PostMapping("/isLogin")
    public ResponseDataEntity isLogin(){
    	ResponseDataEntity response = new ResponseDataEntity();
        Subject subject = SecurityUtils.getSubject();
        boolean flg = subject.isAuthenticated();
        if(flg) {
            User user = (User) subject.getPrincipal();
			List<Menu> menus = userMenuService.getMenuListByUserId(user.getUserId());
			if (menus == null || menus.size() == 0) {
				response.setCode(ResponseDataEntity.UNAUTHORIZED);
				return response;
			}

			response.setCode(ResponseDataEntity.OK);
			response.setData(LoginVO.toVO(user, menus));
			return response;
        }
        response.setCode(ResponseDataEntity.ERROR);
        return response;

    }


}
