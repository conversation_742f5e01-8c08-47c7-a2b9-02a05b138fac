package com.megvii.service;


import com.megvii.client.DhrRestClient;
import com.megvii.common.CommonException;
import com.megvii.common.ExcelUtil;
import com.megvii.entity.opdb.Column;
import com.megvii.entity.opdb.ExcelType;
import com.megvii.entity.opdb.User;

import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2020/1/14 18:47
 */
@Service
public class ExcelService {

    private Logger logger= LoggerFactory.getLogger(ExcelService.class);

    @Autowired
    private DhrRestClient dhrRestClient;

    /**
     * 生成商旅excel
     * 跳过没工号的人和外包
     * 已禁用状态的人第一列set 1
     * 公司匹配不到的使用默认公司
     */
    public File createGlytExcel(List<User> users, List<String> companyList , String modelPath, String resultPath, String fileName) {

        File newFile = ExcelUtil.createNewFile(modelPath, resultPath, fileName);
        InputStream is = null;
        XSSFWorkbook workbook = null;
        XSSFSheet sheet = null;
        try {
            is = new FileInputStream(newFile);// 将excel文件转为输入流
            workbook = new XSSFWorkbook(is);// 创建个workbook，
            // 获取第一个sheet
            sheet = workbook.getSheetAt(0);
        } catch (Exception e1) {
            e1.printStackTrace();
        }

        if (sheet != null) {
            try {
                int rowCount = 1;
                for (User user : users) {
                    try {
                        //跳过没工号的人
                        if (user.getWorkNumber() == null || user.getWorkNumber().equals("")) {
                            continue;
                        }

                        //跳过外包 桌面支持的外包在正式员工的库里
                        if (user.getUserId().indexOf("t-")!=-1) {
                            continue;
                        }

                        //跳过黑名单中的人
                        if (user.getCompany() == null || companyList.contains(user.getCompany())) {
                            continue;
                        }

                        //跳过data++执行中心人员
                        if (user.getCreateTag() != null && user.getCreateTag() == 4) {
                            continue;
                        }

                        //跳过mach人员
                        if (user.getCreateTag() != null && user.getCreateTag() == 5) {
                            continue;
                        }

                        XSSFRow row = sheet.createRow(rowCount);

                        XSSFCell cell = row.createCell(0);
                        cell.setCellValue(user.getStatus().equals("已禁用")?"1":"");

                        XSSFCell cell1 = row.createCell(1);
                        String company = dhrRestClient.getCompanyValue(user.getCompany());
                        cell1.setCellValue(company!=null?company:"北京旷视科技有限公司");

                        XSSFCell cell2 = row.createCell(2);
                        cell2.setCellValue(user.getWorkNumber());

                        rowCount+=1;
                    } catch (Exception e) {
                        logger.error(user.getUserId() + "插入excel异常****");
                        continue;
                    }

                }

                // 写数据
                FileOutputStream fos = new FileOutputStream(newFile);

                workbook.write(fos);
                fos.flush();
                fos.close();

            } catch (Exception e) {
                logger.error("生成excel出错*******");
            } finally {
                try {
                    if (null != is) {
                        is.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return newFile;
    }


    /**
     * 导出表格到临时文件
     * @param modelClass
     * @param excelType
     * @param dataList
     * @param fileName
     * @param <T>
     * @return
     */
    public <T> Map<String, Object> exportExcel(Class<T> modelClass, ExcelType excelType, List<T> dataList, String fileName) {
        Map<String, Object> resultMap = new HashMap<>();
        Workbook workbook;
        switch (excelType) {
            case XLS:
                workbook = new HSSFWorkbook();
                break;
            case XLSX:
                workbook = new XSSFWorkbook();
                break;
            default:
                throw new RuntimeException("导出格式非法!");
        }
        // 存放被@ColumnIndex注解修饰的字段列表
        List<Field> fields = new ArrayList<>();
        for (Field field : modelClass.getDeclaredFields()) {
            if (field.isAnnotationPresent(Column.class) && field.getAnnotation(Column.class).export()) {
                field.setAccessible(true);
                fields.add(field);
            }
        }

        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(
            createHelper.createDataFormat().getFormat("yyyy-mm-dd"));
        fields.sort(Comparator.comparingInt(o -> o.getAnnotation(Column.class).index()));
        Sheet sheet = workbook.createSheet();
        Row sheetHead = sheet.createRow(0);
        for (int column = 0; column < fields.size(); column++) {
            Cell cell = sheetHead.createCell(column);
            cell.setCellValue(fields.get(column).getAnnotation(Column.class).name());
        }
        for (int row = 1; row <= dataList.size(); row++) {
            Row dataRow = sheet.createRow(row);
            for (int column = 0; column < fields.size(); column++) {
                Cell cell = dataRow.createCell(column);
                setCellValue(fields.get(column), dataList.get(row - 1), cell);
                if (fields.get(column).getType().equals(Date.class)) {
                    cell.setCellStyle(dateStyle);
                }
            }
        }
        fileName = fileName + excelType.getFileSuffix();
        String filePath;
        try {
            filePath = "/data/server/expertContract/".concat(fileName);
            FileOutputStream outputStream = new FileOutputStream(filePath);
            workbook.write(outputStream);
        }catch (IOException e) {
            logger.error("写出文件流失败. errorMessage: {}, error: {}.", e.getMessage(), e);
            throw new CommonException("返回文件失败！");
        }finally {
            try {
                workbook.close();
            } catch (IOException e) {
                logger.error("关闭文件读取流失败! errorMessage: {}, error: {}.", e.getMessage(), e);
            }
        }
        resultMap.put("fileName", fileName);
        resultMap.put("filePath", filePath);
        return resultMap;
    }

    /**
     * 导出表格直接返回文件流
     * @param modelClass
     * @param excelType
     * @param dataList
     * @param fileName
     * @param response
     * @param <T>
     */
    public <T> void exportExcel(Class<T> modelClass, ExcelType excelType, List<T> dataList, String fileName, HttpServletResponse response) {
        Workbook workbook;
        switch (excelType) {
            case XLS:
                workbook = new HSSFWorkbook();
                break;
            case XLSX:
                workbook = new XSSFWorkbook();
                break;
            default:
                throw new RuntimeException("导出格式非法!");
        }
        // 存放被@ColumnIndex注解修饰的字段列表
        List<Field> fields = new ArrayList<>();
        for (Field field : modelClass.getDeclaredFields()) {
            if (field.isAnnotationPresent(Column.class) && field.getAnnotation(Column.class).export()) {
                field.setAccessible(true);
                fields.add(field);
            }
        }

        CreationHelper createHelper = workbook.getCreationHelper();
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(
            createHelper.createDataFormat().getFormat("yyyy-mm-dd"));
        fields.sort(Comparator.comparingInt(o -> o.getAnnotation(Column.class).index()));
        Sheet sheet = workbook.createSheet();
        Row sheetHead = sheet.createRow(0);
        for (int column = 0; column < fields.size(); column++) {
            Cell cell = sheetHead.createCell(column);
            cell.setCellValue(fields.get(column).getAnnotation(Column.class).name());
        }
        for (int row = 1; row <= dataList.size(); row++) {
            Row dataRow = sheet.createRow(row);
            for (int column = 0; column < fields.size(); column++) {
                Cell cell = dataRow.createCell(column);
                setCellValue(fields.get(column), dataList.get(row - 1), cell);
                if (fields.get(column).getType().equals(Date.class)) {
                    cell.setCellStyle(dateStyle);
                }
            }
        }
        fileName = fileName + excelType.getFileSuffix();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-disposition", "attachment; filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            logger.error("写出文件流失败. errorMessage: {}, error: {}.", e.getMessage(), e);
            throw new CommonException("返回文件失败！");
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                logger.error("关闭文件读取流失败! errorMessage: {}, error: {}.", e.getMessage(), e);
            }
        }
    }

    private <T> void setCellValue(Field field, T model, Cell cell) {
        try {
            Object fieldValue = field.get(model);
            if (null == fieldValue) {
                cell.setCellValue("");
                return;
            }
            if (field.getType().equals(String.class)) {
                cell.setCellValue((String) fieldValue);
            } else if (field.getType().equals(BigDecimal.class)) {
                cell.setCellValue(((BigDecimal) fieldValue).doubleValue());
            } else if (field.getType().equals(Date.class)) {
                cell.setCellValue((Date) fieldValue);
            } else if (field.getType().equals(Integer.class)) {
                cell.setCellValue((Integer) fieldValue);
            } else if (field.getType().equals(Boolean.class)) {
                cell.setCellValue((Boolean) fieldValue);
            } else if (field.getType().isEnum()) {
                cell.setCellValue(((Enum) fieldValue).name());
            } else {
                logger.error("{}字段类型非法！ 支持的类型: String, Date, BigDecimal, Boolean, Integer", field.getName());
                throw new RuntimeException(field.getName() + "字段类型非法！ 支持的类型: String, Date, BigDecimal, Boolean, Integer");
            }
        } catch (IllegalAccessException e) {
            logger.error("获取给{}类{}字段值失败! errorMessage: {}, error: {}.", model.getClass().getName(), field.getName(), e.getMessage(), e);
            throw new RuntimeException("获取" + model.getClass().getName() + "类" + field.getName() + "字段值失败!");
        }
    }
}
