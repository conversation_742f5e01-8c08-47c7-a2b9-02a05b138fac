package com.megvii.service;

import com.megvii.client.DhrRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.DataUserTemp;
import com.megvii.entity.opdb.Team;
import com.megvii.entity.opdb.User;
import com.megvii.entity.api.ItUserVO;
import com.megvii.mapper.DataUserTempMapper;
import com.megvii.mapper.UserMapper;
import com.megvii.mapper.filter.UserFilter;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class UserService {

    private Logger logger= LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private BeisenDataParserService beisenDataParserService;

    @Autowired
    private OaService oaService;

    @Autowired
    private TeamService teamService;

    @Autowired
    private DingdingService dingdingService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private CheckService checkService;



    public boolean saveUserV2(ItUserVO vo) {
        User user = getUserBody(vo);
        int result = 0;
        //新员工要分配内部账号和工号
        if (user.isNeedSave()) {
            String userId = getUserId(vo.getUserId().trim().toLowerCase());
            user.setUserId(userId);

            long workCode = redisTemplate.opsForValue().increment("mit_sf_cur_userId_PROD", 1);
            workCode = workCode - 1;
            user.setWorkNumber(workCode + "");
            result = userMapper.insert(user);
        } else {
            result = userMapper.updateUser(user);
        }

        if (user.isDelay()) {
            mailSendService.sendDelayEntryMail(user);
            dingdingService.sendEntryDelayHook(user.getSpell(), user.getUserId(), DateTimeUtil.date2String(DateTimeUtil.string2DateTime3(vo.getExpectDate())), DateTimeUtil.date2String(user.getExpectDate()));
        }

        if (result == 1) {
            return true;
        } else {
            return false;
        }

    }

    private User getUserBody(ItUserVO vo) {
        User user = getUserByMokaId(vo.getMokaId());
        if (user == null) {
            user = new User();
            user.setNeedSave(true);
        }

        user.setMokaId(vo.getMokaId());
        user.setOfferId(vo.getOfferId());

        user.setTitle(vo.getTitle());
        user.setReferTitle(vo.getReferTitle());
        user.setEmployType(vo.getEmployType());
        user.setEmployBase(vo.getEmployBase());
        user.setSource(vo.getSource());
        user.setDuty(vo.getDuty());
        user.setWorkBase(vo.getWorkBase());
        user.setSpell(vo.getSpell());
        user.setCell(vo.getCell());
        user.setPriEmail(vo.getPriEmail());

        Map<String, Object> dateMap = checkService.getHireDate(vo.getExpectDate(), user.getEmployType());
        user.setExpectDate((LocalDateTime) dateMap.get("expectDate"));
        user.setRegularDate((LocalDateTime) dateMap.get("regularDate"));
        user.setDelay((boolean) dateMap.get("isDelay"));

        Team team = teamService.getTeamByCode(vo.getTeamCode());
        if (team != null) {
            user.setTeam(team.getName());
            user.setTeamCode(team.getSfCode());
            user.setMentor(team.getTeamLeader());
            user.setHrbp(team.getHrg());
            user.setTeamId(team.getId());
            //data++执行中心处理方法
            Team biaozhuTeam = teamService.getTeamByCode("BD322");
            if (team.getName().contains(biaozhuTeam.getShortName())) {
                user.setCreateTag(4);
            }
        }

        user.setSequence(vo.getSequence() != null ? Integer.valueOf(vo.getSequence()) : null);
        user.setUpdater("beisen");
        user.setEntryFlow("OA流程");
        user.setFlowTime(DateTimeUtil.date2String4(LocalDateTime.now()));
        user.setStatus("");
        user.setDimissionDate(null);

        return user;
    }



    /**
     * 保存从beisen拉取的user信息 推送OA入职流程
     * @param userInfo
     * @return
     */
    public String saveUser(Map userInfo){
        StringBuilder msg = new StringBuilder();

        User user = beisenDataParserService.getUser(userInfo);
        if (!user.isNeedSave())
            return user.getParseMsg();

        /**
         * 从redis中获取工号
         * 假如redis中是90
         * workCode取得结果为91  redis中变为91  实际90是应该使用的工号
         * 使用-1后的工号 保证redis中的工号是可用的
         */
        long workCode = redisTemplate.opsForValue().increment("mit_sf_cur_userId_PROD",1);
        workCode = workCode - 1;

        user.setWorkNumber(workCode+"");
        user.setUpdater("beisen");
        user.setEntryFlow("OA流程");
        user.setFlowTime(DateTimeUtil.date2String4(LocalDateTime.now()));
        user.setStatus("");

        userMapper.insert(user);
        msg.append("<span style=\"color:#3eda67;\">成功--></span>beiSenId:"+user.getBeisenId()+",userName:"+user.getSpell()+",userId:"+user.getUserId()+",hireDate:"+DateTimeUtil.date2String(user.getExpectDate())+",workNumber:"+user.getWorkNumber());

        if (oaService.createEntryFlow(user)) {
            msg.append(",OA推送成功");
        } else {
            msg.append(",OA推送失败");
        }

        return msg.toString();

    }

    /**
     * 传入姓名全拼 查询是否包含重复user_id
     * @param userId
     * @return
     */
    public String getUserId(String userId) {
        //查看数据库是否存在重名user_id 返回最大的user_id
        String othUserId = userMapper.selectMaxUserIdByRegexp(userId);
        //查询缩写的full_id
        String fullId = userMapper.selectMaxFullIdByRegexp(userId);

        //已经存在重名,向上增加
        if (othUserId != null) {
            try {
                int count = Integer.parseInt(othUserId.replaceAll(userId, ""));
                count += 1;
                //小于10 补0
                if (count < 10) {
                    userId += "0" + count;
                } else {
                    userId += count;
                }
            } catch (Exception e) {
                userId += "02";
            }
        } else if (fullId != null && othUserId == null) {
            userId += "02";
        }

        return userId;
    }



    /**
     * @return  当前需要创建sf账号的人员
     */
    public List<User> getUserListNeedToLdap(){
        return userMapper.selectUsersNeedToLdap();
    }


    /**
     * @return 当前需要同步至sf的人员
     */
    public List<User> getUserListNeedToSf(){
        return userMapper.selectUserListNeedToSf();
    }



    public User getUserByBeiSenId(String beiSenId){
        return userMapper.selectUserByBeiSenId(beiSenId);
    }

    public User getUserByMokaId(String mokaId){
        return userMapper.selectUserByMokaId(mokaId);
    }


    public User getUserByUserId(String userId){
        return userMapper.selectUserByUserId(userId);
    }

    public User getUserByWorkNumber(String workNumber){
        return userMapper.selectUserByWorkNumber(workNumber);
    }

    public void updateUser4Aliyun(User user){
        userMapper.updateUser4Aliyun(user);

    }
    public void saveUserPassword(User user){
        userMapper.updateUserPassword(user);
    }

    public void updateUserDidiId(User user) {
        userMapper.updateUserDidiId(user);
    }

    public List<User> getUserByExpectDate() {
        return userMapper.selectUserByExpectDate();
    }

    /**
     * @return 当前需要激活office365的人员
     */
    public List<User> getUserListNeedToOffice365() {
        return userMapper.selectUsersNeedToOffice365();
    }

    public void updateUserDingdingIdAndUnionid(User user) {
        userMapper.updateUserDingdingIdAndUnionid(user);
    }

    public void updateUserEntryFlow(User user) {
        userMapper.updateUserEntryFlow(user);
    }

    public List<User> getUserByDimissionDate() {
        return userMapper.selectUserByDimissionDate();
    }

    public void updateUser(User user) {
        userMapper.updateUser(user);
    }

    public List<User> getAllUser() {
        return userMapper.selectAllUser();
    }

    public List<User> getUserByUpdateTime(Integer day) {
        return userMapper.selectUsersByUpdateTime(day);
    }

    public List<User> getUsersByFilter(UserFilter filter) {
        return userMapper.selectUsersByFilter(filter);
    }

    /**
     * 指定天数内入职和离职的人员
     * @return
     */
    public List<User> getUsersByDiffExpectDimissDate(int diff) {
        return userMapper.selectUsersByDiffExpectDimissDate(diff);
    }

}
