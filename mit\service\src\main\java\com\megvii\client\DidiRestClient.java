package com.megvii.client;

import com.alibaba.fastjson.JSONObject;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * 滴滴接口请求类
 */
@Component
@PropertySource("classpath:didi.properties")
public class DidiRestClient {

    private Logger logger= LoggerFactory.getLogger(DidiRestClient.class);

    @Value("${DIDI_TOKEN_URL}")
    private String tokenUrl;

    @Value("${DIDI_CLIENT_ID}")
    private String clientId;

    @Value("${DIDI_COMPANY_ID}")
    private String companyId;

    @Value("${DIDI_SIGN_KEY}")
    private String signKey;

    @Value("${DIDI_CLIENT_SECRET}")
    private String clientSecret;

    @Value("${DIDI_GRANT_TYPE}")
    private String grantType;

    @Value("${DIDI_PHONE_NUM}")
    private String phoneNum;

    @Value("${DIDI_BASE_URL}")
    private String baseUrl;

    @Value("#{'${DIDI_SUPER_MANAGER}'.split(',')}")
    private List<String> superManager = new ArrayList<>();

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RedisTemplate redisTemplate;


    /**
     * 获取单独订单详情
     * @param token
     * @param orderId
     * @return
     */
    public JSONObject getOrderDetail(String token, String orderId) {
        String endPoint = "/Order/detail";

        JSONObject data = setBasicParam(token);
        data.put("order_id", orderId);
        getSign(data);

        String url = baseUrl + endPoint + "?access_token=" + token + "&client_id=" + clientId + "&company_id=" + companyId +
                "&timestamp=" + data.getString("timestamp") + "&sign=" + data.getString("sign") + "&order_id=" + orderId;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        return jsonObject;
    }

    /**
     * 获取费用科目列表
     * @param token
     * @return
     */
    public JSONObject getBudgetItem(String token) {
        String endPoint = "/BudgetItem/get";

        JSONObject data = setBasicParam(token);
        data.put("offset", 0);
        data.put("length", 100);
        getSign(data);

        String url = baseUrl + endPoint + "?access_token=" + token + "&client_id=" + clientId + "&company_id=" + companyId +
                "&timestamp=" + data.getString("timestamp") + "&sign=" + data.getString("sign") + "&offset=" + 0 +"&length=" + 100;

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        return jsonObject;
    }


    /**
     * 查询用车制度信息
     * @param token
     * @return
     */
    public JSONObject getRegulation(String token) {
        String endPoint = "/Regulation/get";

        JSONObject data = setBasicParam(token);
        getSign(data);

        String url = baseUrl + endPoint + "?access_token=" + token + "&client_id=" + clientId + "&company_id=" + companyId +
                "&timestamp=" + data.getString("timestamp") + "&sign=" + data.getString("sign");

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        return jsonObject;
    }



    /**
     * 查询公司信息
     * @param token
     * @return
     */
    public JSONObject getCompany(String token) {
        String endPoint = "/Company/detail";

        JSONObject data = setBasicParam(token);
        getSign(data);

        String url = baseUrl + endPoint + "?access_token=" + token + "&client_id=" + clientId + "&company_id=" + companyId +
                "&timestamp=" + data.getString("timestamp") + "&sign=" + data.getString("sign");

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        return jsonObject;
    }

    /**
     * 修改滴滴服务器端数据 不更改内部数据库
     * @param token
     * @param workNumber
     * @return
     */
    public boolean editMember(String token, String didiId, String cell, String workNumber, String teamId, String regulationId) {
        String endPoint = "/Member/edit";

        JSONObject data = setBasicParam(token);
        data.put("member_id", didiId);

        JSONObject info = new JSONObject();
        info.put("phone", cell);
        info.put("realname", workNumber);
        info.put("employee_number", workNumber);
        info.put("department", teamId);
        info.put("regulation_id", regulationId);
        info.put("use_company_money", 1);

        for (String s : superManager) {
            if (workNumber.equals(s)) {
                info.put("system_role", 2);
                break;
            }
        }
        if (!info.containsKey("system_role")) {
            info.put("system_role", 0);
        }

        data.put("data", info.toString());

        getSign(data);

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(),headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(baseUrl + endPoint ,restTemplate.exchange(baseUrl + endPoint,
                HttpMethod.POST, requestEntity, String.class).getBody());

        if (jsonObject != null && (jsonObject.getString("errno").equals("0") || jsonObject.getString("errno").equals("50233"))) {
            return true;
        } else {
            logger.info(jsonObject.getString("errmsg"));
            return false;
        }
    }


    /**
     * 按照work_number 向滴滴post删除指令
     * @param token
     * @param didiId
     * @return
     */
    public boolean delMember(String token, List<String> didiId) {
        String endPoint = "/Member/del";

        String member = "";
        for (String s : didiId) {
            member = member + "_" + s;
        }
        member = member.substring(1, member.length());

        JSONObject data = setBasicParam(token);
        data.put("member_id", member);

        getSign(data);

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(),headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(baseUrl + endPoint ,restTemplate.exchange(baseUrl + endPoint,
                HttpMethod.POST, requestEntity, String.class).getBody());

        if (jsonObject != null && jsonObject.get("errno").equals(0)) {
            return true;
        } else {
            logger.error(jsonObject.getString("errmsg"));
            return false;
        }
    }


    /**
     * 添加滴滴人员 工号 手机号 邮箱不能重复
     * @param token
     * @param teamId
     * @param workNumber
     * @param cell
     * @return
     */
    public JSONObject addMember2Didi(String token, String teamId, String workNumber, String cell, String regulationId) {
        String endPoint = "/Member/single";

        JSONObject data = setBasicParam(token);

        JSONObject info = new JSONObject();
        info.put("department", teamId);
        info.put("employee_number", workNumber);
        info.put("phone", cell);
        info.put("realname", workNumber);
        info.put("regulation_id", regulationId);
        info.put("use_company_money", 1);

        for (String s : superManager) {
            if (workNumber.equals(s)) {
                info.put("system_role", 2);
                break;
            }
        }
        if (!info.containsKey("system_role")) {
            info.put("system_role", 0);
        }

        data.put("data", info.toString());

        getSign(data);

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(),headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(baseUrl + endPoint ,restTemplate.exchange(baseUrl + endPoint,
                HttpMethod.POST, requestEntity, String.class).getBody());

        return jsonObject;
    }


    /**
     * 员工在滴滴服务器的信息 返回didi_id
     * 手机号和工号只能同时传一个 不能都传
     * 滴滴接口逻辑模糊查询，传工号有可能匹配出手机号
     * @param token
     * @param workNumber 工号 筛选条件
     * @param cell 手机号 筛选条件
     * @param offset
     * @param length
     * @return
     */
    public JSONObject getMemberFromDidi(String token, String workNumber, String cell, Integer offset, Integer length) {
        String endPoint = "/Member/get";

        if (offset == null) {
            offset = 0;
        }
        if (length == null) {
            length = 100;
        }

        JSONObject data = setBasicParam(token);
        data.put("offset", offset);
        data.put("length", length);

        if (workNumber != null) {
            data.put("employee_number", workNumber);
        }
        if (cell != null) {
            data.put("phone", cell);
        }

        getSign(data);
        String sign = data.getString("sign");

        String url = baseUrl + endPoint + "?access_token=" + token + "&client_id=" + clientId + "&company_id=" + companyId +
                "&timestamp=" + data.getString("timestamp") + "&offset=" + offset +"&length=" + length + "&sign=" + sign;

        if (workNumber != null) {
            url = url + "&employee_number=" + workNumber;
        }
        if (cell != null) {
            url = url + "&phone=" + cell;
        }

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(null ,headers);
        JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class).getBody());

        if (jsonObject != null) {
            if (jsonObject.get("errno").equals(0)) {
                JSONObject result = jsonObject.getJSONObject("data");
                return result;
            } else {
                logger.info(jsonObject.getString("errmsg"));
                return null;
            }
        } else {
            logger.info("no response");
            return null;
        }
    }

    /**
     * 优先从redis获取didi token
     * @return
     * @throws Exception
     */
    public String getAuthToken() throws Exception{
        Object token=redisTemplate.opsForValue().get("mitexecutor_didi_token");
        if (token==null){
            token =getToken();
            redisTemplate.opsForValue().set("mitexecutor_didi_token",token);
            redisTemplate.expire("mitexecutor_didi_token",1800, TimeUnit.SECONDS);
        }

        return (String) token;
    }


    /**
     * 获取didi token
     * @return
     */
    public String getToken() {

        try {
            JSONObject data = new JSONObject(true);
            data.put("client_id", clientId);
            data.put("client_secret", clientSecret);
            data.put("grant_type", grantType);
            data.put("phone", phoneNum);
            data.put("sign_key", signKey);
            data.put("timestamp", LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));


            getSign(data);

            HttpHeaders headers=new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(),headers);
            JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(tokenUrl ,restTemplate.exchange(tokenUrl,
                    HttpMethod.POST, requestEntity, String.class).getBody());

            String token = jsonObject.getString("access_token");

            if (token != null) {
                return token;
            } else {
                String errmsg = jsonObject.getString("errmsg");
                logger.info(errmsg);
            }
        } catch (Exception e) {
            logger.error("token获取错误", e);
        }
        return null;
    }

    /**
     * set 滴滴基本请求参数
     * @param
     * @return
     */
    private JSONObject setBasicParam(String token) {
        JSONObject data = new JSONObject();
        data.put("client_id", clientId);
        data.put("company_id", companyId);
        data.put("timestamp", LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        data.put("sign_key", signKey);
        data.put("access_token", token);

        return data;
    }

    /**
     *
     * 参数放入map中，根据KEY排序，转换为字符串 ,替换为& 去掉最边缘大括号, 参数的json不去掉括号 MD5加密 删掉sign_key
     * 加密后的字符串作为sign参数
     * @param data 获取滴滴sign 修改data内容
     * @return
     */
    private void getSign(JSONObject data) {
        Map map = new HashMap();
        for (Map.Entry<String, Object> stringObjectEntry : data.entrySet()) {
            map.put(stringObjectEntry.getKey(), stringObjectEntry.getValue());
        }

        Map resultMap = sortMapByKey(map);
        String signStr = resultMap.toString().replaceAll(", ", "&");
        String signStr2 = signStr.substring(1, signStr.length() - 1);
        String sign = DigestUtils.md5Hex(signStr2);

        data.remove("sign_key");
        data.put("sign", sign);
    }

    /**
     * 使用 Map按key进行排序
     * @param map
     * @return
     */
    private static Map<String, String> sortMapByKey(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }

        Map<String, String> sortMap = new TreeMap<String, String>(
                new MapKeyComparator());

        sortMap.putAll(map);

        return sortMap;
    }

    private static class MapKeyComparator implements Comparator<String> {

        @Override
        public int compare(String str1, String str2) {

            return str1.compareTo(str2);
        }
    }

}
