package com.megvii.mapper;

import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserMapper {

    int insert(User user);

    /**
     * @param beiSenId 北森人员id
     * @return
     */
    User selectUserByBeiSenId(@Param("beiSenId")String beiSenId);

    /**
     * @param mokaId
     * @return
     */
    User selectUserByMokaId(@Param("mokaId")String mokaId);

    /**
     * @param userId IT_user中人员登录名
     * @return
     */
    User selectUserByUserId(@Param("userId")String userId);


    /**
     * @param userPinyin   人员姓名的拼音
     * 使用正则表达的方式查出user_id相同的全部人员
     * 根据名字后的数字倒序排序，查找第一条
     * select user_id from IT_user where user_id REGEXP concat('^',#{userPinyin},'([0-9])*$') ORDER BY REPLACE(user_id, #{userPinyin}, '')+0 DESC limit 1
     * @return
     */
    String selectMaxUserIdByRegexp(String userPinyin);


    /**
     * full_id,避免跟总裁全名重复
     * @param userPinyin
     * @return
     */
    String selectMaxFullIdByRegexp(String userPinyin);


    void updateUser4Aliyun(User user);


    /**
     *
     * 查询条件updater != 'beisen'  and status != '已禁用' and entry_flow != 'OA流程' and expect_date=now()+1
     * @return 需要同步到ldap的人员
     */
    List<User> selectUsersNeedToLdap();


    /**
     * 查询条件updater != 'beisen'  and status != '已禁用' and entry_flow != 'OA流程' and expect_date=now()+1
     * @return
     */
    List<User> selectUsersNeedToOffice365();

    /**
     * 查询条件status != '已禁用' and entry_flow != 'OA流程' and expect_date=now()
     *
     * @return 需要同步到sf的人员
     */
    List<User> selectUserListNeedToSf();

    /**
     *
     * 修改员工密码
     * @param user
     */
    void updateUserPassword(User user);

    /**
     * 修改didi_id
     * @param user
     */
    void updateUserDidiId(User user);

    /**
     *
     * 查询条件expect_date = now()
     * @return
     */
    List<User> selectUserByExpectDate();

    /**
     * 修改钉钉id和unionid
     * @param user
     */
    void updateUserDingdingIdAndUnionid(User user);

    /**
     * 修改entryFlow
     * @param user
     */
    void updateUserEntryFlow(User user);

    /**
     * 查询条件 dimission_date >= now()
     * @return
     */
    List<User> selectUserByDimissionDate();

    /**
     * 通过工号查询
     * @param workNumber
     * @return
     */
    User selectUserByWorkNumber(String workNumber);

    int updateUser(User user);

    /**
     * 查询所有users
     * @return
     */
    List<User> selectAllUser();

    /**
     * 查询过去5天修改过信息的人员
     * @return
     */
    List<User> selectUsersByUpdateTime(Integer day);

    List<User> selectUsersByFilter(@Param("filter") UserFilter filter);

    /**
     * 获取指定天数的入职人员和离职人员
     * @return
     */
    List<User> selectUsersByDiffExpectDimissDate(int diff);

}
