package com.megvii.cas;

import com.megvii.cas.prod.CasCustomProperties;
import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import org.apache.shiro.authc.UnknownAccountException;

/**
 * @创建人 cl
 * @创建日期 2019/10/16
 * @描述  回调拦截器
 */
public class CallbackFilter extends io.buji.pac4j.filter.CallbackFilter {


    public CallbackFilter(CasCustomProperties casCustomProperties) {
        super();
        setCallbackLogic(new MyCallbackLogic(casCustomProperties));
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        try{
            super.doFilter(servletRequest, servletResponse, filterChain);
        }catch (Exception e){
            if (e instanceof UnknownAccountException){
                throw new ServletException(e.getMessage());
            }else {
                throw e;
            }
        }
    }
}