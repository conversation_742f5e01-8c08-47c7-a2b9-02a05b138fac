:host {
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 64px;

    a {
      flex: auto;
    }

    img {
      width: 100%;
      max-width: 112px;
    }
  }
}

:host ::ng-deep {
  .switch-role {
    z-index: 10000;
    position: fixed;
    top: 250px;
    right: 0;
    width: 30px;
    background: linear-gradient(208deg, rgba(57, 216, 250, 1) 0%, rgba(55, 154, 246, 1) 100%);
    box-shadow: 0 -3px 20px 0 rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    padding: 10px 0;
    display: flex;
    align-items: center;
    flex-direction: column;

    i {
      font-size: 20px;
      line-height: 20px;
      margin-bottom: 5px;
      color: #fff;
    }

    span:nth-of-type(1) {
      writing-mode: vertical-lr;
      font-size: 16px;
      color: #fff;
      display: inline-block;
      text-align: center;
    }

    span:nth-of-type(2) {
      position: absolute;
      right: 15px;
      top: -10px;
      height: 19px;
      padding: 0 5px;
      background: rgba(255, 82, 82, 1);
      border-radius: 11px;
      color: #fff;
    }
  }

  .dingTalk {
    z-index: 10000;
    position: fixed;
    top: 370px;
    right: 0;
    width: 30px;
    height: 30px;
    background-color: #343434;
    border-radius: 6px;
    padding: 7px;

    i {
      color: #fff;
      font-size: 16px;
      line-height: 16px;
      height: 16px;
    }
  }

  .ant-menu {
    font-size: 16px;
  }

  .ant-layout-header {
    height: 60px;
    padding: 0;
    line-height: 60px;
    background-color: rgb(255, 255, 255);
    box-shadow: 0 20px 0 0 rgba(0, 0, 0, 0.05);
  }

  .top-menu {
    line-height: 80px;
    display: inline-block;

    .ant-menu-item {
      top: 0;
      display: inline-block;
      // border-bottom: 5px solid transparent;
      color: rgb(0, 18, 35);

      a {
        color: #d6d9d9;
      }
    }

    .ant-menu-submenu-title {
      top: 0;
      display: inline-block;
      // border-bottom: 5px solid transparent;

      span {
        color: #d6d9d9;
      }
    }

    .menuIcon {
      font-size: 16px;
      margin-right: 18px;
    }

    a.default,
    span.default {
      color: #939393;
    }
  }

  .avatar {
    display: inline-block;
    margin: 8px 40px 8px 0;
    width: 40px;
    background-size: cover;
    background-position: center;
    border-radius: 20px;
    height: 40px;
    overflow: hidden;
  }

  .logout,
  .username {
    color: #000;
    font-size: 14px;

    a {
      color: #000;
      display: flex;
      align-items: center;
    }
  }

  .sys-title {
    display: inline-block;
    font-size: 20px;
    font-weight: 600;
    margin-left: 30px;
  }

  .role-name {
    margin-left: 5px;
  }

  .logout {
    margin-right: 40px;

    & > a:hover {
      color: #379af6;
    }
  }

  .ant-menu-horizontal {
    border-bottom: none;
  }

  .bell-img {
    width: 18px;
    vertical-align: text-top;
  }

  .footer {
    text-align: center;
    color: #999;
    font-size: 12px;
  }
}
