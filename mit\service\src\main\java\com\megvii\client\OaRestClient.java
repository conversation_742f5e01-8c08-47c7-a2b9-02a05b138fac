package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Base64;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import com.megvii.entity.opdb.TempUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/10/23 11:05
 */
@Component
@PropertySource(value = "classpath:oa.${spring.profiles.active}.properties",encoding = "UTF-8")
public class OaRestClient {

    private Logger logger= LoggerFactory.getLogger(OaRestClient.class);

    @Value("${OA_BASE_URL}")
    private String baseUrl;

    @Value("${OA_USERNAME}")
    private String username;

    @Value("${OA_PASSWORD}")
    private String password;

    @Value("${E9_OA_BASE_URL}")
    private String e9BaseUrl;

    @Value("${E9_OA_APP_KEY}")
    private String e9AppKey;

    @Value("${E9_OA_EVENT_KEY}")
    private String e9EventKey;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${OA_KONG_USERNAME}")
    private String kongUsername;

    @Value("${OA_KONG_PASSWORD}")
    private String kongPassword;

    @Value("${OA_GET_TEMP_USER}")
    private String oaGetTempUserUrl;

    @Value("${OA_OUT_SOURCING_UPDATE}")
    private String oaOutSourcingUpdate;

    /**
     * 创建E9 OA流程
     * @param data
     * @return
     */
    public boolean sendE9OAFlow(MultiValueMap data) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("appkey", e9AppKey);
        headers.set("eventkey", e9EventKey);

        long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        headers.set("timestamp", String.valueOf(timeStamp));

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<MultiValueMap<String, String>>(data, headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(e9BaseUrl, httpEntity ,JSONObject.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK) && responseEntity.getBody().getString("code").equals("100")) {
            return true;
        }
        return false;
    }


    /**
     * 发送OA流程
     * @param data
     * @param workflowId
     * @param workflowUrl
     * @return
     * @throws Exception
     */
    public boolean sendOAFlow(MultiValueMap data , String workflowId, String workflowUrl) throws Exception {
        if (workflowUrl == null) {
            workflowUrl = "/workflow/CreateRequest.jsp";
        }

        data.add("workflowid", workflowId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("sessionkey", getSession());

        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<MultiValueMap<String, String>>(data, headers);

        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(baseUrl + workflowUrl, httpEntity ,JSONObject.class);
        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject result = responseEntity.getBody();
            if (result.getString("requestId") != null && Integer.valueOf(result.getString("requestId")) > 0) {
                return true;
            } else {
                logger.error("MIT推送OA流程失败!工作流ID:" + workflowId);
                throw new Exception("MIT推送OA流程失败!工作流ID:" + workflowId);
            }
        } else {
            logger.error("MIT推送OA流程失败!工作流ID:" + workflowId);
            throw new Exception("MIT推送OA流程失败!工作流ID:" + workflowId);
        }
    }



    /**
     * 获取OA session 优先从redis中获取
     * @return
     * @throws Exception
     */
    private String getSession() throws Exception{
        Object session = redisTemplate.opsForValue().get("mitexecutor_oa_token");
        if (session == null){
            session = getOASession();
            redisTemplate.opsForValue().set("mitexecutor_oa_token", session);
            redisTemplate.expire("mitexecutor_oa_token",3600, TimeUnit.SECONDS);
        }

        return (String) session;
    }

    /**
     * 获取OA session认证
     * @return
     * @throws Exception
     */
    private String getOASession() throws Exception {
        String url = baseUrl + "/VerifyLogin.jsp?loginid=" + username + "&password=" + password;

        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, null ,JSONObject.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject data = responseEntity.getBody();
            if (data.getString("status").replaceAll(" ","").equals("200")) {
                return data.getString("sessionkey");
            } else {
                logger.error("OA获取session请求成功!验证失败!" + data.getString("message"));
                throw new Exception("OA获取session请求成功!验证失败!" + data.getString("message"));
            }
        } else {
            logger.error("OA获取session请求失败!");
            throw new Exception("OA获取session请求失败!");
        }
    }

    /**
     * @param spell 用户名
     * @param cell 手机号
     * @return 数据list
     */
    public String getTempUserOAId(String spell, String cell) {
        JSONObject data = new JSONObject();

        JSONObject operationInfo = new JSONObject();
        operationInfo.put("operator", "321321");

        data.put("operationinfo", operationInfo);

        JSONObject mainTable = new JSONObject();
        //TODO 这里通过姓名查询
        mainTable.put("xm",spell);
        mainTable.put("lxdh",cell);
//        mainTable.put("py",tempUser.getUserId().replaceAll("t-",""));
//        mainTable.put("id",1);

        data.put("mainTable", mainTable);

        JSONObject pageInfo = new JSONObject();
        pageInfo.put("pageNo", String.valueOf(1));
        pageInfo.put("pageSize", String.valueOf(100));

        data.put("pageInfo", pageInfo);


        //获取时间戳
        String currentTimeTamp = getTimestamp();
        String md5Source = "ASYNC" + "123456" + currentTimeTamp;
        String md5OfStr = getMD5Str(md5Source).toLowerCase();

        JSONObject header = new JSONObject();
        header.put("systemid", "ASYNC");
        header.put("currentDateTime", currentTimeTamp);
        header.put("Md5", md5OfStr);
        data.put("header", header);


        // 创建一个RestTemplate对象
//        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头，指定请求体的类型为application/x-www-form-urlencoded
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 设置请求体参数
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("datajson", data.toJSONString());
        logger.info("OA中查询用户信息的请求参数:{}",map);

        // 创建HttpEntity对象，用于封装请求头和请求体
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(map, headers);

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(oaGetTempUserUrl, httpEntity, String.class);


        JSONArray jsonArray = JSONObject.parseObject(responseEntity.getBody()).getJSONArray("result");
        logger.info("OA中查询到的用户信息:{}",jsonArray);
        String result = null;
        if (jsonArray != null) {
            if(jsonArray.size() > 1){
                logger.error("OA中存在多个该用户:{}",spell);
                throw new RuntimeException("OA中存在多个该用户");
            }
            JSONObject jsonObject = jsonArray.getJSONObject(0);
            if (jsonObject == null || jsonObject.getJSONObject("mainTable") == null) {
                logger.error("OA中不存在该用户:{}",spell);
                throw new RuntimeException("OA中不存在该用户");
            }
            result = String.valueOf(jsonObject.getJSONObject("mainTable").getString("id"));
            logger.info("{}OA中查询到的用户id:{}",spell,result);
        }else{
            logger.error("OA中不存在该用户:{}",spell);
            throw new RuntimeException("OA中不存在该用户");
        }
        return result;
    }

    /**
     * 获取时间戳   格式如：19990101235959
     * @return
     */
    public static String getTimestamp(){
        return getCurDateTime().replace("-", "").replace(":", "").replace(" ", "");
    }
    /**
     * 获取当前日期时间。 YYYY-MM-DD HH:MM:SS
     * @return		当前日期时间
     */
    public static String getCurDateTime() {
        Date newdate = new Date();
        long datetime = newdate.getTime();
        Timestamp timestamp = new Timestamp(datetime);
        return (timestamp.toString()).substring(0, 19);
    }
    public String getMD5Str(String plainText){
        //定义一个字节数组
        byte[] secretBytes = null;
        try {
            // 生成一个MD5加密计算摘要
            MessageDigest md = MessageDigest.getInstance("MD5");
            //对字符串进行加密
            md.update(plainText.getBytes());
            //获得加密后的数据
            secretBytes = md.digest();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("没有md5这个算法！");
        }
        //将加密后的数据转换为16进制数字
        String md5code = new BigInteger(1, secretBytes).toString(16);
        // 如果生成数字未满32位，需要前面补0
        for (int i = 0; i < 32 - md5code.length(); i++) {
            md5code = "0" + md5code;
        }
        return md5code;
    }
    public String outsourcingUpdate(TempUser tempUser, String id) {
        HttpHeaders headers = getOaConsumerHeader();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("id", id);
//        if (tempUser.getTeam() != null) {
//            data.put("szxmz", tempUser.getTeam());
//        }
        if (tempUser.getMentor() != null) {
            data.put("xmfzr", tempUser.getMentor());
        }
        if (tempUser.getDismissTime() != null) {
            data.put("lcrq", tempUser.getDismissTime());
        }

        if (tempUser.getUserId() != null) {
            data.put("dlm", tempUser.getUserId());
        }

        if (tempUser.getProject() != null) {
            data.put("ywx", tempUser.getTeam());
        }

        logger.info("tempUser.getHrg()"+tempUser.getHrg());
        if (tempUser.getHrg() != null) {
            data.put("hrg", tempUser.getHrg());
        }

        HttpEntity<String> requestEntity = new HttpEntity<String>(JSONObject.toJSONString(data), headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(oaOutSourcingUpdate, requestEntity, String.class);
        return responseEntity.getBody();
    }
    private HttpHeaders getOaConsumerHeader(){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.set("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString((kongUsername + ":" + kongPassword).getBytes()));
        return headers;
    }
}
