package com.megvii.controller.jwt.api;


import com.megvii.entity.opdb.User;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/9/25 14:28
 */
@Data
public class JobCardUserInfoVO {

    private Integer id;
    private String userId;
    private String spell;
    private Integer teamId;
    private String status;
    private String expectDate;
    private String workNumber;
    private String employType;
    private String dimissionDate;
    private String updateTime;

    public static List<JobCardUserInfoVO> toVOs(List<User> users) {
        List<JobCardUserInfoVO> vos = new ArrayList<>();
        for (User user : users) {
            //过滤data人员
            if (user.getCreateTag() != null && (user.getCreateTag() == 4||user.getCreateTag() == 9)) {
                continue;
            }
            vos.add(toVO(user));
        }

        return vos;
    }


    private static JobCardUserInfoVO toVO(User user) {
        JobCardUserInfoVO vo = new JobCardUserInfoVO();
        vo.setId(user.getId());
        vo.setUserId(user.getUserId());
        vo.setSpell(user.getSpell());
        vo.setTeamId(user.getTeamId());
        vo.setStatus(user.getStatus());
        vo.setExpectDate(user.getExpectDate()!=null?user.getExpectDate().toString():null);
        vo.setWorkNumber(user.getWorkNumber());
        vo.setEmployType(user.getEmployType());
        vo.setDimissionDate(user.getDimissionDate()!=null?user.getDimissionDate().toString():null);
        vo.setUpdateTime(user.getUpdateTime()!=null?user.getUpdateTime().toString():null);

        return vo;
    }


}
