package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.DhrService;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/5 19:11
 */
@Service
public class PutDhrUserOperatorService extends BaseOperatorService {

    @Autowired
    private UserService userService;

    @Autowired
    private DhrService dhrService;

    @Autowired
    private PutDhrUserOperatorService putDhrUserOperatorService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId=paraMap.get("userId");
        if(userId!=null){
            return OperateByUserId((String) userId);
        }else {
            List<User> users = userService.getUserListNeedToSf();

            if (users.size()==0){
                return new OperateResult(1, "无符合创建DHR账号条件的人员");
            }else{
                StringBuilder msg=new StringBuilder();
                for (int i=0;i<users.size();i++){
                    OperateResult operateResult = putDhrUserOperatorService.operateOnce(paraMap,users.get(i));
                    msg.append(operateResult.getMsg()+"<br/>");
                }
                return new OperateResult(1,msg.toString());
            }
        }
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        User user = (User)params[0];
        String msg = dhrService.syncUser2Dhr(user);
        return new OperateResult(1,msg);
    }


    public OperateResult OperateByUserId(String userId){
        User user=userService.getUserByUserId(userId);
        if (user!=null){
            String msg= dhrService.syncUser2Dhr(user);
            return new OperateResult(1,msg);
        }else {
            return new OperateResult(1,"人员"+userId+"不存在");
        }
    }



}
