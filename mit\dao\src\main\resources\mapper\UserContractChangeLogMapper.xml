<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserContractChangeLogMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.contract.UserContractChangeLog">
        <id column="id" property="id"/>
         <result column="user_id" property="userId"/>
        <result column="contract_number" property="contractNumber"/>
        <result column="updator_user_id" property="updatorUserId"/>
        <result column="updator_name" property="updatorName"/>
        <result column="update_time" property="updateTime"/>
        <result column="change_type" property="changeType"/>

    </resultMap>

    <insert id="insertChangeLog" parameterType="com.megvii.entity.opdb.contract.UserContractChangeLog" >
        INSERT INTO `user_contract_change_log` (
	        `id`,
	        `user_id`,
	        `contract_number`,
	        `updator_user_id`,
	        `updator_name`,
	        `update_time`,
	        `change_type`
        )
        VALUES
	    (
		    0,
		    #{userId},
		    #{contractNumber},
		    #{updatorUserId},
	    	#{updatorName},
		    NOW(),
		    #{changeType}
	    );
    </insert>

    <select id="selectChangeLogByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from user_contract_change_log where user_id = #{userId} ORDER BY update_time DESC
    </select>




</mapper>
