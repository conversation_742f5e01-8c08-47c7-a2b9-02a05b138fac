<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.SecureGroupMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.SecureGroup">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
    </resultMap>

    <select id="selectAllSecureGroup" resultType="java.lang.String">
		SELECT `name` FROM secure_group
	</select>

</mapper>
