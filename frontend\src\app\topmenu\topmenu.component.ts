import { Component, OnInit } from '@angular/core'
import {
  EventsService,
  LocalStorage,
  SendRequest,
  GlobalVarService,
  MessageService,
  ApiService
} from '../core/services'
import { Router } from '@angular/router'
import { environment } from 'src/environments/environment'
import { setWaterMark } from 'src/utils/watermark'
import { formatDate } from 'src/utils/formatDate'
import menuIconList from '../core/values/menu-Icon-list'
import { ClassGetter } from '@angular/compiler/src/output/output_ast'

@Component({
  selector: 'app-topmenu',
  templateUrl: './topmenu.component.html',
  styleUrls: ['./topmenu.component.less']
})
export class TopmenuComponent implements OnInit {
  window = <any>window
  indexLink = '/'
  user = {}
  menuIconList = menuIconList
  menuTree = []
  selRole = {}
  userName = ''
  deptName = ''
  // 菜单是否折叠

  isCollapsed = false

  constructor(
    private sendRequest: SendRequest,
    private router: Router,
    private eventsService: EventsService,
    private globalVarService: GlobalVarService,
    private message: MessageService,
    private ls: LocalStorage,
    private apiService: ApiService
  ) { }

  ngOnInit() {
    this.isLogin()
  }

  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed
  }

  userId = ''
  workNumber = ''

  isLogin() {
    this.apiService.isLogin().subscribe((res) => {
      const { code, data, msg } = res
      if (code === 1) {
        // 未登录
        this.redirect()
        return
      }
      if (code === 403) {
        // 无权限
        this.router.navigate(['/500'], {})
        return
      }
      if (code === 0) {
        // 已登录
        this.userName = data['spell']
        this.deptName = data['team']
        this.menuTree = data['menus']
        if (this.isRootPath()) {
          this.deepTraversal(this.menuTree)
        }
      }

      this.userId = data.hasOwnProperty('userId') ? data.userId : ''
      this.workNumber = data.hasOwnProperty('workNumber') ? `-${data.workNumber}` : ''
      
      setWaterMark(`${this.userId}${this.workNumber}`, formatDate(new Date()))
      window.onresize = () => {
        setWaterMark(`${this.userId}${this.workNumber}`, formatDate(new Date()))
      }
    })
  }

  redirect() {
    ; (window as any).location.href =
      environment.ssoUrl +
      '/login?service=' +
      escape(environment.loginUrl + '?callback=' + encodeURI((<any>window).location.href))
  }

  isRootPath() {
    return this.window.location.pathname === environment.urlPrefix + '/'
  }

  // 深度遍历跳转
  deepTraversal(tree) {
    if (tree) {
      let stack = [] // 同来存放将来要访问的节点
      stack = [...stack, ...tree]
      while (stack.length != 0) {
        const item = stack.shift() // 正在访问的节点
        if (item.children && item.children.length === 0) {
          this.router.navigate([item['url']]) // 跳转页面
          break // 结束循环
        }
        if (!!item.children && item.children.length !== 0) {
          stack = [...stack, ...item.children]
        }
      }
    }
  }

  logout() {
    // 清空服务端身份信息
    this.window.location.href = environment.logoutUrl
  }

  toDefault() {
    if (this.menuTree.length > 0) {
      const firstMenu = this.menuTree[0]
      if (firstMenu.children.length > 0) {
        const defaultUrl = firstMenu.children[0]['url']
        this.router.navigate([defaultUrl])
      }
    }
  }
}
