package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.DingdingService;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/12/22 16:31
 */
@Service
public class SyncDingdingIdOperatorService extends BaseOperatorService  {

	@Autowired
	private UserService userService;

	@Autowired
	private DingdingService dingdingService;

	@Autowired
	private SyncDingdingIdOperatorService syncDingdingIdOperatorService;

	@Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
		UserFilter filter = new UserFilter();

		filter.setEntryFlowStatus(3);
		filter.setStatusCode(0);
		filter.setDingdingIdCode(0);
		List<User> users = userService.getUsersByFilter(filter);
		if (users.size() > 0) {
			StringBuilder msg = new StringBuilder();
			for (User user : users) {
				OperateResult result = syncDingdingIdOperatorService.operateOnce(paraMap, user);
				msg.append(result.getMsg() + "<br/>");
			}
			return new OperateResult(1, msg.toString());
		} else {
			return new OperateResult(1, "没有需要同步的账号");
		}

    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        User user = (User) params[0];
        if (user != null){
            String result = dingdingService.addDingdingId(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1, "信息不存在");
        }
    }




}
