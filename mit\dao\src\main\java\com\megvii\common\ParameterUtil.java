package com.megvii.common;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * job中心传过来的参数进行处理
 */
public class ParameterUtil {

    /**
     * 将a=1,b=2这种形式的参数转换为Map
     */
    public static Map<String,String> getParaMap(String s) throws Exception{
        Map<String,String> paraMap=new HashMap<>();
        try{
            if(s!=null&&!"".equals(s.trim())){
                List<String> paras= Arrays.asList(s.split(","));
                for (String para:paras){
                    String[] a=para.split("=");
                    String key=a[0];
                    String value=a[1];
                    paraMap.put(key,value);
                }
            }
        }catch (Exception e){
            throw new Exception("参数解析错误,请检查参数格式");
        }
        return paraMap;

    }


     /**
     * 将a=1&b=2这种形式的参数转换为Map
     */
    public static Map<String,String> getParaMapV2(String s) throws Exception{
        Map<String,String> paraMap=new HashMap<>();
        try{
            if(s!=null&&!"".equals(s.trim())){
                List<String> paras= Arrays.asList(s.split("&"));
                for (String para:paras){
                    String[] a=para.split("=");
                    String key=a[0];
                    if (a.length == 1) {
                        paraMap.put(key,null);
                        continue;
                    }
                    String value= URLDecoder.decode(a[1], "utf-8");
                    paraMap.put(key,value);
                }
            }
        }catch (Exception e){
            throw new Exception("参数解析错误,请检查参数格式");
        }
        return paraMap;

    }


    public static <T>void fatherToChild(T father,T child) throws Exception {
        if (child.getClass().getSuperclass()!=father.getClass()){
            throw new Exception("child 不是 father 的子类");
        }
        Class<?> fatherClass = father.getClass();
        Field[] declaredFields = fatherClass.getDeclaredFields();
        for (int i = 0; i < declaredFields.length; i++) {
            Field field=declaredFields[i];
            Method method=fatherClass.getDeclaredMethod("get" + StringUtil.upperHeadChar(field.getName()));
            Object obj = method.invoke(father);
            field.setAccessible(true);
            field.set(child,obj);
        }

    }




}
