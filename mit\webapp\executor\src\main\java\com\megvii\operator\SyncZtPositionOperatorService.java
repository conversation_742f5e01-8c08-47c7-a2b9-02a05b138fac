package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.SyncZtUserService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/4/14 15:33
 */
@Service
public class SyncZtPositionOperatorService extends BaseOperatorService {

	@Autowired
	private SyncZtPositionOperatorService syncZtPositionOperatorService;

	@Autowired
	private SyncZtUserService syncZtUserService;


	@Override
    public OperateResult operateMain(Map paraMap, Object... params) {
		return syncZtPositionOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = syncZtUserService.syncZtUserPosition();
        return new OperateResult(1,msg);
    }


}
