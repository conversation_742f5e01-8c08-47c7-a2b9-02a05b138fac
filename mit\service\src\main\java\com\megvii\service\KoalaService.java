package com.megvii.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.client.KoalaRestClient;
import com.megvii.common.FileUtil;
import com.megvii.config.FtpService;
import com.megvii.entity.entry.EntryUserInfo;
import com.megvii.entity.opdb.ImageFile;
import com.megvii.entity.opdb.MitUserSystem;
import com.megvii.entity.opdb.User;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Service
@PropertySource("classpath:koala.${spring.profiles.active}.properties")
public class KoalaService {

    Logger logger= LoggerFactory.getLogger(KoalaService.class);

    @Autowired
    FtpService ftpService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    private KoalaRestClient koalaRestClient;

    @Value("${KOALA_HOST}")
    private String url;

    @Value("${KOALA_USER}")
    private String userName;

    @Value("${KOALA_PASSWORD}")
    private String password;

    @Value("${UPLOAD_PATH}")
    private String path;

    @Value("${UPLOAD_LOCAL_PATH}")
    private String localPath;

    @Value("${KOALA_USER}")
    private String koalaUserName;

    @Value("${KOALA_HOST}")
    private String koalaHost;

    @Value("${KOALA_PASSWORD}")
    private String koalaPassword;

    @Autowired
    private MitUserSystemService mitUserSystemService;

    /**
     * 3.1.0版本使用
     * 删除koala
     * @param user
     * @return
     */
    public String deleteKoalaInfo(User user) {
        if (user.getKoalaId() == null) {
            return user.getUserId() + "koalaId为null";
        }
        try {
            return koalaRestClient.deleteSubject(user.getKoalaId(), user.getUserId());
        } catch (Exception e) {
            logger.error(user.getUserId() + "删除koala失败:" + e.getMessage());
            return user.getUserId() + "删除koala失败:" + e.getMessage();
        }
    }

    /**
     * 旧版本koala使用
     * 删除koala
     * @param user
     * @return
     */
    public String deleteOldKoalaInfo(User user) {
        String koalaId;
        MitUserSystem mitUserSystem = mitUserSystemService.selectMitUserSystemByUserId(user.getUserId());
        if (mitUserSystem != null) {
            koalaId = mitUserSystem.getKoalaId();
        } else {
            koalaId = user.getKoalaId();
        }

        HttpHeaders headers=new HttpHeaders();
        List<String> cookiesList = new ArrayList<>();
        cookiesList.add(getCookies());
        headers.put(HttpHeaders.COOKIE,cookiesList);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity httpEntity = new HttpEntity(null, headers);

        try {
            restTemplate.delete(url + "/subject/" + koalaId, httpEntity);
            return user.getUserId() + "删除koala成功";
        } catch (Exception e) {
            logger.error(user.getUserId() + "删除koala失败");
            return user.getUserId() + "删除koala失败";
        }
    }


    /**
     *
     * 将生活照保存至ftp
     * 证件照保上传至考拉
     * @param userInfo
     * @return
     */
//    public String savaKoalaInfo(EntryUserInfo userInfo{
//        StringBuilder msg = new StringBuilder();
//        ImageFile photoCertFile = FileUtil.getByteFromBase64(userInfo.getPhotoCert(),userInfo.getUserId());
        //        2021-06-21 注释
//        ImageFile photoLifeFile = FileUtil.getByteFromBase64(userInfo.getPhotoLife(),userInfo.getUserId());
//        try {
//            boolean pdfFlag=ftpService.uploadImageFile(photoLifeFile.getFileBytes(),path,photoCertFile.getFileName());
//            if (pdfFlag){
//                stringBuilder.append(userInfo.getUserId() + "上传ftp成功,");
//            }else {
//                stringBuilder.append("<span style=\"color:#e83c4c;\"> " + userInfo.getUserId() + " 上传ftp失败</span>,");
//            }
//        }catch (Exception e){
//            logger.error(userInfo.getUserId() + "上传ftp错误",e);
//            stringBuilder.append("<span style=\"color:#e83c4c;\"> " + userInfo.getUserId() + "上传ftp错误</span>");
//        }

//        try {
//            boolean localFlag = saveOnLocal(userInfo.getUserId(), photoCertFile);
//            if (localFlag) {
//                msg.append(userInfo.getUserId() + "保存证件照到本地成功,");
//            } else {
//                msg.append("<span style=\"color:#e83c4c;\"> " + userInfo.getUserId() + " 保存证件照到本地失败</span>,");
//            }
//
//        } catch (IOException e) {
//            logger.error(userInfo.getUserId() + "保存证件照到本地错误" + e.getMessage());
//            msg.append("<span style=\"color:#e83c4c;\">" + userInfo.getUserId() + "保存证件照到本地错误</span>");
//        }
//        return msg.toString();
//    }

    /**
     * 10.199.1.81
     * 上传koala底库 创建用户
     * @param photoCertFile
     * @param userInfo
     * @return
     */
    public String addKoalaUserV1(ImageFile photoCertFile, EntryUserInfo userInfo) throws Exception {
        JSONObject koalaJson = upload2Koala(photoCertFile, userInfo);
        if (koalaJson != null) {
            if (koalaJson.getInteger("code") != null && koalaJson.getInteger("code").equals(0)) {
                String koalaId = String.valueOf(koalaJson.getJSONObject("data").getIntValue("id"));

                MitUserSystem mitUserSystem = new MitUserSystem();
				mitUserSystem.setUserId(userInfo.getUserId());
				mitUserSystem.setKoalaId(koalaId);
				mitUserSystemService.addMitUserSystem(mitUserSystem);

                return koalaId;
            }
        }
        throw new Exception(userInfo.getUserId() + "上传考拉【失败】" + koalaJson.get("myMsg"));
    }



    /**
     * 10.172.198.83
     * 上传koala底库 创建用户
     * @param photoCertFile
     * @param userInfo
     * @return
     */
    public String addKoalaUserV2(ImageFile photoCertFile, EntryUserInfo userInfo) throws Exception {
        //上传koala时灰度上传
        Integer koalaId = koalaRestClient.uploadBase(photoCertFile, userInfo.getUserId());
        String subjectId = koalaRestClient.importSubject(koalaId, userInfo);

        return subjectId;
    }

    /**
     *
     * 上传证件照至考拉
     * @param imageFile
     * @return
     */
    public JSONObject upload2Koala(ImageFile imageFile,EntryUserInfo userInfo){
        HttpHeaders headers=new HttpHeaders();
        List<String> cookiesList = new ArrayList<>();
        String cookies=getCookies();
        cookiesList.add(cookies);
        headers.put(HttpHeaders.COOKIE,cookiesList);
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> form = new LinkedMultiValueMap();
        InputStreamResource fileResource=new InputStreamResource(new ByteArrayInputStream(imageFile.getFileBytes())){
            @Override
            public long contentLength(){
                return imageFile.getFileBytes().length;
            }
            @Override
            public String getFilename(){
                return imageFile.getFileName();
            }
        };
        form.add("photo", fileResource);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(form, headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(koalaHost+"/subject/photo", requestEntity, JSONObject.class);
        JSONObject resultJson=responseEntity.getBody();
        JSONObject result=new JSONObject();
        if (resultJson.getInteger("code").equals(0)){
            result=importSubject(resultJson,cookies,userInfo);
        }else {
            result.put("myMsg",StringEscapeUtils.unescapeJava(resultJson.getString("desc")));
            logger.error("upload2Koala "+userInfo.getUserId(),StringEscapeUtils.unescapeJava(resultJson.getString("desc")));
        }
        return result;
    }


    /**
     * 保存证件照到本地
     * @param userId
     * @param photoCertFile
     * @return
     * @throws IOException
     */
    public boolean saveOnLocal(String userId ,ImageFile photoCertFile){
        logger.info(userId + "开始保存文件到本地");
        try {
            byte[] getData = photoCertFile.getFileBytes();
            File saveDir = new File(localPath);
            if (!saveDir.exists()) {
                saveDir.mkdir();
            }

            FileOutputStream fos = null;
            File file = new File(saveDir + File.separator + photoCertFile.getFileName());
            fos = new FileOutputStream(file);
            fos.write(getData);

            if (fos != null) {
                fos.close();
            }
            logger.info(userId + "保存文件到本地结束");
            return true;
        } catch (Exception e) {
            logger.error("保存文件到本地异常" + e.getMessage());
            return false;
        }
    }


    public JSONObject importSubject(JSONObject jsonObject,String cookies,EntryUserInfo userInfo){
        int koalaId=jsonObject.getJSONObject("data").getIntValue("id");
        HttpHeaders headers=new HttpHeaders();
        List<String> cookiesList = new ArrayList<>();
        cookiesList.add(cookies);
        headers.put(HttpHeaders.COOKIE,cookiesList);
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject data=new JSONObject();
        JSONArray array=new JSONArray();
        array.add(koalaId);
        data.put("subject_type",0);
        data.put("name", userInfo.getSpell());
        data.put("gender",userInfo.getGenderNumber());
        data.put("photo_ids",array);
        data.put("email",userInfo.getUserId()+"@megvii.com");
        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(),headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(koalaHost+"/subject", requestEntity, JSONObject.class);
        JSONObject resultJson=responseEntity.getBody();
        if (resultJson.getInteger("code").equals(0)){
            int subjectId = resultJson.getJSONObject("data").getIntValue("id");
            updateSubject(subjectId,cookies,userInfo,array);
        }else {
            resultJson.put("myMsg",StringEscapeUtils.unescapeJava(resultJson.getString("desc")));
            logger.error("importSubject:",StringEscapeUtils.unescapeJava(resultJson.getString("desc")));
        }
        return resultJson;
    }

    public void updateSubject(int subjectId,String cookies,EntryUserInfo userInfo,JSONArray photos){
        HttpHeaders headers=new HttpHeaders();
        List<String> cookiesList = new ArrayList<>();
        cookiesList.add(cookies);
        headers.put(HttpHeaders.COOKIE,cookiesList);
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject data=new JSONObject();
        data.put("subject_type",0);
        data.put("gender",userInfo.getGenderNumber());
        data.put("name", userInfo.getSpell());
        data.put("photo_ids",photos);
        HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(),headers);
        restTemplate.put(koalaHost+"/subject/"+subjectId,requestEntity);
    }
    /**
     * 获取考拉登录cookies
     * @return
     */
    public String getCookies(){

        HttpHeaders headers=new HttpHeaders();
        headers.add("User-Agent","Koala Admin");
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> map = new HashMap<>();
        map.put("username",koalaUserName);
        map.put("password",koalaPassword);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity(map, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(koalaHost+"/auth/login", requestEntity, String.class);
        String cookies=responseEntity.getHeaders().getFirst("Set-Cookie");
        return cookies;
    }




    /**
     * unicode转中文
     * @param dataStr
     * @return
     */
    public static String decodeUnicode(final String dataStr) {
        int start = 0;
        int end = 0;
        final StringBuffer buffer = new StringBuffer();
        while (start > -1) {
            end = dataStr.indexOf("\\u", start + 2);
            String charStr = "";
            if (end == -1) {
                charStr = dataStr.substring(start + 2, dataStr.length());
            } else {
                charStr = dataStr.substring(start + 2, end);
            }
            char letter = (char) Integer.parseInt(charStr, 16); // 16进制parse整形字符串。
            buffer.append(new Character(letter).toString());
            start = end;
        }
        return buffer.toString();
    }




}
