import { Injectable } from '@angular/core'
import { SendRequest, UtilsService } from '../core/services'

@Injectable()
export class ContractService {
  constructor(private sendRequest: SendRequest) {}

  // 模糊搜索部门
  getTeamList(data) {
    return this.sendRequest.get('/api/team/search', data)
  }

  // 模糊搜索人员列表，用于新签和合同管理
  getUserList(data) {
    return this.sendRequest.post('/api/user/search', data)
  }

  // 模糊搜索人员列表，用于续签
  getExtendUserList(data) {
    return this.sendRequest.post('/api/user/backlog/search', data)
  }

  // 模糊搜索人员列表，用于转签
  getEndorseUserList(data) {
    return this.sendRequest.post('/api/user/changebacklog/search', data)
  }

  // 模糊搜索人员列表，用于变更
  getChangeUserList(data) {
    return this.sendRequest.post('/api/user/changebacklog/search2', data)
  }

  //模糊搜索人员列表，用于待作废
  getVoidUserList(data) {
    return this.sendRequest.post('/api/user/backlog/search2', data)
  }

  // 根据userid获取合同历史
  getContractlistWithSnapshot(data) {
    return this.sendRequest.get('/api/user/manageContract', data)
  }
  // 其他合同获取合同历史
  getOtherContractWithSnapshot(data) {
    return this.sendRequest.get('/api/user/contract', data)
  }

  // 根据userid获取合同历史
  getContractHistoryWithoutSnapshot(data) {
    return this.sendRequest.get('/api/contract/list', data)
  }

  // 根据userid获取操作历史
  getOprtHistory(data) {
    return this.sendRequest.get('/api/contract/changelog', data)
  }

  // 获取人员的基本信息
  getBaseInfo(data) {
    return this.sendRequest.get('/api/user/info', data)
  }

  // 保存人员的基本信息
  saveBaseInfo(data) {
    return this.sendRequest.post('/api/user/info', data)
  }

  // 获取快照详情
  getSnapshot(data) {
    return this.sendRequest.get('/api/contract/spanshot', data)
  }
  // 获取合同新签快照详情
  getNewSnapshot(data) {
    return this.sendRequest.get('/api/contract/spanshot/detailWithIntern', data)
  }

  // 获取所有合同状态
  getContractStatusList() {
    return this.sendRequest.get('/resource/contractStatus', {})
  }

  // 删除旧快照，生成新快照
  createSpanshot(data) {
    return this.sendRequest.post('/api/contract/spanshot/create', data)
  }

  // 修改快照
  updateSpanshot(data) {
    return this.sendRequest.post('/api/contract/spanshot/update', data)
  }

  // 查看合同
  getReviewContractUrl(data) {
    return this.sendRequest.get('/api/contract/info', data)
  }

  // 撤销合同
  recallContract(data) {
    return this.sendRequest.post('/api/contract/recall', data)
  }

  // 作废合同
  cancelContract(data) {
    return this.sendRequest.post('/api/contract/cancel', data)
  }

  // 删除附件
  deleteFile(data) {
    return this.sendRequest.delete('/api/contract/snapshot/file', data)
  }

  // 签署合同
  signContract(data) {
    return this.sendRequest.post('/api/contract/sign', data)
  }
  // 新签合同签署
  signContractNew(data) {
    return this.sendRequest.post('/api/contract/sign/new', data)
  }

  // 转签变更签署合同
  signContractChange(data) {
    return this.sendRequest.post('/api/changecontract/sign', data)
  }

  // 删除草稿
  deleteDraft(data) {
    return this.sendRequest.post('/api/contract/spanshot/delete', data)
  }

  // 线下签署
  signOffline(data) {
    return this.sendRequest.post('/api/contract/sign/offline', data)
  }

  // 转签变更线下签署
  signOfflineChange(data) {
    return this.sendRequest.post('/api/changecontract/sign/offline', data)
  }

  // 线下签署获取合同信息，合同转签 & 合同变更
  getContractBacklog(data) {
    return this.sendRequest.get('/api/changecontract/getContractBacklog', data)
  }

  // 线下签署获取合同信息，新签合同 & 续签合同
  getContractInfo(data) {
    return this.sendRequest.get('/api/contract/getContractInfo', data)
  }

  // 在续签合同页面，作废完成后调用
  voidingContract(data) {
    return this.sendRequest.get(
      `/api/user/backlog/updateBacklogDimissionTag?userId=${data.userId}`,
      {}
    )
  }

  // 编辑开始和结束时间提交
  submitEditAgree(data) {
    return this.sendRequest.post('/api/contract/updateInfo', data)
  }

  // 新增协议
  submitAddAgree(data) {
    return this.sendRequest.post('/api/user/addContract', data)
  }

  // 新增协议上传附件
  uploadAgreeFile(data, formData) {
    return this.sendRequest.uploadFile(
      `/api/contract/contractFile/upload?userId=${data.id}&fileName=${data.name}`,
      formData
    )
  }

  //编辑协议
  submitEditSelfAgree(data) {
    return this.sendRequest.post('/api/contract/updateOfflineInfo', data)
  }

  // 删除文件
  deleteAgree(data) {
    return this.sendRequest.delete('/api/contract/contract/file', data)
  }
}
