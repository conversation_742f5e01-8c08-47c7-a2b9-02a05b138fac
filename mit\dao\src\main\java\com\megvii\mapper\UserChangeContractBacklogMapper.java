package com.megvii.mapper;

import com.megvii.entity.opdb.contract.UserChangeContractBacklog;
import com.megvii.mapper.filter.UserBacklogFilter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/10 18:37
 */
public interface UserChangeContractBacklogMapper {

	int insertUserChangeContractBacklog(UserChangeContractBacklog backlog);

	int updateUserChangeContractBacklog(UserChangeContractBacklog backlog);

	List<UserChangeContractBacklog> selectBacklogByFilter(@Param("filter") UserBacklogFilter filter);

	UserChangeContractBacklog selectNotFinishBacklogByUserId(String userId);

	int updateUserChangeContractBacklogInfo(UserChangeContractBacklog backlog);
}
