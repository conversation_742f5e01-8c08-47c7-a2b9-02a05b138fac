package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * 北森接口请求类
 */
@Component
@PropertySource("classpath:beisen.${spring.profiles.active}.properties")
public class BeisenRestClient {
    private Logger logger= LoggerFactory.getLogger(BeisenRestClient.class);

    @Value("${userUrl1}")
    private String userUrl1;

    @Value("${userUrl2}")
    private String userUrl2;

    @Value("${userUrl3}")
    private String userUrl3;

    @Value("${userUrl4}")
    private String userUrl4;

    @Value("${applicantInfoUrl}")
    private String applicantInfoUrl;

    @Value("${offerInfoUrl}")
    private String offerInfoUrl;

    @Value("${token}")
    private String token;

    @Autowired
    RestTemplate restTemplate;

    /**
     * @param startDate 起止时间
     * @param endDate  结束时间
     * @return  所有offer人员的beisen id
     */
    public List<Integer> getAllBeisenUserId(LocalDateTime startDate, LocalDateTime endDate){
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add("Authorization",token);
        HttpEntity<String> requestEntity = new HttpEntity<>(null, requestHeaders);
        List userIdList=new ArrayList();

        String s1 = userUrl1 + "&" + getDateParam(startDate,endDate);
        logger.info("ur11:"+s1);
        userIdList.addAll((List)RequestCorvertUtil.getJSONArrayFromStr(s1,restTemplate.exchange(s1, HttpMethod.GET, requestEntity, String.class).getBody()));

        String s2 = userUrl2 + "&" + getDateParam(startDate,endDate);
        logger.info("ur12:"+s2);
        userIdList.addAll((List)RequestCorvertUtil.getJSONArrayFromStr(s2,restTemplate.exchange(s2, HttpMethod.GET, requestEntity, String.class).getBody()));

        String s3 = userUrl3 + "&" + getDateParam(startDate,endDate);
        logger.info("ur13:"+s3);
        userIdList.addAll((List)RequestCorvertUtil.getJSONArrayFromStr(s3,restTemplate.exchange(s3, HttpMethod.GET, requestEntity, String.class).getBody()));

        String s4 = userUrl4 + "&" + getDateParam(startDate,endDate);
        logger.info("ur14:"+s4);
        userIdList.addAll((List)RequestCorvertUtil.getJSONArrayFromStr(s4,restTemplate.exchange(s4, HttpMethod.GET, requestEntity, String.class).getBody()));
        return userIdList;
    }


    /**
     * @param userId  beisen Id
     * @return  人员信息
     */
    public Map<String, JSONArray> getUserInfo(int userId){
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add("Authorization",token);
        HttpEntity<String> requestEntity = new HttpEntity<String>(null, requestHeaders);
        Map result=new HashMap();
        String s1=applicantInfoUrl+userId;
        String s2=offerInfoUrl+userId;
        result.put("applicantInfo",RequestCorvertUtil.getJSONArrayFromStr(s1,restTemplate.exchange(s1, HttpMethod.GET, requestEntity, String.class).getBody()));
        result.put("offerInfo",RequestCorvertUtil.getJSONArrayFromStr(s2,restTemplate.exchange(s2, HttpMethod.GET, requestEntity, String.class).getBody()));
        return result;
    }


    /**
     * 日期参数转换
     * @param startDate
     * @param endDate
     * @return
     */
    private String getDateParam(LocalDateTime startDate,LocalDateTime endDate){
        DateTimeFormatter formatter=DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return "start_time="+startDate.format(formatter)+"&end_time="+endDate.format(formatter);
    }



}

