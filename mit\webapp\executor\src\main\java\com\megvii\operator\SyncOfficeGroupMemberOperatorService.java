package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.SyncOfficeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/10 18:23
 */
@Service
public class SyncOfficeGroupMemberOperatorService extends BaseOperatorService {

    @Autowired
    private SyncOfficeGroupMemberOperatorService syncOfficeGroupMemberOperatorService;

    @Autowired
    private SyncOfficeService syncOfficeService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return syncOfficeGroupMemberOperatorService.operateOnce(paraMap, params);
    }
    public OperateResult operateByGroupName(String groupName) {
        String msg = syncOfficeService.syncGroupMembersByName(groupName);
        return new OperateResult(1,msg);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = syncOfficeService.syncAllOfficeGroupMembers();
        return new OperateResult(1,msg);
    }



}
