package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.service.DhrService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/17 18:48
 */
@Service
public class DhrDimissionOperatorService extends BaseOperatorService {

    @Autowired
    private DhrDimissionOperatorService dhrDimissionOperatorService;

    @Autowired
    private DhrService dhrService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return dhrDimissionOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        String msg = dhrService.getDimssionDateFromDhr();
        return new OperateResult(1,msg);
    }



}
