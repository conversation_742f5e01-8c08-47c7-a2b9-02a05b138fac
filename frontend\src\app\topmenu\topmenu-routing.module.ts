import { NgModule } from '@angular/core'
import { Routes, RouterModule } from '@angular/router'
import { TopmenuComponent } from './topmenu.component'
import { NoAuthComponent } from '../status'

const routes: Routes = [
  {
    path: '',
    component: TopmenuComponent,
    data: {
      keep: true
    },
    children: [
      {
        path: 'auth',
        loadChildren: () => import('../auth/auth.module').then((m) => m.AuthModule),
        data: {
          title: '权限管理'
        }
      },
      {
        path: 'contract',
        loadChildren: () => import('../contract/contract.module').then((m) => m.ContractModule),
        data: {
          title: '合同管理'
        }
      },
      {
        path: 'system',
        loadChildren: () => import('../system/system.module').then((m) => m.SystemModule),
        data: {
          title: '系统管理'
        }
      },
      {
        path: '500',
        component: NoAuthComponent,
        data: {
          title: '500'
        }
      }
    ]
  }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TopmenuRoutingModule {}
