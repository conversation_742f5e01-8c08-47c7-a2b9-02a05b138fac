package com.megvii.controller;

import com.megvii.controller.resource.api.ResponseDataEntity;
import org.apache.shiro.authz.AuthorizationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2020/5/9 16:27
 */
@ControllerAdvice
public class ControllerExceptionHandler {

	Logger logger= LoggerFactory.getLogger(ControllerExceptionHandler.class);

	@ResponseBody
    @ExceptionHandler(AuthorizationException.class)
    public ResponseDataEntity customizeException(AuthorizationException e) {
        logger.error("======>server catch exception:{}, exception:{}", e.getMessage(), e);

        ResponseDataEntity response = new ResponseDataEntity();
        response.setCode(ResponseDataEntity.UNAUTHORIZED);
        response.setMsg("无权限");

        return response;
    }


}
