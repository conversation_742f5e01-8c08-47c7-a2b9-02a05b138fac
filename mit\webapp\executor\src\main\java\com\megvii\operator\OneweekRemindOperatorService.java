package com.megvii.operator;

import com.megvii.common.DateTimeUtil;
import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.RemindService;
import com.megvii.service.UserService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/2/17 16:16
 */
@Service
public class OneweekRemindOperatorService extends BaseOperatorService {

    @Autowired
    private OneweekRemindOperatorService oneweekRemindOperatorService;

    @Autowired
    private UserService userService;

    @Autowired
    private RemindService remindService;


    /**
     *
     * 主操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
        Object userId = paraMap.get("userId");
        if(userId != null){
            return OperateByUserId((String) userId);
        } else {
            UserFilter filter = new UserFilter();
            filter.setStatusCode(0);
            filter.setEntryFlowStatus(0);
            filter.setExpectDate(DateTimeUtil.date2String(DateTimeUtil.dateAddInt(LocalDateTime.now(), -7)));

            List<User> users = userService.getUsersByFilter(filter);
            if (users.size() > 0) {
                StringBuilder msg = new StringBuilder();
                for (User user : users) {
                    OperateResult result = oneweekRemindOperatorService.operateOnce(paraMap, user);
                    msg.append(result.getMsg() + "<br/>");
                }
                return new OperateResult(1,msg.toString());
            } else {
                return new OperateResult(1, "没有要发送账号");
            }
        }
    }


    /**
     *
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        User user = (User) params[0];
        if (user!=null){
            String result = remindService.sendOneweekRemind(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1, user.getUserId() + "信息不存在");
        }
    }


    /**
     *
     * 根据userId添加
     * @param userId
     * @return
     */
    public OperateResult OperateByUserId(String userId){
        User user = userService.getUserByUserId(userId);

        if (user != null){
            String result = remindService.sendOneweekRemind(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1,userId + "信息不存在");
        }
    }







}
