import { Component, OnInit } from '@angular/core'
import { ParamStoreService } from '../../core/services/paramStore.service'

@Component({
  selector: 'app-mgt',
  templateUrl: './mgt.component.html',
  styleUrls: ['./mgt.component.less']
})
export class MgtComponent implements OnInit {
  isShowSelf = true
  selectedIndex = 0

  constructor(private paramStoreService: ParamStoreService) {}

  ngOnInit(): void {
    this.selectedIndex = this.paramStoreService.getMap('mgt')
  }

  onActivate(event) {
    this.isShowSelf = false
  }

  onDeactivate(event) {
    this.isShowSelf = true
  }

  selectedIndexChange(event) {
    this.selectedIndex = event
    this.paramStoreService.saveMap('mgt', event)
  }
}
