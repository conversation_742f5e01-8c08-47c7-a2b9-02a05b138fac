package com.megvii;

import com.megvii.client.SfRestClient;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.*;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest

public class SfTest {
    @Autowired
    SfRestClient sfRestClient;

    @Autowired
    UserService userService;
    @Autowired
    MMISMegviiConfigService mmisMegviiConfigService;

    @Autowired
    SyncSfUserService syncSfUserService;

    @Autowired
    private LunaDiracUserService lunaDiracUserService;

    @Autowired
    private DiracUserService diracUserService;

    @Autowired
    private SyncSfTeamService syncSfTeamService;

    @Test
    public void testIsExist(){
        boolean b=sfRestClient.isExistUser("101969");
        return;
    }
    @Test
    public void testLocation(){
        String s= sfRestClient.getLocationStr("上海");
        return;
    }

    @Test
    public void testConfig(){
        Map map=mmisMegviiConfigService.getWorkSiteMap();
        return;
    }

    @Test
    public void testWorkBase(){
        String s=sfRestClient.getWorkCodeBase("海龙大厦");
        return;
    }


    @Test
    public void testSyn(){
        User user=userService.getUserByUserId("zhangtong02");
        syncSfUserService.synUser2Sf(user);
        return;
    }

    @Test
    public void testCreatSfUser(){
        sfRestClient.createUser("103929","yangweiqi","杨玮琪","北京","","");
    }

    @Test
    public void testGet() {
//        JSONArray sfGroup = sfRestClient.getGroups();
//        JSONArray sfBu = sfRestClient.getBus();
//        JSONArray sfTeam = sfRestClient.getTeams();
//        JSONArray sfSubTeam = sfRestClient.getSubTeams();
//        JSONArray sfSeciton = sfRestClient.getSections();
//        JSONArray users = sfRestClient.getUsers();
//        JSONArray ships = sfRestClient.getSfRelationships();
//        sfRestClient.getSfEmp();

//        syncSfUserService.getSfUserInfo();
//        sfRestClient.addEmailInfo("101232", "xuchunfang");
//        User user = userService.getUserByWorkNumber("104656");
//        System.out.println(user);
//
//        LunaDiracUser lunaDiracUser = lunaDiracUserService.getLunaDiracUserByUsername("twb");
//        System.out.println(lunaDiracUser);
//        lunaDiracUser.setCname("唐文斌1");
//        lunaDiracUserService.updateLunaDiracUser(lunaDiracUser);
//
//        DiracUser diracUser = diracUserService.selectUserByUserId("brian");
//        System.out.println(diracUser);
//        diracUser.setSpell("李百恩1");
//        diracUserService.updateDiracUser(diracUser);




        System.out.println(1);
    }

    @Test
    public void testSync() {
//        syncSfTeamService.syncSfTeam();
        String a = syncSfUserService.syncSfUser("xuxiaofeng");
//        System.out.println(a);

//        syncSfTeamService.syncDisableSfTeam();

    }

    @Test
    public void testDate() {
        LocalDateTime start = LocalDateTime.ofEpochSecond(Long.valueOf("1567666541000")/1000, 0, ZoneOffset.of("+0"));
        LocalDateTime end = LocalDateTime.ofEpochSecond(Long.valueOf("1567320941000")/1000, 0, ZoneOffset.of("+0"));



        int diff = DateTimeUtil.diffDateByDay(start, end);
        System.out.println(diff);

        long a = start.toLocalDate().toEpochDay() - end.toLocalDate().toEpochDay();
        System.out.println(a);

    }

    @Test
    public void testGetUser() {
//        List<User> userList = userService.getUserByUpdateTime(20);
        UserFilter filter = new UserFilter();
        filter.setStatusCode(1);

        List<User> userList = userService.getUsersByFilter(filter);
        for (User user : userList) {
            System.out.println(user);
        }



    }


}
