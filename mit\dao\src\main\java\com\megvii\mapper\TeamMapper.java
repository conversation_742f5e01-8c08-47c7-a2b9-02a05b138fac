package com.megvii.mapper;

import com.megvii.entity.opdb.Team;
import java.util.List;

public interface TeamMapper {

    Team selectTeamByCode(String code);

    Team selectTeamByName(String name);

    Team selectTeamById(int id);

    Team selectTeamByDingDingId(int id);

    int addTeam(Team team);

    int updateTeamById(Team team);

    List<Team> selectAllTeam();

    List<Team> selectActiviteTeam();

    List<Team> selectTeamsByName(String name);

}
