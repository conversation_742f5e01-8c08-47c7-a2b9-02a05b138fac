:host {
  .search-container {
    overflow: hidden;
    .ant-input,
    .ant-select {
      width: 180px;
    }
    & > span {
      display: inline-block;
      margin-bottom: 10px;

      & > label {
        display: inline-block;
        width: 100px;
        text-align: right;
      }
    }

    .pick-up-label {
      width: 60px !important;
    }
    // .ant-input {
    //   width: 234px ;
    // }
  }
  .invalid-contract {
    td > span {
      color: #d9d9d9;
    }
  }

  .ant-btn {
    border-radius: 0px;
  }
  .signStatus {
    display: inline-block;
    min-width: 80px;
  }
}

:host::ng-deep {
  .user-item {
    background: rgb(255, 255, 255) !important;
  }
  .content-block > .search-container .date-pick .ant-input {
    width: 234px !important;
  }
  .ant-table table th {
    padding: 11px 5px;
  }

  .ant-table-tbody > tr > td {
    padding: 16px 5px;
  }
}
