package com.megvii.controller.jwt.api;


import com.megvii.entity.opdb.Team;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/9/25 14:32
 */
@Data
public class JobCardTeamInfoVO {

    private Integer id;
    private String name;
    private String sfCode;

    public static List<JobCardTeamInfoVO> toVOs(List<Team> teams) {
        List<JobCardTeamInfoVO> vos = new ArrayList<>();
        for (Team team : teams) {
            vos.add(toVO(team));
        }

        return vos;
    }

    private static JobCardTeamInfoVO toVO(Team team) {
        JobCardTeamInfoVO vo = new JobCardTeamInfoVO();
        vo.setId(team.getId());
        vo.setName(team.getName());
        vo.setSfCode(team.getSfCode());

        return vo;
    }

}
