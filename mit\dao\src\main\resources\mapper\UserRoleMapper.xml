<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.UserRoleMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.UserRole">
        <id column="id" property="id"/>
        <result column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="usable_status" property="usableStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <insert id="insertUserRole" parameterType="com.megvii.entity.opdb.UserRole" >
        INSERT INTO `it_mit_user_role` (
	        `id`,
	        `role_id`,
	        `role_name`,
	        `role_code`,
	        `user_id`,
	        `user_name`,
	        `usable_status`,
	        `create_time`,
	        `create_user`,
	        `update_time`,
	        `update_user`
        )
        VALUES
	    (
		    0,
		    #{roleId},
		    #{roleName},
	    	#{roleCode},
		    #{userId},
		    #{userName},
		    #{usableStatus},
		    NOW(),
		    #{createUser},
		    NOW(),
		    #{updateUser}
	    );
    </insert>

    <update id="updateUserRole" parameterType="com.megvii.entity.opdb.UserRole">
        update it_mit_user_role
        <set>
            update_time = NOW(),
            <if test="usableStatus != null ">
                usable_status = #{usableStatus},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="removeUserRole" parameterType="int" >
        DELETE FROM it_mit_user_role WHERE id = #{id}
    </delete>

    <select id="selectUserRole" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from it_mit_user_role
    </select>

    <select id="selectUserRoleByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from it_mit_user_role where user_id = #{userId}
    </select>

    <select id="selectAllUser" resultType="java.lang.String">
        SELECT DISTINCT user_id FROM it_mit_user_role
    </select>

</mapper>
