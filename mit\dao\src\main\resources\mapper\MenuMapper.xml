<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.MenuMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.Menu">
        <id column="id" property="id"/>
        <result column="menu_name" property="menuName"/>
        <result column="menu_code" property="menuCode"/>
        <result column="url" property="url"/>
        <result column="priority" property="priority"/>
        <result column="menu_level" property="menuLevel"/>
        <result column="parent_id" property="parentId"/>
        <result column="parent_code" property="parentCode"/>
        <result column="usable_status" property="usableStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <sql id="menuColumns" >
        t3.id AS id,
        t3.menu_name AS menu_name,
        t3.menu_code AS menu_code,
        t3.url AS url,
        t3.priority AS priority,
        t3.menu_level AS menu_level,
        t3.parent_id AS parent_id,
        t3.parent_code AS parent_code,
        t3.usable_status AS usable_status,
        t3.create_time AS create_time,
        t3.create_user AS create_user,
        t3.update_time AS update_time,
        t3.update_user AS update_user
    </sql>

    <insert id="insertMenu" parameterType="com.megvii.entity.opdb.Menu" >
        INSERT INTO `it_mit_menu` (
	        `id`,
	        `menu_name`,
	        `menu_code`,
	        `url`,
	        `priority`,
	        `menu_level`,
	        `parent_id`,
	        `parent_code`,
	        `usable_status`,
	        `create_time`,
	        `create_user`,
	        `update_time`,
	        `update_user`
        )
        VALUES
	    (
		    0,
		    #{menuName},
		    #{menuCode},
	    	#{url},
		    #{priority},
		    #{menuLevel},
		    #{parentId},
		    #{parentCode},
		    #{usableStatus},
		    NOW(),
		    #{createUser},
		    NOW(),
		    #{updateUser}
	    );
    </insert>

    <update id="updateMenu" parameterType="com.megvii.entity.opdb.Menu">
        update it_mit_menu
        <set>
            update_time = NOW(),
            <if test="menuName != null ">
                menu_name = #{menuName},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectMenuByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from it_mit_menu where menu_code = #{code}
    </select>

    <select id="selectMenuByUserId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="menuColumns" />
        FROM
	    it_mit_user_role t1
	    INNER JOIN it_mit_role_menu t2 ON t1.role_id = t2.role_id
	    INNER JOIN it_mit_menu t3 ON t2.menu_id = t3.id
        WHERE
	    user_id = #{userId}
	    ORDER BY t3.priority
    </select>


</mapper>
