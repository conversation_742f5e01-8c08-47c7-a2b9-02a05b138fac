package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@PropertySource(value = "classpath:office365.properties",encoding = "UTF-8")
public class Office365Client {

    Logger logger= LoggerFactory.getLogger(Office365Client.class);

    @Value("${OFFICE_TOKEN_URL}")
    private String officeTokenUrl;
    @Value("${OFFICE_CLIENT_ID}")
    private String officeClientId;
    @Value("${OFFICE_CLIENT_SECRET}")
    private String officeClientSecret;
    @Value("${OFFICE_GRANT_TYPE}")
    private String officeGrantType;
    @Value("${OFFICE_SCOPE}")
    private String officeScope;
    @Value("${OFFICE_RESOURCE}")
    private String officeResource;
    @Value("${MAIL_ADDRESS_SUFFIX}")
    private String mailSuffix;
    @Value("${OFFICE_GET_LICENSE}")
    private String getLicenseUrl;

    @Value("${OFFICE_GRAPH_URL}")
    private String officeGraphUrl;
    @Value("${OFFICE_LICENSE}")
    private String officeLicense;

    @Autowired
    RestTemplate restTemplate;
    @Autowired
    RedisTemplate redisTemplate;

//    public Map<String, JSONObject> getDeltaGroup() throws UnsupportedEncodingException {
//        String url = (String) redisTemplate.opsForValue().get("mitexecutor_office365_deltaLink");
//
//        if (url == null) {
////            String endPoint = "/groups/delta/?$select=displayName,description,mail";
//            String endPoint = "/groups/delta";
//            url = officeGraphUrl + endPoint;
//        }
//
//        HttpHeaders headers = getAuthHeader();
//        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
//
//        //
//        Map<String, JSONObject> values = new HashMap<>();
//        List<String> list = new ArrayList<>();
//        while (true) {
//            JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(url,
//                    restTemplate.exchange(url, HttpMethod.GET,httpEntity,String.class).getBody());
//
//            JSONArray value = (JSONArray) resultData.get("value");
//            for (Object o : value) {
//                JSONObject group = (JSONObject) o;
//
//                if (group.getString("mail") == null) {
//                    continue;
//                }
//                String[] mail = group.getString("mail").split("@");
//                values.put(mail[0], group);
//                list.add(mail[0]);
//
//
//            }
//
//            String nextUrl = null;
//            nextUrl = resultData.getString("@odata.nextLink");
//            if (nextUrl != null) {
//                url = URLDecoder.decode(nextUrl,"ISO-8859-1");
//            } else if (nextUrl == null) {
//                redisTemplate.opsForValue().set("mitexecutor_office365_deltaLink",resultData.getString("@odata.deltaLink"));
//                redisTemplate.expire("mitexecutor_office365_deltaLink",18000, TimeUnit.SECONDS);
//                break;
//            }
//        }
//        return values;
//
//    }


    /**
     * 通过group_id 获取group的成员的内部账号（已废弃）
     * @param groupId
     * @return
     */
    public List<String> getGroupMemberById(String groupId) {
        String url = officeGraphUrl + "/groups/" + groupId + "/members?$select=mail";
        HttpHeaders headers = getAuthHeader();

        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        try {
            List<String> result = new ArrayList<>();
            while (true) {
                JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(url,
                        restTemplate.exchange(url, HttpMethod.GET,httpEntity,String.class).getBody());

                for (Object value : resultData.getJSONArray("value")) {
                    JSONObject data = (JSONObject) value;
                    result.add(data.getString("mail").split("@")[0]);
                }

                String nextUrl = resultData.getString("@odata.nextLink");
                if (nextUrl != null) {
                    url = URLDecoder.decode(nextUrl,"ISO-8859-1");
                    continue;
                } else {
                    return result;
                }
            }

        } catch (Exception e) {
            logger.error("查询组异常:" + e);
            return null;
        }
    }




    /**
     * 修改office365 visibility 属性 （已废弃）
     * @param name
     * @param groupGraphId
     * @param visibility
     * @return
     */
    public String updateGroupVisibility(String name, String groupGraphId, String visibility) {
        String endPoint = "/groups/" + groupGraphId;

        HttpHeaders headers = getAuthHeader();

        JSONObject data = new JSONObject();
        data.put("visibility", visibility);

        HttpEntity<String> httpEntity=new HttpEntity<>(data.toJSONString(),headers);

        try {
            restTemplate.patchForObject(officeGraphUrl + endPoint, httpEntity, String.class);
        } catch (Exception e) {
            logger.error(name + "修改visibility失败:" + e.getMessage());
            return name + "修改visibility失败:" + e.getMessage();
        }

        return name + "修改visibility成功";
    }


    /**
     * 获取office365邮箱授权（已废弃）
     * @param email
     * @return
     */
    public List<String> getLicense(String email) {
        String url = getLicenseUrl + email + "?$select=assignedLicenses";

        HttpHeaders headers = getAuthHeader();
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(url,
                restTemplate.exchange(url, HttpMethod.GET,httpEntity,String.class).getBody());

        JSONArray liceses = resultData.getJSONArray("assignedLicenses");
        List<String> result = new ArrayList<>();

        for (Object licese : liceses) {
            JSONObject data = (JSONObject) licese;
            result.add(data.getString("skuId"));

        }

        return result;

    }



    /**
     * office365账号解除授权（已废弃）
     * @param email
     * @return
     */
    public boolean delAssign(String email, String license) {
        String url = officeGraphUrl + "/users/" + email + "/assignLicense";

        JSONArray removeLicenses = new JSONArray();
        removeLicenses.add(license);

        JSONObject data = new JSONObject();
        data.put("addLicenses", new JSONArray());
        data.put("removeLicenses", removeLicenses);

        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, new HttpEntity<String>(data.toJSONString(),getAuthHeader()),JSONObject.class);

        if (responseEntity.getStatusCodeValue()==200)
            return true;
        else
            return false;
    }


    /**
     * 获取用户所有group（已废弃）
     * @param email
     * @return
     */
    public Map<String, JSONObject> getUserGroups(String email) {
        String url = officeGraphUrl + "/users/" + email + "/memberOf";

        HttpHeaders headers = getAuthHeader();
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(url,
                restTemplate.exchange(url, HttpMethod.GET,httpEntity,String.class).getBody());

        JSONArray value = (JSONArray) resultData.get("value");
        Map<String, JSONObject> values = new HashMap<>();
        for (Object o : value) {
            JSONObject group = (JSONObject) o;
            String[] mail = group.getString("mail").split("@");
            values.put(mail[0], group);
        }

        return values;
    }



    /**
     * 删除user的office365 邮件组
     * @param userGraphId
     * @param groupGraphId
     * @return
     */
    public boolean deleteUserGroup(String userGraphId, String groupGraphId) {
        String endPoint = "/groups/" + groupGraphId + "/members/" + userGraphId + "/$ref";

        HttpHeaders headers = getAuthHeader();

        JSONObject data = new JSONObject();
        data.put("@odata.id", "https://microsoftgraph.chinacloudapi.cn/v1.0/directoryObjects/" + userGraphId);

        HttpEntity<String> httpEntity = new HttpEntity<>(data.toJSONString(), headers);
        ResponseEntity<JSONObject> result = restTemplate.exchange(officeGraphUrl + endPoint, HttpMethod.DELETE, httpEntity, JSONObject.class);

        if (result.getStatusCode().equals(HttpStatus.NO_CONTENT)) {
            return true;
        } else {
            logger.info(userGraphId + "删除" + groupGraphId + "失败");
            return false;
        }
    }


    /**
     * put方法更新头像 更新前先检查是否存在头像（已废弃）
     * @param userId
     * @param officeId
     * @param file
     * @return
     */
    public String updateUserProfile(String userId, String officeId, byte[] file) {
        String endPoint = "/users/" + officeId + "/photo/$value";

        HttpEntity httpEntity = new HttpEntity(file,getAuthHeader());
        try {
            ResponseEntity data = restTemplate.exchange(officeGraphUrl + endPoint, HttpMethod.PUT,httpEntity,String.class);

            if (data != null && data.getStatusCode().equals(HttpStatus.OK)) {
                return userId + "上传头像成功";
            } else {
                return userId + "上传头像失败";
            }
        } catch (Exception e) {
            logger.error(userId + "上传头像错误" + e.getMessage());
            return userId + "上传头像错误" + e.getMessage();
        }
    }


    /**
     * 检查是否上传头像 通过contentType判断是否存在 404也是没头像（已废弃）
     * @param officeId 员工的officeId 本地库没有 调接口去查询
     * @param userId 用于记录日志
     * @return
     */
    public boolean isExistPhotoByUserId(String userId, String officeId) {
        String endPoint = "/users/" + officeId + "/photo/$value";

        HttpEntity httpEntity = new HttpEntity(null,getAuthHeader());

        try {
            ResponseEntity data = restTemplate.exchange(officeGraphUrl + endPoint, HttpMethod.GET,httpEntity,String.class);
            if (data != null && data.getStatusCode().equals(HttpStatus.OK) && data.getHeaders().getContentType() != null) {
                logger.info(userId + "已有officee365头像");
                return true;
            } else {
                return false;
            }
        } catch (HttpClientErrorException e1) {
            if (e1.getRawStatusCode() == 404) {
                return false;
            } else {
                logger.error(userId + "上传office365头像错误" + e1.getMessage());
                return true;
            }
        }
    }


    /**
     * 添加用户group（已废弃）
     * @param userGraphId
     * @param groupGraphId
     * @return
     */
    public boolean addUserGroup(String userGraphId, String groupGraphId) {
        String endPoint = "/groups/" + groupGraphId + "/members/$ref";

        HttpHeaders headers=getAuthHeader();
        JSONObject data = new JSONObject();
        data.put("@odata.id", "https://microsoftgraph.chinacloudapi.cn/v1.0/directoryObjects/" + userGraphId);

        HttpEntity<String> httpEntity=new HttpEntity<>(data.toJSONString(),headers);
        JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(officeGraphUrl + endPoint,
                restTemplate.exchange(officeGraphUrl + endPoint, HttpMethod.POST, httpEntity, String.class).getBody());

        if (resultData.getString("status_code").equals("204")) {
            return true;
        } else {
            String message = resultData.getJSONObject("error").getString("message");
            if (message.indexOf("already exist") != -1) {
                return true;
            } else {
                logger.info(message);
                return false;
            }
        }
    }

    /**
     * 获取在office365的所有group 返回array（已废弃）
     * @return
     * @throws UnsupportedEncodingException
     */
    public JSONArray getGroupArrayFromOffice() throws UnsupportedEncodingException {
        JSONArray result = new JSONArray();
        String endPoint = "/groups";

        HttpHeaders headers = getAuthHeader();
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        String url = officeGraphUrl + endPoint;

        while (true) {
            JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(url,
                    restTemplate.exchange(url, HttpMethod.GET,httpEntity,String.class).getBody());

            JSONArray value = (JSONArray) resultData.get("value");
            result.addAll(value);

            String nextUrl = null;
            nextUrl = resultData.getString("@odata.nextLink");
            if (nextUrl != null) {
                url = URLDecoder.decode(nextUrl,"ISO-8859-1");
            } else if (nextUrl == null) {
                break;
            }
        }

        return result;

    }


    /**
     * 获取在office365的所有group 返回map (替换阿里mail)
     * @return
     * @throws UnsupportedEncodingException
     */
    public Map<String, JSONObject> getGroupFromOffice() throws Exception {
        String endPoint = "/groups";

        HttpHeaders headers = getAuthHeader();
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);

        String url = officeGraphUrl + endPoint;
        Map<String, JSONObject> values = new HashMap<>();
        while (true) {
            JSONObject resultData = RequestCorvertUtil.getJSONObjectFromStr(url,
                    restTemplate.exchange(url, HttpMethod.GET,httpEntity,String.class).getBody());

            JSONArray value = (JSONArray) resultData.get("value");
            for (Object o : value) {
                JSONObject group = (JSONObject) o;
                String[] mail = group.getString("mail").split("@");
                values.put(mail[0].toLowerCase(), group);
            }

            String nextUrl = null;
            nextUrl = resultData.getString("@odata.nextLink");
            if (nextUrl != null) {
                url = URLDecoder.decode(nextUrl,"ISO-8859-1");
            } else if (nextUrl == null) {
                break;
            }
        }
        return values;
    }




    /**
     *
     * 给email授权（已废弃）
     * @param email
     * @return
     */
    public boolean assignAccount(String email){
        try{
            JSONObject data=new JSONObject();
            JSONArray addLicenses=new JSONArray();
            JSONObject addLicense=new JSONObject();
            addLicense.put("disabledPlans",new JSONArray());
            addLicense.put("skuId",officeLicense);
            addLicenses.add(addLicense);
            data.put("addLicenses",addLicenses);
            data.put("removeLicenses",new JSONArray());

            ResponseEntity<JSONObject> responseEntity=restTemplate.postForEntity(officeGraphUrl+"/users/"+email+"/assignLicense",new HttpEntity<String>( data.toJSONString(),getAuthHeader()),JSONObject.class);

            if (responseEntity.getStatusCodeValue()==200)
                return true;
            else
                return false;
        }catch (Exception e){
            logger.error("assignAccount失败",e);
            return false;
        }

    }
    /**
     *
     * 修改邮箱状态（已废弃）
     * @param email
     * @param enable
     * @return
     */
    public boolean modifyAccountEnabled(String email,boolean enable){
        try{
            HttpHeaders headers=getAuthHeader();
            JSONObject data=new JSONObject();
            data.put("accountEnabled",enable);
            HttpEntity<String> httpEntity=new HttpEntity<>(data.toJSONString(),headers);
            restTemplate.patchForObject(officeGraphUrl+"/users/"+email,httpEntity,String.class);
            return true;
        }catch (Exception e){
            logger.error("修改accountEnabled出错",e);
            return false;
        }
    }

    /**
     *
     * 修改location信息，同时修改邮箱后缀（已废弃）
     * @param email
     * @param location
     * @return
     */
    public boolean modifyLocation(String email,String location, String newEmail){

        try{
            HttpHeaders headers=getAuthHeader();
            JSONObject data=new JSONObject();
            data.put("usageLocation",location);
            data.put("userPrincipalName",newEmail);
            HttpEntity<String> httpEntity=new HttpEntity<>(data.toJSONString(),headers);
            restTemplate.patchForObject(officeGraphUrl+"/users/"+email,httpEntity,String.class);
            return true;
        }catch (Exception e){
            logger.error("修改location出错",e);
            return false;
        }

    }

    public JSONObject getUserOfficeInfo(String email) throws Exception{
        try{
            HttpEntity httpEntity=new HttpEntity(null,getAuthHeader());
            JSONObject resultData=RequestCorvertUtil.getJSONObjectFromStr(officeGraphUrl+"/users/"+email,restTemplate.exchange(officeGraphUrl+"/users/"+email, HttpMethod.GET,httpEntity,String.class).getBody());
            return resultData;
        }catch (HttpClientErrorException e){
            logger.warn("获取user信息错误", e);
            return null;
        }
    }

    public JSONObject getUserOfficeInfoBySelect(String email, String selectStr) throws HttpClientErrorException {
        HttpEntity httpEntity=new HttpEntity(null,getAuthHeader());
        JSONObject resultData=RequestCorvertUtil.getJSONObjectFromStr(officeGraphUrl+"/users/"+email + selectStr,restTemplate.exchange(officeGraphUrl+"/users/"+email + selectStr, HttpMethod.GET,httpEntity,String.class).getBody());
        return resultData;
    }



    /**
     * 优先从redis中获取
     * @return  带Authorization认证的请求头
     */
    public HttpHeaders getAuthHeader(){
        Object token=redisTemplate.opsForValue().get("mitexecutor_office365_token");
        if (token==null){
            token =getToken();
            redisTemplate.opsForValue().set("mitexecutor_office365_token",token);
            redisTemplate.expire("mitexecutor_office365_token",1800, TimeUnit.SECONDS);
        }
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Authorization",(String)token);

        return headers;
    }

    /**
     * @return  从office365获取token
     */
    private String getToken(){
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap data=new LinkedMultiValueMap();
        data.add("client_id",officeClientId);
        data.add("client_secret",officeClientSecret);
        data.add("grant_type",officeGrantType);
        data.add("scope",officeScope);
        data.add("resource",officeResource);

        HttpEntity<MultiValueMap<String, String>> httpEntity=new HttpEntity<>(data,headers);
        ResponseEntity<JSONObject> responseEntity= restTemplate.postForEntity(officeTokenUrl,httpEntity,JSONObject.class);
        return responseEntity.getBody().getString("token_type")+" "+responseEntity.getBody().getString("access_token");
    }
}
