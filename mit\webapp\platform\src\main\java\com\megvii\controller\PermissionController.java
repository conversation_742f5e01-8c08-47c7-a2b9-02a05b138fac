package com.megvii.controller;

import com.megvii.controller.api.*;
import com.megvii.controller.resource.api.ResponseDataEntity;
import com.megvii.entity.opdb.OperateTypeEnum;
import com.megvii.entity.opdb.Role;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserRole;
import com.megvii.service.UserMenuService;
import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/11 10:54
 */
@RestController
@RequestMapping("/api/permission")
public class PermissionController {

	@Autowired
	private UserMenuService userMenuService;

	@GetMapping("/user/role")
	@ResponseBody
	@RequiresPermissions("PERMISSION")
	public ResponseDataEntity getUserRole(@RequestParam("userId") String userId) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			List<UserRole> userRoles = userMenuService.getRoleByUserId(userId);

			response.setCode(ResponseDataEntity.OK);
			response.setData(UserRoleVO.toVO(userRoles));
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("failure");
		}

		return response;
	}


	@GetMapping("/alluser")
	@ResponseBody
	@RequiresPermissions("PERMISSION")
	public ResponseDataEntity getAllUser() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			List<User> users = userMenuService.getAllUser();
			response.setCode(ResponseDataEntity.OK);
			response.setData(UserVO.toVOs(users));
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("failure");
		}

		return response;
	}


	@PostMapping("/user/role")
	@ResponseBody
	@OperateLog(operType= OperateTypeEnum.ADD)
	@RequiresPermissions("PERMISSION")
	public ResponseDataEntity addUserRole(@RequestBody UserRoleRequest roleRequest) {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			userMenuService.addUserRole(roleRequest.getUserId(), roleRequest.getRoleCodes());
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setMsg("failure");
		}

		return response;
	}

	@GetMapping("/role")
	@ResponseBody
	@RequiresPermissions("PERMISSION")
	public ResponseDataEntity getAllUserRole() {
		ResponseDataEntity response = new ResponseDataEntity();
		try {
			List<Role> roles = userMenuService.getAllRole();

			response.setData(RoleVO.toVOs(roles));
			response.setCode(ResponseDataEntity.OK);
			response.setMsg("success");
		} catch (Exception e) {
			response.setCode(ResponseDataEntity.ERROR);
			response.setData(null);
			response.setMsg("failure");
		}
		return response;
	}


}
