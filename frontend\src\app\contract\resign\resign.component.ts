import { Component, OnInit, ViewChild } from '@angular/core'
import { ResignTableComponent } from './resign-table/resign-table.component'
import { ParamStoreService } from '../../core/services/paramStore.service'
import { SaveKey } from '../../core/services/saveTabKey.service'
@Component({
  selector: 'app-resign',
  templateUrl: './resign.component.html',
  styleUrls: ['./resign.component.less']
})
export class ResignComponent implements OnInit {
  isShowSelf = true
  selectedIndex = 0
  @ViewChild('noHandle') noHandle: ResignTableComponent
  @ViewChild('noSign') noSign: ResignTableComponent
  @ViewChild('noStamp') noStamp: ResignTableComponent
  @ViewChild('noEmail') noEmail: ResignTableComponent

  constructor(private paramStoreService: ParamStoreService, private saveKey: SaveKey) {
    this.saveKey.setMap('selectKey', 0)
  }

  ngOnInit(): void {}
  ngDoCheck() {
    if (this.selectedIndex !== this.saveKey.getMap('selectKey')) {
      this.selectedIndex = this.saveKey.getMap('selectKey')
      if (this.selectedIndex === 0) {
        this.noHandle.getDimissionUserLists()
      } else if (this.selectedIndex === 1) {
        this.noSign.getDimissionUserLists()
      } else if (this.selectedIndex === 2) {
        this.noStamp.getDimissionUserLists()
      } else {
        this.noEmail.getDimissionUserLists()
      }
    }
  }
  onActivate(event) {
    this.isShowSelf = false
  }
  onDeactivate(event) {
    this.isShowSelf = true
  }

  selectedIndexChange(event) {
    this.selectedIndex = event
    this.saveKey.setMap('selectKey', event)
  }
}
