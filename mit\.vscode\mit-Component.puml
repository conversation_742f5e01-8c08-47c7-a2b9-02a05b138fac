@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5
!include DEVICONS/angular.puml
!include DEVICONS/java.puml
!include DEVICONS/msql_server.puml
!include FONTAWESOME/users.puml

LAYOUT_AS_SKETCH()

actor userA as "新员工" <<新员工入职前>>
actor userB as "新员工" <<新员工入职后>>

System_Boundary(mit, "mit"){
    [mit_定时任务] as executor
    [mit_webapi] as webapi
    [mit_电子签章] as contractplatform
    [service] as service
    [dao] as dao


}

System_Ext(staffentry, "staffentry"){
    [local_nginx]
    [frontend] as frontend
    [backend] as backend
}

System_Ext(python_mit, "Python 老mit")


ContainerDb(DB_opdb, "DB_OPDB", "my-m.op.megvii-inc.com:3306/OPDB")
ContainerDb(DB_luna, "DB_Luna", "my-m.op.megvii-inc.com:3306/luna")
ContainerDb(DB_dirac, "DB_dirac", "my-m.op.megvii-inc.com:3306/dirac")
ContainerDb(DB_staffentry, "DB_staffentry", "db.staffentry.megvii-inc.com:3306/staffentry")
ContainerDb(DB_redis, "DB_redis", "10.201.109.8")

[xxl_job]



userA --> [local_nginx]
userB --> [contractplatform]
[xxl_job] --> [executor]
[local_nginx] --> [frontend]
[executor] --> [service]
[webapi] --> [service]
[contractplatform] --> [service]
[service] --> [dao]
[dao] --> [DB_opdb]
[dao] --> [DB_luna]
[dao] --> [DB_dirac]
[dao] --> [DB_staffentry]
[dao] --> [DB_redis]
[frontend] --> [backend]
[backend] --> [DB_staffentry]
[backend] --> [DB_redis]
[python_mit] --> [DB_opdb]
[python_mit] --> [DB_luna]
[python_mit] --> [DB_dirac]
[python_mit] --> [DB_staffentry]
[python_mit] --> [DB_redis]


@enduml
