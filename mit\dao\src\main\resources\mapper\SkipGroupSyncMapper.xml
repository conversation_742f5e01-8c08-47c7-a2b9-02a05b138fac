<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.SkipGroupSyncMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.SkipGroupSync">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
    </resultMap>

    <select id="selectSkipSyncGroup" resultType="java.lang.String">
		SELECT `name` FROM skip_group_sync
	</select>

</mapper>
