package com.megvii.service;

import com.megvii.common.DateTimeUtil;
import com.megvii.common.MailUtil;
import com.megvii.entity.opdb.MMISMailTemplate;
import com.megvii.entity.opdb.MailTemplateInfo;
import com.megvii.entity.opdb.TempUser;
import com.megvii.entity.opdb.User;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.mail.MessagingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

/**
 * 邮件发送，异步
 */
@Service
public class MailSendService {

    private Logger logger= LoggerFactory.getLogger(MailSendService.class);

    @Autowired
    private MMISMailTemplateService mmisMailTemplateService;

    @Autowired
    private TemplateEngine templateEngine;


    /**
     * 创建ad账号邮件
     * @param users
     */
    @Async
    public void sendAdMailMsg(List<User> users) {
        int errorCount = 0;
        int successCount = 0;

        List<User> sendUserList = new ArrayList<>();
        for (User user : users) {
             if (user.getLdapFlag() == 1) {
                successCount++;
                sendUserList.add(user);
            } else if (user.getLdapFlag() == 0) {
                errorCount++;
                sendUserList.add(user);
            } else if (user.getLdapFlag() == 2) {

            }
        }

        try {
            if (successCount > 0 || errorCount > 0) {
                Context context = new Context();
                context.setVariable("result", sendUserList);
                String text = templateEngine.process(MailTemplateInfo.MIT_ADD_AD_USER_TEMPLATE, context);
                MailUtil.sendHtmlEmail(MailTemplateInfo.MIT_ADD_AD_USER_TITLE + "[成功" + successCount + "个][失败" + errorCount + "个]", text, "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
            }
        } catch (MessagingException e) {
            logger.error("MIT创建ldap账号邮件异常" + e.getMessage());
        }
    }



    /**
     *
     * @param subject  邮件主题
     * @param content  邮件内容
     * @param to       收件人地址，多个地址以逗号隔开
     */
    @Async("asyncMailExecutor")
    public void sendMail(String subject,String content,String to){

        try{
            logger.info("邮件提醒--->subject："+subject+",to:"+to);
            MailUtil.sendHtmlEmail(subject,content,to);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    @Async("asyncMailExecutor")
    public void sendAttachmentEmail(String subject,String content,String to,String file){
        try{
            logger.info("邮件通知--->subject："+subject+",to:"+to);
            MailUtil.sendAttachmentEmail(subject,content,to,file);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    public void sendImgEmail(String subject,String content,String to,String file){
        try{
            logger.info("入职邮件通知--->subject："+subject+",to:"+to);
            MailUtil.sendImgEmail(subject,content,to,file);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 入职日期顺延提醒邮件
     * @param user
     */
    @Async
    public void sendDelayEntryMail(User user) {
        if (user.getEmployType().indexOf("实习生") != -1) {
            String mailConntent = "因OA流程处理完成时间超过原定入职时间，现将员工入职时间自动更改为下一个入职日，" + DateTimeUtil.date2String(user.getExpectDate()) + "，如有疑问请联系ER同学。";
            sendMail("员工入职日期顺延提醒" + user.getUserId(), mailConntent, user.getHrbp() + "@megvii.com");
        } else {
            String mailConntent = "因OA流程处理完成时间超过原定入职时间，且月底最后一周不办理正式员工入职，现将员工入职时间自动更改为次月第一个入职日，" + DateTimeUtil.date2String(user.getExpectDate()) + "，如有疑问请联系ER同学。";
            sendMail("员工入职日期顺延提醒" + user.getUserId(), mailConntent, user.getHrbp() + "@megvii.com");
        }
    }

    /**
     * OA回传提醒邮件
     * @param user
     * @param recvs
     */
    @Async
    public void sendMitAddUserMail(User user, String recvs) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", user.getUserId());
        params.put("updater", user.getUpdater());
        params.put("employType", user.getEmployType());
        params.put("spell", user.getSpell());
//        params.put("cell", user.getCell());
        params.put("employBase", user.getEmployBase());
        params.put("workBase", user.getWorkBase());
        params.put("team", user.getTeam());
        params.put("mentor", user.getMentor());
        params.put("buddy", user.getBuddy());
        params.put("seatNumber", user.getSeatNumber()!=null?user.getSeatNumber():"");
        params.put("computer", user.getComputer()!=null?user.getComputer():"");
        params.put("os", user.getOs()!=null?user.getOs():"");
        params.put("expectDate", user.getExpectDate());
        params.put("memo", user.getMemo()!=null?user.getMemo():"");

        Context context = new Context();
        context.setVariables(params);
        String text =  templateEngine.process(MailTemplateInfo.MIT_ADD_USER_TEMPLATE, context);

        try {
            MailUtil.sendHtmlEmail(MailTemplateInfo.MIT_ADD_USER_TITLE + "[" + user.getUserId() + "][" + user.getSpell() + "][" + user.getEmployBase() + "]", text, recvs);
            logger.info("MIT新增员工邮件提醒--->to:" + recvs);
        } catch (MessagingException e) {
            logger.error("MIT新增员工提醒邮件异常" + e.getMessage());
        }
    }


    /**
     * OA回传补全信息邮件
     * @param user
     * @param limitDate
     */
    @Async
    public void sendEntryInfoMail(User user, LocalDateTime limitDate) {
        String date = DateTimeUtil.date2String4(limitDate);

        try {
            logger.info("补全信息邮件提醒--->");

            Map<String, Object> params = new HashMap<>();
            params.put("spell", user.getSpell());
            params.put("date", date);
            params.put("userId", user.getUserId());
            params.put("password", user.getOriPassword());

            Context context = new Context();
            context.setVariables(params);
            String content =  templateEngine.process(MailTemplateInfo.USER_ADD_INFO_TEMPLATE, context);

            MailUtil.sendHtmlEmail(MailTemplateInfo.USER_ADD_INFO_TITLE, content, user.getHrbp() + "@megvii.com," + user.getPriEmail());
        } catch (Exception e) {
            logger.error("信息收集邮件发送异常:" + e.getMessage());
        }
    }

    /**
     * 发送明日入职提醒邮件
     * 1人和多人使用相同模板
     * @param users
     * @param recvs
     * @return
     * @throws Exception
     */
    public String sendTomorrowEntryMail(List<User> users, String recvs, String tips) {
        if (users == null || users.size() == 0) {
            return "没有要提醒的用户";
        }

        Context context = new Context();
        context.setVariable("result", users);
        if (tips != null) {
            context.setVariable("tips", tips);
        }
        String text =  templateEngine.process(MailTemplateInfo.USER_TOMORROW_ENTRY_TEMPLATE, context);

        try {
            MailUtil.sendHtmlEmail(MailTemplateInfo.USER_TOMORROW_ENTRY_TITLE + "[共" + users.size() + "人入职]", text, recvs);
            logger.info("明日入职提醒邮件收件人:" + recvs);
            return "明日入职提醒邮件发送成功" ;
        } catch (Exception e) {
            logger.error("明日入职提醒邮件发送异常:" + e.getMessage());
            return "明日入职提醒邮件发送异常";
        }

    }


    /**
     * 今日入职欢迎邮件
     * @param user
     * @param recvs
     * @return
     */
    public String sendTodayWelcomeMail(User user, String recvs, String mentor, String buddy, String hrg) {

        MMISMailTemplate template = mmisMailTemplateService.getMMISMailTemplateByType("welcome");
        if (template == null) {
            logger.error(user.getUserId() + "查不到邮件模板");
            return user.getUserId() + "查不到邮件模板";
        }

        StringBuilder body = new StringBuilder();
        body.append("<!DOCTYPE html><html><head></head><body><b>Hi，<span style=\"font-size:20px\">" + user.getSpell() + ":</span></b>");
        body.append(template.getContent().split("<<<<<<<<<<>>>>>>>>>>")[0]);
        body.append("<p>有几位<strong><span style=\"font-size:20px\">重要同学</span></strong>要尽快认识一下：<br><br>\n" +
                "                       1. <strong>你的leader：<span style=\"font-size:15px\">" + mentor + "</span></strong><br><br>\n" +
                "                       在Megvii直接对你负责的伙伴，就是这个家伙从茫茫人海选中了你。<br><br>\n" +
                "                       2. <strong>你的buddy：<span style=\"font-size:15px\">" + buddy + "</span></strong><br><br>\n" +
                "                       我们为每一位新人小伙伴指定一位“老司机”，负责帮助新人融入及克服陌生环境的一切困难。有问题，找Buddy！<br>\n" +
                "                       （当然了，加入创业团队之前，相信你已经做好了要自力更生的心理准备 ：））<br><br>\n" +
                "                       3. <strong>你的HRG：<span style=\"font-size:15px\">" + hrg + "</span></strong><br><br>\n" +
                "                       他/她将会是你强有力的后备支持，关于人力资源的任何问题都可以第一时间找他/她！<br><br>\n" +
                "                       4. <strong>ER和IT的小伙伴们也是日后支持你工作的小帮手哦，详细分工见以下入职简介或访问wiki.</strong><br>\n" +
                "                       在职过程中如遇任何问题，记得主动寻求帮助。<br>\n" +
                "                       =======================================================</p>");
        body.append(template.getContent().split("<<<<<<<<<<>>>>>>>>>>")[1]);
        body.append("</body></html>");

        try {
            MailUtil.sendHtmlEmail("Day One Orientation", body.toString(), recvs);
            logger.info("今日入职提醒邮件收件人:" + recvs);
        } catch (MessagingException e) {
            logger.error(user.getUserId() + "今日入职发送失败");
            return user.getUserId() + "今日入职发送失败";
        }

        return user.getUserId() + "发送成功";

    }


    /**
     * 下周入职提醒邮件
     * @param users
     * @param recvs
     * @return
     */
    public String sendNextWeekEntryMail(List<User> users, String recvs) {

        Context context = new Context();
        context.setVariable("result", users);
        String text =  templateEngine.process(MailTemplateInfo.USER_NEXT_WEEK_ENTRY_TEMPLATE, context);

        try {
            MailUtil.sendHtmlEmail(MailTemplateInfo.USER_NEXT_WEEK_ENTRY_TITLE + "[下周共 " + users.size() +  " 人预计入职]", text, recvs);
        } catch (MessagingException e) {
            logger.error("下周入职提醒发送失败");
            return "下周入职提醒发送失败";
        }

        return "下周入职提醒发送成功";
    }


    /**
     * 未完成信息收集员工提醒
     * @param users
     * @param recv
     * @return
     */
    public String sendNotInfoMail(List<User> users, String recv) {

        Context context = new Context();
        context.setVariable("result", users);
        String text =  templateEngine.process(MailTemplateInfo.USER_NOT_INFO_TEMPLATE, context);

        try {
            MailUtil.sendHtmlEmail(MailTemplateInfo.USER_NOT_INFO_TITLE + "[" + users.size() + "人待处理]", text, recv);
        } catch (MessagingException e) {
            logger.error("信息收集提醒邮件发送失败");
            return "发送失败";
        }

        return "发送成功";
    }


    /**
     * 入职首周邮件
     * @param user
     * @param recvs
     * @return
     */
    public String sendOneweekMail(User user, String recvs) {
        Map<String, Object> params = new HashMap<>();
        params.put("spell", user.getSpell());
        params.put("mentor", user.getMentor());

        Context context = new Context();
        context.setVariables(params);
        String text =  templateEngine.process(MailTemplateInfo.USER_ONE_WEEK_TEMPLATE, context);

        try {
            MailUtil.sendHtmlEmail(MailTemplateInfo.USER_ONE_WEEK_TITLE, text, recvs);
        } catch (MessagingException e) {
            logger.error(e.getMessage());
            return user.getUserId() + "发送失败";
        }

        return user.getUserId() + "发送成功";
    }


    /**
     * 外包明日入职提醒
     * @param list
     * @param recv
     * @return
     */
    @Async
    public void sendTempEntryMail(List<TempUser> list, String recv) {

        Context context = new Context();
        context.setVariable("result", list);
        String text =  templateEngine.process(MailTemplateInfo.TEMP_USER_ENTRY_TEMPLATE, context);
        try {
            MailUtil.sendHtmlEmail(MailTemplateInfo.TEMP_USER_ENTRY_TITLE, text, recv);
       } catch (MessagingException e) {
            logger.error("外包员工入职提醒发送失败:" + e.getMessage());
        }

    }


    /**
     * 未完成OA流程人员提醒
     * @param users
     * @return
     */
    public String sendNotOaMail(List<User> users, String recv) {

        Context context = new Context();
        context.setVariable("result", users);
        String text =  templateEngine.process(MailTemplateInfo.USER_NOT_OA_TEMPLATE, context);

        try {
            MailUtil.sendHtmlEmail( MailTemplateInfo.USER_NOT_OA_TITLE + "[" + users.size() + "人待处理]", text, recv);
        } catch (MessagingException e) {
            logger.error("未完成OA流程提醒邮件发送失败");
            return "发送失败";
        }

        return "发送成功";

    }

    /**
     * 发送账号密码通知邮件到员工私人邮箱
     * @param user 用户对象
     * @return 发送结果信息
     */
    public String sendPasswordNotificationMail(User user) {
        try {
            // 检查私人邮箱是否存在
            if (user.getPriEmail() == null || user.getPriEmail().trim().isEmpty()) {
                return user.getUserId() + "私人邮箱地址为空，跳过邮件发送";
            }

            // 邮件标题和内容
            String emailSubject = "公司单点登录系统账号密码同步";
            String emailContent = "您的内部账户用户名为：" + user.getUserId() + "，初始密码为：" + user.getOriPassword() +
                    "，请妥善保管密码，并尽快修改。修改方式及更多操作请查看电脑桌面上的《新人手册》-办公指引篇。";

            // 发送邮件
            sendMail(emailSubject, emailContent, user.getPriEmail());
            
            logger.info("账号密码通知邮件发送成功--->" + "userId:" + user.getUserId() + ",to:" + user.getPriEmail());
            return user.getUserId() + "私人邮箱通知发送成功";
            
        } catch (Exception e) {
            logger.error("发送私人邮箱通知失败: " + user.getUserId() + ", email: " + user.getPriEmail() + ", error: " + e.getMessage());
            return user.getUserId() + "私人邮箱通知发送失败";
        }
    }

    /**
     * 发送入职相关信息通知邮件到员工私人邮箱
     * @param user 用户对象
     * @return 发送结果信息
     */
    public String sendWelcomeNotificationMail(User user) {
        try {
            // 检查私人邮箱是否存在
            if (user.getPriEmail() == null || user.getPriEmail().trim().isEmpty()) {
                return user.getUserId() + "私人邮箱地址为空，跳过邮件发送";
            }

            // 邮件标题
            String emailSubject = "入职相关信息通知";
            String emailContent = generateWelcomeEmailContent(user);

            // 如果内容为空（不发送欢迎信息的情况）
            if (emailContent == null) {
                return user.getUserId() + "不发送欢迎信息";
            }

            // 发送邮件
            sendMail(emailSubject, emailContent, user.getPriEmail());
            
            logger.info("入职相关信息通知邮件发送成功--->" + "userId:" + user.getUserId() + ",to:" + user.getPriEmail());
            return user.getUserId() + "私人邮箱通知发送成功";
            
        } catch (Exception e) {
            logger.error("发送入职相关信息通知邮件失败: " + user.getUserId() + ", email: " + user.getPriEmail() + ", error: " + e.getMessage());
            return user.getUserId() + "私人邮箱通知发送失败";
        }
    }

    /**
     * 根据用户信息生成欢迎邮件内容
     * @param user 用户对象
     * @return 邮件内容，如果不发送则返回null
     */
    private String generateWelcomeEmailContent(User user) {
        String workBase = user.getWorkBase();
        String content;

        // 根据createTag判断使用哪种短信模板的逻辑
        if (workBase.equals("大恒")) {
            content = "新同事你好：欢迎加入，请按照offer中的要求准备好入职材料，明天上午8点50分到苏州街3号大恒科技大厦南座16层前台，我们会在那里等候并为您办理入职手续。";
        } else if (workBase.equals("海龙")) {
            content = "新同事你好：欢迎加入，请按照offer中的要求准备好入职材料，明天上午8点50分到海龙大厦H座17层前台，我们会在那里等候并为您办理入职手续。";
        } else if (workBase.equals("金隅")) {
            content = "新同事你好：欢迎加入，请按照offer中的要求准备好入职材料，明天上午8点50分到金隅智造S1-1层前台，我们会在那里等候并为您办理入职手续。地址：北京市海淀区建材城中路27号-金隅智造S1-1层。";
        } else {
            return null; // 不发送欢迎信息
        }

        return content;
    }

    /**
     * 发送入职信息填报未完成提醒邮件到员工私人邮箱
     * @param user 用户对象
     * @param password 密码
     * @return 发送结果信息
     */
    public String sendUserInfoNotificationMail(User user, String password) {
        try {
            // 检查私人邮箱是否存在
            if (user.getPriEmail() == null || user.getPriEmail().trim().isEmpty()) {
                return user.getUserId() + "私人邮箱地址为空，跳过邮件发送";
            }

            // 邮件标题
            String emailSubject = "入职信息填报未完成提醒";
            String emailContent = generateUserInfoEmailContent(user, password);

            // 发送邮件
            sendMail(emailSubject, emailContent, user.getPriEmail());
            
            logger.info("入职信息填报未完成提醒邮件发送成功--->" + "userId:" + user.getUserId() + ",to:" + user.getPriEmail());
            return user.getUserId() + "私人邮箱通知发送成功";
            
        } catch (Exception e) {
            logger.error("发送入职信息填报未完成提醒邮件失败: " + user.getUserId() + ", email: " + user.getPriEmail() + ", error: " + e.getMessage());
            return user.getUserId() + "私人邮箱通知发送失败";
        }
    }

    /**
     * 根据用户信息生成入职信息填报提醒邮件内容
     * @param user 用户对象
     * @param password 密码
     * @return 邮件内容
     */
    private String generateUserInfoEmailContent(User user, String password) {
        // 根据createTag判断使用哪种短信模板的逻辑
        if (user.getCreateTag() != null && (user.getCreateTag() == 5 || user.getCreateTag() == 8 || user.getCreateTag() == 7|| user.getCreateTag() == 9)) {
            // McSMSRestClient.sendUncompletedNotice 的逻辑 - 使用模板格式
            return "亲爱的" + user.getSpell() + "，欢迎加入公司！在入职前，我们需要您在入职系统里补全您的个人信息：\n网址：https://onboard-epif.ytuuu.com\n账号："
                    + user.getUserId() + "\n密码：" + password + "\n您也可以通过查收邮件快速访问。如未填写将影响入职办理，请尽快填写。";
            
        } else if (user.getCreateTag() != null && user.getCreateTag() == 6) {
            // CqSMSRestClient.sendUncompletedNotice 的逻辑 - 使用模板格式
            return "亲爱的" + user.getSpell() + "，欢迎加入公司！在入职前，我们需要您在入职系统里补全您的个人信息：\n网址：https://onboard-epif.ytuuu.com\n账号："
                    + user.getUserId() + "\n密码：" + password + "\n您也可以通过查收邮件快速访问。如未填写将影响入职办理，请尽快填写。";
            
        } else {
            // SMSService.sendUserInfoRemindSms 的逻辑
            return "亲爱的" + user.getSpell() + "，欢迎加入公司！在入职前，我们需要您在入职系统里补全您的个人信息：\n网址：https://onboard-epif.megvii-inc.com\n账号："
                    + user.getUserId() + "\n密码：" + password + "\n您也可以通过查收邮件快速访问。如未填写将影响入职办理，请尽快填写。";
        }
    }

}
