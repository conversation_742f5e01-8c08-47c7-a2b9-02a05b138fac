import { Component, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ResignService } from '../resign.service'
import { MessageService } from '../../../core/services'
import { MapService } from '../../../core/services/map.service'
import { NzModalService } from 'ng-zorro-antd/modal'
import { SaveKey } from '../../../core/services/saveTabKey.service'
@Component({
  selector: 'app-resign-user',
  templateUrl: './resign-user.component.html',
  styleUrls: ['./resign-user.component.less']
})
export class ResignUserComponent implements OnInit {
  constructor(
    private router: Router,
    private resignService: ResignService,
    private messageService: MessageService,
    private mapService: MapService,
    private modalService: NzModalService,
    private saveKey: SaveKey
  ) {}

  backurl = ''
  nzSelectedIndex = '0'
  isBasicLoading = false // 人员基本信息加载中
  isBasicTwoLoading = false
  isBasicInfoLoading = false
  // 人员基本信息字段
  spell = '' // 姓名
  workNumber = '' //工号
  id = '' //id
  dismissionDate = '' //离职日期
  employType = '' //入职类型
  position = '' //岗位名称
  company = '' //签约公司主体
  mentor = '' //上级主管
  team = '' //部门
  hrg = '' //hrg
  address = '' //联系地址
  certType = '' //证件编号
  certNo = '' //身份证号
  cell = '' //电话
  selfEmail = '' //邮箱
  competition = 0 //竞业协议
  memo = '' //备注
  //身份证类型列表
  certTypeList = []

  specialCompany // 是否含有特定公司
  specialCompanyTxt = '' //特定公司名称
  // 股份处理方式
  stockManage
  sersSignDate = '' //SERSE协议签署日期
  stockNumber = 0 //笔数选择
  extendVOS = []
  waitTime = '' //等待期
  sersKeepNumber //SERS保留总数
  sersInvalidNumber //sersInvalidNumber
  num1 = ''
  // effectDate1 = ''
  // url里面的userId和dismissionId
  userId = ''
  dismissionId = ''
  // 岗位名称列表
  positionList = []
  // 签约公司列表
  companyOfOption: Array<{ value: string; text: string }> = []
  // 是否显示待签署合同详情
  isShowDetail = false
  // 判断是否第一次点击签署
  isTheFirstTime = true
  // 是否有补偿
  compensation = 0
  // 股份
  shares = 0
  loading = false
  shareAndStock = 0
  isOpen = false
  lzbc = ''
  remarks = ''
  ngOnInit(): void {
    const urlArr = this.router.url.toLowerCase().split('/user')
    this.backurl = urlArr ? urlArr[0] : ''
    this.getCertType()
    this.getSearchPeople()
    this.getPositonList()
    this.getEntryCompanyList()
    this.getInfoMoney()
  }
  typeSelChange(event) {
    const { index } = event
    this.nzSelectedIndex = index
  }
  translateObjToArr(obj) {
    const arr = []
    Object.keys(obj).forEach((key) => arr.push({ value: key, label: obj[key] }))
    return arr
  }
  // 获取岗位名称列表
  getPositonList() {
    this.mapService.getMap('position').then(
      (data) => {
        this.positionList = this.translateObjToArr(data as any) || []
      },
      (error) => {
        this.messageService.error(error)
      }
    )
  }
  // 获取签约公司列表
  getEntryCompanyList() {
    this.mapService.getMap('company').then(
      (data) => (this.companyOfOption = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }
  getSearchPeople() {
    const urls = this.router.url.toLowerCase().split('/user')[1]
    const ids = urls.substring(2, urls.length).split('&')
    const userid = ids[0].split('=')[1]
    this.userId = userid
    const dismissionid = ids[1].split('=')[1]
    this.dismissionId = dismissionid
    let params = {
      userId: userid,
      dismissionId: dismissionid
    }
    this.resignService.getDismissionUserInfo(params).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.spell = data['spell'] // 姓名
        this.workNumber = data['workNumber'] //工号
        this.id = data['id'] //id
        this.userId = data['userId']
        this.dismissionDate = data['dismissionDate'] //离职日期
        this.employType = data['employType'] //入职类型
        this.position = data['position'] //岗位名称
        this.company = data['company'] //签约公司主体
        this.memo = data['memo'] //备注
        this.mentor = data['mentor'] //上级主管
        this.team = data['team'] //部门
        this.hrg = data['hrg'] //hrg
        this.address = data['address'] //联系地址
        this.certType = data['certType'] //证件编号
        this.certNo = data['certNo'] //身份证号
        this.cell = data['cell'] //电话
        this.selfEmail = data['selfEmail'] //邮箱
        this.competition = data['competition'] //竞业协议
        this.compensation = data['compensation'] //补偿
        this.shares = data['shares'] //股份
        this.shareAndStock = this.shares
      } else {
        this.messageService.error(msg)
      }
    })
  }
  // 修改离职人员基本信息
  editUserIn() {
    let params = {
      userId: this.userId,
      address: this.address,
      cell: this.cell,
      certNo: this.certNo,
      competition: this.competition,
      selfEmail: this.selfEmail,
      dismissionId: this.dismissionId
    }
    this.resignService.editUserInform(params).subscribe((res) => {
      const { code, msg } = res
      if (code === 0) {
        this.isShowDetail = true
        this.messageService.success('保存成功！')
        this.getUnSnapContract()
      } else {
        this.messageService.error(msg)
      }
    })
  }
  // 获取带签署合同中的信息
  getUnSnapContract() {
    let params = {
      dismissionId: this.dismissionId
    }
    this.resignService.getUserContract(params).subscribe((res) => {
      const { code, data, msg } = res
      if (code == 0) {
        const { snapshot, extendVOS } = data
        console.log('exxxxx', extendVOS)
        this.specialCompany = snapshot['specialCompany']
        if (this.specialCompany) {
          this.specialCompanyTxt = snapshot['specialCompanyTxt']
        }
        // debugger
        this.stockManage = snapshot['stockManage']
        this.shareAndStock = this.stockManage
        if (this.stockManage) {
          this.waitTime = snapshot['waitTime']
          this.stockNumber = snapshot['stockNumber']
          this.extendVOS = extendVOS
          this.sersKeepNumber = snapshot['sersKeepNumber']
        }
        this.sersSignDate = snapshot['sersSignDate']
        this.sersInvalidNumber = snapshot['sersInvalidNumber']
        // 判断是否有创建时间，如果没有创建时间，那就是第一次
        this.isTheFirstTime = data.snapshot.createTime === null ? true : false
      }
    })
  }
  // 获取身份证类型列表
  getCertType() {
    this.mapService.getMap('idType').then(
      (data) => (this.certTypeList = (data as any) || []),
      (error) => {
        this.messageService.error(error)
      }
    )
  }
  // 是否含有特定公司变化
  companyChange(value) {
    this.specialCompany = value == '0' ? 0 : 1
  }
  // 股份处理方式变化
  stockManageChange(value) {
    this.stockManage = value == '0' ? 0 : 1
    this.shareAndStock = this.stockManage
  }
  // 笔数改变
  stockNumberChange(value) {
    this.stockNumber = parseInt(value)
    this.extendVOS = []
    for (let i = 0; i < this.stockNumber; i++) {
      let nums = {
        num: i + 1,
        effectDate: '',
        sersNum: '',
        waitTime: '',
        userId: this.userId
      }
      this.extendVOS.push(nums)
    }
  }
  // 是否提交确认框
  isShowConfirm(): void {
    this.modalService.confirm({
      nzTitle: '<i>确认要签署本份合同吗?</i>',
      nzContent: '<b>点击签署后，将会发送给员工进行线上签署，数据将会进入“员工签署中”状态！</b>',
      nzOnOk: () => this.submitUserContract()
    })
  }
  formatDate(obj): string {
    let day = obj.getDate() >= 10 ? obj.getDate() : '0' + obj.getDate()
    let month = obj.getMonth() + 1 >= 10 ? obj.getMonth() + 1 : '0' + (obj.getMonth() + 1)
    let year = obj.getFullYear()
    return year + '-' + month + '-' + day
  }
  // 保存接口是创建离职合同快照
  saveNoContract() {
    let params = {
      snapshot: {
        dimissionId: this.dismissionId,
        userId: this.userId,
        competition: this.competition,
        specialCompany: this.specialCompany,
        specialCompanyTxt: this.specialCompanyTxt,
        company: this.company,
        stockManage: this.stockManage,
        sersSignDate: this.sersSignDate,
        stockNumber: this.stockNumber,
        sersKeepNumber: this.sersKeepNumber,
        sersInvalidNumber: this.sersInvalidNumber,
        waitTime: this.waitTime
      },
      extendVOS: this.extendVOS
    }

    this.resignService.submitContract(params).subscribe((res) => {
      const { code, msg } = res
      if (code === 0) {
        this.messageService.success('保存成功！')
      } else {
        this.messageService.success(msg)
      }
    })
  }
  // 提交待签署合同
  submitUserContract() {
    this.loading = true
    let params = {
      snapshot: {
        dimissionId: this.dismissionId,
        userId: this.userId,
        competition: this.competition,
        specialCompany: this.specialCompany,
        specialCompanyTxt: this.specialCompanyTxt,
        company: this.company,
        stockManage: this.stockManage,
        sersSignDate: this.sersSignDate,
        stockNumber: this.stockNumber,
        sersKeepNumber: this.sersKeepNumber,
        sersInvalidNumber: this.sersInvalidNumber,
        waitTime: this.waitTime
      },
      extendVOS: this.extendVOS
    }
    const methods = this.isTheFirstTime
      ? this.resignService.submitContract(params)
      : this.resignService.updateContract(params)
    methods.subscribe((res) => {
      const { code, msg } = res
      if (code === 0) {
        this.resignService.signContract(param).subscribe((res) => {
          this.loading = false
          const { code, msg } = res
          if (code === 0) {
            this.messageService.success('签署成功！')
            this.router.navigate(['/contract/resign'])
          } else {
            this.messageService.error(msg)
          }
        })
      } else {
        this.loading = false
        this.messageService.error(msg)
      }
    })
    let param = {
      id: this.dismissionId
    }
  }

  // 点击保存合同
  saveUserInfo() {
    this.messageService.success('保存成功')
  }
  // 收起展开
  openInfo(type) {
    let box = document.querySelector('.info-box')
    if (type === 'off') {
      this.isOpen = false
      box.setAttribute('style', 'display: none  !important')
    } else {
      this.isOpen = true
      box.setAttribute('style', 'display: block  !important')
    }
  }
  // 结算单信息接口
  getInfoMoney() {
    let params = {
      dismissionid: this.dismissionId
    }
    this.resignService.getMoneyInfo(params).subscribe((res) => {
      const { code, msg, data } = res
      if (code === 0) {
        this.lzbc = data.lzbc
        this.remarks = data.remark
      } else {
        this.messageService.error(msg)
      }
    })
  }
}
