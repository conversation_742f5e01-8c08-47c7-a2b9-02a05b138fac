html,
body {
  height: 100%;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
}

.icon {
  //用于显示iconfont的symbol方式去啊我是真的潇洒是自身的壮大1
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.content-block {
  margin: 0 20px;
  padding: 30px 20px;

  & > .search-container {
    min-height: 32px;

    .title {
      color: #000;
      font-size: 14px;
    }

    .ant-input {
      width: 166px;
    }

    .ant-btn {
      width: 114px;
    }
  }

  & > .title {
    //padding-left: 37px;
    font-size: 16px;
    line-height: 44px;
    border-bottom: 2px solid #f3f3f4;
    color: #000;
  }

  &.top-block {
    margin-top: 20px;

    .operate-btns {
      float: right;

      button {
        margin-left: 10px;
      }
    }
  }

  .inner-flex-block {
    display: flex;
    min-height: 400px;

    .block-left {
      flex-basis: 260px;
      padding: 10px 20px;
      border-right: 1px solid #f3f3f4;
    }

    .block-right {
      flex-basis: 1040px;
      padding: 10px 20px;
    }
  }
}

.content-item {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 -3px 20px 0 rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  margin-bottom: 20px;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.clearfix::after {
  display: block;
  height: 0;
  content: '';
  clear: both;
  visibility: hidden;
  font-size: 0;
}

.left-10 {
  margin-left: 10px;
}

.right-10 {
  margin-right: 10px;
}

.top-10 {
  margin-top: 10px;
}

.bottom-10 {
  margin-bottom: 10px;
}

.right-30 {
  margin-right: 30px;
}

.top-0 {
  margin-top: 0;
}

.top-30 {
  margin-top: 30px;
}

.bottom-30 {
  margin-bottom: 30px;
}

.bottom-20 {
  margin-bottom: 20px;
}

.top-20 {
  margin-top: 20px;
}

.right-20 {
  margin-right: 20px;
}

.left-20 {
  margin-left: 20px;
  font-weight: 700;
}

.left-30 {
  margin-left: 30px;
  font-weight: 600;
}

.right-40 {
  margin-right: 40px;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.box-shadow {
  box-shadow: 0 -3px 20px 0 rgba(0, 0, 0, 0.05);
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

hr {
  margin-top: 10px;
  margin-bottom: 10px;
  border-top: 1px solid #eee;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

p {
  margin: 0;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.umeditor-content {
  //padding: 0 10px;
  font-size: 16px;
  min-height: 50px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-spaces;
  word-break: break-all;

  img {
    max-width: 100%;
  }

  p {
    margin: 5px 0;
  }
}

.edui-container {
  img {
    max-width: 100%;
  }
}

.umeditor-textarea {
  display: none;
}

.edui-container *,
.edui-container *::before,
.edui-container *::after {
  box-sizing: content-box;
}

.gray-bg {
  background-color: #f4f5f6;
}

.table-container {
  padding: 0 20px 38px;
}

.deadline {
  margin: 30px 40px 20px;
  height: 20px;
  line-height: 20px;
  font-size: 20px;
  vertical-align: middle;
  color: #000;

  .on-time {
    padding: 0 5px;
    color: rgb(47, 152, 242);
  }

  .overdue {
    padding: 0 5px;
    color: #ff1866;
  }
}

.iconfangdajing {
  //搜索icon修改统一颜色
  color: #909090;
}

.warningColor {
  color: #ff1866;
}

.titleAndProjectSel {
  .ant-select {
    margin-top: 24px;
    margin-left: 20px;
    vertical-align: top;
  }
}

.modalFooter {
  text-align: center;
  margin-right: -20px;

  button {
    width: 114px;
  }
}

.refuse-modal-title {
  padding: 22px 0;
  margin: -16px -20px;
  margin-bottom: 0;

  & > i {
    font-size: 14px;
    color: #ff1866;
    margin-left: 20px;
    margin-right: 10px;
  }

  border-bottom: 1px solid rgba(232, 232, 232, 1);
}

.committeeTable {
  .ant-table {
    table {
      thead {
        th {
          &:nth-child(1) {
            padding-left: 70px;
          }

          &:nth-last-child(1) {
            padding-right: 70px;
          }
        }
      }

      tbody {
        tr {
          td {
            &:nth-child(1) {
              padding-left: 70px;
            }

            &:nth-last-child(1) {
              padding-right: 70px;
            }
          }
        }
      }
    }
  }
}

.projectTable {
  .ant-table {
    table {
      thead {
        th {
          &:nth-child(1) {
            padding-left: 70px;
          }

          &:nth-last-child(1) {
            padding-right: 70px;
          }
        }
      }

      tbody {
        tr {
          td {
            &:nth-child(1) {
              padding-left: 70px;
            }

            &:nth-last-child(1) {
              padding-right: 70px;
            }
          }
        }
      }
    }
  }
}

.loading {
  margin-top: 150px;
  text-align: center;
}

.empty-input-color {
  color: #909090;
}

.hello-container {
  .hello-word {
    color: #000;
    font-size: 14px;
    //margin: 46px 0 30px 25px;
    display: inline-block;
  }
}

.approveOperate {
  text-align: right;
}

// 设置ck-editor 高度
.ck-editor__editable {
  min-height: 200px !important;
  max-height: 300px !important;
}

.high-light {
  color: #379af6;
}

.menu-update-modal {
  .modal-item {
    margin: 0 auto 20px;
    text-align: center;

    & > span:nth-of-type(1) {
      color: #000;
      //margin-left: 40px;
      display: inline-block;
      font-size: 16px;
      width: 100px;
      text-align: right;
    }

    .content {
      color: #000;
      text-align: left;
      display: inline-block;
      font-size: 16px;
      width: 250px;
    }

    .ant-input,
    .ant-select {
      width: 250px;
    }
  }
}

.table-oprt {
  & > a {
    margin-right: 10px;
  }
}

.disable-color {
  color: #909090;
  cursor: not-allowed;

  &:hover {
    color: #909090;
  }
}

.title-divider {
  width: 2px;
  display: inline-block;
  background-color: black;
  height: 14px;
  margin-right: 10px;
}

.loading-data {
  text-align: center;
  margin: 150px auto 100px;
}

.form-modal {
  .modal-item {
    margin: 0 auto 20px;
    text-align: center;

    & > span:nth-of-type(1),
    label {
      color: #000;
      //margin-left: 40px;
      display: inline-block;
      font-size: 16px;
      width: 100px;
      text-align: right;
    }

    .content {
      color: #000;
      text-align: left;
      display: inline-block;
      font-size: 16px;
      width: 250px;
    }

    .ant-input,
    .ant-select {
      width: 250px;
    }
  }
}
