@startuml
!include https://unpkg.com/plantuml-style-c4@latest/c4_context.puml

' LAYOUT_AS_SKETCH()
LAYOUT_WITH_LEGEND()

title mit

System_Boundary(mit, "mit"){
    System(executor, "mit定时任务", "10.122.107.32")
    System(webapi, "mit对外api", "http://mit-webapi.mcd.megvii-inc.com/")
    System(staffentry, "mit信息收集", "10.122.107.75 **** https://onboard-epif.megvii-inc.com/")
    System(contractplatform, "mit电子签章", "10.122.107.118 **** https://dhr.megvii-inc.com/mit/contract/new")
}

System_Ext(job, "xxl-job", "10.122.107.33")
System_Ext(beisen, "北森", "10.201.109.176代理")
System_Ext(oa, "OA", "oa.megvii-inc.com")
System_Ext(koala, "Koala", "10.199.1.81")
System_Ext(pangu, "盘古", "10.199.0.163")
System_Ext(erp, "ERP", "XXX.XX.XX")
System_Ext(ldap, "AD", "ldap.megvii-inc.com:636")
System_Ext(brain, "brain++", "www.brainpp.cn")
System_Ext(contract, "契约锁", "10.122.107.106:9182")
System_Ext(mdm, "数据中台", "mdm.megvii-inc.com")
System_Ext(dhr, "DHR", "dhr.megvii-inc.com")
System_Ext(didi, "滴滴", "api.es.xiaojukeji.com")
System_Ext(meituan, "美团", "api-sqt.meituan.com")
System_Ext(dingding, "钉钉", "oapi.dingtalk.com")
System_Ext(git, "git", "git-pd.megvii-inc.com")
System_Ext(glyt, "差旅", "sftp.citsgbt.com")
System_Ext(jobcard, "工卡", "*************")
System_Ext(asset, "资产", "*************")
System_Ext(mail, "邮件服务器", "smtp.megvii-inc.com")
System_Ext(meican, "美餐", "api.meican.com")
System_Ext(office, "office365", "microsoftgraph.chinacloudapi.cn")
System_Ext(sms, "短信服务", "sdk.entinfo.cn:8061")
System_Ext(ftp, "ftp文件", "share.megvii-inc.com")



Rel(job, executor, "任务调度, ip")
Rel(executor, beisen, "拉取北森offer（主动推送未迁移java）,ip")
Rel(executor, oa, "入职/离职推送")
Rel(executor, staffentry, "信息收集拉取, ip操作数据库")
Rel(executor, koala, "上传koala, ip")
Rel(executor, pangu, "上传盘古, ip")
Rel(executor, ftp, "上传共享盘")
Rel(executor, ldap, "ad")
Rel(executor, office, "邮箱")
Rel(executor, sms, "短信服务")
Rel(executor, mail, "邮件服务")
Rel(executor, mdm, "拉取数据")
Rel(executor, dhr, "推送/拉取数据, ip白名单")
Rel(executor, didi, "数据更新, ip白名单")
Rel(executor, dingding, "数据更新")
Rel(executor, meican, "数据更新")
Rel(executor, meituan, "数据更新")
Rel(executor, git, "数据更新")
Rel(executor, glyt, "数据更新")
Rel(executor, brain, "数据更新")

Rel(contractplatform, contract, "合同操作, ip")
Rel(contract, contractplatform, "合同信息回传, ip")

Rel(webapi, staffentry, "发起信息收集, ip操作数据库")
Rel(staffentry, pangu, "图片质量检测, ip")
Rel(staffentry, erp, "支行信息获取, ip")

Rel(oa, webapi, "入职信息回传/离职回调, mcd域名调用, ip白名单")
Rel(dhr, webapi, "发送钉钉通知, mcd域名调用, ip白名单")
Rel(jobcard, webapi, "拉取数据, mcd域名调用, ip白名单")
Rel(asset, webapi, "拉取数据, mcd域名调用, ip白名单")
Rel(webapi, dingding, "发送钉钉通知")






@enduml