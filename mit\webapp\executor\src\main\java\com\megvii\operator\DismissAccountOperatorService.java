package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.User;
import com.megvii.service.DimissionService;
import com.megvii.service.MailSendService;
import com.megvii.service.UserService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/11/4 20:58
 */
@Service
public class DismissAccountOperatorService extends BaseOperatorService {

    @Autowired
    private UserService userService;

    @Autowired
    private DimissionService dimissionService;

    @Autowired
    private DismissAccountOperatorService dismissAccountOperatorService;

    @Autowired
    private MailSendService mailSendService;

    /**
     *
     * 主操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {

        List<User> users = userService.getUserByDimissionDate();
        if (users.size() > 0) {
            StringBuilder msg = new StringBuilder();
            for (User user : users) {
                OperateResult result = dismissAccountOperatorService.operateOnce(paraMap, user);
                msg.append(result.getMsg() + "<br/>");
            }

            mailSendService.sendMail("离职任务提醒", msg.toString(), "<EMAIL>,<EMAIL>");
            return new OperateResult(1, msg.toString());
        } else {
            return new OperateResult(1, "没有离职账号");
        }

    }


    /**
     *
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        User user = (User) params[0];
        if (user != null){
            String result = dimissionService.dismissAccount(user);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1, user.getUserId() + "信息不存在");
        }
    }



}
