package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.UserService;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 测试用例，操作类，
 */
@Service
public class SayHelloOperateService extends BaseOperatorService {

	private Logger logger = LoggerFactory.getLogger(SayHelloOperateService.class);

	@Autowired
	private SayHelloOperateService sayHelloService;

	@Autowired
    private UserService userService;

	/**
	 * 主操作
	 *
	 * @param paraMap
	 * @param params  参数
	 * @return
	 * @throws Exception
	 */
	@Override
	public OperateResult operateMain(Map paraMap, Object... params) throws Exception {
		Object pwd = paraMap.get("pwd");

		if ("initdata".equals(String.valueOf(pwd))) {
		    OperateResult operateResult = sayHelloService.operateOnce(paraMap, null);
		    return new OperateResult(1, operateResult.getMsg());
        }

		return new OperateResult(1, "初始化错误");
	}

	/**
	 * 单次操作，模拟异常抛出
	 *
	 * @param paraMap
	 * @param params  参数
	 * @return
	 * @throws Exception
	 */
	@Override
	protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {

		return new OperateResult(1, "");
	}

}
