package com.megvii.operator;


import com.megvii.common.OperateResult;
import com.megvii.entity.opdb.TempUser;
import com.megvii.service.DimissionService;
import com.megvii.service.MailSendService;
import com.megvii.service.TempUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/1/7 16:07
 */
@Service
public class TempDismissOperatorService extends BaseOperatorService {

    @Autowired
    private TempUserService tempUserService;

    @Autowired
    private DimissionService dimissionService;

    @Autowired
    private TempDismissOperatorService tempDismissOperatorService;

    @Autowired
    private MailSendService mailSendService;

    /**
     *	外包离场删除账号
     * 主操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        List<TempUser> tempUsers = tempUserService.getNeedDisTempUserList();
        if (tempUsers.size() > 0) {
            StringBuilder msg = new StringBuilder();
            for (TempUser user : tempUsers) {
                OperateResult result = tempDismissOperatorService.operateOnce(paraMap, user);
                msg.append(result.getMsg() + "<br/>");
            }

            mailSendService.sendMail("外包离场任务提醒", msg.toString(), "<EMAIL>,<EMAIL>");
            return new OperateResult(1, msg.toString());
        } else {
            return new OperateResult(1, "没有离职账号");
        }

    }


    /**
     *
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
        TempUser tempUser = (TempUser) params[0];
        if (tempUser != null){
            String result = dimissionService.tempDismissAccount(tempUser);
            return new OperateResult(1, result);
        } else {
            return new OperateResult(1, tempUser.getUserId() + "信息不存在");
        }
    }






}
