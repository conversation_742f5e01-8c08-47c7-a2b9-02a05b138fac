package com.megvii.controller.api;

import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.UserDto;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/20 15:17
 */
@Data
public class UserVO {

	private int id;
	private String workNumber;
	private String team;
	private String userId;
	private String mentor;
	private String spell;
	private String hrg;
	private String cell;
	private String workBase;
	private String employType;
	private String employBase;
	private String position;
	private String address;
	private String company;
	private String certType;
	private String certNo;
	private String expectDate;
	private String memo;
	private String contractStatus;
	private Integer competition;
	private String selfEmail;
	private String dismissionDate;
	/**
	 * 是否有补偿金
	 */
	private Integer compensation;

	/**
	 * 是否有股份
	 */
	private Integer shares;

	/**
	 * OA流程、信息收集、等待入职、在职
	 */
	private String entryFlow;
	/**
	 * offerId
	 */
	private String offerId;

	private String certName;

	public static List<UserVO> toVOs(List<User> users) {
		List<UserVO> vos = new ArrayList<>();
		for (User user : users) {
			vos.add(toVO(user));
		}
		return vos;
	}

	public static UserVO toVO(User user) {
		UserVO vo = new UserVO();
		vo.setId(user.getId());
		vo.setWorkNumber(user.getWorkNumber());
		vo.setTeam(user.getTeam());
		vo.setUserId(user.getUserId());
		vo.setMentor(user.getMentor());
		vo.setSpell(user.getSpell());
		vo.setHrg(user.getHrbp());
		vo.setCell(user.getCell());
		vo.setWorkBase(user.getWorkBase());
		vo.setEmployType(user.getEmployType());
		vo.setEmployBase(user.getEmployBase());
		if (user.getPosition() == null || user.getPosition().indexOf("无")!=-1) {
			vo.setPosition(null);
		} else {
			vo.setPosition(user.getPosition());
		}
		vo.setAddress(user.getAddress());
		vo.setCompany(user.getCompany());
		vo.setCertType(user.getCertType());
		vo.setCertNo(user.getCertNo());
		vo.setExpectDate(user.getExpectDate()!=null?DateTimeUtil.date2String(user.getExpectDate()):null);
		vo.setMemo(user.getMemo());
		vo.setContractStatus(user.getContractStatus());
		vo.setEntryFlow(user.getEntryFlow());
		vo.setOfferId(user.getOfferId());
		vo.setCertName(user.getCertName());
		return vo;
	}

	public static UserVO dtoToVO(UserDto user) {
		UserVO vo = toVO(user);
		vo.setSelfEmail(user.getSelfEmail());
		vo.setCompetition(user.getCompetition());
		vo.setDismissionDate(DateTimeUtil.date2String(user.getDimissionDate()));
		vo.setCompensation(user.getCompensation());
		vo.setShares(user.getShares());
		return vo;
	}
}
