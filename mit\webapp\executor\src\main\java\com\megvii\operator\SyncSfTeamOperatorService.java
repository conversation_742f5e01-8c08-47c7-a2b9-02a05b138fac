package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.SyncSfTeamService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/9/10 17:44
 */
@Service
public class SyncSfTeamOperatorService extends BaseOperatorService {

    @Autowired
    private SyncSfTeamOperatorService syncSfTeamOperatorService;

    @Autowired
    private SyncSfTeamService syncSfTeamService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) {
        return syncSfTeamOperatorService.operateOnce(paraMap, params);
    }

    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) {
//        String msg = syncSfTeamService.syncSfTeam();
//        return new OperateResult(1,msg);
        return new OperateResult(1,"");
    }

}
