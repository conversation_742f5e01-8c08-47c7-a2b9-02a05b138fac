package com.megvii.client;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.entity.opdb.contract.UserContractSnapshot;
import com.megvii.service.contract.ContractRestService;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileOutputStream;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/22 18:17
 */
@Component
@PropertySource(value = "classpath:contract.${spring.profiles.active}.properties", encoding = "UTF-8")
public class ContractRestClient {

	private Logger logger = LoggerFactory.getLogger(ContractRestClient.class);

	@Autowired
	private ContractRestService contractRestService;

	@Value("${TOKEN}")
	private String token;

	@Value("${SECRET}")
	private String secret;

	@Value("${BASE_URL}")
	private String baseUrl;

	@Value("${CREATE_DOC_URL}")
	private String createDocUrl;

	@Value("${CREATE_CONTRACT_URL}")
	private String createContractUrl;

	@Value("${EDIT_CONTRACT_URL}")
	private String editContractUrl;

	@Value("${DELETE_CONTRACT_URL}")
	private String deleteContractUrl;

	@Value("${SEND_CONTRACT_URL}")
	private String sendContractUrl;

	@Value("${SIGN_CONTRACT_URL}")
	private String signContractUrl;

	@Value("${DETAIL_CONTRACT_URL}")
	private String contractDetailUrl;

	@Value("${RECALL_CONTRACT_URL}")
	private String recallContractUrl;

	@Value("${CANCEL_CONTRACT_URL}")
	private String cancelContractUrl;

	@Value("${VIEW_CONTRACT_URL}")
	private String viewContractUrl;

	@Value("${ADD_CONTRACT_FILE_URL}")
	private String addContractFileUrl;

	@Value("${DOWNLOAD_CONTRACT_FILE_URL}")
	private String downloadContractFileUrl;

	@Value("${CREATE_CONTRACT_BY_FILE_URL}")
	private String createContractByFileUrl;


	@Autowired
	private RestTemplate restTemplate;

	/**
	 * 通过附件创建文件 返回文档ID
	 * 可将文档加入到合同中
	 *
	 * @param files
	 * @return
	 * @throws Exception
	 */
	public Long createContractByFile(List<File> files) throws Exception {
		String url = baseUrl + createContractByFileUrl;
		HttpHeaders headers = getHeaders(MediaType.MULTIPART_FORM_DATA);

		MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();

		for (File file : files) {
			FileSystemResource resource = new FileSystemResource(file);
			params.add("files", resource);
		}
		params.add("title", "协议书");

		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

		JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url,
				HttpMethod.POST, requestEntity, String.class).getBody());

		if (jsonObject.getString("code").equals("0")) {
			return Long.valueOf(jsonObject.getString("documentId"));
		} else {
			logger.error("通过文件创建合同失败：" + jsonObject.getString("message"));
			throw new Exception("通过文件创建合同失败：" + jsonObject.getString("message"));
		}
	}

	/**
	 * 通过附件创建文件 返回文档ID
	 * 可将文档加入到合同中
	 *
	 * @param files
	 * @return
	 * @throws Exception
	 */
	public Long createNewContractByFile(List<File> files, UserContractSnapshot snapshot) throws Exception {
		String url = baseUrl + createContractByFileUrl;
		HttpHeaders headers = getHeaders(MediaType.MULTIPART_FORM_DATA);

		MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();

		for (File file : files) {
			FileSystemResource resource = new FileSystemResource(file);
			params.add("files", resource);
		}
		if (snapshot.getContractType().equals("劳动合同")) {
			params.add("title", "员工录用通知");
		} else {
			params.add("title", "实习生录用通知");
		}


		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<MultiValueMap<String, Object>>(params, headers);

		JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url,
				HttpMethod.POST, requestEntity, String.class).getBody());

		if (jsonObject.getString("code").equals("0")) {
			return Long.valueOf(jsonObject.getString("documentId"));
		} else {
			logger.error("通过文件创建合同失败：" + jsonObject.getString("message"));
			throw new Exception("暂无员工录用通知，请确认员工录用通知已生成或已上传后，再发起签署");
		}
	}



	/**
	 * 从电子签下载合同zip 保存到指定路径
	 *
	 * @param contractId
	 * @param path
	 * @return
	 */
	public boolean downloadContractZip(String contractId, String path) {
		String url = baseUrl + downloadContractFileUrl + "?contractId=" + contractId;

		HttpHeaders headers = getHeaders(null);
		HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);

		ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.GET,
				requestEntity, byte[].class);

		try {
			File file = new File(path + contractId + "." + response.getHeaders().getContentType().getSubtype());
			file.createNewFile();
			FileOutputStream fos = new FileOutputStream(file);
			fos.write(response.getBody());
			fos.flush();
			fos.close();
			return true;
		} catch (Exception e) {
			logger.error(contractId + "保存合同zip异常:" + e);
			return false;
		}
	}


	/**
	 * 向指定合同中添加附件
	 *
	 * @param file
	 * @param contractId
	 * @return
	 */
	public boolean addFileToContract(File file, String contractId) throws Exception {
		String url = baseUrl + addContractFileUrl;

		HttpHeaders headers = getHeaders(MediaType.MULTIPART_FORM_DATA);
		MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();
		params.add("contractId", contractId);
		params.add("title", file.getName());

		FileSystemResource resource = new FileSystemResource(file);
		params.add("file", resource);

		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<MultiValueMap<String, Object>>(params, headers);
		JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url,
				HttpMethod.POST, requestEntity, String.class).getBody());

		if (jsonObject.getString("code").equals("0")) {
			return true;
		} else {
			logger.error(contractId + "上传附件异常：" + jsonObject.getString("message"));
			throw new Exception(contractId + "上传附件异常：" + jsonObject.getString("message"));
		}
	}


	/**
	 * ******* 根据合同id 获取合同url
	 *
	 * @param contractId
	 * @return
	 * @throws Exception
	 */
	public String getViewContractUrl(String contractId) throws Exception {
		String url = baseUrl + viewContractUrl + "?contractId=" + contractId + "&expireTime=600";

		HttpHeaders headers = getHeaders(null);
		HttpEntity<String> requestEntity = new HttpEntity<String>(null, headers);

		JSONObject jsonObject = restTemplate.exchange(url, HttpMethod.GET, requestEntity, JSONObject.class).getBody();

		if (jsonObject.getString("code").equals("0")) {
			return jsonObject.getString("viewUrl");
		} else {
			logger.error("获取合同url失败：" + jsonObject.getString("message"));
			throw new Exception("获取合同url失败：" + jsonObject.getString("message"));
		}
	}


	/**
	 * 作废合同
	 *
	 * @param contractId
	 * @param sealId
	 * @return
	 */
	public boolean cancelContract(String contractId, String sealId) {
		String url = baseUrl + cancelContractUrl;

		HttpHeaders headers = getHeaders(MediaType.MULTIPART_FORM_DATA);

		MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
		params.add("contractId", contractId);
		params.add("sealId", sealId);
		HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<MultiValueMap<String, String>>(params, headers);

		JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url,
				HttpMethod.POST, requestEntity, String.class).getBody());

		if (jsonObject.getString("code").equals("0")) {
			return true;
		} else {
			logger.error(jsonObject.getString("message"));
			return false;
		}

	}


	/**
	 * 1.3.4 撤回合同
	 *
	 * @param contractId
	 * @return
	 */
	public boolean recallContract(String contractId) {
		String url = baseUrl + recallContractUrl;

		HttpHeaders headers = getHeaders(MediaType.MULTIPART_FORM_DATA);

		MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
		params.add("contractId", contractId);
		HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<MultiValueMap<String, String>>(params, headers);

		JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url,
				HttpMethod.POST, requestEntity, String.class).getBody());

		if (jsonObject.getString("code").equals("0")) {
			return true;
		} else {
			logger.error(jsonObject.getString("message"));
			return false;
		}
	}


	/**
	 * ******* 发起签署
	 *
	 * @param contractId
	 * @return
	 */
	public boolean sendContract(String contractId) {
		String url = baseUrl + sendContractUrl;
		HttpHeaders headers = getHeaders(MediaType.APPLICATION_JSON);
		JSONObject data = new JSONObject();
		data.put("contractId", contractId);
		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(), headers);
		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, requestEntity, JSONObject.class);
		if (responseEntity.getBody().getString("code").equals("0")) {
			return true;
		} else {
			logger.error(contractId + "发起签署失败：" + responseEntity.getBody().getString("message"));
			return false;
		}
	}

	/**
	 * 1.3.5删除合同
	 * 描述：删除合同，只能删除“草稿”状态下的合同。
	 * @param contractId
	 * @return
	 */
	public boolean deleteContract(String contractId) {
		String url = baseUrl + deleteContractUrl;
		HttpHeaders headers = getHeaders(MediaType.MULTIPART_FORM_DATA);
		MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
		params.add("contractId", contractId);
		HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<MultiValueMap<String, String>>(params, headers);
		JSONObject jsonObject = RequestCorvertUtil.getJSONObjectFromStr(url, restTemplate.exchange(url,
				HttpMethod.POST, requestEntity, String.class).getBody()); //form-data方式请求
//		JSONObject data = new JSONObject();
//		data.put("contractId", contractId);
//		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(), headers);
//		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, requestEntity, JSONObject.class); //json方式请求
		if (jsonObject.getString("code").equals("0")) {
			return true;
		} else {
			logger.error(contractId + "删除合同失败：" + jsonObject.getString("message"));
			return false;
		}
	}


	/**
	 * 1.1.4
	 * 创建合同 data在service中生成
	 *
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public String createContract(JSONObject data) throws Exception {
		String url = baseUrl + createContractUrl;

		HttpHeaders headers = getHeaders(MediaType.APPLICATION_JSON);

		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(), headers);
		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, requestEntity, JSONObject.class);

		if (responseEntity.getBody().getString("code").equals("0")) {
			return responseEntity.getBody().getString("contractId");
		} else {
			logger.error("创建合同失败：" + responseEntity.getBody().getString("message"));
			throw new Exception("创建合同失败：" + responseEntity.getBody().getString("message"));
		}
	}

	/**
	 * 1.1.5.4 编辑合同
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public Boolean editContract(JSONObject data) throws Exception {
		String url = baseUrl + editContractUrl;

		HttpHeaders headers = getHeaders(MediaType.APPLICATION_JSON);

		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(), headers);
		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, requestEntity, JSONObject.class);

		if (responseEntity.getBody().getString("code").equals("0")) {
			return true;
		} else {
			logger.error("编辑合同失败：" + responseEntity.getBody().getString("message"));
			throw new Exception("编辑合同失败：" + responseEntity.getBody().getString("message"));
		}
	}

	/**
	 * 根据分类盖章
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public Boolean signByCompany(JSONObject data) throws Exception {
		String url = baseUrl + signContractUrl;
		HttpHeaders headers = getHeaders(MediaType.APPLICATION_JSON);
		HttpEntity<String> requestEntity = new HttpEntity<String>(data.toJSONString(), headers);
		ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, requestEntity, JSONObject.class);
		if (responseEntity.getBody().getString("code").equals("0")) {
			return true;
		} else {
			logger.error("盖章合同失败：" + responseEntity.getBody().getString("message"));
			throw new Exception("盖章合同失败：" + responseEntity.getBody().getString("message"));
		}
	}

	/**
	 * 获取合同文件id
	 * @param contractId
	 * @return
	 * @throws Exception
	 */
	public String getContractDetail(String contractId) throws Exception {
			String url = baseUrl + contractDetailUrl+"?contractId="+contractId;
			ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url,HttpMethod.GET, new HttpEntity<Object>(getHeaders()),JSONObject.class);
			if (responseEntity.getBody().getString("code").equals("0")) {
				return responseEntity.getBody().getJSONObject("contract").getJSONArray("documents").getJSONObject(0).get("id").toString();
			} else {
				logger.error("获取合同详情失败：" + responseEntity.getBody().getString("message"));
				throw new Exception("获取合同详情失败：" + responseEntity.getBody().getString("message"));
			}
		}
	public JSONArray getContractDetails(String contractId) throws Exception {
		String url = baseUrl + contractDetailUrl+"?contractId="+contractId;
		ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url,HttpMethod.GET, new HttpEntity<Object>(getHeaders()),JSONObject.class);
		if (responseEntity.getBody().getString("code").equals("0")) {
			return JSONArray.parseArray(responseEntity.getBody().getJSONObject("contract").getJSONArray("documents").toJSONString());
		} else {
			logger.error("获取合同详情失败：" + responseEntity.getBody().getString("message"));
			throw new Exception("获取合同详情失败：" + responseEntity.getBody().getString("message"));
		}
	}

	public JSONObject getContractByContractDetails(String contractId) throws Exception {
		String url = baseUrl + contractDetailUrl+"?contractId="+contractId;
		ResponseEntity<JSONObject> responseEntity = restTemplate.exchange(url,HttpMethod.GET, new HttpEntity<Object>(getHeaders()),JSONObject.class);
		if (responseEntity.getBody().getString("code").equals("0")) {
			return responseEntity.getBody().getJSONObject("contract");
		} else {
			logger.error("获取合同详情失败：" + responseEntity.getBody().getString("message"));
			throw new Exception("获取合同详情失败：" + responseEntity.getBody().getString("message"));
		}
	}

	/**
	 * 获取电子签请求头 传入contentType
	 *
	 * @param mediaType
	 * @return
	 */
	private HttpHeaders getHeaders(MediaType mediaType) {
		HttpHeaders headers = new HttpHeaders();

		long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));

		headers.set("x-qys-accesstoken", token);
		headers.set("x-qys-timestamp", String.valueOf(timeStamp));
		headers.set("x-qys-signature", DigestUtils.md5Hex(token + secret + timeStamp));
		if (mediaType != null) headers.setContentType(mediaType);

		return headers;
	}
	private MultiValueMap<String, String> getHeaders() {
		MultiValueMap<String, String> headers = new LinkedMultiValueMap<String, String>();
		long timeStamp = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
		headers.set("x-qys-accesstoken", token);
		headers.set("x-qys-timestamp", String.valueOf(timeStamp));
		headers.set("x-qys-signature", DigestUtils.md5Hex(token + secret + timeStamp));
		return headers;
	}

}
