package com.megvii.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.megvii.service.MMISMegviiConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/10/16 18:45
 */
@Component
@PropertySource(value = "classpath:dhr.${spring.profiles.active}.properties",encoding = "UTF-8")
public class DhrRestClient {

    Logger logger=LoggerFactory.getLogger(DhrRestClient.class);

    @Value("${DHR_ADMIN}")
    private String dhrAdmin;

    @Value("${DHR_PWD}")
    private String dhrPwd;

    @Value("${DHR_DATA_URL}")
    private String dhrDataUrl;

    @Value("${DHR_PUT_START_URID}")
    private String dhrPutStartUrid;

    @Value("${DHR_PUT_DATA_URID}")
    private String dhrPutDataUrid;

    @Value("${DHR_PUT_CLOSE_URID}")
    private String dhrPutCloseUrid;

    @Value("${DHR_GET_DATA_URID}")
    private String dhrGetDataUrid;

    @Autowired
    private MMISMegviiConfigService mmisMegviiConfigService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取DHR薪酬类型 code
     *
     * @param temp
     * @return
     */
    public String getSalaryTypeCode(String temp) {
        return getDhrCode(temp, "mitexecutor_DHR_salarytype", "CN_SALARY_TYPE");
    }

    /**
     * 获取DHR sign on 类型 code
     *
     * @param temp
     * @return
     */
    public String getSignOnTypeCode(String temp) {
        return getDhrCode(temp, "mitexecutor_DHR_signontype", "CN_SIGNON_TYPE");
    }

    /**
     * 获取DHR 证件类型 value
     *
     * @param temp
     * @return
     */
    public String getCertTypeValue(String temp) {
        return getDhrValue(temp, "mitexecutor_DHR_certtype_value", "CN_IDTYPE");
    }

    /**
     * 获取DHR 序列code
     * @param temp
     * @return
     */
    public String getSalesCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_sales", "CN_EMP_PROPERTY");
    }


    /**
     * 获取DHR 序列code
     * @param temp
     * @return
     */
    public String getSequenceCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_sequence", "CN_JOBSEQUENCE");
    }


    /**
     * 获取DHR relation code
     * @param temp
     * @return
     */
    public String getRelationCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_relation", "CN_RELATION");
    }


    /**
     * 获取DHR T-shirt code
     * @param temp
     * @return
     */
    public String getTSizeCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_tsize", "CN_TSSIZE");
    }


    /**
     * 获取DHR 生育code
     * @param temp
     * @return
     */
    public String getFertilitystateCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_fertilitystate", "CN_FERTILITYSTATE");
    }


    /**
     * 获取DHR 民族code
     * @param temp
     * @return
     */
    public String getNationCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_nationality", "CN_NATIONALITY");
    }

    /**
     * 获取DHR 银行code
     * @param temp
     * @return
     */
    public String getBankCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_paycard", "CN_PAYCARD");
    }

    /**
     * 获取DHR 公司value
     * @param temp
     * @return
     */
    public String getBankValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_paycard", "CN_PAYCARD");
    }


    /**
     * 获取DHR 招聘渠道code
     * @param temp
     * @return
     */
    public String getSourceCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_job_recruit", "CN_RECRUIT");
    }

    /**
     * 获取DHR 职能code
     * @param temp
     * @return
     */
    public String getDutyCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_job_capacity", "CN_JOB_CAPACITY");
    }

    /**
     * 获取DHR 公司value
     * @param temp
     * @return
     */
    public String getCompanyValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_contract_value", "CN_CONTRACT");
    }

    /**
     * 获取DHR 公司code
     * @param temp
     * @return
     */
    public String getCompanyCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_contract", "CN_CONTRACT");
    }


    /**
     * 获取DHR 员工类型value
     * @param temp
     * @return
     */
    public String getEmpTypeValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_emp_type_value", "CN_EMP_TYPE");
    }

    /**
     * 获取DHR 员工类型code
     * @param temp
     * @return
     */
    public String getEmpTypeCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_emp_type", "CN_EMP_TYPE");
    }

    /**
     * 获取DHR 参考技术定级code
     * @param temp
     * @return
     */
    public String getReferTitleCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_refer_title", "CN_REF_TEC_LEVEL");
    }


    /**
     * 获取DHR 入职地点code
     * @param temp
     * @return
     */
    public String getEmployBaseCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_location", "CN_LOCATION");
    }


    /**
     * 获取DHR 工作地点code
     * @param temp
     * @return
     */
    public String getWorkBaseCode(String temp){
        return getDhrCode(temp, "mitexecutor_DHR_office_place", "CN_OFFICE_PLACE");
    }

    /**
     * 获取DHR 国家value
     * @param temp
     * @return
     */
    public String getCountryValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_country_value", "CN_COUNTRY");
    }


    /**
     * 获取DHR 民族value
     * @param temp
     * @return
     */
    public String getNationValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_nationality_value", "CN_NATIONALITY");
    }

    /**
     * 获取DHR 国家value
     * @param temp
     * @return
     */
    public String getPartyValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_party_value", "CN_PARTY");
    }

    /**
     * 获取DHR CN_RESIDENT value 户籍性质
     * @param temp
     * @return
     */
    public String getResidentValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_resident_value", "CN_RESIDENT");
    }


    public String getResidentTypeValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_residentType", "CN_RESIDENT");
    }

    /**
     * 获取DHR CN_PLACE value
     * @param temp
     * @return
     */
    public String getCityValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_place_value", "CN_PLACE");
    }
    /***
     * 获取DHR婚姻状况码表
     * @param temp
     * @return
     */
    public String getMarriageValue(String temp){
        return getDhrValue(temp,"mitexecutor_dhr_marriage","CN_MARRIAGE");
    }


    /**
     * 获取DHR 生育value
     * @param temp
     * @return
     */
    public String getFertilitystateValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_fertilitystate_value", "CN_FERTILITYSTATE");
    }
    /**
     * 获取DHR 健康状况value
     * @param temp
     * @return
     */
    public String getHealthValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_health_value", "CN_HEALTH");
    }


    /**
     * 获取DHR T-shirt code
     * @param temp
     * @return
     */
    public String getTSizeValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_tsize_value", "CN_TSSIZE");
    }

    /**
     * 获取DHR relation value
     * @param temp
     * @return
     */
    public String getRelationValue(String temp){
        return getDhrValue(temp, "mitexecutor_DHR_relation_value", "CN_RELATION");
    }

    /**
     * 获取DHR 学位 value
     *
     * @param temp
     * @return
     */
    public String getDegreeTypeValue(String temp) {
        return getDhrValue(temp, "mitexecutor_DHR_degreetype_value", "CN_DEGREETYPE");
    }

    /**
     * 获取DHR 学历 value
     *
     * @param temp
     * @return
     */
    public String getEduTypeValue(String temp) {
        return getDhrValue(temp, "mitexecutor_DHR_edutype_value", "CN_EDUTYPE");
    }


    /**
     * 推送图片到DHR
     * @param base64
     * @param userId
     * @return
     * @throws Exception
     */
    public String putPhoto(String base64, String userId, String title) throws Exception {
        String url = dhrDataUrl + "/upattachcol";

        String layout = base64.replaceFirst("data:image/.*?;base64,","");

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("accessToken", getToken());
        data.put("layout", layout);
        data.put("title", title);
        data.put("filetype", base64.split(";base64")[0].replaceAll("data:image/", ""));

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            String id = responseEntity.getBody().split(":")[1];
            return id;
        } else {
            logger.error(userId + "推送图片到DHR异常");
            throw new Exception(userId + "推送图片到DHR异常");
        }
    }



    /**
     * 从DHR系统获取数据
     * @param funcId  跟实施方确定sql的ID
     * @return
     * @throws Exception
     */
    public JSONArray getData(String funcId) throws Exception {
        String url = dhrDataUrl + "/GetData";

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject paras = new JSONObject();
//        paras.put("U_EID", dhrGetDataUeid);

        JSONObject data = new JSONObject();
        data.put("funcId", funcId);
        data.put("accessToken", getToken());
        data.put("paras", paras);
        data.put("dataFormat", "json");
        data.put("dataPart", "0");

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject response = JSON.parseObject(responseEntity.getBody());
            if (Objects.isNull(response.getJSONObject("result"))){
                return new JSONArray();
            }
            JSONArray result = response.getJSONObject("result").getJSONObject("object").getJSONArray("row");

            logger.info(result.toJSONString());
            return result;
        } else {
            logger.error("DHR getData错误!");
            throw new Exception("DHR getData错误!");
        }
    }

    public JSONArray getDataObject(String funcId,JSONObject paras) throws Exception {
        String url = dhrDataUrl + "/GetData";
        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
//        paras.put("U_EID", dhrGetDataUeid);
        JSONObject data = new JSONObject();
        data.put("funcId", funcId);
        data.put("accessToken", getToken());
        data.put("paras", paras);
        data.put("dataFormat", "json");
        data.put("dataPart", "0");
        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);
        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject response = JSON.parseObject(responseEntity.getBody());
            JSONArray result = response.getJSONObject("result").getJSONObject("object").getJSONArray("row");
            logger.info(result.toJSONString());
            return result;
        } else {
            logger.error("DHR getData错误!");
            throw new Exception("DHR getData错误!");
        }
    }

    /**
     * putData后调用 解析推送数据
     * @return
     * @throws Exception
     */
    public String putClose(String userId, String funcId) throws Exception {
        String url = dhrDataUrl + "/PutClose";

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject paras = new JSONObject();
        paras.put("URID", dhrPutCloseUrid);

        JSONObject data = new JSONObject();
        data.put("funcId", funcId);
        data.put("accessToken", getToken());
        data.put("paras", paras);

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject result = JSON.parseObject(responseEntity.getBody());
            if (result.getString("msgId").equals("0")) {
                return "putClose成功:" + result.getString("msg");
            } else {
                logger.error(userId + "----DHR putClose错误:" + result.getString("msg"));
                throw new Exception("putClose错误:" + result.getString("msg"));
            }
        } else {
            logger.error(userId + "DHR putClose错误!");
            throw new Exception(userId + "DHR putClose错误!");
        }
    }

    /**
     * 向DHR推送入职人员数据 每次只推送一人
     * @param userId
     * @param bizData
     * @return
     * @throws Exception
     */
    public boolean putData(String userId, JSONArray bizData, String funcId) throws Exception {
        String url = dhrDataUrl + "/PutData";

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject paras = new JSONObject();
        paras.put("URID", dhrPutDataUrid);

        JSONObject data = new JSONObject();
        data.put("funcId", funcId);
        data.put("accessToken", getToken());
        data.put("paras", paras);
        data.put("bizData", bizData);

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject result = JSON.parseObject(responseEntity.getBody());
            if (result.getString("msgId").equals("0")) {
                return true;
            } else {
                logger.error(userId + "推送DHR错误:" + result.getString("msg"));
                return false;
            }
        } else {
            logger.error(userId + "推送DHR错误:" + responseEntity.getStatusCode());
            return false;
        }
    }


    /**
     * DHR推送数据前执行，清空临时表
     * @return
     * @throws Exception
     */
    public boolean putStart(String funcId) throws Exception {
        String url = dhrDataUrl + "/PutStart";

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject paras = new JSONObject();
        paras.put("URID", dhrPutStartUrid);

        JSONObject data = new JSONObject();
        data.put("funcId", funcId);
        data.put("accessToken", getToken());
        data.put("paras", paras);

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject result = JSON.parseObject(responseEntity.getBody());
            if (result.getString("msgId").equals("0")) {
                return true;
            } else {
                logger.error("DHR putStart错误:" + result.getString("msg"));
                throw new Exception("DHR putStart错误:" + result.getString("msg"));
            }
        } else {
            logger.error("DHR putStart错误!");
            throw new Exception("DHR putStart错误!");
        }
    }



    /**
     * 获取DHR token 优先从redis中获取
     * @return
     * @throws Exception
     */
    public String getToken() throws Exception{
        Object token=redisTemplate.opsForValue().get("mitexecutor_dhr_token");
        if (token == null){
            token = getDhrToken();
            redisTemplate.opsForValue().set("mitexecutor_dhr_token",token);
            redisTemplate.expire("mitexecutor_dhr_token",300, TimeUnit.SECONDS);
        }

        return (String) token;
    }

    /**
     * 获取DHR的token
     * @return
     */
    private String getDhrToken() throws Exception {
        String url = dhrDataUrl + "/StartSession";

        HttpHeaders headers=new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        JSONObject data = new JSONObject();
        data.put("acc", dhrAdmin);
        data.put("pwd", dhrPwd);

        HttpEntity<String> httpEntity = new HttpEntity<String>(data.toJSONString() ,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity ,String.class);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            JSONObject result = JSON.parseObject(responseEntity.getBody());
            if (result.getString("msgId").equals("0")) {
                return result.getString("result");
            } else {
                logger.error("获取DHR token错误:" + result.getString("msg"));
                throw new Exception("获取DHR token错误:" + result.getString("msg"));
            }
        } else {
            logger.error("获取DHR token错误!");
            throw new Exception("获取DHR token错误!");
        }
    }

    /**
     * @param key
     * @param redisKey
     * @param mdmKey
     * @return
     */
    private String getDhrValue(String key, String redisKey, String mdmKey) {
        Map<String,Object> map = redisTemplate.opsForHash().entries(redisKey);
        if (map.size() == 0){
            map = mmisMegviiConfigService.getConfigCodeMapByKey(mdmKey);
            redisTemplate.opsForHash().putAll(redisKey, map);
            redisTemplate.expire(redisKey, 120, TimeUnit.SECONDS);
        }
        return (String) map.get(key);
    }

    /**
     * @param key
     * @param redisKey
     * @param mdmKey
     * @return
     */
    private String getDhrCode(String key, String redisKey, String mdmKey) {
        Map<String,Object> map = redisTemplate.opsForHash().entries(redisKey);
        if (map.size() == 0){
            map = mmisMegviiConfigService.getConfigValueMapByKey(mdmKey);
            redisTemplate.opsForHash().putAll(redisKey, map);
            redisTemplate.expire(redisKey, 120, TimeUnit.SECONDS);
        }
        return (String) map.get(key);
    }



}
