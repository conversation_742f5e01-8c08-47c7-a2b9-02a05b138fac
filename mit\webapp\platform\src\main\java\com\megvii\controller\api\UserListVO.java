package com.megvii.controller.api;

import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.User;
import com.megvii.entity.opdb.contract.ContractStatus;
import com.megvii.entity.opdb.contract.UserChangeContractBacklog;
import com.megvii.entity.opdb.contract.UserContractBacklog;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.thymeleaf.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/4/16 16:56
 */
@Data
public class UserListVO {

	private int id;
	private String workNumber;
	private String userId;
	private String spell;
	private String team;
	private String workBase;
	private String expectDate;
	private String dimissionDate;
	private String employType;
	private String entryFlow;
	private String contractStatus;
	private String endDate;
	private String dimissionTag;
	/**
	 *新签署开始日期
	 */
	private String newConBeginDate;
	public static List<UserListVO> backlogToVOs (List<UserContractBacklog> users) {
		List<UserListVO> vos = new ArrayList<>();
		for (UserContractBacklog backlog : users) {
			vos.add(toVO(backlog.getUser(),backlog.getEndDate(),backlog.getDimissionTag()));
		}

		return vos;
	}
	public static List<UserListVO> changeBacklogToVOs (List<UserChangeContractBacklog> users) {
		List<UserListVO> vos = new ArrayList<>();
		for (UserChangeContractBacklog backlog : users) {
			vos.add(toVO1(backlog.getUser(),backlog.getNewConBeginDate()));
		}

		return vos;
	}

	public static List<UserListVO> toVOs (List<User> users) {
		List<UserListVO> vos = new ArrayList<>();
		for (User user : users) {
			vos.add(toVO(user));
		}

		return vos;
	}


	private static UserListVO toVO(User user) {
		UserListVO vo = new UserListVO();
		vo.setId(user.getId());
		vo.setWorkNumber(user.getWorkNumber());
		vo.setUserId(user.getUserId());
		vo.setSpell(user.getSpell());
		vo.setTeam(user.getTeam());
		vo.setWorkBase(user.getWorkBase());
		if (user.getExpectDate()!=null) vo.setExpectDate(DateTimeUtil.date2String(user.getExpectDate()));
		if (user.getDimissionDate()!=null) vo.setDimissionDate(DateTimeUtil.date2String(user.getDimissionDate()));
		vo.setEmployType(user.getEmployType());
		if (user.getStatus() == null || user.getStatus().equals("")) {
			vo.setEntryFlow(user.getEntryFlow());
		} else {
			vo.setEntryFlow(user.getStatus());
		}
		vo.setContractStatus(user.getContractStatus()!=null?user.getContractStatus():ContractStatus.NOTHING);

		return vo;
	}

	private static UserListVO toVO(User user,String endDate,String dimissionTag) {
		UserListVO vo = toVO(user);
		vo.setEndDate(StringUtils.isEmpty(endDate)?"":endDate.split(" ")[0]);
		vo.setDimissionTag(dimissionTag);
		return vo;
	}
	private static UserListVO toVO1(User user,String newConBeginDate) {
		UserListVO vo = toVO(user);
		vo.setNewConBeginDate(newConBeginDate);
		return vo;
	}
}
