package com.megvii;

import com.megvii.client.SmsRestClient;
import com.megvii.entity.opdb.User;
import com.megvii.mapper.filter.UserFilter;
import com.megvii.service.*;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2020/2/6 14:35
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestRemind {


    @Autowired
    private RemindService remindService;

    @Autowired
    private MailSendService mailSendService;

    @Autowired
    private UserService userService;

    @Autowired
    private SmsRestClient smsRestClient;

    @Autowired
    private TempUserService tempUserService;

    @Autowired
    private GlytService glytService;

    @Test
    public void testSend() {

//        remindService.nextWeekEntryRemind();

        UserFilter filter = new UserFilter();
        filter.setExpectDate("2020-02-17");

        List<User> list = userService.getUsersByFilter(filter);

        mailSendService.sendTomorrowEntryMail(list, null, "LALALALALA");




    }


    @Test
    public void testGlyt() {
        glytService.updateGlytUsers(-5);
    }




}
