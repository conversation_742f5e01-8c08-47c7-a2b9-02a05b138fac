import { Injectable } from '@angular/core'
import { SendRequest, UtilsService } from '../core/services'

@Injectable()
export class AuthService {
  constructor(private sendRequest: SendRequest) {}

  // 获取全部的人员列表
  getAllUser() {
    return this.sendRequest.get('/api/permission/alluser', {})
  }

  // http://10.168.0.68:8089/api/permission/user/role?userId=jiangyuhong
  // 根据人员获取全部的角色数组
  getRoleByUser(data) {
    return this.sendRequest.get('/api/permission/user/role', data)
  }

  // 获取全部角色
  getAllRole() {
    return this.sendRequest.get('/api/permission/role', {})
  }

  // // 模糊搜索人员列表，用于新签和合同管理
  // getUserList() {
  //   return this.sendRequest.post('/api/user/search', {});
  // }

  // 绑定角色
  postRoleByUser(data) {
    return this.sendRequest.post('/api/permission/user/role', data)
  }

  // 模糊搜索人员列表，用于新签和合同管理
  searchUser(data) {
    return this.sendRequest.post('/api/user/search', data)
  }
}
