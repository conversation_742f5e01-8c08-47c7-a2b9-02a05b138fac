package com.megvii.controller;

import com.megvii.entity.opdb.User;
import com.megvii.service.LdapService;
import com.megvii.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test")
public class testController {

    @Autowired
    private LdapService ldapService;

    @Autowired
    private UserService userService;

    public void test(){
        User user = userService.getUserByUserId("zhangliangliang03");
        ldapService.synUser2Ldap(user);
    }
}
