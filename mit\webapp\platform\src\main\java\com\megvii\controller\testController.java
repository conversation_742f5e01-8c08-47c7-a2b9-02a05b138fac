package com.megvii.controller;

import com.megvii.entity.opdb.User;
import com.megvii.service.LdapService;
import com.megvii.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.web.bind.annotation.*;

import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import java.util.*;

@RestController
@RequestMapping("/api/test")
public class testController {

    @Autowired
    private LdapService ldapService;

    @Autowired
    private UserService userService;

    @Autowired
    private LdapTemplate ldapTemplate;

    @Value("${spring.ldap.ou}")
    private String ou;

    public void test(){
        User user = userService.getUserByUserId("zhangliangliang03");
        ldapService.synUser2Ldap(user);
    }

    /**
     * 查看LDAP中的所有用户
     */
    @GetMapping("/ldap/users")
    public List<Map<String, Object>> getAllLdapUsers() {
        String filter = "(objectClass=user)";
        return ldapTemplate.search(ou, filter, new AttributesMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapFromAttributes(Attributes attributes) throws NamingException {
                Map<String, Object> userMap = new HashMap<>();

                // 获取常用属性
                addAttributeToMap(userMap, attributes, "sAMAccountName");
                addAttributeToMap(userMap, attributes, "displayName");
                addAttributeToMap(userMap, attributes, "mail");
                addAttributeToMap(userMap, attributes, "department");
                addAttributeToMap(userMap, attributes, "telephoneNumber");
                addAttributeToMap(userMap, attributes, "distinguishedName");
                addAttributeToMap(userMap, attributes, "userAccountControl");

                return userMap;
            }
        });
    }

    /**
     * 查看LDAP中的所有组
     */
    @GetMapping("/ldap/groups")
    public List<Map<String, Object>> getAllLdapGroups() {
        String filter = "(objectClass=group)";
        return ldapTemplate.search("ou=Groups,ou=Company", filter, new AttributesMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapFromAttributes(Attributes attributes) throws NamingException {
                Map<String, Object> groupMap = new HashMap<>();

                addAttributeToMap(groupMap, attributes, "sAMAccountName");
                addAttributeToMap(groupMap, attributes, "displayName");
                addAttributeToMap(groupMap, attributes, "description");
                addAttributeToMap(groupMap, attributes, "distinguishedName");

                // 获取组成员
                Attribute memberAttr = attributes.get("member");
                if (memberAttr != null) {
                    List<String> members = new ArrayList<>();
                    NamingEnumeration<?> memberEnum = memberAttr.getAll();
                    while (memberEnum.hasMore()) {
                        String memberDN = (String) memberEnum.next();
                        // 提取CN部分作为用户名
                        if (memberDN.startsWith("CN=")) {
                            String cn = memberDN.substring(3, memberDN.indexOf(","));
                            members.add(cn);
                        }
                    }
                    groupMap.put("members", members);
                    groupMap.put("memberCount", members.size());
                }

                return groupMap;
            }
        });
    }

    /**
     * 根据用户名查看特定用户信息
     */
    @GetMapping("/ldap/user/{userId}")
    public Map<String, Object> getLdapUser(@PathVariable String userId) {
        String filter = "(sAMAccountName=" + userId + ")";
        List<Map<String, Object>> users = ldapTemplate.search(ou, filter, new AttributesMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapFromAttributes(Attributes attributes) throws NamingException {
                Map<String, Object> userMap = new HashMap<>();

                // 获取所有属性
                NamingEnumeration<? extends Attribute> attrEnum = attributes.getAll();
                while (attrEnum.hasMore()) {
                    Attribute attr = attrEnum.next();
                    String attrName = attr.getID();

                    if (attr.size() == 1) {
                        userMap.put(attrName, attr.get());
                    } else {
                        List<Object> values = new ArrayList<>();
                        NamingEnumeration<?> valueEnum = attr.getAll();
                        while (valueEnum.hasMore()) {
                            values.add(valueEnum.next());
                        }
                        userMap.put(attrName, values);
                    }
                }

                return userMap;
            }
        });

        return users.isEmpty() ? null : users.get(0);
    }

    /**
     * 查看LDAP目录结构
     */
    @GetMapping("/ldap/structure")
    public List<String> getLdapStructure() {
        try {
            // 查看基础目录结构
            List<String> structure = new ArrayList<>();
            structure.addAll(ldapTemplate.list(""));
            structure.addAll(ldapTemplate.list("ou=Company"));
            structure.addAll(ldapTemplate.list("ou=Users,ou=Company"));
            structure.addAll(ldapTemplate.list("ou=Groups,ou=Company"));
            return structure;
        } catch (Exception e) {
            return Arrays.asList("Error: " + e.getMessage());
        }
    }

    /**
     * 搜索LDAP数据
     */
    @GetMapping("/ldap/search")
    public List<Map<String, Object>> searchLdap(
            @RequestParam(defaultValue = "") String baseDn,
            @RequestParam(defaultValue = "(objectClass=*)") String filter,
            @RequestParam(defaultValue = "10") int limit) {

        if (baseDn.isEmpty()) {
            baseDn = ou;
        }

        List<Map<String, Object>> results = ldapTemplate.search(baseDn, filter, new AttributesMapper<Map<String, Object>>() {
            @Override
            public Map<String, Object> mapFromAttributes(Attributes attributes) throws NamingException {
                Map<String, Object> resultMap = new HashMap<>();

                // 获取主要属性
                addAttributeToMap(resultMap, attributes, "distinguishedName");
                addAttributeToMap(resultMap, attributes, "objectClass");
                addAttributeToMap(resultMap, attributes, "sAMAccountName");
                addAttributeToMap(resultMap, attributes, "displayName");
                addAttributeToMap(resultMap, attributes, "mail");

                return resultMap;
            }
        });

        // 限制返回结果数量
        return results.size() > limit ? results.subList(0, limit) : results;
    }

    /**
     * 辅助方法：将属性添加到Map中
     */
    private void addAttributeToMap(Map<String, Object> map, Attributes attributes, String attributeName) throws NamingException {
        Attribute attr = attributes.get(attributeName);
        if (attr != null) {
            if (attr.size() == 1) {
                map.put(attributeName, attr.get());
            } else {
                List<Object> values = new ArrayList<>();
                NamingEnumeration<?> valueEnum = attr.getAll();
                while (valueEnum.hasMore()) {
                    values.add(valueEnum.next());
                }
                map.put(attributeName, values);
            }
        }
    }
}
