package com.megvii.common;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/18 14:32
 */
public class CheckJsonValidUtil {

    /**
     * 检查json信息 不为null
     * @param map
     * @param keys
     * @return
     */
    public static String checkJsonArgs(Map<String, String> map, List<String> keys) {
        StringBuilder msg = new StringBuilder();
        for (String key : keys) {
            String value = map.get(key);
            if (value == null) {
                msg.append(key + "不能为null***");
            }
        }
        if (msg.toString().equals("")) {
            return null;
        } else {
            return msg.toString();
        }
    }




}
