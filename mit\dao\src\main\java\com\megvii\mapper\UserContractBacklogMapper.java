package com.megvii.mapper;

import com.megvii.entity.opdb.contract.UserContractBacklog;
import com.megvii.mapper.filter.UserBacklogFilter;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2020/4/10 18:37
 */
public interface UserContractBacklogMapper {

	int insertUserContractBacklog(UserContractBacklog backlog);

	int updateUserContractBacklog(UserContractBacklog backlog);
	int updateUserContractBacklogTag(UserContractBacklog backlog);

	List<UserContractBacklog> selectBacklogByFilter(@Param("filter")UserBacklogFilter filter);

	UserContractBacklog selectNotFinishBacklogByUserId(String userId);

	UserContractBacklog selectBacklogByUserId(String userId);


}
