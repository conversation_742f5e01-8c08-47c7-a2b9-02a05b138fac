import { Injectable } from '@angular/core'

@Injectable({
  providedIn: 'root'
})
export class LocalStorage {
  public localStorage: any = (<any>window).localStorage

  constructor() {
    if (!localStorage) {
      throw new Error('Current browser does not support Local Storage')
    }
    this.localStorage = localStorage
  }

  public set(key: string, value: string): void {
    this.localStorage[key] = value
  }

  public get(key: string): string {
    return this.localStorage[key] || false
  }

  public setObject(key: string, value: any, expired?: any): void {
    this.localStorage[key] = JSON.stringify(value || {})
    if (expired) {
      // 天为单位
      this.localStorage[`${key}_expires`] = Date.now() + 1000 * 60 * 60 * 24 * expired
    }
  }

  public getObject(key: string): any {
    const expired = this.localStorage[`${key}_expires`]
    const now = Date.now()
    if (expired && expired <= now) {
      this.remove(key)
    }
    return JSON.parse(this.localStorage[key] || '{}')
  }

  public remove(key: string): any {
    this.localStorage.removeItem(key)
  }
}
