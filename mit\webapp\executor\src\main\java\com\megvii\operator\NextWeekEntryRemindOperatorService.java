package com.megvii.operator;

import com.megvii.common.OperateResult;
import com.megvii.service.RemindService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/2/11 19:34
 */
@Service
public class NextWeekEntryRemindOperatorService extends BaseOperatorService {

    @Autowired
    private NextWeekEntryRemindOperatorService nextWeekEntryRemindOperatorService;

    @Autowired
    private RemindService remindService;

    @Override
    public OperateResult operateMain(Map paraMap, Object... params) throws Exception {

         return nextWeekEntryRemindOperatorService.operateOnce(paraMap, params);

    }

    /**
     * 单次操作
     * @param paraMap
     * @param params  参数
     * @return
     * @throws Exception
     */
    @Override
    protected OperateResult operateOnce(Map paraMap, Object... params) throws Exception {
        String result = remindService.nextWeekEntryRemind();
        return new OperateResult(1, result);

    }


}
