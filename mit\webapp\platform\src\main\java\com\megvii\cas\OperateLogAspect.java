package com.megvii.cas;

import com.alibaba.fastjson.JSON;
import com.megvii.common.CommonException;
import com.megvii.common.IPUtils;
import com.megvii.controller.api.OperateLog;
import com.megvii.entity.opdb.OperateLogDO;
import com.megvii.entity.opdb.User;
import com.megvii.service.OperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Objects;

/**
 * 利用Spring AOP方式记录操作日志
 *
 * <AUTHOR>
 * @date 2020/04/17
 */
@Aspect
@Component
@Slf4j
public class OperateLogAspect {

    @Autowired
    private OperateLogService logService;

    @Around(value = "@annotation(operateLog)")
    public Object opetateLog(ProceedingJoinPoint pjp, final OperateLog operateLog) throws Throwable {
        //开始时间
        long startTime = System.currentTimeMillis();
        //获取操作用户信息
        User sysUserDO = (User)SecurityUtils.getSubject().getPrincipal();
        String operateUser = sysUserDO.getUserId();
        if (StringUtils.isEmpty(operateUser)){
            //默认第三方调用
            operateUser = "client";
        }
        //获取请求信息
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String realIp = IPUtils.getRealIP(request);
        String requestUri = request.getRequestURI();
        //获取参数数组 排除上传文件
        Object[] args = Arrays.stream(pjp.getArgs()).filter(arg ->
                (!(arg instanceof HttpServletRequest) && !(arg instanceof MultipartFile) && !(arg instanceof HttpServletResponse))).toArray();
        String requestParams = JSON.toJSONString(args);
        OperateLogDO operateLogDO = new OperateLogDO(operateUser,requestUri,requestParams,realIp,operateLog.operType().getCode());
        try {
            //执行原方法并获取返回结果
            Object ret = pjp.proceed(pjp.getArgs());
            String result = JSON.toJSONString(ret);
            //结束时间
            long endTime = System.currentTimeMillis();
            operateLogDO.setResponseResult(result);
            operateLogDO.setTimeConsuming((int)(endTime - startTime));
            logService.save(operateLogDO);
            return ret;
        }catch(Throwable throwable){
            String result = JSON.toJSONString(throwable.getMessage());
            //结束时间
            long endTime = System.currentTimeMillis();
            operateLogDO.setResponseResult(result);
            operateLogDO.setTimeConsuming((int)(endTime - startTime));
            logService.save(operateLogDO);
            log.error("执行{}方法出错: {}, {}", pjp.getSignature().toString(), throwable.getMessage(), throwable.getStackTrace());
            if (throwable.getClass().equals(CommonException.class)){
                throw new CommonException(throwable.getMessage(), throwable.getCause());
            }else {
                throw new RuntimeException(throwable.getMessage(), throwable.getCause());
            }
        }
    }
}
