<ng-container *ngIf="isShowSelf">
  <nz-breadcrumb>
    <nz-breadcrumb-item> 合同管理 </nz-breadcrumb-item>
    <nz-breadcrumb-item> 合同转签 </nz-breadcrumb-item>
  </nz-breadcrumb>
  <div class="content-block content-item">
    <app-title title="合同转签"></app-title>
    <nz-tabset
      nzSize="large"
      (nzSelectedIndexChange)="selectedIndexChange($event)"
      [nzSelectedIndex]="selectedIndex"
    >
      <nz-tab [nzTitle]="endorseTpl">
        <ng-template #endorseTpl>
          <nz-badge [nzCount]="endorse.total || 0" [nzOffset]="[15, -5]">
            合同主体变更
          </nz-badge>
        </ng-template>
        <app-contract-table type="endorse" #endorse></app-contract-table>
      </nz-tab>
    </nz-tabset>
  </div>
</ng-container>
<router-outlet
  (activate)="onActivate($event)"
  (deactivate)="onDeactivate($event)"
>
</router-outlet>
