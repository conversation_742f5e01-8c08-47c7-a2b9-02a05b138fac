import { throwError as observableThrowError, Observable } from 'rxjs'
import { catchError, tap } from 'rxjs/operators'
import { Injectable } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { environment } from '../../../environments/environment'
import { Router } from '@angular/router'

@Injectable({
  providedIn: 'root'
})
export class SendRequest {
  constructor(private httpClient: HttpClient, private router: Router) {}

  private _send(method: string, url: string, params: any, body: any, headers: any) {
    const options: any = {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        ...headers
      },
      params,
      body
    }
    return this.httpClient
      .request(method, environment.urlPrefix + url, options)
      .pipe(catchError(this._handleError.bind(this)))
  }

  private _handleError(error: Response) {
    if (error.status === 408) {
      ;(<any>window).location.href =
        environment.ssoUrl +
        '/login?service=' +
        escape(environment.loginUrl + '?callback=' + encodeURI((<any>window).location.href))
    }
    return observableThrowError(error || 'Server error')
  }

  send(method: string, url: string, params: any, data: any, headers: any = {}): Observable<any> {
    return this._send(method, url, params, data, headers)
  }

  get(url: string, params: any, body: any = {}, headers: any = {}): Observable<any> {
    return this._send('GET', url, params, body, headers)
  }

  post(url: string, data: any, headers: any = {}): Observable<any> {
    return this._send('POST', url, {}, data, headers)
  }

  put(url: string, data: any, headers: any = {}): Observable<any> {
    return this._send('PUT', url, {}, data, headers)
  }

  patch(url: string, data: any, headers: any = {}): Observable<any> {
    return this._send('PATCH', url, {}, data, headers)
  }

  delete(url: string, params: string, headers: any = {}): Observable<any> {
    return this._send('DELETE', url, params, {}, headers)
  }
  private upsend(method: string, url: string, params: any, body: any, headers: any) {
    const options: any = {
      withCredentials: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        ...headers
      },
      params,
      body
    }
    return this.httpClient
      .request(method, environment.urlPrefix + url, options)
      .pipe(catchError(this._handleError.bind(this)))
  }
  uploadFile(url: string, data: any, headers: any = {}): Observable<any> {
    return this.upsend('POST', url, {}, data, headers)
  }
}
