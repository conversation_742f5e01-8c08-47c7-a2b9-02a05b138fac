package com.megvii;

import com.megvii.client.NsqProduceClient;
import com.megvii.entity.opdb.GAGroup;
import com.megvii.service.GAGroupService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestNsq {

    @Autowired
    private NsqProduceClient nsqProduceClient;

    @Autowired
    private GAGroupService gaGroupService;

    @Test
    public void testNsq() {
        GAGroup gaGroup = gaGroupService.getGroupByEmail("-<EMAIL>");
        System.out.println(gaGroup.getGid());
    }



}
