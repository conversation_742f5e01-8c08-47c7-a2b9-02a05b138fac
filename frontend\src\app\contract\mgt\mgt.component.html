<ng-container *ngIf="isShowSelf">
  <nz-breadcrumb>
    <nz-breadcrumb-item>
      合同管理
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>
      合同信息管理
    </nz-breadcrumb-item>
  </nz-breadcrumb>
  <div class="content-block content-item">
    <app-title title="合同管理"></app-title>
    <nz-tabset
      nzSize="large"
      (nzSelectedIndexChange)="selectedIndexChange($event)"
      [nzSelectedIndex]="selectedIndex"
    >
      <nz-tab nzTitle="当前在职">
        <app-contract-table type="active"></app-contract-table>
      </nz-tab>
      <nz-tab nzTitle="已离职">
        <app-contract-table type="quit"></app-contract-table>
      </nz-tab>
    </nz-tabset>
  </div>
</ng-container>

<router-outlet
  (activate)="onActivate($event)"
  (deactivate)="onDeactivate($event)"
>
</router-outlet>
