<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.RoleMapper">

    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.Role">
        <id column="id" property="id"/>
        <result column="role_type" property="roleType"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <result column="usable_status" property="usableStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

     <select id="selectRoleByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from it_mit_role where role_code = #{code}
    </select>

    <insert id="insertRole" parameterType="com.megvii.entity.opdb.Role" >
        INSERT INTO `it_mit_role` (
	        `id`,
	        `role_type`,
	        `role_name`,
	        `role_code`,
	        `usable_status`,
	        `create_time`,
	        `create_user`,
	        `update_time`,
	        `update_user`
        )
        VALUES
	    (
		    0,
		    #{roleType},
		    #{roleName},
	    	#{roleCode},
		    #{usableStatus},
		    NOW(),
		    #{createUser},
		    NOW(),
		    #{updateUser}
	    );
    </insert>

    <update id="updateRole" parameterType="com.megvii.entity.opdb.Role">
        update it_mit_role
        <set>
            update_time = NOW(),
            <if test="usableStatus != null ">
                usable_status = #{usableStatus},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectAllRole" resultMap="BaseResultMap" parameterType="java.lang.String">
        select * from it_mit_role
    </select>



</mapper>
