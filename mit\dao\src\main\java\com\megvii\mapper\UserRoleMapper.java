package com.megvii.mapper;

import com.megvii.entity.opdb.UserRole;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/9 17:37
 */
public interface UserRoleMapper {

	int insertUserRole(UserRole userRole);

	int updateUserRole(UserRole userRole);

	int removeUserRole(int id);

	List<UserRole> selectUserRole();

	List<UserRole> selectUserRoleByUserId(String userId);

	List<String> selectAllUser();



}
