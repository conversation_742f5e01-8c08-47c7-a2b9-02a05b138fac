package com.megvii.mapper;

import com.megvii.entity.opdb.UserDimissionProcessBacklogPO;
import org.apache.ibatis.annotations.Param;

public interface UserDimissionProcessBacklogMapper {
    Boolean addProcess(UserDimissionProcessBacklogPO po);
    Boolean updateProcess(UserDimissionProcessBacklogPO po);

    UserDimissionProcessBacklogPO  getProcess(@Param("dimissionId") String dimissionId);

    Boolean deleteProcess(@Param("dimissionId") String dimissionId);
}
