package com.megvii.service;

import com.megvii.common.CheckJsonValidUtil;
import com.megvii.common.DateTimeUtil;
import com.megvii.entity.opdb.Team;
import com.megvii.mapper.EntryDateMapper;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/12/30 16:17
 */
@Service
@PropertySource(value = "classpath:group.properties",encoding = "UTF-8")
public class CheckService {

	Logger logger= LoggerFactory.getLogger(CheckService.class);

    @Value("${DATA_BIAOZHU_TEAM_CODE}")
    private String biaozhuTeamCode;

    @Autowired
	private TeamService teamService;

    @Autowired
    private EntryDateMapper entryDateMapper;

    @Autowired
    private SyncZTDataService syncZTDataService;


    /**
     * 根据部门名称判断是否是研究院的部门
     * 查询顶层部门判断是否是G05
     * @param name
     * @return
     */
    public boolean checkRTeam(String name) {
        if (name.indexOf("研究院")!=-1) {
            Team team = teamService.getTeamByName(name);
            if (team == null) {
                return false;
            }
            Team topTeam = syncZTDataService.getTopParentTeam(team);
            if ("G05".equals(topTeam.getSfCode())) {
                return true;
            }
        }
        return false;
    }

	/**
	 * 检查OA回传的必填字段
	 * @param map
	 * @return
	 */
    public String checkKey(Map<String, String> map) {
        List<String> keys = new ArrayList<>();
        keys.add("user_id");
        keys.add("spell");
        keys.add("cell");
        keys.add("employ_type");
        keys.add("company");
        keys.add("work_base");
        keys.add("email");
        keys.add("source");
        keys.add("seat_number");
        keys.add("expect_date");
        keys.add("team");
        keys.add("mentor");
        keys.add("employ_base");
        keys.add("computer");
        keys.add("os");
        keys.add("work_number");
        keys.add("buddy");
        keys.add("sequence");
//        keys.add("sales");

        //检查字段
        return CheckJsonValidUtil.checkJsonArgs(map, keys);
	}


	/**
	 * 检查OA回传的部门和标注的tag是否匹配
	 * @param map
	 * @return
	 */
	public boolean checkDataTeamAndTag(Map<String, String> map) {

		//OA回传的部门
		Team team = teamService.getTeamByCode(map.get("team"));
		//标注中心的部门树
		Team biaozhuTeam = teamService.getTeamByCode(biaozhuTeamCode);

		//部门标注  tag旷视
		if (team.getName().indexOf(biaozhuTeam.getShortName())!=-1 && "否".equals(map.get("biaozhu"))) {
			return false;
		}
		//部门旷视 tag标注
		if (team.getName().indexOf(biaozhuTeam.getShortName())==-1 && "是".equals(map.get("biaozhu"))) {
			return false;
		}
		return true;
	}



    /** 计算入职日期、合同到期时间
     * @param expectDate
     * @return
     */
    public Map<String, Object> getHireDate(String expectDate,String employType){
        Map<String,Object> dateMap = new HashMap<>();
        if(expectDate != null && !"".equals(expectDate.trim()) && !"0001/1/1 0:00:00".equals(expectDate) && !"1/1/0001 12:00:00 AM".equals(expectDate)){
            LocalDateTime date = null;
            //日志转换
            if(expectDate.toLowerCase().contains("am") || expectDate.toLowerCase().contains("pm")){
                try{
                    date = DateTimeUtil.string2DateTime2(expectDate);
                }catch (Exception ex){
                    logger.error(expectDate,ex);
                }
            }else {
                try{
                    date = DateTimeUtil.string2DateTime3(expectDate);
                }catch (Exception e){
                    logger.error(expectDate,e);
                }
            }

            UserHireDate userHireDate = new UserHireDate(date);

            try {
                 //根据入职规则对入职日期进行调整
                date = getMonOrWedDay(employType, userHireDate).hireDate;

            } catch (Exception ex) {
                logger.error("getHireDate,日期解析错误", ex);
            }


            dateMap.put("expectDate",date);
            LocalDateTime regularDate=null;
            if (date!=null&&employType!=null && "正式员工".equals(employType)){
                regularDate=DateTimeUtil.dateAddInt(date,180);
            }
            dateMap.put("regularDate",regularDate);
            dateMap.put("isDelay",userHireDate.isDelayEntry);
        }
        return dateMap;
    }



	 /**
     * 获取最近的入职日期
     * 1.white
     * 2。取最近的周一周三
     * 3.是否已经过了
     * 4.是否月末
     * 5.black
     *
     * @param userHireDate
     * @return
     */
    private UserHireDate getMonOrWedDay(String employType,UserHireDate userHireDate) throws Exception {

        List<LocalDateTime> entryDateList = entryDateMapper.selectEntryDate(DateTimeUtil.dateAddInt(userHireDate.hireDate, -2));
        List<LocalDateTime> entryBlackDateList = entryDateMapper.selectBlackEntryDate(DateTimeUtil.dateAddInt(userHireDate.hireDate, -2));

        //是否在特殊入职日期中
        if (entryDateList.contains(userHireDate.hireDate)) {
            return userHireDate;
        }

        LocalDateTime date = userHireDate.hireDate;
        LocalDateTime monthLastDay = DateTimeUtil.getMonthLastDay(date);
        LocalDateTime monday = DateTimeUtil.getMonDayOfWeek(date);
        LocalDateTime wednesday = DateTimeUtil.getWednesdayOfWeek(date);

        if (DateTimeUtil.diffDateByDay(date, monday) > 0 && DateTimeUtil.diffDateByDay(date, wednesday) < 0) {
            date = wednesday;
            userHireDate.isDelayEntry=true;
        } else if (DateTimeUtil.diffDateByDay(date, wednesday) > 0) {
            date = DateTimeUtil.getMonDayOfNextWeek(date);
            userHireDate.isDelayEntry=true;
        }

        userHireDate.hireDate=date;

        //是否是已过时间，
        if(LocalDateTime.now().isAfter(date)){
            userHireDate.hireDate=DateTimeUtil.dateAddInt(date,1);
            userHireDate.isDelayEntry=true;
            return getMonOrWedDay(employType,userHireDate);
        }

        //如果date处于当月最后七天，则推迟到下月
        if ((employType == null || employType.indexOf("实习生") == -1)
                && DateTimeUtil.diffDateByDay(monthLastDay, date) <= 6 && date.getDayOfMonth() > 25) {
            userHireDate.hireDate = DateTimeUtil.getfirstDayOfNextMonth(monthLastDay);
            userHireDate.isDelayEntry = true;
            return getMonOrWedDay(employType,userHireDate);
        }

        //是否在排除的入职日期里，如果是，日期加1再递归调用getMonOrWedDay
        if (entryBlackDateList.contains(userHireDate.hireDate)) {
            userHireDate.hireDate = DateTimeUtil.dateAddInt(date, 1);
            userHireDate.isDelayEntry = true;
            return getMonOrWedDay(employType,userHireDate);
        }

        return userHireDate;
    }



     class UserHireDate{
        boolean isDelayEntry = false;
        LocalDateTime hireDate;

        public UserHireDate() {
        }

        public UserHireDate(LocalDateTime hireDate) {
            this.hireDate = hireDate;
        }
    }


}
