<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.megvii.mapper.OperateLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.megvii.entity.opdb.OperateLogDO">
        <id column="id" property="id" />
        <result column="operate_user_id" property="operateUserId" />
        <result column="operate_date" property="operateDate" />
        <result column="request_url" property="requestUrl" />
        <result column="request_params" property="requestParams" />
        <result column="request_ip" property="requestIp" />
        <result column="response_result" property="responseResult" />
        <result column="time_consuming" property="timeConsuming" />
        <result column="operate_type" property="operateType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, operate_user_id, operate_date, request_url, request_params, request_ip, response_result, time_consuming,operate_type
    </sql>


    <select id="getOperateLogs" resultMap="BaseResultMap" parameterType="java.lang.String">
        select l.operate_date,l.request_url,l.request_params,l.request_ip,l.time_consuming,l.operate_user_id,l.operate_type
        from sys_operate_log l
        <where>
            <if test="vo.operateUserId != null and vo.operateUserId != ''">
              and l.operate_user_id like concat('%', #{vo.operateUserId}, '%')
            </if>
            <if test="vo.startTime != null and vo.startTime != '' and vo.endTime != null and vo.endTime != ''" >
              and date_format(l.operate_date,'%Y-%m-%d')  &gt;= DATE_FORMAT( #{vo.startTime} , '%Y-%m-%d' )
              and date_format(l.operate_date,'%Y-%m-%d')  &lt;= DATE_FORMAT( #{vo.endTime} , '%Y-%m-%d' )
            </if>
            <if test="vo.requestUrl != null and vo.requestUrl != ''">
              and l.request_url like concat('%', #{vo.requestUrl}, '%')
            </if>
            <if test="vo.requestIp != null and vo.requestIp != ''">
                and l.request_ip like concat('%', #{vo.requestIp}, '%')
            </if>
        </where>
        order by operate_date desc
    </select>

    <insert id="save" parameterType="com.megvii.entity.opdb.OperateLogDO" >
        insert into sys_operate_log(
        `id`,
operate_user_id,operate_date, request_url, request_params, request_ip, response_result, time_consuming,operate_type
        )values (
		    0,
		    #{operateUserId},
		    #{operateDate},
		    #{requestUrl},
		    #{requestParams},
		    #{requestIp},
		    #{responseResult},
		    #{timeConsuming},
		    #{operateType}
		    )
    </insert>
</mapper>
